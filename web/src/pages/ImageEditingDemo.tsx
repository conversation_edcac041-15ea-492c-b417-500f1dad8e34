import React, { useState, useCallback } from 'react';
import { useImageEditing } from '@/lib/image-editing/hooks/use-image-editing';
import { useImageEditingQueue } from '@/lib/image-editing/hooks/use-image-editing-queue';
import { useImageEditingAnalytics } from '@/lib/image-editing/hooks/use-image-editing-analytics';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Upload, Image, Zap, BarChart, Clock, AlertCircle } from 'lucide-react';

export default function ImageEditingDemo() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [editingPreferences, setEditingPreferences] = useState({
    style: 'natural' as const,
    conservative: true,
    maxBudget: 5.0
  });

  // Main image editing hook
  const {
    isLoading,
    progress,
    result,
    error,
    analyzeImage,
    generateEditedImage,
    cancelAnalysis,
    analytics
  } = useImageEditing({
    enableQueue: true,
    enableBatch: true,
    userId: 'demo-user'
  });

  // Queue management hook
  const {
    queueStatus,
    queueHealth,
    pauseQueue,
    resumeQueue,
    clearQueue
  } = useImageEditingQueue();

  // Analytics hook
  const {
    analytics: detailedAnalytics,
    recentErrors,
    systemHealth,
    refreshAnalytics,
    exportAnalytics
  } = useImageEditingAnalytics();

  // Handle file selection
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  }, []);

  // Handle analysis start
  const handleAnalyze = useCallback(async () => {
    if (!selectedFile) return;

    try {
      await analyzeImage(selectedFile, editingPreferences);
    } catch (error) {
      console.error('Analysis failed:', error);
    }
  }, [selectedFile, editingPreferences, analyzeImage]);

  // Handle image generation
  const handleGenerateEdit = useCallback(async (recommendation: any) => {
    if (!previewUrl) return;

    try {
      // Convert preview URL to base64
      const response = await fetch(previewUrl);
      const blob = await response.blob();
      const reader = new FileReader();
      
      reader.onload = async () => {
        const base64 = (reader.result as string).split(',')[1];
        const editedImage = await generateEditedImage(recommendation, base64);
        
        // Create blob URL for preview
        const editedBlob = new Blob([Buffer.from(editedImage, 'base64')], { type: 'image/png' });
        const editedUrl = URL.createObjectURL(editedBlob);
        
        // You would handle the edited image here
        console.log('Generated edited image:', editedUrl);
      };
      
      reader.readAsDataURL(blob);
    } catch (error) {
      console.error('Image generation failed:', error);
    }
  }, [previewUrl, generateEditedImage]);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Image Editing Demo</h1>
        <p className="text-muted-foreground">
          Upload an image to get AI-powered editing recommendations
        </p>
      </div>

      <Tabs defaultValue="upload" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="upload">Upload & Analyze</TabsTrigger>
          <TabsTrigger value="queue">Queue Status</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="health">System Health</TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="space-y-6">
          {/* File Upload Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="w-5 h-5" />
                Upload Image
              </CardTitle>
              <CardDescription>
                Select an image to analyze for editing opportunities
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-4">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
                />
                
                {selectedFile && (
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">
                      {selectedFile.name}
                    </Badge>
                    <Badge variant="outline">
                      {(selectedFile.size / 1024 / 1024).toFixed(1)} MB
                    </Badge>
                  </div>
                )}
              </div>

              {previewUrl && (
                <div className="relative">
                  <img
                    src={previewUrl}
                    alt="Preview"
                    className="max-w-full max-h-96 rounded-lg border"
                  />
                </div>
              )}

              {/* Analysis Controls */}
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Style Preference</label>
                  <select
                    value={editingPreferences.style}
                    onChange={(e) => setEditingPreferences(prev => ({ 
                      ...prev, 
                      style: e.target.value as any 
                    }))}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="natural">Natural</option>
                    <option value="enhanced">Enhanced</option>
                    <option value="artistic">Artistic</option>
                    <option value="professional">Professional</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Max Budget</label>
                  <input
                    type="number"
                    value={editingPreferences.maxBudget}
                    onChange={(e) => setEditingPreferences(prev => ({ 
                      ...prev, 
                      maxBudget: parseFloat(e.target.value) 
                    }))}
                    step="0.50"
                    min="0.50"
                    max="10.00"
                    className="w-full p-2 border rounded-md"
                  />
                </div>
              </div>

              <div className="flex space-x-2">
                <Button
                  onClick={handleAnalyze}
                  disabled={!selectedFile || isLoading}
                  className="flex-1"
                >
                  {isLoading ? (
                    <>
                      <Clock className="w-4 h-4 mr-2 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Zap className="w-4 h-4 mr-2" />
                      Analyze Image
                    </>
                  )}
                </Button>

                {isLoading && (
                  <Button
                    variant="outline"
                    onClick={cancelAnalysis}
                  >
                    Cancel
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Progress Section */}
          {progress && (
            <Card>
              <CardHeader>
                <CardTitle>Analysis Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>{progress.message}</span>
                    <span>{progress.progress.toFixed(0)}%</span>
                  </div>
                  <Progress value={progress.progress} />
                  <div className="text-xs text-muted-foreground">
                    Stage: {progress.stage} | Step: {progress.currentStep || 'N/A'}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Results Section */}
          {result && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Image className="w-5 h-5" />
                  Editing Recommendations
                </CardTitle>
                <CardDescription>
                  Estimated improvement: {result.estimatedImprovementScore}%
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {result.recommendations.map((rec, index) => (
                    <div
                      key={rec.id}
                      className="p-4 border rounded-lg space-y-3"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant={rec.priority === 'high' ? 'default' : 'secondary'}>
                            {rec.priority}
                          </Badge>
                          <h3 className="font-semibold">{rec.title}</h3>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">${rec.estimatedCost.toFixed(2)}</Badge>
                          <Badge variant="outline">{rec.difficulty}</Badge>
                        </div>
                      </div>
                      
                      <p className="text-sm text-muted-foreground">
                        {rec.description}
                      </p>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">
                          Expected: {rec.expectedImprovement}
                        </span>
                        <Button
                          size="sm"
                          onClick={() => handleGenerateEdit(rec)}
                        >
                          Generate Edit
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="queue" className="space-y-6">
          {/* Queue Status */}
          <Card>
            <CardHeader>
              <CardTitle>Queue Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{queueStatus.queueSize}</div>
                  <div className="text-sm text-muted-foreground">In Queue</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{queueStatus.processing}</div>
                  <div className="text-sm text-muted-foreground">Processing</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {Math.round(queueStatus.averageWaitTime / 1000)}s
                  </div>
                  <div className="text-sm text-muted-foreground">Avg Wait</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {Math.round(queueStatus.estimatedWaitTime / 1000)}s
                  </div>
                  <div className="text-sm text-muted-foreground">Est. Wait</div>
                </div>
              </div>
              
              <div className="mt-4 flex space-x-2">
                <Button variant="outline" onClick={pauseQueue}>
                  Pause
                </Button>
                <Button variant="outline" onClick={resumeQueue}>
                  Resume
                </Button>
                <Button variant="destructive" onClick={clearQueue}>
                  Clear Queue
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Queue Health */}
          <Card>
            <CardHeader>
              <CardTitle>Queue Health</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${
                    queueHealth.isHealthy ? 'bg-green-500' : 'bg-red-500'
                  }`} />
                  <span className="font-medium">
                    {queueHealth.isHealthy ? 'Healthy' : 'Issues Detected'}
                  </span>
                </div>
                
                {queueHealth.issues.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Issues:</h4>
                    {queueHealth.issues.map((issue, index) => (
                      <Alert key={index} variant="destructive">
                        <AlertDescription>{issue}</AlertDescription>
                      </Alert>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {/* Analytics Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart className="w-5 h-5" />
                Analytics Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {detailedAnalytics.totalRequests}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Requests</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {detailedAnalytics.successRate.toFixed(1)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Success Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {detailedAnalytics.averageResponseTime.toFixed(0)}ms
                  </div>
                  <div className="text-sm text-muted-foreground">Avg Response</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {detailedAnalytics.errorRate.toFixed(1)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Error Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {detailedAnalytics.cacheHitRate.toFixed(1)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Cache Hit Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    ${detailedAnalytics.costAnalysis.totalCost.toFixed(2)}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Cost</div>
                </div>
              </div>

              <div className="mt-4 flex space-x-2">
                <Button variant="outline" onClick={refreshAnalytics}>
                  Refresh
                </Button>
                <Button variant="outline" onClick={() => exportAnalytics('json')}>
                  Export JSON
                </Button>
                <Button variant="outline" onClick={() => exportAnalytics('csv')}>
                  Export CSV
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Recent Errors */}
          {recentErrors.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Recent Errors</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {recentErrors.slice(0, 5).map((error, index) => (
                    <Alert key={index} variant="destructive">
                      <AlertDescription>
                        <div className="flex justify-between items-start">
                          <span>{error.message}</span>
                          <span className="text-xs">
                            {new Date(error.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="health" className="space-y-6">
          {/* System Health */}
          <Card>
            <CardHeader>
              <CardTitle>System Health</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Status</span>
                  <Badge variant={
                    systemHealth.status === 'healthy' ? 'default' :
                    systemHealth.status === 'degraded' ? 'secondary' : 'destructive'
                  }>
                    {systemHealth.status}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-muted-foreground">Response Time</div>
                    <div className="font-medium">{systemHealth.responseTime}ms</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Error Rate</div>
                    <div className="font-medium">{systemHealth.errorRate.toFixed(1)}%</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Cache Hit Rate</div>
                    <div className="font-medium">{systemHealth.cacheHitRate.toFixed(1)}%</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Active Requests</div>
                    <div className="font-medium">{systemHealth.activeRequests}</div>
                  </div>
                </div>
                
                <div className="text-xs text-muted-foreground">
                  Last checked: {new Date(systemHealth.lastCheck).toLocaleString()}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}