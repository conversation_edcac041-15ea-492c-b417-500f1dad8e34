import { <PERSON><PERSON><PERSON>, Brain, Camera, Check, Crown, Sparkles, TrendingUp, User, X } from "lucide-react";
import { <PERSON> } from "@tanstack/react-router";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function AnalysisComparison() {
  const features = [
    {
      category: "AI Model",
      basic: "Gemini 2.5 Flash / GPT-4o Mini",
      advanced: "OpenRouter o3 (Latest Reasoning Model)",
      basicIcon: "⚡",
      advancedIcon: "🧠"
    },
    {
      category: "Analysis Depth",
      basic: "6 Standard Steps",
      advanced: "Multi-Expert, Multi-Phase Analysis",
      basicIcon: "📊",
      advancedIcon: "🔬"
    },
    {
      category: "Expert Perspectives",
      basic: "Single AI Analysis",
      advanced: "5 Expert Personas (Photography, Psychology, Fashion, Data Science, Dating Coach)",
      basicIcon: "👤",
      advancedIcon: "👥"
    },
    {
      category: "Scoring System",
      basic: "Simple 0-100 Scores",
      advanced: "Weighted Sub-Scores, Percentile Rankings, Market Competitiveness",
      basicIcon: "📈",
      advancedIcon: "📊"
    },
    {
      category: "Insights Quality",
      basic: "Generic Recommendations",
      advanced: "Prioritized, Impact-Scored, Evidence-Based Insights",
      basicIcon: "💡",
      advancedIcon: "🎯"
    },
    {
      category: "Reasoning Transparency",
      basic: "Score + Basic Insights",
      advanced: "Full Chain-of-Thought, Evidence Citations, Confidence Metrics",
      basicIcon: "📝",
      advancedIcon: "🔍"
    },
    {
      category: "Comparative Analysis",
      basic: "Standalone Assessment",
      advanced: "Market Positioning, Competitive Benchmarking, Percentile Rankings",
      basicIcon: "📋",
      advancedIcon: "📊"
    },
    {
      category: "Personalization",
      basic: "One-Size-Fits-All",
      advanced: "Demographic-Aware, Platform-Specific, Target Audience Aligned",
      basicIcon: "🎨",
      advancedIcon: "🎯"
    },
    {
      category: "Processing Time",
      basic: "~30 seconds",
      advanced: "~2-3 minutes",
      basicIcon: "⚡",
      advancedIcon: "⏱️"
    },
    {
      category: "Cost",
      basic: "Free",
      advanced: "Premium Feature",
      basicIcon: "🆓",
      advancedIcon: "👑"
    }
  ];

  const imageFeatures = [
    "Pre-analysis demographic detection",
    "Technical quality assessment",
    "Multi-expert visual analysis",
    "Psychological appeal evaluation",
    "Fashion and style assessment",
    "Market performance prediction",
    "Platform-specific optimization",
    "Improvement impact scoring"
  ];

  const bioFeatures = [
    "Linguistic analysis (readability, sentiment, grammar)",
    "Big 5 personality trait detection",
    "Attachment style assessment",
    "Emotional intelligence indicators",
    "Market positioning analysis",
    "Target audience alignment",
    "Three optimized bio versions",
    "Conversion potential scoring"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-6">
          <div className="text-center">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-2">
              Basic vs Advanced Analysis
            </h1>
            <p className="text-lg text-gray-600">
              Compare our free basic analyzers with premium advanced AI analysis
            </p>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        {/* Hero Comparison */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <Card className="border-2">
            <CardHeader className="text-center">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <Sparkles className="h-6 w-6 text-blue-600" />
                <CardTitle className="text-2xl">Basic Analysis</CardTitle>
              </div>
              <CardDescription>Fast, reliable, and completely free</CardDescription>
              <Badge variant="secondary" className="w-fit mx-auto">Free Forever</Badge>
            </CardHeader>
            <CardContent className="text-center">
              <div className="space-y-4">
                <p className="text-sm text-gray-600">
                  Get instant feedback with our proven AI analysis system. Perfect for quick improvements and general guidance.
                </p>
                <div className="flex justify-center space-x-4">
                  <Button asChild>
                    <Link to="/image-analyzer">
                      <Camera className="mr-2 h-4 w-4" />
                      Try Image Analyzer
                    </Link>
                  </Button>
                  <Button asChild variant="outline">
                    <Link to="/bio-analyzer">
                      <User className="mr-2 h-4 w-4" />
                      Try Bio Analyzer
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-blue-50">
            <CardHeader className="text-center">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <Crown className="h-6 w-6 text-purple-600" />
                <CardTitle className="text-2xl">Advanced Analysis</CardTitle>
              </div>
              <CardDescription>Professional-grade insights powered by OpenRouter o3</CardDescription>
              <Badge className="w-fit mx-auto bg-purple-600">Premium Feature</Badge>
            </CardHeader>
            <CardContent className="text-center">
              <div className="space-y-4">
                <p className="text-sm text-gray-600">
                  Get expert-level analysis with our most advanced AI system. Comprehensive insights, detailed scoring, and professional recommendations.
                </p>
                <div className="flex justify-center space-x-4">
                  <Button asChild className="bg-gradient-to-r from-purple-600 to-blue-600">
                    <Link to="/image-analyzer-pro">
                      <Brain className="mr-2 h-4 w-4" />
                      Try Advanced Image
                    </Link>
                  </Button>
                  <Button asChild variant="outline" className="border-purple-300">
                    <Link to="/bio-analyzer-pro">
                      <TrendingUp className="mr-2 h-4 w-4" />
                      Try Advanced Bio
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Feature Comparison Table */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="text-2xl text-center">Feature Comparison</CardTitle>
            <CardDescription className="text-center">
              See exactly what you get with each analysis tier
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-4 px-2 font-semibold">Feature</th>
                    <th className="text-center py-4 px-2 font-semibold">
                      <div className="flex items-center justify-center space-x-2">
                        <Sparkles className="h-4 w-4 text-blue-600" />
                        <span>Basic</span>
                      </div>
                    </th>
                    <th className="text-center py-4 px-2 font-semibold">
                      <div className="flex items-center justify-center space-x-2">
                        <Crown className="h-4 w-4 text-purple-600" />
                        <span>Advanced</span>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {features.map((feature, index) => (
                    <tr key={index} className="border-b hover:bg-gray-50">
                      <td className="py-4 px-2 font-medium">{feature.category}</td>
                      <td className="py-4 px-2 text-center">
                        <div className="flex items-center justify-center space-x-2">
                          <span className="text-lg">{feature.basicIcon}</span>
                          <span className="text-sm text-gray-600">{feature.basic}</span>
                        </div>
                      </td>
                      <td className="py-4 px-2 text-center">
                        <div className="flex items-center justify-center space-x-2">
                          <span className="text-lg">{feature.advancedIcon}</span>
                          <span className="text-sm text-gray-700 font-medium">{feature.advanced}</span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Specific Features */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Camera className="h-5 w-5 text-purple-600" />
                <span>Advanced Image Analysis Features</span>
              </CardTitle>
              <CardDescription>
                Professional photography and psychology insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {imageFeatures.map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{feature}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5 text-blue-600" />
                <span>Advanced Bio Analysis Features</span>
              </CardTitle>
              <CardDescription>
                Psychological profiling and market optimization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {bioFeatures.map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{feature}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-purple-600 to-blue-600 text-white">
          <CardContent className="text-center py-12">
            <h2 className="text-3xl font-bold mb-4">Ready to Upgrade Your Analysis?</h2>
            <p className="text-lg mb-8 opacity-90">
              Experience the power of professional-grade AI analysis with our advanced system
            </p>
            <div className="flex justify-center space-x-4">
              <Button size="lg" variant="secondary">
                <Link to="/image-analyzer-pro" className="flex items-center">
                  Start Advanced Image Analysis
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600">
                <Link to="/bio-analyzer-pro" className="flex items-center">
                  Start Advanced Bio Analysis
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
