import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  useImageEditing, 
  useImageEditingQueue, 
  useImageEditingAnalytics 
} from '@/lib/image-editing';
import { 
  Upload, 
  Image, 
  Zap, 
  BarChart, 
  Clock, 
  AlertCircle, 
  Play, 
  Pause, 
  Trash2,
  Download,
  RefreshCw,
  Settings
} from 'lucide-react';

export default function ImageEditorTest() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [editingPreferences, setEditingPreferences] = useState({
    style: 'natural' as const,
    conservative: true,
    maxBudget: 5.0
  });

  console.log('🎨 ImageEditorTest: Component initialized', {
    selectedFile: selectedFile?.name,
    previewUrl: !!previewUrl,
    editingPreferences
  });

  // Main image editing hook
  const {
    isLoading,
    progress,
    result,
    error,
    analyzeImage,
    generateEditedImage,
    cancelAnalysis,
    analytics
  } = useImageEditing({
    enableQueue: true,
    enableBatch: true,
    userId: 'test-user'
  });

  console.log('🔧 ImageEditorTest: useImageEditing state', {
    isLoading,
    hasProgress: !!progress,
    hasResult: !!result,
    hasError: !!error,
    analytics
  });

  // Queue management hook
  const {
    queueStatus,
    queueHealth,
    pauseQueue,
    resumeQueue,
    clearQueue
  } = useImageEditingQueue();

  console.log('📋 ImageEditorTest: Queue status', {
    queueStatus,
    queueHealth
  });

  // Analytics hook
  const {
    analytics: detailedAnalytics,
    recentErrors,
    systemHealth,
    refreshAnalytics,
    exportAnalytics
  } = useImageEditingAnalytics();

  console.log('📊 ImageEditorTest: Analytics', {
    detailedAnalytics,
    recentErrors: recentErrors?.length,
    systemHealth
  });

  // Handle file selection
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    console.log('📁 ImageEditorTest: File selected', { 
      fileName: file?.name, 
      fileSize: file?.size, 
      fileType: file?.type 
    });
    
    if (file) {
      setSelectedFile(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      console.log('🖼️ ImageEditorTest: Preview URL created', { url });
    }
  }, []);

  // Handle analysis start
  const handleAnalyze = useCallback(async () => {
    if (!selectedFile) {
      console.warn('⚠️ ImageEditorTest: No file selected for analysis');
      return;
    }

    console.log('🔍 ImageEditorTest: Starting analysis', {
      fileName: selectedFile.name,
      preferences: editingPreferences
    });

    try {
      const analysisResult = await analyzeImage(selectedFile, editingPreferences);
      console.log('✅ ImageEditorTest: Analysis completed', { analysisResult });
    } catch (error) {
      console.error('❌ ImageEditorTest: Analysis failed', { error });
    }
  }, [selectedFile, editingPreferences, analyzeImage]);

  // Handle image generation
  const handleGenerateEdit = useCallback(async (recommendation: any) => {
    if (!previewUrl) {
      console.warn('⚠️ ImageEditorTest: No preview URL for image generation');
      return;
    }

    console.log('🎨 ImageEditorTest: Starting image generation', { recommendation });

    try {
      // Convert preview URL to base64
      const response = await fetch(previewUrl);
      const blob = await response.blob();
      const reader = new FileReader();
      
      reader.onload = async () => {
        const base64 = (reader.result as string).split(',')[1];
        console.log('🔄 ImageEditorTest: Converting to base64 completed');
        
        const editedImage = await generateEditedImage(recommendation, base64);
        console.log('✅ ImageEditorTest: Image generation completed', { 
          editedImageLength: editedImage.length 
        });
        
        // Create blob URL for preview
        const editedBlob = new Blob([Buffer.from(editedImage, 'base64')], { type: 'image/png' });
        const editedUrl = URL.createObjectURL(editedBlob);
        
        console.log('🖼️ ImageEditorTest: Edited image URL created', { editedUrl });
      };
      
      reader.readAsDataURL(blob);
    } catch (error) {
      console.error('❌ ImageEditorTest: Image generation failed', { error });
    }
  }, [previewUrl, generateEditedImage]);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">🧪 Image Editor Test Lab</h1>
        <p className="text-muted-foreground">
          Test and debug the AI-powered image editing system
        </p>
      </div>

      <Tabs defaultValue="upload" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="upload">Upload & Test</TabsTrigger>
          <TabsTrigger value="queue">Queue</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="health">Health</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="space-y-6">
          {/* File Upload Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="w-5 h-5" />
                Upload Test Image
              </CardTitle>
              <CardDescription>
                Select an image to test the editing functionality
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="hidden"
                  id="file-upload"
                />
                <label htmlFor="file-upload" className="cursor-pointer">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <p className="mt-2 text-sm text-gray-600">
                    Click to upload or drag and drop
                  </p>
                  <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                </label>
              </div>

              {previewUrl && (
                <div className="mt-4">
                  <img
                    src={previewUrl}
                    alt="Preview"
                    className="max-w-full h-64 object-contain mx-auto rounded-lg border"
                  />
                  <p className="text-sm text-center mt-2 text-gray-600">
                    {selectedFile?.name} ({(selectedFile?.size || 0 / 1024 / 1024).toFixed(2)} MB)
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Settings Panel */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Test Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Style</Label>
                  <select
                    value={editingPreferences.style}
                    onChange={(e) => setEditingPreferences(prev => ({
                      ...prev,
                      style: e.target.value as 'natural'
                    }))}
                    className="w-full p-2 border rounded"
                  >
                    <option value="natural">Natural</option>
                    <option value="dramatic">Dramatic</option>
                    <option value="artistic">Artistic</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label>Conservative Mode</Label>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={editingPreferences.conservative}
                      onCheckedChange={(checked) => setEditingPreferences(prev => ({
                        ...prev,
                        conservative: checked
                      }))}
                    />
                    <span className="text-sm">
                      {editingPreferences.conservative ? 'On' : 'Off'}
                    </span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Max Budget: ${editingPreferences.maxBudget}</Label>
                  <Slider
                    value={[editingPreferences.maxBudget]}
                    onValueChange={([value]) => setEditingPreferences(prev => ({
                      ...prev,
                      maxBudget: value
                    }))}
                    max={20}
                    min={1}
                    step={0.5}
                    className="w-full"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex gap-4 justify-center">
            <Button
              onClick={handleAnalyze}
              disabled={!selectedFile || isLoading}
              size="lg"
            >
              {isLoading ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Zap className="w-4 h-4 mr-2" />
                  Start Analysis
                </>
              )}
            </Button>

            {isLoading && (
              <Button
                onClick={cancelAnalysis}
                variant="outline"
                size="lg"
              >
                Cancel
              </Button>
            )}
          </div>

          {/* Progress Display */}
          {progress && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  Processing Progress
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>{progress.stage}</span>
                    <span>{progress.percentage}%</span>
                  </div>
                  <Progress value={progress.percentage} className="w-full" />
                  {progress.estimatedTimeRemaining && (
                    <p className="text-sm text-gray-600">
                      Estimated time remaining: {progress.estimatedTimeRemaining}s
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Error:</strong> {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Results Display */}
          {result && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Image className="w-5 h-5" />
                  Analysis Results
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold">{result.overallScore}</div>
                      <div className="text-sm text-gray-600">Overall Score</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{result.recommendations?.length || 0}</div>
                      <div className="text-sm text-gray-600">Recommendations</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold">${result.estimatedCost?.toFixed(2) || '0.00'}</div>
                      <div className="text-sm text-gray-600">Est. Cost</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{result.processingTime || 0}s</div>
                      <div className="text-sm text-gray-600">Process Time</div>
                    </div>
                  </div>

                  {result.recommendations && result.recommendations.length > 0 && (
                    <div className="space-y-3">
                      <h4 className="font-medium">Editing Recommendations:</h4>
                      {result.recommendations.map((rec: any, index: number) => (
                        <div key={index} className="border rounded-lg p-4 space-y-2">
                          <div className="flex justify-between items-start">
                            <h5 className="font-medium">{rec.type}</h5>
                            <div className="flex gap-2">
                              <Badge variant="outline">${rec.estimatedCost?.toFixed(2) || '0.00'}</Badge>
                              <Badge variant="outline">{rec.difficulty || 'Unknown'}</Badge>
                            </div>
                          </div>
                          
                          <p className="text-sm text-gray-600">
                            {rec.description || 'No description available'}
                          </p>
                          
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">
                              Expected: {rec.expectedImprovement || 'Unknown'}
                            </span>
                            <Button
                              size="sm"
                              onClick={() => handleGenerateEdit(rec)}
                            >
                              Generate Edit
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="queue" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart className="w-5 h-5" />
                Queue Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-2">
                  <Button onClick={pauseQueue} variant="outline" size="sm">
                    <Pause className="w-4 h-4 mr-2" />
                    Pause Queue
                  </Button>
                  <Button onClick={resumeQueue} variant="outline" size="sm">
                    <Play className="w-4 h-4 mr-2" />
                    Resume Queue
                  </Button>
                  <Button onClick={clearQueue} variant="outline" size="sm">
                    <Trash2 className="w-4 h-4 mr-2" />
                    Clear Queue
                  </Button>
                </div>

                {queueStatus && (
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold">{queueStatus.position}</div>
                      <div className="text-sm text-gray-600">Position</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{queueStatus.queueSize}</div>
                      <div className="text-sm text-gray-600">Queue Size</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{queueStatus.estimatedTime}s</div>
                      <div className="text-sm text-gray-600">Est. Time</div>
                    </div>
                  </div>
                )}

                {queueHealth && (
                  <div className="mt-4">
                    <h4 className="font-medium mb-2">Queue Health</h4>
                    <div className="text-sm space-y-1">
                      <div>Status: <Badge>{queueHealth.status}</Badge></div>
                      <div>Processing Rate: {queueHealth.processingRate}/min</div>
                      <div>Error Rate: {queueHealth.errorRate}%</div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart className="w-5 h-5" />
                System Analytics
              </CardTitle>
              <div className="flex gap-2">
                <Button onClick={refreshAnalytics} variant="outline" size="sm">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Refresh
                </Button>
                <Button onClick={exportAnalytics} variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold">{detailedAnalytics.totalRequests}</div>
                  <div className="text-sm text-gray-600">Total Requests</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">{detailedAnalytics.successRate.toFixed(1)}%</div>
                  <div className="text-sm text-gray-600">Success Rate</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">{detailedAnalytics.averageProcessingTime.toFixed(1)}s</div>
                  <div className="text-sm text-gray-600">Avg Process Time</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">{recentErrors?.length || 0}</div>
                  <div className="text-sm text-gray-600">Recent Errors</div>
                </div>
              </div>

              {recentErrors && recentErrors.length > 0 && (
                <div className="mt-6">
                  <h4 className="font-medium mb-2">Recent Errors</h4>
                  <div className="space-y-2">
                    {recentErrors.slice(0, 5).map((error: any, index: number) => (
                      <div key={index} className="text-sm p-2 bg-red-50 rounded border-l-4 border-red-400">
                        <div className="font-medium">{error.type}</div>
                        <div className="text-gray-600">{error.message}</div>
                        <div className="text-xs text-gray-500">{error.timestamp}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="health" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="w-5 h-5" />
                System Health
              </CardTitle>
            </CardHeader>
            <CardContent>
              {systemHealth ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <Badge variant={systemHealth.status === 'healthy' ? 'default' : 'destructive'}>
                        {systemHealth.status}
                      </Badge>
                      <div className="text-sm text-gray-600 mt-1">Overall Status</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">{systemHealth.uptime}h</div>
                      <div className="text-sm text-gray-600">Uptime</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">{systemHealth.memoryUsage}%</div>
                      <div className="text-sm text-gray-600">Memory Usage</div>
                    </div>
                  </div>

                  {systemHealth.services && (
                    <div>
                      <h4 className="font-medium mb-2">Service Status</h4>
                      <div className="space-y-2">
                        {Object.entries(systemHealth.services).map(([service, status]) => (
                          <div key={service} className="flex justify-between items-center">
                            <span className="text-sm">{service}</span>
                            <Badge variant={status === 'healthy' ? 'default' : 'destructive'}>
                              {status as string}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-gray-600">Loading system health data...</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Debug Settings</CardTitle>
              <CardDescription>
                Configure test environment settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>Verbose Logging</Label>
                  <Switch />
                </div>
                <div className="flex items-center justify-between">
                  <Label>Mock API Responses</Label>
                  <Switch />
                </div>
                <div className="flex items-center justify-between">
                  <Label>Enable Queue</Label>
                  <Switch defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <Label>Enable Batch Processing</Label>
                  <Switch defaultChecked />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
