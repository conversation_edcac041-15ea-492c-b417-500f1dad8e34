import { User<PERSON><PERSON>on, useUser } from "@clerk/tanstack-react-start";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Camera,
  FileText,
  TrendingUp,
  Star,
  Heart,
  MessageCircle,
  Settings,
  BarChart3,
} from "lucide-react";
import { Link } from "@tanstack/react-router";

export function Dashboard() {
  const { user } = useUser();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">
              Welcome back, {user?.firstName || "User"}!
            </h1>
            <p className="text-slate-600 mt-1">
              Track your dating profile performance and optimize for better
              matches
            </p>
          </div>
          <UserButton />
          <Button variant="secondary" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Profile Score
              </CardTitle>
              <Star className="h-4 w-4 text-amber-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">8.2</div>
              <p className="text-xs text-muted-foreground">
                +0.5 from last week
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Matches</CardTitle>
              <Heart className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">24</div>
              <p className="text-xs text-muted-foreground">
                +12% from last week
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Messages</CardTitle>
              <MessageCircle className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">156</div>
              <p className="text-xs text-muted-foreground">
                +8% from last week
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Profile Views
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">342</div>
              <p className="text-xs text-muted-foreground">
                +25% from last week
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Camera className="h-5 w-5 text-flame-red" />
                Photo Analysis
              </CardTitle>
              <CardDescription>
                Optimize your photos for maximum appeal
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Last analysis</span>
                <Badge variant="secondary">3 days ago</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Photos analyzed</span>
                <span className="text-sm font-medium">8/10</span>
              </div>
              <Button
                asChild
                className="w-full bg-gradient-to-r from-flame-red to-sparks-pink hover:opacity-90"
              >
                <Link to="/image-analyzer">Analyze New Photos</Link>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-flame-red" />
                Bio Optimization
              </CardTitle>
              <CardDescription>
                Craft the perfect bio to attract matches
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Bio score</span>
                <Badge variant="secondary">7.8/10</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Last updated</span>
                <span className="text-sm font-medium">1 week ago</span>
              </div>
              <Button
                asChild
                className="w-full bg-gradient-to-r from-flame-red to-sparks-pink hover:opacity-90"
              >
                <Link to="/bio-analyzer">Optimize Bio</Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Your latest profile improvements and results
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between border-b pb-2">
                <div className="flex items-center gap-3">
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm">Photo analysis completed</span>
                </div>
                <span className="text-xs text-muted-foreground">
                  2 hours ago
                </span>
              </div>
              <div className="flex items-center justify-between border-b pb-2">
                <div className="flex items-center gap-3">
                  <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">
                    Bio updated with AI suggestions
                  </span>
                </div>
                <span className="text-xs text-muted-foreground">1 day ago</span>
              </div>
              <div className="flex items-center justify-between border-b pb-2">
                <div className="flex items-center gap-3">
                  <div className="h-2 w-2 bg-amber-500 rounded-full"></div>
                  <span className="text-sm">New match milestone reached</span>
                </div>
                <span className="text-xs text-muted-foreground">
                  3 days ago
                </span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="h-2 w-2 bg-purple-500 rounded-full"></div>
                  <span className="text-sm">
                    Profile optimization suggestions received
                  </span>
                </div>
                <span className="text-xs text-muted-foreground">
                  1 week ago
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Settings Link */}
        <div className="mt-8 flex justify-center">
          <Button
            asChild
            variant="secondary"
            className="flex items-center gap-2"
          >
            <Link to="/account-settings">
              <Settings className="h-4 w-4" />
              Account Settings
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
