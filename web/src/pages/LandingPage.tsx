import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>Up,
  UploadCloud,
} from "lucide-react";
import { <PERSON> } from "@tanstack/react-router";
import { AnimatedPhoneMockup } from "@/components/animated-phone-mockup";
import { StaticPhoneMockup } from "@/components/static-phone-mockup";
import { Button } from "@/components/ui/button";

export function LandingPage() {
  return (
    <div className="bg-cloud-white text-graphite-90">
      <HeroSection />
      <SocialProofSection />
      <HowItWorksSection />
      {/* <PricingSection /> */}
      <Footer />
    </div>
  );
}

function HeroSection() {
  return (
    <section className="w-full bg-gradient-hero">
      <div className="container mx-auto grid min-h-screen items-center gap-8 px-4 md:grid-cols-2 md:px-6 lg:gap-16">
        <div className="space-y-6">
          <h1 className="text-h1-mobile md:text-h1">Stop guessing. Start matching.</h1>
          <p className="text-body-lg text-graphite-60 max-w-md">
            Upgrade your Tinder profile with AI-powered optimization. Get more matches, better
            conversations, and find your soulmate.
          </p>
          <div className="flex flex-col gap-4 sm:flex-row sm:flex-wrap">
            
          <div className="bg-red-200 flex flex-col gap-4">
            <h1>DEVELOPMENT BUTTONS</h1>
            <Button asChild size="lg" variant="primary">
              <Link to="/image-analyzer">Try Image Analyzer</Link>
            </Button>
            <Button asChild size="lg" variant="primary">
              <Link to="/welcome">Get Started</Link>
            </Button>
            <Button asChild size="lg" variant="tertiary" className="sm:w-auto">
              <Link to="/bio-analyzer">Try Bio Analyzer</Link>
            </Button>
          </div>

           
            
          </div> 
        </div>
        <div className="relative ml-10 h-full items-center justify-center md:flex">
          <StaticPhoneMockup rotation={-6} />
          <AnimatedPhoneMockup rotation={6} className="relative" />
        </div>
      </div>
    </section>
  );
}

function SocialProofSection() {
  const stats = [
    { icon: TrendingUp, value: "+25%", label: "Average Match Rate Increase" },
    {
      icon: Heart,
      value: "9/10",
      label: "Users Report Higher Quality Matches",
    },
    { icon: Sparkles, value: "4s", label: "Average Analysis Time Per Photo" },
  ];

  return (
    <section className="py-12 md:py-24 bg-cloud-white">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid gap-8 md:grid-cols-3">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <stat.icon className="mx-auto h-12 w-12 text-flame-red" />
              <p className="text-display-1-mobile md:text-display-1 mt-4">{stat.value}</p>
              <p className="text-body-md text-graphite-60">{stat.label}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

function HowItWorksSection() {
  const steps = [
    {
      icon: UploadCloud,
      title: "Upload Your Profile",
      description:
        "Securely upload your current dating profile photos and bio. We keep your data private.",
    },
    {
      icon: ScanSearch,
      title: "AI-Powered Analysis",
      description:
        "Our model scores your photos on key metrics and rewrites your bio to be more engaging.",
    },
    {
      icon: Star,
      title: "Get Your Glow-Up",
      description:
        "Implement our actionable tips, update your profile, and watch your matches increase.",
    },
  ];

  return (
    <section id="image-analyzer" className="py-12 md:py-24 bg-gray-50">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center max-w-2xl mx-auto">
          <h2 className="text-h2-mobile md:text-h2">How It Works in 3 Simple Steps</h2>
          <p className="text-body-lg text-graphite-60 mt-4">
            Transform your dating profile from overlooked to overbooked.
          </p>
        </div>
        <div className="grid gap-8 md:grid-cols-3 mt-12">
          {steps.map((step, index) => (
            <div key={index} className="flex flex-col items-center text-center p-6 rounded-lg">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-flame-red text-cloud-white mb-6">
                <step.icon className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-semibold">{step.title}</h3>
              <p className="text-graphite-60 mt-2">{step.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

function Footer() {
  return (
    <footer className="bg-graphite-90 text-cloud-white/80">
      <div className="container mx-auto px-4 md:px-6 py-12">
        <div className="grid gap-8 md:grid-cols-4">
          <div>
            <h4 className="font-semibold text-lg text-cloud-white">TinderOptimizer</h4>
            <p className="mt-2 text-sm">AI-powered profile analysis.</p>
          </div>
          <div>
            <h5 className="font-semibold text-cloud-white">Product</h5>
            <ul className="mt-4 space-y-2 text-sm">
              <li>
                <Link to="/image-analyzer" className="hover:text-cloud-white">
                  Image Analyzer
                </Link>
              </li>
              <li>
                <Link to="/bio-analyzer" className="hover:text-cloud-white">
                  Bio Analyzer
                </Link>
              </li>
              <li>
                <Link to="#pricing" className="hover:text-cloud-white">
                  Pricing
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h5 className="font-semibold text-cloud-white">Company</h5>
            <ul className="mt-4 space-y-2 text-sm">
              <li>
                <Link to="#" className="hover:text-cloud-white">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="#" className="hover:text-cloud-white">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link to="#" className="hover:text-cloud-white">
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h5 className="font-semibold text-cloud-white">Connect</h5>
            <div className="flex space-x-4 mt-4">{/* Add social icons here */}</div>
          </div>
        </div>
        <div className="border-t border-graphite-60/50 mt-8 pt-8 text-center text-sm">
          <p>&copy; {new Date().getFullYear()} TinderOptimizer. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}
