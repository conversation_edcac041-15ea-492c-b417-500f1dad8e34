 import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/tanstack-react-start'

// //@ts-ignore
 import { cn } from "@/lib/utils";
import "@/globals.css";


/// <reference types="vite/client" />
import type { ReactNode } from 'react'
import {
  Outlet,
  createRootRoute,
  HeadContent,
  Scripts,
} from '@tanstack/react-router'

export const Route = createRootRoute({
  head: () => ({
    meta: [
      {
        charSet: 'utf-8',
      },
      {
        name: 'viewport',
        content: 'width=device-width, initial-scale=1',
      },
      {
        title: 'Tinder AI Profile Analyzer',
      },
    ],
  }),
  component: RootComponent,
})

function RootComponent() {
  return (
    <RootDocument>
      <Outlet />
    </RootDocument>
  )
}

function RootDocument({ children }: Readonly<{ children: ReactNode }>) {
  return (
    <ClerkProvider>    
    <html lang="en" >
      <head>
        <HeadContent />
      </head>
      <body className={cn("min-h-screen bg-background font-sans antialiased")}> 
        {children}
        <Scripts />
      </body>
    </html>
    </ClerkProvider>
  )
}