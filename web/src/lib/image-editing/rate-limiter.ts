/**
 * Rate limiting and usage control service for image editing
 * Provides comprehensive rate limiting, cost tracking, and usage monitoring
 */

import type { EditingUsageStats } from "@/types/analysis";

export interface RateLimitConfig {
  // Request limits
  analysisPerHour: number;
  analysisPerDay: number;
  generationPerHour: number;
  generationPerDay: number;
  
  // Concurrent limits
  maxConcurrentRequests: number;
  maxConcurrentPerUser: number;
  
  // Cost limits
  dailyCostLimit: number;
  monthlyCostLimit: number;
  
  // Burst limits
  burstAnalysisLimit: number;
  burstGenerationLimit: number;
  burstWindow: number; // in milliseconds
  
  // Recovery rates
  analysisRecoveryRate: number; // requests per minute
  generationRecoveryRate: number; // requests per minute
  
  // Premium user multipliers
  premiumMultiplier: number;
  proMultiplier: number;
}

export interface RateLimitResult {
  allowed: boolean;
  remainingRequests: number;
  resetTime: number;
  retryAfter?: number;
  reason?: string;
  costRemaining: number;
}

export interface UserUsageStats {
  userId: string;
  userTier: 'free' | 'premium' | 'pro';
  
  // Current usage
  analysisCount: {
    hour: number;
    day: number;
    hourResetTime: number;
    dayResetTime: number;
  };
  
  generationCount: {
    hour: number;
    day: number;
    hourResetTime: number;
    dayResetTime: number;
  };
  
  // Cost tracking
  costUsage: {
    daily: number;
    monthly: number;
    dailyResetTime: number;
    monthlyResetTime: number;
  };
  
  // Burst tracking
  burstUsage: {
    analysis: number;
    generation: number;
    windowStart: number;
  };
  
  // Concurrent tracking
  concurrentRequests: number;
  
  // Violations
  violations: {
    count: number;
    lastViolation: number;
    type: string;
  }[];
}

export interface GlobalUsageStats {
  totalUsers: number;
  activeUsers: number;
  totalRequests: number;
  totalCost: number;
  averageResponseTime: number;
  errorRate: number;
  concurrentRequests: number;
}

export class RateLimiter {
  private config: RateLimitConfig;
  private userStats: Map<string, UserUsageStats> = new Map();
  private globalStats: GlobalUsageStats = {
    totalUsers: 0,
    activeUsers: 0,
    totalRequests: 0,
    totalCost: 0,
    averageResponseTime: 0,
    errorRate: 0,
    concurrentRequests: 0
  };
  
  private cleanupTimer?: number;
  private persistenceTimer?: number;
  private requestTimes: number[] = [];

  constructor(config?: Partial<RateLimitConfig>) {
    this.config = {
      // Request limits
      analysisPerHour: 100,
      analysisPerDay: 500,
      generationPerHour: 20,
      generationPerDay: 50,
      
      // Concurrent limits
      maxConcurrentRequests: 100,
      maxConcurrentPerUser: 3,
      
      // Cost limits
      dailyCostLimit: 10.0, // $10 per day
      monthlyCostLimit: 250.0, // $250 per month
      
      // Burst limits
      burstAnalysisLimit: 10,
      burstGenerationLimit: 5,
      burstWindow: 60 * 1000, // 1 minute
      
      // Recovery rates
      analysisRecoveryRate: 2, // 2 requests per minute
      generationRecoveryRate: 0.5, // 0.5 requests per minute
      
      // Premium user multipliers
      premiumMultiplier: 3,
      proMultiplier: 10,
      
      ...config
    };

    this.initializeFromStorage();
    this.startCleanupTimer();
    this.startPersistenceTimer();
  }

  /**
   * Check if analysis request is allowed
   */
  async checkAnalysisLimit(userId: string, userTier: 'free' | 'premium' | 'pro' = 'free'): Promise<RateLimitResult> {
    const stats = this.getUserStats(userId, userTier);
    const limits = this.getEffectiveLimits(userTier);
    
    // Update usage counts
    this.updateUsageCounts(stats);
    
    // Check concurrent requests
    if (stats.concurrentRequests >= limits.maxConcurrentPerUser) {
      return {
        allowed: false,
        remainingRequests: 0,
        resetTime: Date.now() + 60000, // 1 minute
        retryAfter: 60000,
        reason: 'Too many concurrent requests',
        costRemaining: this.getRemainingCost(stats)
      };
    }
    
    // Check global concurrent limit
    if (this.globalStats.concurrentRequests >= this.config.maxConcurrentRequests) {
      return {
        allowed: false,
        remainingRequests: 0,
        resetTime: Date.now() + 30000, // 30 seconds
        retryAfter: 30000,
        reason: 'System busy, please try again later',
        costRemaining: this.getRemainingCost(stats)
      };
    }
    
    // Check burst limits
    if (stats.burstUsage.analysis >= limits.burstAnalysisLimit) {
      const timeSinceWindow = Date.now() - stats.burstUsage.windowStart;
      if (timeSinceWindow < this.config.burstWindow) {
        return {
          allowed: false,
          remainingRequests: 0,
          resetTime: stats.burstUsage.windowStart + this.config.burstWindow,
          retryAfter: this.config.burstWindow - timeSinceWindow,
          reason: 'Burst limit exceeded',
          costRemaining: this.getRemainingCost(stats)
        };
      }
    }
    
    // Check hourly limit
    if (stats.analysisCount.hour >= limits.analysisPerHour) {
      return {
        allowed: false,
        remainingRequests: 0,
        resetTime: stats.analysisCount.hourResetTime,
        retryAfter: stats.analysisCount.hourResetTime - Date.now(),
        reason: 'Hourly limit exceeded',
        costRemaining: this.getRemainingCost(stats)
      };
    }
    
    // Check daily limit
    if (stats.analysisCount.day >= limits.analysisPerDay) {
      return {
        allowed: false,
        remainingRequests: 0,
        resetTime: stats.analysisCount.dayResetTime,
        retryAfter: stats.analysisCount.dayResetTime - Date.now(),
        reason: 'Daily limit exceeded',
        costRemaining: this.getRemainingCost(stats)
      };
    }
    
    // All checks passed
    return {
      allowed: true,
      remainingRequests: Math.min(
        limits.analysisPerHour - stats.analysisCount.hour,
        limits.analysisPerDay - stats.analysisCount.day
      ),
      resetTime: stats.analysisCount.hourResetTime,
      costRemaining: this.getRemainingCost(stats)
    };
  }

  /**
   * Check if generation request is allowed
   */
  async checkGenerationLimit(
    userId: string, 
    estimatedCost: number, 
    userTier: 'free' | 'premium' | 'pro' = 'free'
  ): Promise<RateLimitResult> {
    const stats = this.getUserStats(userId, userTier);
    const limits = this.getEffectiveLimits(userTier);
    
    // Update usage counts
    this.updateUsageCounts(stats);
    
    // Check cost limits
    if (stats.costUsage.daily + estimatedCost > limits.dailyCostLimit) {
      return {
        allowed: false,
        remainingRequests: 0,
        resetTime: stats.costUsage.dailyResetTime,
        retryAfter: stats.costUsage.dailyResetTime - Date.now(),
        reason: 'Daily cost limit exceeded',
        costRemaining: Math.max(0, limits.dailyCostLimit - stats.costUsage.daily)
      };
    }
    
    if (stats.costUsage.monthly + estimatedCost > limits.monthlyCostLimit) {
      return {
        allowed: false,
        remainingRequests: 0,
        resetTime: stats.costUsage.monthlyResetTime,
        retryAfter: stats.costUsage.monthlyResetTime - Date.now(),
        reason: 'Monthly cost limit exceeded',
        costRemaining: Math.max(0, limits.monthlyCostLimit - stats.costUsage.monthly)
      };
    }
    
    // Check concurrent requests
    if (stats.concurrentRequests >= limits.maxConcurrentPerUser) {
      return {
        allowed: false,
        remainingRequests: 0,
        resetTime: Date.now() + 60000,
        retryAfter: 60000,
        reason: 'Too many concurrent requests',
        costRemaining: this.getRemainingCost(stats)
      };
    }
    
    // Check burst limits
    if (stats.burstUsage.generation >= limits.burstGenerationLimit) {
      const timeSinceWindow = Date.now() - stats.burstUsage.windowStart;
      if (timeSinceWindow < this.config.burstWindow) {
        return {
          allowed: false,
          remainingRequests: 0,
          resetTime: stats.burstUsage.windowStart + this.config.burstWindow,
          retryAfter: this.config.burstWindow - timeSinceWindow,
          reason: 'Burst limit exceeded',
          costRemaining: this.getRemainingCost(stats)
        };
      }
    }
    
    // Check hourly limit
    if (stats.generationCount.hour >= limits.generationPerHour) {
      return {
        allowed: false,
        remainingRequests: 0,
        resetTime: stats.generationCount.hourResetTime,
        retryAfter: stats.generationCount.hourResetTime - Date.now(),
        reason: 'Hourly limit exceeded',
        costRemaining: this.getRemainingCost(stats)
      };
    }
    
    // Check daily limit
    if (stats.generationCount.day >= limits.generationPerDay) {
      return {
        allowed: false,
        remainingRequests: 0,
        resetTime: stats.generationCount.dayResetTime,
        retryAfter: stats.generationCount.dayResetTime - Date.now(),
        reason: 'Daily limit exceeded',
        costRemaining: this.getRemainingCost(stats)
      };
    }
    
    // All checks passed
    return {
      allowed: true,
      remainingRequests: Math.min(
        limits.generationPerHour - stats.generationCount.hour,
        limits.generationPerDay - stats.generationCount.day
      ),
      resetTime: stats.generationCount.hourResetTime,
      costRemaining: this.getRemainingCost(stats)
    };
  }

  /**
   * Record successful analysis request
   */
  recordAnalysisRequest(userId: string, processingTime: number): void {
    const stats = this.getUserStats(userId);
    
    // Update counts
    stats.analysisCount.hour++;
    stats.analysisCount.day++;
    stats.concurrentRequests++;
    
    // Update burst usage
    const now = Date.now();
    if (now - stats.burstUsage.windowStart > this.config.burstWindow) {
      stats.burstUsage.windowStart = now;
      stats.burstUsage.analysis = 1;
    } else {
      stats.burstUsage.analysis++;
    }
    
    // Update global stats
    this.globalStats.totalRequests++;
    this.globalStats.concurrentRequests++;
    this.recordResponseTime(processingTime);
    
    // Schedule concurrent request decrement
    setTimeout(() => {
      stats.concurrentRequests = Math.max(0, stats.concurrentRequests - 1);
      this.globalStats.concurrentRequests = Math.max(0, this.globalStats.concurrentRequests - 1);
    }, 1000); // Assume 1 second minimum processing time
  }

  /**
   * Record successful generation request
   */
  recordGenerationRequest(userId: string, cost: number, processingTime: number): void {
    const stats = this.getUserStats(userId);
    
    // Update counts
    stats.generationCount.hour++;
    stats.generationCount.day++;
    stats.concurrentRequests++;
    
    // Update costs
    stats.costUsage.daily += cost;
    stats.costUsage.monthly += cost;
    
    // Update burst usage
    const now = Date.now();
    if (now - stats.burstUsage.windowStart > this.config.burstWindow) {
      stats.burstUsage.windowStart = now;
      stats.burstUsage.generation = 1;
    } else {
      stats.burstUsage.generation++;
    }
    
    // Update global stats
    this.globalStats.totalRequests++;
    this.globalStats.totalCost += cost;
    this.globalStats.concurrentRequests++;
    this.recordResponseTime(processingTime);
    
    // Schedule concurrent request decrement
    setTimeout(() => {
      stats.concurrentRequests = Math.max(0, stats.concurrentRequests - 1);
      this.globalStats.concurrentRequests = Math.max(0, this.globalStats.concurrentRequests - 1);
    }, processingTime);
  }

  /**
   * Record failed request
   */
  recordFailedRequest(userId: string, reason: string): void {
    const stats = this.getUserStats(userId);
    
    // Update violation count
    stats.violations.push({
      count: 1,
      lastViolation: Date.now(),
      type: reason
    });
    
    // Keep only last 10 violations
    if (stats.violations.length > 10) {
      stats.violations.shift();
    }
    
    // Update global error rate
    this.globalStats.totalRequests++;
    this.updateErrorRate();
  }

  /**
   * Get user usage statistics
   */
  getUserUsageStats(userId: string): EditingUsageStats {
    const stats = this.getUserStats(userId);
    const limits = this.getEffectiveLimits(stats.userTier);
    
    return {
      userId,
      analysisCount: stats.analysisCount.hour + stats.analysisCount.day,
      generationCount: stats.generationCount.hour + stats.generationCount.day,
      totalCost: stats.costUsage.daily + stats.costUsage.monthly,
      lastUsed: new Date(),
      dailyLimits: {
        analysis: limits.analysisPerDay,
        generation: limits.generationPerDay,
        cost: limits.dailyCostLimit
      }
    };
  }

  /**
   * Get global usage statistics
   */
  getGlobalStats(): GlobalUsageStats {
    return { ...this.globalStats };
  }

  /**
   * Reset user limits (admin function)
   */
  resetUserLimits(userId: string): void {
    const stats = this.getUserStats(userId);
    const now = Date.now();
    
    // Reset all counters
    stats.analysisCount = {
      hour: 0,
      day: 0,
      hourResetTime: now + 60 * 60 * 1000,
      dayResetTime: now + 24 * 60 * 60 * 1000
    };
    
    stats.generationCount = {
      hour: 0,
      day: 0,
      hourResetTime: now + 60 * 60 * 1000,
      dayResetTime: now + 24 * 60 * 60 * 1000
    };
    
    stats.costUsage = {
      daily: 0,
      monthly: 0,
      dailyResetTime: now + 24 * 60 * 60 * 1000,
      monthlyResetTime: now + 30 * 24 * 60 * 60 * 1000
    };
    
    stats.burstUsage = {
      analysis: 0,
      generation: 0,
      windowStart: now
    };
    
    stats.concurrentRequests = 0;
    stats.violations = [];
  }

  /**
   * Update rate limit configuration
   */
  updateConfig(newConfig: Partial<RateLimitConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get effective limits for user tier
   */
  private getEffectiveLimits(userTier: 'free' | 'premium' | 'pro'): RateLimitConfig {
    const multiplier = userTier === 'pro' ? this.config.proMultiplier : 
                      userTier === 'premium' ? this.config.premiumMultiplier : 1;
    
    return {
      ...this.config,
      analysisPerHour: Math.floor(this.config.analysisPerHour * multiplier),
      analysisPerDay: Math.floor(this.config.analysisPerDay * multiplier),
      generationPerHour: Math.floor(this.config.generationPerHour * multiplier),
      generationPerDay: Math.floor(this.config.generationPerDay * multiplier),
      dailyCostLimit: this.config.dailyCostLimit * multiplier,
      monthlyCostLimit: this.config.monthlyCostLimit * multiplier,
      burstAnalysisLimit: Math.floor(this.config.burstAnalysisLimit * multiplier),
      burstGenerationLimit: Math.floor(this.config.burstGenerationLimit * multiplier),
      maxConcurrentPerUser: Math.floor(this.config.maxConcurrentPerUser * multiplier)
    };
  }

  /**
   * Get or create user stats
   */
  private getUserStats(userId: string, userTier: 'free' | 'premium' | 'pro' = 'free'): UserUsageStats {
    let stats = this.userStats.get(userId);
    
    if (!stats) {
      const now = Date.now();
      stats = {
        userId,
        userTier,
        analysisCount: {
          hour: 0,
          day: 0,
          hourResetTime: now + 60 * 60 * 1000,
          dayResetTime: now + 24 * 60 * 60 * 1000
        },
        generationCount: {
          hour: 0,
          day: 0,
          hourResetTime: now + 60 * 60 * 1000,
          dayResetTime: now + 24 * 60 * 60 * 1000
        },
        costUsage: {
          daily: 0,
          monthly: 0,
          dailyResetTime: now + 24 * 60 * 60 * 1000,
          monthlyResetTime: now + 30 * 24 * 60 * 60 * 1000
        },
        burstUsage: {
          analysis: 0,
          generation: 0,
          windowStart: now
        },
        concurrentRequests: 0,
        violations: []
      };
      
      this.userStats.set(userId, stats);
      this.globalStats.totalUsers++;
    }
    
    return stats;
  }

  /**
   * Update usage counts and reset expired counters
   */
  private updateUsageCounts(stats: UserUsageStats): void {
    const now = Date.now();
    
    // Reset hourly counters if expired
    if (now > stats.analysisCount.hourResetTime) {
      stats.analysisCount.hour = 0;
      stats.analysisCount.hourResetTime = now + 60 * 60 * 1000;
    }
    
    if (now > stats.generationCount.hourResetTime) {
      stats.generationCount.hour = 0;
      stats.generationCount.hourResetTime = now + 60 * 60 * 1000;
    }
    
    // Reset daily counters if expired
    if (now > stats.analysisCount.dayResetTime) {
      stats.analysisCount.day = 0;
      stats.analysisCount.dayResetTime = now + 24 * 60 * 60 * 1000;
    }
    
    if (now > stats.generationCount.dayResetTime) {
      stats.generationCount.day = 0;
      stats.generationCount.dayResetTime = now + 24 * 60 * 60 * 1000;
    }
    
    // Reset cost counters if expired
    if (now > stats.costUsage.dailyResetTime) {
      stats.costUsage.daily = 0;
      stats.costUsage.dailyResetTime = now + 24 * 60 * 60 * 1000;
    }
    
    if (now > stats.costUsage.monthlyResetTime) {
      stats.costUsage.monthly = 0;
      stats.costUsage.monthlyResetTime = now + 30 * 24 * 60 * 60 * 1000;
    }
  }

  /**
   * Get remaining cost for user
   */
  private getRemainingCost(stats: UserUsageStats): number {
    const limits = this.getEffectiveLimits(stats.userTier);
    return Math.max(0, limits.dailyCostLimit - stats.costUsage.daily);
  }

  /**
   * Record response time for monitoring
   */
  private recordResponseTime(time: number): void {
    this.requestTimes.push(time);
    
    // Keep only last 100 response times
    if (this.requestTimes.length > 100) {
      this.requestTimes.shift();
    }
    
    // Update average
    this.globalStats.averageResponseTime = 
      this.requestTimes.reduce((sum, t) => sum + t, 0) / this.requestTimes.length;
  }

  /**
   * Update error rate
   */
  private updateErrorRate(): void {
    // Simple error rate calculation - in production, you'd use a sliding window
    const errorCount = Array.from(this.userStats.values())
      .reduce((total, stats) => total + stats.violations.length, 0);
    
    this.globalStats.errorRate = this.globalStats.totalRequests > 0 ? 
      (errorCount / this.globalStats.totalRequests) * 100 : 0;
  }

  /**
   * Initialize from localStorage
   */
  private initializeFromStorage(): void {
    try {
      const stored = localStorage.getItem('tinderop-rate-limiter');
      if (stored) {
        const data = JSON.parse(stored);
        
        // Restore user stats
        for (const [userId, stats] of Object.entries(data.userStats || {})) {
          this.userStats.set(userId, stats as UserUsageStats);
        }
        
        // Restore global stats
        if (data.globalStats) {
          this.globalStats = { ...this.globalStats, ...data.globalStats };
        }
      }
    } catch (error) {
      console.warn('Failed to initialize rate limiter from storage:', error);
    }
  }

  /**
   * Persist to localStorage
   */
  private persistToStorage(): void {
    try {
      const data = {
        userStats: Object.fromEntries(this.userStats.entries()),
        globalStats: this.globalStats
      };
      
      localStorage.setItem('tinderop-rate-limiter', JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to persist rate limiter to storage:', error);
    }
  }

  /**
   * Start cleanup timer
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = window.setInterval(() => {
      this.cleanupExpiredStats();
    }, 60 * 60 * 1000); // Every hour
  }

  /**
   * Start persistence timer
   */
  private startPersistenceTimer(): void {
    this.persistenceTimer = window.setInterval(() => {
      this.persistToStorage();
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Clean up expired statistics
   */
  private cleanupExpiredStats(): void {
    const now = Date.now();
    const expiredUsers: string[] = [];
    
    for (const [userId, stats] of this.userStats.entries()) {
      // Remove users with no activity in the last 24 hours
      const lastActivity = Math.max(
        stats.analysisCount.dayResetTime - 24 * 60 * 60 * 1000,
        stats.generationCount.dayResetTime - 24 * 60 * 60 * 1000
      );
      
      if (now - lastActivity > 24 * 60 * 60 * 1000) {
        expiredUsers.push(userId);
      }
    }
    
    for (const userId of expiredUsers) {
      this.userStats.delete(userId);
      this.globalStats.totalUsers--;
    }
  }

  /**
   * Cleanup resources
   */
  dispose(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    if (this.persistenceTimer) {
      clearInterval(this.persistenceTimer);
    }
    
    this.persistToStorage();
    this.userStats.clear();
  }
}

// Singleton instance
export const rateLimiter = new RateLimiter();