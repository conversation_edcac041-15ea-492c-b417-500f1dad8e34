/**
 * Intelligent caching system for image editing results
 * Provides multi-tier caching with LRU eviction and cost optimization
 */

import type { EditingAnalysisResult, EditingCache } from "@/types/analysis";

export interface CacheConfig {
  maxMemorySize: number; // Maximum cache size in MB
  maxEntries: number; // Maximum number of cached entries
  defaultTTL: number; // Default time-to-live in milliseconds
  enablePersistence: boolean; // Enable localStorage persistence
  compressionLevel: number; // Compression level (0-9)
  gcInterval: number; // Garbage collection interval in milliseconds
}

export interface CacheStats {
  hits: number;
  misses: number;
  evictions: number;
  memoryUsage: number; // in MB
  entryCount: number;
  hitRate: number;
  averageAccessTime: number;
}

export interface CacheEntry {
  key: string;
  data: EditingAnalysisResult;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccess: number;
  size: number; // in bytes
  compressed: boolean;
}

export class ImageEditingCache {
  private cache: Map<string, CacheEntry> = new Map();
  private config: CacheConfig;
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    memoryUsage: 0,
    entryCount: 0,
    hitRate: 0,
    averageAccessTime: 0
  };
  private gcTimer?: number;
  private accessTimes: number[] = [];

  constructor(config?: Partial<CacheConfig>) {
    this.config = {
      maxMemorySize: 50, // 50MB default
      maxEntries: 1000,
      defaultTTL: 24 * 60 * 60 * 1000, // 24 hours
      enablePersistence: true,
      compressionLevel: 6,
      gcInterval: 5 * 60 * 1000, // 5 minutes
      ...config
    };

    this.initializeCache();
    this.startGarbageCollection();
  }

  /**
   * Initialize cache with persisted data
   */
  private initializeCache(): void {
    if (!this.config.enablePersistence || typeof window === 'undefined' || typeof localStorage === 'undefined') return;

    try {
      const persistedData = localStorage.getItem('tinderop-editing-cache');
      if (persistedData) {
        const parsedData = JSON.parse(persistedData);
        const now = Date.now();

        // Restore non-expired entries
        for (const [key, entry] of Object.entries(parsedData)) {
          const cacheEntry = entry as CacheEntry;
          if (now - cacheEntry.timestamp < cacheEntry.ttl) {
            this.cache.set(key, cacheEntry);
          }
        }

        this.updateStats();
      }
    } catch (error) {
      console.warn('Failed to initialize cache from localStorage:', error);
    }
  }

  /**
   * Start garbage collection timer
   */
  private startGarbageCollection(): void {
    if (typeof window === 'undefined') return;

    if (this.gcTimer) {
      clearInterval(this.gcTimer);
    }

    this.gcTimer = window.setInterval(() => {
      this.performGarbageCollection();
    }, this.config.gcInterval);
  }

  /**
   * Get cached result
   */
  async get(key: string): Promise<EditingAnalysisResult | null> {
    const startTime = performance.now();
    const entry = this.cache.get(key);

    if (!entry) {
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }

    // Check if entry is expired
    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      this.stats.evictions++;
      this.updateStats();
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccess = now;
    this.stats.hits++;
    
    const accessTime = performance.now() - startTime;
    this.recordAccessTime(accessTime);
    this.updateHitRate();

    // Decompress if needed
    if (entry.compressed) {
      try {
        const decompressed = await this.decompress(entry.data);
        return decompressed;
      } catch (error) {
        console.warn('Failed to decompress cached data:', error);
        this.cache.delete(key);
        return null;
      }
    }

    return entry.data;
  }

  /**
   * Store result in cache
   */
  async set(
    key: string, 
    data: EditingAnalysisResult, 
    ttl: number = this.config.defaultTTL
  ): Promise<void> {
    try {
      // Calculate data size
      const dataSize = this.calculateDataSize(data);
      
      // Check if we need to evict entries
      await this.ensureCapacity(dataSize);

      // Compress if beneficial
      let finalData = data;
      let compressed = false;
      
      if (dataSize > 100 * 1024) { // Compress if larger than 100KB
        try {
          finalData = await this.compress(data);
          compressed = true;
        } catch (error) {
          console.warn('Failed to compress cache data:', error);
        }
      }

      const entry: CacheEntry = {
        key,
        data: finalData,
        timestamp: Date.now(),
        ttl,
        accessCount: 1,
        lastAccess: Date.now(),
        size: dataSize,
        compressed
      };

      this.cache.set(key, entry);
      this.updateStats();
      
      // Persist to localStorage if enabled
      if (this.config.enablePersistence) {
        await this.persistCache();
      }
    } catch (error) {
      console.error('Failed to cache editing result:', error);
    }
  }

  /**
   * Check if key exists in cache
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    // Check if expired
    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.updateStats();
      return false;
    }

    return true;
  }

  /**
   * Remove specific entry from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.updateStats();
    }
    return deleted;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      memoryUsage: 0,
      entryCount: 0,
      hitRate: 0,
      averageAccessTime: 0
    };
    
    if (this.config.enablePersistence) {
      localStorage.removeItem('tinderop-editing-cache');
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Get cache configuration
   */
  getConfig(): CacheConfig {
    return { ...this.config };
  }

  /**
   * Update cache configuration
   */
  updateConfig(newConfig: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Restart garbage collection if interval changed
    if (newConfig.gcInterval) {
      this.startGarbageCollection();
    }
  }

  /**
   * Generate cache key from image data and preferences
   */
  generateCacheKey(
    imageData: string, 
    preferences?: {
      conservative?: boolean;
      style?: string;
      maxBudget?: number;
      priorityTypes?: string[];
    }
  ): string {
    const prefsString = preferences ? JSON.stringify(preferences) : '';
    const combined = imageData + prefsString;
    
    // Create hash of the combined string
    let hash = 0;
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    
    return `editing_${Math.abs(hash).toString(16)}`;
  }

  /**
   * Ensure cache has capacity for new entry
   */
  private async ensureCapacity(requiredSize: number): Promise<void> {
    const currentMemoryUsage = this.stats.memoryUsage * 1024 * 1024; // Convert to bytes
    const maxMemoryBytes = this.config.maxMemorySize * 1024 * 1024;
    
    // Check if we need to evict based on memory
    if (currentMemoryUsage + requiredSize > maxMemoryBytes) {
      await this.evictLRU(requiredSize);
    }
    
    // Check if we need to evict based on entry count
    if (this.cache.size >= this.config.maxEntries) {
      await this.evictLRU(0);
    }
  }

  /**
   * Evict least recently used entries
   */
  private async evictLRU(requiredSize: number): Promise<void> {
    const entries = Array.from(this.cache.entries());
    
    // Sort by last access time (oldest first)
    entries.sort((a, b) => a[1].lastAccess - b[1].lastAccess);
    
    let freedBytes = 0;
    const requiredBytes = requiredSize;
    
    for (const [key, entry] of entries) {
      if (freedBytes >= requiredBytes && this.cache.size < this.config.maxEntries) {
        break;
      }
      
      this.cache.delete(key);
      freedBytes += entry.size;
      this.stats.evictions++;
    }
    
    this.updateStats();
  }

  /**
   * Perform garbage collection
   */
  private performGarbageCollection(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];
    
    // Find expired entries
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        expiredKeys.push(key);
      }
    }
    
    // Remove expired entries
    for (const key of expiredKeys) {
      this.cache.delete(key);
      this.stats.evictions++;
    }
    
    if (expiredKeys.length > 0) {
      this.updateStats();
    }
  }

  /**
   * Persist cache to localStorage
   */
  private async persistCache(): Promise<void> {
    if (!this.config.enablePersistence || typeof window === 'undefined' || typeof localStorage === 'undefined') return;

    try {
      const cacheData = Object.fromEntries(this.cache.entries());
      const serialized = JSON.stringify(cacheData);

      // Check if localStorage has enough space
      const availableSpace = this.getAvailableStorageSpace();
      if (serialized.length > availableSpace) {
        console.warn('Insufficient localStorage space for cache persistence');
        return;
      }

      localStorage.setItem('tinderop-editing-cache', serialized);
    } catch (error) {
      console.warn('Failed to persist cache to localStorage:', error);
    }
  }

  /**
   * Get available localStorage space
   */
  private getAvailableStorageSpace(): number {
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') return 0;

    try {
      const totalSpace = JSON.stringify(localStorage).length;
      const maxSpace = 5 * 1024 * 1024; // 5MB typical limit
      return Math.max(0, maxSpace - totalSpace);
    } catch {
      return 0;
    }
  }

  /**
   * Calculate data size in bytes
   */
  private calculateDataSize(data: EditingAnalysisResult): number {
    try {
      const serialized = JSON.stringify(data);
      return new Blob([serialized]).size;
    } catch {
      return 0;
    }
  }

  /**
   * Compress data using gzip-like compression
   */
  private async compress(data: EditingAnalysisResult): Promise<EditingAnalysisResult> {
    try {
      const serialized = JSON.stringify(data);
      const compressed = await this.gzipCompress(serialized);
      
      // Return compressed data wrapped in a way that preserves structure
      return {
        ...data,
        _compressed: true,
        _data: compressed
      } as any;
    } catch (error) {
      console.warn('Compression failed:', error);
      return data;
    }
  }

  /**
   * Decompress data
   */
  private async decompress(data: EditingAnalysisResult): Promise<EditingAnalysisResult> {
    try {
      if (!(data as any)._compressed) {
        return data;
      }
      
      const compressed = (data as any)._data;
      const decompressed = await this.gzipDecompress(compressed);
      return JSON.parse(decompressed);
    } catch (error) {
      console.warn('Decompression failed:', error);
      return data;
    }
  }

  /**
   * Simple gzip compression using CompressionStream
   */
  private async gzipCompress(data: string): Promise<string> {
    if (!('CompressionStream' in window)) {
      // Fallback: return original data
      return data;
    }
    
    const stream = new CompressionStream('gzip');
    const writer = stream.writable.getWriter();
    const reader = stream.readable.getReader();
    
    const chunks: Uint8Array[] = [];
    
    // Start compression
    writer.write(new TextEncoder().encode(data));
    writer.close();
    
    // Read compressed data
    let result = await reader.read();
    while (!result.done) {
      chunks.push(result.value);
      result = await reader.read();
    }
    
    // Convert to base64 string
    const compressed = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
    let offset = 0;
    for (const chunk of chunks) {
      compressed.set(chunk, offset);
      offset += chunk.length;
    }
    
    return btoa(String.fromCharCode(...compressed));
  }

  /**
   * Simple gzip decompression using DecompressionStream
   */
  private async gzipDecompress(compressedData: string): Promise<string> {
    if (!('DecompressionStream' in window)) {
      // Fallback: return original data
      return compressedData;
    }
    
    const stream = new DecompressionStream('gzip');
    const writer = stream.writable.getWriter();
    const reader = stream.readable.getReader();
    
    // Convert base64 to Uint8Array
    const binaryString = atob(compressedData);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    
    // Start decompression
    writer.write(bytes);
    writer.close();
    
    // Read decompressed data
    const chunks: Uint8Array[] = [];
    let result = await reader.read();
    while (!result.done) {
      chunks.push(result.value);
      result = await reader.read();
    }
    
    // Convert back to string
    const decompressed = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
    let offset = 0;
    for (const chunk of chunks) {
      decompressed.set(chunk, offset);
      offset += chunk.length;
    }
    
    return new TextDecoder().decode(decompressed);
  }

  /**
   * Record access time for performance monitoring
   */
  private recordAccessTime(time: number): void {
    this.accessTimes.push(time);
    
    // Keep only last 100 access times
    if (this.accessTimes.length > 100) {
      this.accessTimes.shift();
    }
    
    // Update average
    this.stats.averageAccessTime = 
      this.accessTimes.reduce((sum, t) => sum + t, 0) / this.accessTimes.length;
  }

  /**
   * Update hit rate statistic
   */
  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  }

  /**
   * Update cache statistics
   */
  private updateStats(): void {
    this.stats.entryCount = this.cache.size;
    
    // Calculate memory usage
    let totalSize = 0;
    for (const entry of this.cache.values()) {
      totalSize += entry.size;
    }
    this.stats.memoryUsage = totalSize / (1024 * 1024); // Convert to MB
    
    this.updateHitRate();
  }

  /**
   * Cleanup resources
   */
  dispose(): void {
    if (this.gcTimer) {
      clearInterval(this.gcTimer);
      this.gcTimer = undefined;
    }
    
    this.clear();
  }
}

// Singleton instance
export const imageEditingCache = new ImageEditingCache();