import type { ErrorInfo } from './types';

export class ImageEditingErrorHandler {
  private readonly maxRetries = 3;
  private readonly baseDelay = 1000;
  private readonly maxDelay = 30000;

  /**
   * Handle errors with retry logic
   */
  async handleWithRetry<T>(
    operation: () => Promise<T>,
    context: string,
    retryCount: number = 0
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      const errorInfo = this.categorizeError(error as Error);
      
      // Log error for monitoring
      this.logError(error as Error, context, retryCount);

      // Check if error is retryable and we haven't exceeded max retries
      if (errorInfo.retryable && retryCount < this.maxRetries) {
        const delay = this.calculateDelay(retryCount, errorInfo.retryAfter);
        
        console.log(`Retrying ${context} in ${delay}ms (attempt ${retryCount + 1}/${this.maxRetries})`);
        
        await this.sleep(delay);
        return this.handleWithRetry(operation, context, retryCount + 1);
      }

      // Transform error for user consumption
      throw this.transformError(error as Error, errorInfo);
    }
  }

  /**
   * Categorize error and determine if it's retryable
   */
  private categorizeError(error: Error): ErrorInfo {
    const message = error.message.toLowerCase();
    const errorString = error.toString().toLowerCase();

    // OpenAI API specific errors
    if (message.includes('rate limit')) {
      return {
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Rate limit exceeded. Please try again later.',
        retryable: true,
        retryAfter: this.extractRetryAfter(error.message) || 60000
      };
    }

    if (message.includes('insufficient quota') || message.includes('billing')) {
      return {
        code: 'INSUFFICIENT_QUOTA',
        message: 'Insufficient API quota. Please check your billing.',
        retryable: false
      };
    }

    if (message.includes('invalid api key') || message.includes('unauthorized')) {
      return {
        code: 'INVALID_API_KEY',
        message: 'Invalid API key. Please check your configuration.',
        retryable: false
      };
    }

    if (message.includes('timeout') || message.includes('network')) {
      return {
        code: 'NETWORK_ERROR',
        message: 'Network error. Please check your connection.',
        retryable: true,
        retryAfter: 2000
      };
    }

    if (message.includes('server error') || message.includes('internal error')) {
      return {
        code: 'SERVER_ERROR',
        message: 'Server error. Please try again.',
        retryable: true,
        retryAfter: 5000
      };
    }

    if (message.includes('image too large') || message.includes('file size')) {
      return {
        code: 'IMAGE_TOO_LARGE',
        message: 'Image file is too large. Please use a smaller image.',
        retryable: false
      };
    }

    if (message.includes('invalid image') || message.includes('unsupported format')) {
      return {
        code: 'INVALID_IMAGE_FORMAT',
        message: 'Invalid image format. Please use a supported format (JPEG, PNG, WebP).',
        retryable: false
      };
    }

    if (message.includes('content policy') || message.includes('safety')) {
      return {
        code: 'CONTENT_POLICY_VIOLATION',
        message: 'Image violates content policy. Please use a different image.',
        retryable: false
      };
    }

    // Generic errors
    if (errorString.includes('fetch')) {
      return {
        code: 'FETCH_ERROR',
        message: 'Network request failed. Please try again.',
        retryable: true,
        retryAfter: 2000
      };
    }

    if (message.includes('parse') || message.includes('json')) {
      return {
        code: 'PARSE_ERROR',
        message: 'Failed to parse response. Please try again.',
        retryable: true,
        retryAfter: 1000
      };
    }

    // Unknown error
    return {
      code: 'UNKNOWN_ERROR',
      message: 'An unexpected error occurred. Please try again.',
      retryable: true,
      retryAfter: 5000,
      details: {
        originalMessage: error.message,
        stack: error.stack
      }
    };
  }

  /**
   * Transform error for user-friendly display
   */
  private transformError(originalError: Error, errorInfo: ErrorInfo): Error {
    const error = new Error(errorInfo.message);
    
    // Add additional properties for debugging
    (error as any).code = errorInfo.code;
    (error as any).retryable = errorInfo.retryable;
    (error as any).retryAfter = errorInfo.retryAfter;
    (error as any).details = errorInfo.details;
    (error as any).originalError = originalError;

    return error;
  }

  /**
   * Extract retry-after time from error message
   */
  private extractRetryAfter(message: string): number | null {
    const match = message.match(/retry after (\d+) seconds?/i);
    if (match) {
      return parseInt(match[1]) * 1000;
    }

    const minuteMatch = message.match(/try again in (\d+) minutes?/i);
    if (minuteMatch) {
      return parseInt(minuteMatch[1]) * 60 * 1000;
    }

    return null;
  }

  /**
   * Calculate delay with exponential backoff
   */
  private calculateDelay(retryCount: number, retryAfter?: number): number {
    if (retryAfter) {
      return Math.min(retryAfter, this.maxDelay);
    }

    const exponentialDelay = this.baseDelay * Math.pow(2, retryCount);
    const jitter = Math.random() * 0.1 * exponentialDelay;
    
    return Math.min(exponentialDelay + jitter, this.maxDelay);
  }

  /**
   * Log error for monitoring and debugging
   */
  private logError(error: Error, context: string, retryCount: number): void {
    const errorDetails = {
      timestamp: new Date().toISOString(),
      context,
      retryCount,
      message: error.message,
      stack: error.stack,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // Log to console for development
    console.error('ImageEditingError:', errorDetails);

    // In production, you would send this to your error tracking service
    // Example: Sentry, LogRocket, Bugsnag, etc.
    this.sendToErrorTracking(errorDetails);
  }

  /**
   * Send error to tracking service
   */
  private sendToErrorTracking(errorDetails: any): void {
    // In production, implement your error tracking service here
    // For now, we'll just store in localStorage for debugging
    try {
      const errors = JSON.parse(localStorage.getItem('tinderop_errors') || '[]');
      errors.push(errorDetails);
      
      // Keep only last 100 errors
      if (errors.length > 100) {
        errors.splice(0, errors.length - 100);
      }
      
      localStorage.setItem('tinderop_errors', JSON.stringify(errors));
    } catch (e) {
      console.warn('Failed to store error for tracking:', e);
    }
  }

  /**
   * Get recent errors for debugging
   */
  getRecentErrors(): any[] {
    try {
      return JSON.parse(localStorage.getItem('tinderop_errors') || '[]');
    } catch (e) {
      console.warn('Failed to retrieve recent errors:', e);
      return [];
    }
  }

  /**
   * Clear error log
   */
  clearErrors(): void {
    localStorage.removeItem('tinderop_errors');
  }

  /**
   * Utility method for sleep
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Validate image before processing
   */
  validateImage(imageBase64: string, fileName: string): void {
    // Check file size (base64 is ~33% larger than original)
    const sizeInBytes = (imageBase64.length * 3) / 4;
    const maxSizeInBytes = 20 * 1024 * 1024; // 20MB
    
    if (sizeInBytes > maxSizeInBytes) {
      throw new Error(`Image file is too large (${(sizeInBytes / 1024 / 1024).toFixed(1)}MB). Maximum size is ${maxSizeInBytes / 1024 / 1024}MB.`);
    }

    // Check format
    const supportedFormats = ['image/jpeg', 'image/png', 'image/webp'];
    const format = this.getImageFormat(imageBase64);
    
    if (!supportedFormats.includes(format)) {
      throw new Error(`Unsupported image format: ${format}. Supported formats: ${supportedFormats.join(', ')}`);
    }

    // Check if image is valid base64
    if (!this.isValidBase64(imageBase64)) {
      throw new Error('Invalid image data. Please try uploading the image again.');
    }
  }

  /**
   * Get image format from base64 data
   */
  private getImageFormat(base64: string): string {
    const header = base64.substring(0, 50);
    
    if (header.startsWith('/9j/')) return 'image/jpeg';
    if (header.startsWith('iVBORw0KGgo')) return 'image/png';
    if (header.startsWith('UklGR')) return 'image/webp';
    
    return 'unknown';
  }

  /**
   * Validate base64 string
   */
  private isValidBase64(str: string): boolean {
    try {
      // Remove data URL prefix if present
      const base64 = str.replace(/^data:image\/[a-z]+;base64,/, '');
      
      // Check if it's valid base64
      return btoa(atob(base64)) === base64;
    } catch (e) {
      return false;
    }
  }
}

export const imageEditingErrorHandler = new ImageEditingErrorHandler();