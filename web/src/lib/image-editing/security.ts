/**
 * Security validation and sanitization service for image editing
 * Provides comprehensive security measures for image processing
 */

export interface SecurityValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  sanitizedData?: {
    fileName: string;
    mimeType: string;
    size: number;
    dimensions?: { width: number; height: number };
  };
}

export interface SecurityConfig {
  maxFileSize: number; // in bytes
  maxDimensions: { width: number; height: number };
  allowedMimeTypes: string[];
  maxFilenameLength: number;
  enableContentScanning: boolean;
  enableMetadataRemoval: boolean;
}

export class SecurityService {
  private config: SecurityConfig;

  constructor(config?: Partial<SecurityConfig>) {
    this.config = {
      maxFileSize: 20 * 1024 * 1024, // 20MB
      maxDimensions: { width: 4096, height: 4096 },
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
      maxFilenameLength: 255,
      enableContentScanning: true,
      enableMetadataRemoval: true,
      ...config
    };
  }

  /**
   * Validate file input for security compliance
   */
  async validateFile(file: File): Promise<SecurityValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // File size validation
    if (file.size > this.config.maxFileSize) {
      errors.push(`File size exceeds maximum limit of ${this.config.maxFileSize / (1024 * 1024)}MB`);
    }

    // MIME type validation
    if (!this.config.allowedMimeTypes.includes(file.type)) {
      errors.push(`File type ${file.type} is not supported. Allowed types: ${this.config.allowedMimeTypes.join(', ')}`);
    }

    // Filename validation
    const sanitizedFileName = this.sanitizeFilename(file.name);
    if (sanitizedFileName.length > this.config.maxFilenameLength) {
      errors.push(`Filename exceeds maximum length of ${this.config.maxFilenameLength} characters`);
    }

    // Content validation
    if (this.config.enableContentScanning) {
      const contentValidation = await this.validateImageContent(file);
      if (!contentValidation.valid) {
        errors.push(...contentValidation.errors);
      }
      warnings.push(...contentValidation.warnings);
    }

    // Image dimensions validation
    try {
      const dimensions = await this.getImageDimensions(file);
      if (dimensions.width > this.config.maxDimensions.width || 
          dimensions.height > this.config.maxDimensions.height) {
        errors.push(`Image dimensions exceed maximum size of ${this.config.maxDimensions.width}x${this.config.maxDimensions.height}`);
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings,
        sanitizedData: {
          fileName: sanitizedFileName,
          mimeType: file.type,
          size: file.size,
          dimensions
        }
      };
    } catch (error) {
      errors.push('Failed to validate image format');
      return {
        valid: false,
        errors,
        warnings
      };
    }
  }

  /**
   * Validate base64 image data
   */
  async validateBase64Image(base64Data: string, fileName: string): Promise<SecurityValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Extract metadata from base64 data
      const metadata = this.parseBase64Metadata(base64Data);
      
      // Validate MIME type
      if (!this.config.allowedMimeTypes.includes(metadata.mimeType)) {
        errors.push(`Image type ${metadata.mimeType} is not supported`);
      }

      // Validate file size
      if (metadata.size > this.config.maxFileSize) {
        errors.push(`Image size exceeds maximum limit of ${this.config.maxFileSize / (1024 * 1024)}MB`);
      }

      // Validate filename
      const sanitizedFileName = this.sanitizeFilename(fileName);
      if (sanitizedFileName.length > this.config.maxFilenameLength) {
        errors.push(`Filename exceeds maximum length of ${this.config.maxFilenameLength} characters`);
      }

      // Validate image dimensions
      const dimensions = await this.getBase64ImageDimensions(base64Data);
      if (dimensions.width > this.config.maxDimensions.width || 
          dimensions.height > this.config.maxDimensions.height) {
        errors.push(`Image dimensions exceed maximum size of ${this.config.maxDimensions.width}x${this.config.maxDimensions.height}`);
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings,
        sanitizedData: {
          fileName: sanitizedFileName,
          mimeType: metadata.mimeType,
          size: metadata.size,
          dimensions
        }
      };
    } catch (error) {
      errors.push('Failed to validate base64 image data');
      return {
        valid: false,
        errors,
        warnings
      };
    }
  }

  /**
   * Sanitize filename to prevent directory traversal and other attacks
   */
  sanitizeFilename(filename: string): string {
    // Remove directory traversal attempts
    let sanitized = filename.replace(/[/\\:*?"<>|]/g, '_');
    
    // Remove control characters and non-printable characters
    sanitized = sanitized.replace(/[\x00-\x1f\x7f-\x9f]/g, '');
    
    // Remove leading/trailing whitespace and dots
    sanitized = sanitized.trim().replace(/^\.+|\.+$/g, '');
    
    // Ensure filename is not empty
    if (!sanitized) {
      sanitized = 'untitled';
    }
    
    // Truncate if too long
    if (sanitized.length > this.config.maxFilenameLength) {
      const ext = sanitized.split('.').pop() || '';
      const nameWithoutExt = sanitized.slice(0, -(ext.length + 1));
      sanitized = nameWithoutExt.slice(0, this.config.maxFilenameLength - ext.length - 1) + '.' + ext;
    }
    
    return sanitized;
  }

  /**
   * Remove metadata from image data
   */
  async removeImageMetadata(base64Data: string): Promise<string> {
    if (!this.config.enableMetadataRemoval) {
      return base64Data;
    }

    try {
      // Create a canvas to strip metadata
      const img = new Image();
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        throw new Error('Failed to create canvas context');
      }

      return new Promise((resolve, reject) => {
        img.onload = () => {
          canvas.width = img.width;
          canvas.height = img.height;
          ctx.drawImage(img, 0, 0);
          
          // Convert back to base64 without metadata
          const cleanBase64 = canvas.toDataURL('image/jpeg', 0.9);
          resolve(cleanBase64);
        };
        
        img.onerror = () => reject(new Error('Failed to load image for metadata removal'));
        img.src = base64Data;
      });
    } catch (error) {
      console.warn('Failed to remove image metadata:', error);
      return base64Data; // Return original if metadata removal fails
    }
  }

  /**
   * Validate image content for malicious patterns
   */
  private async validateImageContent(file: File): Promise<{ valid: boolean; errors: string[]; warnings: string[] }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check for suspicious file signatures
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      
      // Check for valid image headers
      if (!this.hasValidImageHeader(uint8Array, file.type)) {
        errors.push('Invalid image file header');
      }

      // Check for embedded scripts or suspicious patterns
      const suspiciousPatterns = [
        /<script/i,
        /javascript:/i,
        /data:text\/html/i,
        /vbscript:/i,
        /<object/i,
        /<embed/i,
        /<iframe/i
      ];

      // Convert to string for pattern matching (check only first 1KB)
      const headerString = Array.from(uint8Array.slice(0, 1024))
        .map(byte => String.fromCharCode(byte))
        .join('');

      for (const pattern of suspiciousPatterns) {
        if (pattern.test(headerString)) {
          errors.push('Suspicious content detected in image file');
          break;
        }
      }

      return { valid: errors.length === 0, errors, warnings };
    } catch (error) {
      errors.push('Failed to validate image content');
      return { valid: false, errors, warnings };
    }
  }

  /**
   * Check if file has valid image header
   */
  private hasValidImageHeader(uint8Array: Uint8Array, mimeType: string): boolean {
    if (uint8Array.length < 8) return false;

    switch (mimeType) {
      case 'image/jpeg':
        return uint8Array[0] === 0xFF && uint8Array[1] === 0xD8;
      case 'image/png':
        return uint8Array[0] === 0x89 && uint8Array[1] === 0x50 && 
               uint8Array[2] === 0x4E && uint8Array[3] === 0x47;
      case 'image/webp':
        return uint8Array[0] === 0x52 && uint8Array[1] === 0x49 && 
               uint8Array[2] === 0x46 && uint8Array[3] === 0x46 &&
               uint8Array[8] === 0x57 && uint8Array[9] === 0x45 &&
               uint8Array[10] === 0x42 && uint8Array[11] === 0x50;
      default:
        return false;
    }
  }

  /**
   * Get image dimensions from file
   */
  private async getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const objectUrl = URL.createObjectURL(file);
      
      img.onload = () => {
        URL.revokeObjectURL(objectUrl);
        resolve({ width: img.width, height: img.height });
      };
      
      img.onerror = () => {
        URL.revokeObjectURL(objectUrl);
        reject(new Error('Failed to load image for dimension analysis'));
      };
      
      img.src = objectUrl;
    });
  }

  /**
   * Get image dimensions from base64 data
   */
  private async getBase64ImageDimensions(base64Data: string): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        resolve({ width: img.width, height: img.height });
      };
      
      img.onerror = () => {
        reject(new Error('Failed to load base64 image for dimension analysis'));
      };
      
      img.src = base64Data;
    });
  }

  /**
   * Parse metadata from base64 data
   */
  private parseBase64Metadata(base64Data: string): { mimeType: string; size: number } {
    // Extract MIME type from data URL
    const mimeMatch = base64Data.match(/^data:([^;]+);base64,/);
    const mimeType = mimeMatch ? mimeMatch[1] : 'image/jpeg';
    
    // Calculate approximate size (base64 is ~33% larger than original)
    const base64Content = base64Data.split(',')[1] || '';
    const size = Math.floor(base64Content.length * 0.75);
    
    return { mimeType, size };
  }

  /**
   * Validate content policy compliance
   */
  async validateContentPolicy(base64Data: string): Promise<{ compliant: boolean; issues: string[] }> {
    const issues: string[] = [];
    
    try {
      // Note: In a real implementation, you would integrate with content moderation services
      // like AWS Rekognition, Google Vision API, or Azure Computer Vision
      // For now, we'll implement basic checks
      
      // Check image dimensions for extremely small images (likely tracking pixels)
      const dimensions = await this.getBase64ImageDimensions(base64Data);
      if (dimensions.width < 10 || dimensions.height < 10) {
        issues.push('Image dimensions are too small');
      }
      
      // Check for extremely large images that might be used for attacks
      if (dimensions.width > 10000 || dimensions.height > 10000) {
        issues.push('Image dimensions are excessively large');
      }
      
      return {
        compliant: issues.length === 0,
        issues
      };
    } catch (error) {
      issues.push('Failed to validate content policy');
      return {
        compliant: false,
        issues
      };
    }
  }

  /**
   * Generate secure cache key for image data
   */
  generateSecureCacheKey(data: string, salt?: string): Promise<string> {
    // Use Web Crypto API for secure hashing
    const encoder = new TextEncoder();
    const dataBytes = encoder.encode(data + (salt || ''));
    
    return crypto.subtle.digest('SHA-256', dataBytes)
      .then(hashBuffer => {
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      })
      .catch(() => {
        // Fallback to simple hash if crypto API fails
        let hash = 0;
        for (let i = 0; i < data.length; i++) {
          const char = data.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(16);
      });
  }

  /**
   * Sanitize user input for prompts
   */
  sanitizePrompt(prompt: string): string {
    // Remove potentially harmful content from AI prompts
    let sanitized = prompt
      .replace(/[<>]/g, '') // Remove HTML tags
      .replace(/javascript:/gi, '') // Remove JavaScript protocols
      .replace(/data:/gi, '') // Remove data URIs
      .replace(/vbscript:/gi, '') // Remove VBScript
      .replace(/file:/gi, '') // Remove file protocols
      .trim();

    // Limit length to prevent prompt injection
    if (sanitized.length > 2000) {
      sanitized = sanitized.substring(0, 2000);
    }

    return sanitized;
  }
}

// Singleton instance
export const securityService = new SecurityService();