import { useState, useEffect, useCallback } from 'react';
import { imageEditingLogger } from '../logger';

interface UseImageEditingAnalyticsReturn {
  analytics: {
    totalRequests: number;
    successRate: number;
    averageResponseTime: number;
    errorRate: number;
    cacheHitRate: number;
    popularEditingTypes: Array<{ type: string; count: number }>;
    peakHours: Array<{ hour: number; count: number }>;
    costAnalysis: {
      totalCost: number;
      averageCostPerRequest: number;
      costByType: Array<{ type: string; cost: number }>;
    };
  };
  
  recentErrors: Array<{
    timestamp: string;
    context: string;
    message: string;
    retryCount: number;
  }>;
  
  systemHealth: {
    status: 'healthy' | 'degraded' | 'down';
    lastCheck: number;
    responseTime: number;
    errorRate: number;
    queueSize: number;
    cacheHitRate: number;
    activeRequests: number;
  };
  
  // Actions
  refreshAnalytics: () => void;
  exportAnalytics: (format: 'json' | 'csv') => string;
  clearAnalytics: () => void;
  
  // Filtering
  setTimeRange: (range: '1h' | '24h' | '7d' | '30d') => void;
  setFilters: (filters: {
    userId?: string;
    requestId?: string;
    type?: string;
    level?: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
  }) => void;
}

export function useImageEditingAnalytics(): UseImageEditingAnalyticsReturn {
  const [timeRange, setTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h');
  const [filters, setFilters] = useState<{
    userId?: string;
    requestId?: string;
    type?: string;
    level?: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
  }>({});
  
  const [analytics, setAnalytics] = useState({
    totalRequests: 0,
    successRate: 0,
    averageResponseTime: 0,
    errorRate: 0,
    cacheHitRate: 0,
    popularEditingTypes: [],
    peakHours: [],
    costAnalysis: {
      totalCost: 0,
      averageCostPerRequest: 0,
      costByType: []
    }
  });
  
  const [recentErrors, setRecentErrors] = useState<Array<{
    timestamp: string;
    context: string;
    message: string;
    retryCount: number;
  }>>([]);
  
  const [systemHealth, setSystemHealth] = useState({
    status: 'healthy' as const,
    lastCheck: Date.now(),
    responseTime: 0,
    errorRate: 0,
    queueSize: 0,
    cacheHitRate: 0,
    activeRequests: 0
  });

  // Refresh analytics data
  const refreshAnalytics = useCallback(() => {
    // Get analytics for the specified time range
    const analyticsData = imageEditingLogger.getAnalytics(timeRange);
    setAnalytics(analyticsData);
    
    // Get recent errors
    const errorLogs = imageEditingLogger.getLogs({
      level: 'ERROR',
      limit: 20,
      ...filters
    });
    
    const formattedErrors = errorLogs.map(log => ({
      timestamp: new Date(log.timestamp).toISOString(),
      context: log.data.context || 'Unknown',
      message: log.data.errorMessage || 'Unknown error',
      retryCount: log.data.retryCount || 0
    }));
    
    setRecentErrors(formattedErrors);
    
    // Get system health from recent logs
    const healthLogs = imageEditingLogger.getLogs({
      type: 'SYSTEM_HEALTH',
      limit: 1
    });
    
    if (healthLogs.length > 0) {
      const healthData = healthLogs[0].data;
      setSystemHealth({
        status: healthData.status || 'healthy',
        lastCheck: healthData.lastCheck || Date.now(),
        responseTime: healthData.responseTime || 0,
        errorRate: healthData.errorRate || 0,
        queueSize: healthData.queueSize || 0,
        cacheHitRate: healthData.cacheHitRate || 0,
        activeRequests: healthData.activeRequests || 0
      });
    }
  }, [timeRange, filters]);

  // Auto-refresh analytics
  useEffect(() => {
    refreshAnalytics();
    
    // Refresh every 30 seconds
    const interval = setInterval(refreshAnalytics, 30000);
    
    return () => clearInterval(interval);
  }, [refreshAnalytics]);

  // Export analytics
  const exportAnalytics = useCallback((format: 'json' | 'csv') => {
    const logs = imageEditingLogger.getLogs(filters);
    return imageEditingLogger.exportLogs(format);
  }, [filters]);

  // Clear analytics
  const clearAnalytics = useCallback(() => {
    imageEditingLogger.clearLogs();
    refreshAnalytics();
  }, [refreshAnalytics]);

  return {
    analytics,
    recentErrors,
    systemHealth,
    refreshAnalytics,
    exportAnalytics,
    clearAnalytics,
    setTimeRange,
    setFilters
  };
}