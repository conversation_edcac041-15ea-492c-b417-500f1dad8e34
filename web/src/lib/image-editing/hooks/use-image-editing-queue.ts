import { useState, useEffect, useCallback } from 'react';
import { imageEditingQueueManager } from '../queue-manager';

interface UseImageEditingQueueReturn {
  queueStatus: {
    queueSize: number;
    processing: number;
    averageWaitTime: number;
    estimatedWaitTime: number;
  };
  
  queueHealth: {
    isHealthy: boolean;
    issues: string[];
    metrics: {
      queueSize: number;
      processingCount: number;
      oldestItemAge: number;
      isProcessing: boolean;
    };
  };
  
  // Queue management
  pauseQueue: () => void;
  resumeQueue: () => void;
  clearQueue: () => void;
  
  // Item management
  getItemStatus: (id: string) => {
    status: 'queued' | 'processing' | 'completed' | 'failed' | 'not_found';
    position?: number;
    estimatedTime?: number;
    retryCount?: number;
  };
  
  removeItem: (id: string) => boolean;
  updateItemPriority: (id: string, priority: number) => boolean;
  
  // Analytics
  analytics: {
    totalProcessed: number;
    successRate: number;
    averageProcessingTime: number;
    currentThroughput: number;
    peakQueueSize: number;
    retryRate: number;
  };
}

export function useImageEditingQueue(): UseImageEditingQueueReturn {
  const [queueStatus, setQueueStatus] = useState({
    queueSize: 0,
    processing: 0,
    averageWaitTime: 0,
    estimatedWaitTime: 0
  });
  
  const [queueHealth, setQueueHealth] = useState({
    isHealthy: true,
    issues: [],
    metrics: {
      queueSize: 0,
      processingCount: 0,
      oldestItemAge: 0,
      isProcessing: true
    }
  });
  
  const [analytics, setAnalytics] = useState({
    totalProcessed: 0,
    successRate: 0,
    averageProcessingTime: 0,
    currentThroughput: 0,
    peakQueueSize: 0,
    retryRate: 0
  });

  // Update queue status periodically
  useEffect(() => {
    const updateStatus = () => {
      const status = imageEditingQueueManager.getQueueStatus();
      setQueueStatus(status);
      
      const health = imageEditingQueueManager.getHealth();
      setQueueHealth(health);
      
      const analyticsData = imageEditingQueueManager.getAnalytics();
      setAnalytics(analyticsData);
    };

    // Initial update
    updateStatus();

    // Update every 2 seconds
    const interval = setInterval(updateStatus, 2000);

    return () => clearInterval(interval);
  }, []);

  // Queue management actions
  const pauseQueue = useCallback(() => {
    imageEditingQueueManager.pause();
  }, []);

  const resumeQueue = useCallback(() => {
    imageEditingQueueManager.resume();
  }, []);

  const clearQueue = useCallback(() => {
    imageEditingQueueManager.clear();
  }, []);

  // Item management actions
  const getItemStatus = useCallback((id: string) => {
    return imageEditingQueueManager.getItemStatus(id);
  }, []);

  const removeItem = useCallback((id: string) => {
    return imageEditingQueueManager.remove(id);
  }, []);

  const updateItemPriority = useCallback((id: string, priority: number) => {
    return imageEditingQueueManager.updatePriority(id, priority);
  }, []);

  return {
    queueStatus,
    queueHealth,
    pauseQueue,
    resumeQueue,
    clearQueue,
    getItemStatus,
    removeItem,
    updateItemPriority,
    analytics
  };
}