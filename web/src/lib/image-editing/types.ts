export interface ImageEditingRequest {
  requestId?: string;
  userId: string;
  imageBase64: string;
  imageHash: string;
  fileName: string;
  mimeType: string;
  preferences?: {
    style?: 'natural' | 'enhanced' | 'artistic' | 'professional';
    conservative?: boolean;
    maxBudget?: number;
    priorityTypes?: EditingType[];
  };
  metadata?: {
    originalAnalysisScore?: number;
    targetImprovement?: number;
    userFeedback?: string;
  };
}

export interface ImageEditingResult {
  requestId: string;
  originalImageHash: string;
  analysisResult: ImageAnalysisResult;
  recommendations: EditingRecommendation[];
  estimatedImprovementScore: number;
  processingTime: number;
  timestamp: string;
}

export interface ImageAnalysisResult {
  technicalIssues: string[];
  editingOpportunities: EditingOpportunity[];
  overallAssessment: string;
  confidenceScore: number;
}

export interface EditingOpportunity {
  type: EditingType;
  priority: 'high' | 'medium' | 'low';
  description: string;
  suggestedEdit: string;
  expectedImprovement: string;
  detectedIssues: string[];
}

export interface EditingRecommendation {
  id: string;
  type: EditingType;
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  editingPrompt: string;
  expectedImprovement: string;
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedCost: number;
  previewAvailable: boolean;
  processingTime?: number;
  beforeAfterComparison?: {
    beforeScore: number;
    afterScore: number;
    improvementAreas: string[];
  };
}

export type EditingType = 
  | 'lighting'
  | 'background'
  | 'facial'
  | 'style'
  | 'composition'
  | 'color'
  | 'noise_reduction'
  | 'sharpening'
  | 'skin_smoothing'
  | 'teeth_whitening'
  | 'eye_enhancement'
  | 'hair_styling'
  | 'outfit_adjustment'
  | 'posture_correction';

export interface ImageEditingProgress {
  requestId: string;
  stage: 'analyzing' | 'generating' | 'processing' | 'completed' | 'error';
  progress: number; // 0-100
  message: string;
  currentStep?: string;
  totalSteps?: number;
  estimatedTimeRemaining?: number;
}

export interface EditingStep {
  id: string;
  type: EditingType;
  status: 'pending' | 'processing' | 'completed' | 'error';
  prompt: string;
  result?: {
    editedImageBase64: string;
    improvementScore: number;
    processingTime: number;
    cost: number;
  };
  error?: string;
}

export interface BatchEditingRequest {
  images: ImageEditingRequest[];
  batchId: string;
  options?: {
    maxConcurrentRequests?: number;
    prioritizeByScore?: boolean;
    budgetLimit?: number;
  };
}

export interface BatchEditingResult {
  batchId: string;
  results: ImageEditingResult[];
  summary: {
    totalImages: number;
    successfulEdits: number;
    failedEdits: number;
    totalCost: number;
    averageImprovement: number;
    processingTime: number;
  };
}

export interface EditingConfig {
  maxImageSize: number;
  supportedFormats: string[];
  maxConcurrentRequests: number;
  rateLimits: {
    analysisPerHour: number;
    generationPerHour: number;
    generationPerDay: number;
  };
  costs: {
    analysisPerImage: number;
    generationStandard: number;
    generationHighFidelity: number;
  };
}

export interface UsageMetrics {
  userId: string;
  period: 'hour' | 'day' | 'month';
  analysisRequests: number;
  generationRequests: number;
  totalCost: number;
  averageImprovementScore: number;
  mostUsedEditingTypes: EditingType[];
}

export interface CacheEntry {
  key: string;
  data: ImageEditingResult;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  expiresAt: number;
}

export interface RateLimitInfo {
  userId: string;
  analysisCount: number;
  generationCount: number;
  resetTime: number;
  dailyGenerationCount: number;
  dailyResetTime: number;
}

export interface ErrorInfo {
  code: string;
  message: string;
  retryable: boolean;
  retryAfter?: number;
  details?: Record<string, any>;
}

export interface QueueItem {
  id: string;
  priority: number;
  request: ImageEditingRequest;
  retryCount: number;
  maxRetries: number;
  createdAt: number;
  processingStartedAt?: number;
}

export interface ServiceHealth {
  status: 'healthy' | 'degraded' | 'down';
  lastCheck: number;
  responseTime: number;
  errorRate: number;
  queueSize: number;
  cacheHitRate: number;
  activeRequests: number;
}