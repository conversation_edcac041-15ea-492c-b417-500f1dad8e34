import type { QueueItem, ImageEditingRequest, ImageEditingResult } from './types';
import { imageEditingLogger } from './logger';

export class ImageEditingQueueManager {
  private queue: QueueItem[] = [];
  private processing = new Map<string, Promise<ImageEditingResult>>();
  private readonly maxConcurrentRequests = 3;
  private readonly maxQueueSize = 100;
  private readonly maxRetries = 3;
  private readonly retryDelay = 5000; // 5 seconds

  private processingInterval?: NodeJS.Timeout;
  private isProcessing = false;

  constructor() {
    this.startProcessing();
  }

  /**
   * Add request to queue
   */
  async enqueue(request: ImageEditingRequest, priority: number = 0): Promise<string> {
    // Check queue size
    if (this.queue.length >= this.maxQueueSize) {
      throw new Error('Queue is full. Please try again later.');
    }

    // Generate unique ID for this queue item
    const id = this.generateQueueId();

    const queueItem: QueueItem = {
      id,
      priority,
      request,
      retryCount: 0,
      maxRetries: this.maxRetries,
      createdAt: Date.now()
    };

    // Insert in priority order
    this.insertByPriority(queueItem);

    imageEditingLogger.logUserAction(request.userId, 'queue_added', {
      queueId: id,
      priority,
      queueSize: this.queue.length
    });

    return id;
  }

  /**
   * Get queue status
   */
  getQueueStatus(): {
    queueSize: number;
    processing: number;
    averageWaitTime: number;
    estimatedWaitTime: number;
  } {
    const now = Date.now();
    const waitTimes = this.queue.map(item => now - item.createdAt);
    const averageWaitTime = waitTimes.length > 0 
      ? waitTimes.reduce((sum, time) => sum + time, 0) / waitTimes.length 
      : 0;

    // Estimate wait time based on queue position and processing time
    const estimatedProcessingTime = 30000; // 30 seconds average
    const estimatedWaitTime = this.queue.length * estimatedProcessingTime / this.maxConcurrentRequests;

    return {
      queueSize: this.queue.length,
      processing: this.processing.size,
      averageWaitTime,
      estimatedWaitTime
    };
  }

  /**
   * Get item status
   */
  getItemStatus(id: string): {
    status: 'queued' | 'processing' | 'completed' | 'failed' | 'not_found';
    position?: number;
    estimatedTime?: number;
    retryCount?: number;
  } {
    // Check if processing
    if (this.processing.has(id)) {
      return { status: 'processing' };
    }

    // Check if in queue
    const queueIndex = this.queue.findIndex(item => item.id === id);
    if (queueIndex >= 0) {
      const item = this.queue[queueIndex];
      const estimatedTime = queueIndex * 30000; // 30 seconds per item
      
      return {
        status: 'queued',
        position: queueIndex + 1,
        estimatedTime,
        retryCount: item.retryCount
      };
    }

    return { status: 'not_found' };
  }

  /**
   * Remove item from queue
   */
  remove(id: string): boolean {
    const index = this.queue.findIndex(item => item.id === id);
    if (index >= 0) {
      this.queue.splice(index, 1);
      imageEditingLogger.logUserAction('system', 'queue_removed', { queueId: id });
      return true;
    }
    return false;
  }

  /**
   * Clear queue
   */
  clear(): void {
    this.queue = [];
    imageEditingLogger.logUserAction('system', 'queue_cleared', { timestamp: Date.now() });
  }

  /**
   * Pause queue processing
   */
  pause(): void {
    this.isProcessing = false;
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }
    imageEditingLogger.logUserAction('system', 'queue_paused', { timestamp: Date.now() });
  }

  /**
   * Resume queue processing
   */
  resume(): void {
    this.isProcessing = true;
    this.startProcessing();
    imageEditingLogger.logUserAction('system', 'queue_resumed', { timestamp: Date.now() });
  }

  /**
   * Get queue analytics
   */
  getAnalytics(): {
    totalProcessed: number;
    successRate: number;
    averageProcessingTime: number;
    currentThroughput: number;
    peakQueueSize: number;
    retryRate: number;
  } {
    // This would be implemented with persistent storage in production
    // For now, return mock data
    return {
      totalProcessed: 0,
      successRate: 95,
      averageProcessingTime: 25000,
      currentThroughput: 2.5,
      peakQueueSize: 15,
      retryRate: 5
    };
  }

  /**
   * Private methods
   */
  private startProcessing(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }

    this.isProcessing = true;
    this.processingInterval = setInterval(() => {
      this.processQueue();
    }, 1000); // Check every second
  }

  private async processQueue(): Promise<void> {
    if (!this.isProcessing || this.processing.size >= this.maxConcurrentRequests) {
      return;
    }

    const item = this.queue.shift();
    if (!item) {
      return;
    }

    // Start processing
    item.processingStartedAt = Date.now();
    
    const processingPromise = this.processItem(item);
    this.processing.set(item.id, processingPromise);

    try {
      const result = await processingPromise;
      
      // Processing completed successfully
      this.processing.delete(item.id);
      
      imageEditingLogger.logUserAction(item.request.userId, 'queue_processed', {
        queueId: item.id,
        success: true,
        processingTime: Date.now() - (item.processingStartedAt || Date.now()),
        retryCount: item.retryCount
      });

      // Notify completion (would use WebSocket or event system in production)
      this.notifyCompletion(item.id, result);

    } catch (error) {
      this.processing.delete(item.id);
      
      // Handle retry logic
      if (item.retryCount < item.maxRetries) {
        item.retryCount++;
        
        // Add delay before retry
        setTimeout(() => {
          this.insertByPriority(item);
        }, this.retryDelay * item.retryCount);

        imageEditingLogger.logUserAction(item.request.userId, 'queue_retry', {
          queueId: item.id,
          retryCount: item.retryCount,
          error: (error as Error).message
        });
      } else {
        // Max retries exceeded
        imageEditingLogger.logUserAction(item.request.userId, 'queue_failed', {
          queueId: item.id,
          error: (error as Error).message,
          retryCount: item.retryCount
        });

        // Notify failure
        this.notifyFailure(item.id, error as Error);
      }
    }
  }

  private async processItem(item: QueueItem): Promise<ImageEditingResult> {
    // Import the service here to avoid circular dependencies
    const { imageEditingService } = await import('./image-editing-service');
    
    return imageEditingService.analyzeImageForEditing(
      item.request,
      {
        onProgress: (progress) => {
          // Notify progress (would use WebSocket or event system in production)
          this.notifyProgress(item.id, progress);
        }
      }
    );
  }

  private insertByPriority(item: QueueItem): void {
    // Insert item in priority order (higher priority first)
    let insertIndex = 0;
    for (let i = 0; i < this.queue.length; i++) {
      if (this.queue[i].priority < item.priority) {
        insertIndex = i;
        break;
      }
      insertIndex = i + 1;
    }
    
    this.queue.splice(insertIndex, 0, item);
  }

  private generateQueueId(): string {
    return `queue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private notifyCompletion(id: string, result: ImageEditingResult): void {
    // In production, this would use WebSocket, Server-Sent Events, or polling
    // For now, we'll use custom events
    const event = new CustomEvent('imageEditingComplete', {
      detail: { id, result }
    });
    window.dispatchEvent(event);
  }

  private notifyProgress(id: string, progress: any): void {
    const event = new CustomEvent('imageEditingProgress', {
      detail: { id, progress }
    });
    window.dispatchEvent(event);
  }

  private notifyFailure(id: string, error: Error): void {
    const event = new CustomEvent('imageEditingError', {
      detail: { id, error: error.message }
    });
    window.dispatchEvent(event);
  }

  /**
   * Batch processing capabilities
   */
  async enqueueBatch(requests: ImageEditingRequest[], priority: number = 0): Promise<string[]> {
    const ids: string[] = [];
    
    for (const request of requests) {
      // Assign higher priority to later items in batch to process in order
      const batchPriority = priority + (requests.length - ids.length);
      const id = await this.enqueue(request, batchPriority);
      ids.push(id);
    }

    return ids;
  }

  /**
   * Priority management
   */
  updatePriority(id: string, newPriority: number): boolean {
    const index = this.queue.findIndex(item => item.id === id);
    if (index >= 0) {
      const item = this.queue.splice(index, 1)[0];
      item.priority = newPriority;
      this.insertByPriority(item);
      return true;
    }
    return false;
  }

  /**
   * Health check
   */
  getHealth(): {
    isHealthy: boolean;
    issues: string[];
    metrics: {
      queueSize: number;
      processingCount: number;
      oldestItemAge: number;
      isProcessing: boolean;
    };
  } {
    const issues: string[] = [];
    const now = Date.now();
    
    // Check for stalled processing
    if (this.processing.size > 0 && !this.isProcessing) {
      issues.push('Processing stalled');
    }

    // Check for queue size
    if (this.queue.length > this.maxQueueSize * 0.8) {
      issues.push('Queue nearly full');
    }

    // Check for old items
    const oldestItem = this.queue.reduce((oldest, item) => 
      item.createdAt < oldest ? item.createdAt : oldest, now
    );
    
    const oldestItemAge = now - oldestItem;
    if (oldestItemAge > 5 * 60 * 1000) { // 5 minutes
      issues.push('Old items in queue');
    }

    return {
      isHealthy: issues.length === 0,
      issues,
      metrics: {
        queueSize: this.queue.length,
        processingCount: this.processing.size,
        oldestItemAge,
        isProcessing: this.isProcessing
      }
    };
  }
}

export const imageEditingQueueManager = new ImageEditingQueueManager();