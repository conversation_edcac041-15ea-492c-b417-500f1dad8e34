import type { UsageMetrics, ServiceHealth } from './types';

export class ImageEditingLogger {
  private logs: LogEntry[] = [];
  private readonly maxLogs = 1000;
  private readonly logLevel: LogLevel = 'INFO';

  constructor() {
    this.startHealthMonitoring();
  }

  /**
   * Log cache hit
   */
  logCacheHit(requestId: string, imageHash: string): void {
    this.log('INFO', 'CACHE_HIT', {
      requestId,
      imageHash: imageHash.substring(0, 10),
      timestamp: Date.now()
    });
  }

  /**
   * Log cache miss
   */
  logCacheMiss(requestId: string, imageHash: string): void {
    this.log('INFO', 'CACHE_MISS', {
      requestId,
      imageHash: imageHash.substring(0, 10),
      timestamp: Date.now()
    });
  }

  /**
   * Log API request
   */
  logApiRequest(requestId: string, endpoint: string, method: string): void {
    this.log('INFO', 'API_REQUEST', {
      requestId,
      endpoint,
      method,
      timestamp: Date.now()
    });
  }

  /**
   * Log API response
   */
  logApiResponse(requestId: string, statusCode: number, responseTime: number): void {
    this.log('INFO', 'API_RESPONSE', {
      requestId,
      statusCode,
      responseTime,
      timestamp: Date.now()
    });
  }

  /**
   * Log image analysis
   */
  logImageAnalysis(requestId: string, data: {
    imageHash: string;
    processingTime: number;
    recommendationsCount: number;
    improvementScore: number;
  }): void {
    this.log('INFO', 'IMAGE_ANALYSIS', {
      requestId,
      imageHash: data.imageHash.substring(0, 10),
      processingTime: data.processingTime,
      recommendationsCount: data.recommendationsCount,
      improvementScore: data.improvementScore,
      timestamp: Date.now()
    });
  }

  /**
   * Log image generation
   */
  logImageGeneration(requestId: string, data: {
    prompt: string;
    fidelityLevel: string;
    tokens: number;
    cost: number;
  }): void {
    this.log('INFO', 'IMAGE_GENERATION', {
      requestId,
      promptLength: data.prompt.length,
      fidelityLevel: data.fidelityLevel,
      tokens: data.tokens,
      cost: data.cost,
      timestamp: Date.now()
    });
  }

  /**
   * Log error
   */
  logError(requestId: string, error: Error): void {
    this.log('ERROR', 'ERROR_OCCURRED', {
      requestId,
      errorMessage: error.message,
      errorStack: error.stack,
      timestamp: Date.now()
    });
  }

  /**
   * Log rate limit hit
   */
  logRateLimitHit(userId: string, limitType: string): void {
    this.log('WARN', 'RATE_LIMIT_HIT', {
      userId,
      limitType,
      timestamp: Date.now()
    });
  }

  /**
   * Log performance metrics
   */
  logPerformanceMetrics(requestId: string, metrics: {
    totalTime: number;
    analysisTime: number;
    generationTime: number;
    cacheHitRate: number;
    queueTime?: number;
  }): void {
    this.log('INFO', 'PERFORMANCE_METRICS', {
      requestId,
      ...metrics,
      timestamp: Date.now()
    });
  }

  /**
   * Log user action
   */
  logUserAction(userId: string, action: string, metadata?: any): void {
    this.log('INFO', 'USER_ACTION', {
      userId,
      action,
      metadata,
      timestamp: Date.now()
    });
  }

  /**
   * Log system health
   */
  logSystemHealth(health: ServiceHealth): void {
    this.log('INFO', 'SYSTEM_HEALTH', {
      ...health,
      timestamp: Date.now()
    });
  }

  /**
   * Get logs with filtering
   */
  getLogs(filter?: {
    level?: LogLevel;
    type?: string;
    requestId?: string;
    userId?: string;
    startTime?: number;
    endTime?: number;
    limit?: number;
  }): LogEntry[] {
    let filtered = this.logs;

    if (filter) {
      if (filter.level) {
        filtered = filtered.filter(log => log.level === filter.level);
      }
      if (filter.type) {
        filtered = filtered.filter(log => log.type === filter.type);
      }
      if (filter.requestId) {
        filtered = filtered.filter(log => log.data.requestId === filter.requestId);
      }
      if (filter.userId) {
        filtered = filtered.filter(log => log.data.userId === filter.userId);
      }
      if (filter.startTime) {
        filtered = filtered.filter(log => log.timestamp >= filter.startTime!);
      }
      if (filter.endTime) {
        filtered = filtered.filter(log => log.timestamp <= filter.endTime!);
      }
    }

    // Sort by timestamp (newest first)
    filtered.sort((a, b) => b.timestamp - a.timestamp);

    // Apply limit
    if (filter?.limit) {
      filtered = filtered.slice(0, filter.limit);
    }

    return filtered;
  }

  /**
   * Get analytics data
   */
  getAnalytics(timeRange: '1h' | '24h' | '7d' | '30d' = '24h'): {
    totalRequests: number;
    successRate: number;
    averageResponseTime: number;
    errorRate: number;
    cacheHitRate: number;
    popularEditingTypes: Array<{ type: string; count: number }>;
    peakHours: Array<{ hour: number; count: number }>;
    costAnalysis: {
      totalCost: number;
      averageCostPerRequest: number;
      costByType: Array<{ type: string; cost: number }>;
    };
  } {
    const now = Date.now();
    const timeRangeMs = this.getTimeRangeMs(timeRange);
    const startTime = now - timeRangeMs;

    const logsInRange = this.logs.filter(log => log.timestamp >= startTime);
    
    // Calculate metrics
    const totalRequests = logsInRange.filter(log => log.type === 'API_REQUEST').length;
    const successfulRequests = logsInRange.filter(log => 
      log.type === 'API_RESPONSE' && log.data.statusCode >= 200 && log.data.statusCode < 300
    ).length;
    const errorRequests = logsInRange.filter(log => log.level === 'ERROR').length;
    const cacheHits = logsInRange.filter(log => log.type === 'CACHE_HIT').length;
    const cacheMisses = logsInRange.filter(log => log.type === 'CACHE_MISS').length;

    const responseTimes = logsInRange
      .filter(log => log.type === 'API_RESPONSE')
      .map(log => log.data.responseTime || 0);

    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
      : 0;

    // Calculate popular editing types
    const editingTypes = logsInRange
      .filter(log => log.type === 'IMAGE_GENERATION')
      .map(log => log.data.fidelityLevel || 'unknown');

    const typeCount = editingTypes.reduce((acc, type) => {
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const popularEditingTypes = Object.entries(typeCount)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count);

    // Calculate peak hours
    const hourlyCount = logsInRange.reduce((acc, log) => {
      const hour = new Date(log.timestamp).getHours();
      acc[hour] = (acc[hour] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    const peakHours = Object.entries(hourlyCount)
      .map(([hour, count]) => ({ hour: parseInt(hour), count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Calculate cost analysis
    const costLogs = logsInRange.filter(log => log.type === 'IMAGE_GENERATION');
    const totalCost = costLogs.reduce((sum, log) => sum + (log.data.cost || 0), 0);
    const averageCostPerRequest = costLogs.length > 0 ? totalCost / costLogs.length : 0;

    const costByType = costLogs.reduce((acc, log) => {
      const type = log.data.fidelityLevel || 'unknown';
      acc[type] = (acc[type] || 0) + (log.data.cost || 0);
      return acc;
    }, {} as Record<string, number>);

    return {
      totalRequests,
      successRate: totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0,
      averageResponseTime,
      errorRate: totalRequests > 0 ? (errorRequests / totalRequests) * 100 : 0,
      cacheHitRate: (cacheHits + cacheMisses) > 0 ? (cacheHits / (cacheHits + cacheMisses)) * 100 : 0,
      popularEditingTypes,
      peakHours,
      costAnalysis: {
        totalCost,
        averageCostPerRequest,
        costByType: Object.entries(costByType).map(([type, cost]) => ({ type, cost }))
      }
    };
  }

  /**
   * Export logs to JSON
   */
  exportLogs(format: 'json' | 'csv' = 'json'): string {
    if (format === 'json') {
      return JSON.stringify(this.logs, null, 2);
    }

    // CSV format
    const headers = ['timestamp', 'level', 'type', 'requestId', 'data'];
    const rows = this.logs.map(log => [
      new Date(log.timestamp).toISOString(),
      log.level,
      log.type,
      log.data.requestId || '',
      JSON.stringify(log.data)
    ]);

    return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
  }

  /**
   * Clear logs
   */
  clearLogs(): void {
    this.logs = [];
    this.persistLogs();
  }

  /**
   * Private methods
   */
  private log(level: LogLevel, type: string, data: any): void {
    if (!this.shouldLog(level)) {
      return;
    }

    const entry: LogEntry = {
      timestamp: Date.now(),
      level,
      type,
      data
    };

    this.logs.push(entry);

    // Maintain max logs limit
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Persist logs
    this.persistLogs();

    // Console output for development
    if (import.meta.env.DEV) {
      const logMethod = level === 'ERROR' ? 'error' : level === 'WARN' ? 'warn' : 'log';
      console[logMethod](`[${level}] ${type}:`, data);
    }
  }

  private shouldLog(level: LogLevel): boolean {
    const levels = ['DEBUG', 'INFO', 'WARN', 'ERROR'];
    const currentIndex = levels.indexOf(this.logLevel);
    const messageIndex = levels.indexOf(level);
    
    return messageIndex >= currentIndex;
  }

  private getTimeRangeMs(timeRange: string): number {
    const ranges = {
      '1h': 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000
    };
    
    return ranges[timeRange as keyof typeof ranges] || ranges['24h'];
  }

  private persistLogs(): void {
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') return;

    try {
      // Only persist last 100 logs to avoid localStorage overflow
      const logsToStore = this.logs.slice(-100);
      localStorage.setItem('tinderop_logs', JSON.stringify(logsToStore));
    } catch (error) {
      console.warn('Failed to persist logs:', error);
    }
  }

  private loadPersistedLogs(): void {
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') return;

    try {
      const stored = localStorage.getItem('tinderop_logs');
      if (stored) {
        const logs = JSON.parse(stored);
        this.logs = logs;
      }
    } catch (error) {
      console.warn('Failed to load persisted logs:', error);
    }
  }

  private startHealthMonitoring(): void {
    // Load persisted logs on startup
    this.loadPersistedLogs();

    // Monitor health every 5 minutes
    setInterval(() => {
      this.checkSystemHealth();
    }, 5 * 60 * 1000);
  }

  private checkSystemHealth(): void {
    const recentLogs = this.logs.filter(log => 
      log.timestamp > Date.now() - (5 * 60 * 1000) // Last 5 minutes
    );

    const errorLogs = recentLogs.filter(log => log.level === 'ERROR');
    const totalRequests = recentLogs.filter(log => log.type === 'API_REQUEST').length;
    const successfulRequests = recentLogs.filter(log => 
      log.type === 'API_RESPONSE' && log.data.statusCode >= 200 && log.data.statusCode < 300
    ).length;

    const errorRate = totalRequests > 0 ? (errorLogs.length / totalRequests) * 100 : 0;
    const successRate = totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 100;

    // Calculate average response time
    const responseTimes = recentLogs
      .filter(log => log.type === 'API_RESPONSE')
      .map(log => log.data.responseTime || 0);

    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
      : 0;

    // Determine health status
    let status: 'healthy' | 'degraded' | 'down' = 'healthy';
    if (errorRate > 50 || successRate < 50) {
      status = 'down';
    } else if (errorRate > 10 || averageResponseTime > 10000) {
      status = 'degraded';
    }

    const health: ServiceHealth = {
      status,
      lastCheck: Date.now(),
      responseTime: averageResponseTime,
      errorRate,
      queueSize: 0, // Would be implemented with actual queue
      cacheHitRate: this.calculateCacheHitRate(),
      activeRequests: 0 // Would be tracked separately
    };

    this.logSystemHealth(health);
  }

  private calculateCacheHitRate(): number {
    const recentLogs = this.logs.filter(log => 
      log.timestamp > Date.now() - (60 * 60 * 1000) // Last hour
    );

    const cacheHits = recentLogs.filter(log => log.type === 'CACHE_HIT').length;
    const cacheMisses = recentLogs.filter(log => log.type === 'CACHE_MISS').length;
    const totalCacheRequests = cacheHits + cacheMisses;

    return totalCacheRequests > 0 ? (cacheHits / totalCacheRequests) * 100 : 0;
  }
}

type LogLevel = 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';

interface LogEntry {
  timestamp: number;
  level: LogLevel;
  type: string;
  data: any;
}

export const imageEditingLogger = new ImageEditingLogger();