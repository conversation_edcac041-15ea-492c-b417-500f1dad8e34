/**
 * Main image editing service with OpenAI GPT-Image-1 integration
 * Orchestrates analysis, recommendations, and image generation
 */

import { openai } from "@ai-sdk/openai";
import { generateText } from "ai";
import type { 
  EditingRequest, 
  EditingResponse, 
  EditingAnalysisResult, 
  EditingRecommendation,
  EditingProgress,
  EditingServiceConfig,
  ImageGenerationRequest,
  ImageGenerationResponse,
  AnalysisResult,
  EDITING_CATEGORIES 
} from "@/types/analysis";
import { EDITING_ANALYSIS_STEPS } from "@/types/analysis";
import { securityService } from "./security";
import { imageEditingCache } from "./cache";
import { rateLimiter } from "./rate-limiter";

export class ImageEditingService {
  private apiKey: string;
  private isProcessing = false;
  private currentUserId?: string;

  constructor() {
    // Get OpenAI API key from environment variables
    this.apiKey = import.meta.env.VITE_OPENAI_API_KEY;

    if (!this.apiKey) {
      console.error("🔑 VITE_OPENAI_API_KEY not found in environment variables");
      throw new Error(
        "OpenAI API key is required. Please set VITE_OPENAI_API_KEY in your .env file"
      );
    }

    console.log("🔑 OpenAI API key loaded successfully");
    console.log(`🔑 API Key preview: ${this.apiKey.substring(0, 10)}...`);
  }

  /**
   * Analyze image for editing opportunities
   */
  async analyzeImageForEditing(
    request: EditingRequest,
    config: EditingServiceConfig = {}
  ): Promise<EditingAnalysisResult> {
    if (this.isProcessing) {
      throw new Error("Analysis already in progress");
    }

    this.isProcessing = true;
    const startTime = Date.now();

    try {
      // Generate cache key
      const cacheKey = imageEditingCache.generateCacheKey(
        request.imageData,
        request.preferences
      );

      // Check cache first
      const cachedResult = await imageEditingCache.get(cacheKey);
      if (cachedResult) {
        console.log("🎯 Cache hit for editing analysis");
        config.onComplete?.(cachedResult);
        return cachedResult;
      }

      // Security validation
      const securityResult = await securityService.validateBase64Image(
        request.imageData,
        request.fileName
      );

      if (!securityResult.valid) {
        throw new Error(`Security validation failed: ${securityResult.errors.join(", ")}`);
      }

      // Check rate limits
      const userId = this.currentUserId || "anonymous";
      const rateLimitResult = await rateLimiter.checkAnalysisLimit(userId);
      if (!rateLimitResult.allowed) {
        throw new Error(`Rate limit exceeded: ${rateLimitResult.reason}`);
      }

      // Initialize result
      const result: EditingAnalysisResult = {
        fileName: request.fileName,
        originalImage: request.imageData,
        canEdit: true,
        editingRecommendations: [],
        quickFixes: [],
        transformativeEdits: [],
        totalRecommendations: 0,
        estimatedTotalCost: 0,
        processingTime: 0,
        confidence: 0,
        processed: false,
      };

      // Execute editing analysis steps
      const recommendations = await this.executeEditingAnalysis(
        request,
        (stepId, stepName, progress) => {
          const progressData: EditingProgress = {
            fileName: request.fileName,
            currentStep: stepId,
            totalSteps: EDITING_ANALYSIS_STEPS.length,
            stepName,
            progress,
            estimatedTimeRemaining: this.estimateTimeRemaining(progress, startTime)
          };
          config.onProgress?.(progressData);
        }
      );

      // Categorize recommendations
      const categorizedRecommendations = this.categorizeRecommendations(recommendations);
      result.editingRecommendations = categorizedRecommendations.all;
      result.quickFixes = categorizedRecommendations.quickFixes;
      result.transformativeEdits = categorizedRecommendations.transformative;
      result.totalRecommendations = recommendations.length;

      // Calculate costs and confidence
      result.estimatedTotalCost = this.calculateTotalCost(recommendations);
      result.confidence = this.calculateOverallConfidence(recommendations);
      result.processingTime = Date.now() - startTime;
      result.processed = true;

      // Cache the result
      await imageEditingCache.set(cacheKey, result);

      // Record successful request
      rateLimiter.recordAnalysisRequest(userId, result.processingTime);

      config.onComplete?.(result);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.error("❌ Image editing analysis failed:", error);
      
      // Record failed request
      if (this.currentUserId) {
        rateLimiter.recordFailedRequest(this.currentUserId, errorMessage);
      }

      config.onError?.(errorMessage);

      return {
        fileName: request.fileName,
        originalImage: request.imageData,
        canEdit: false,
        editingRecommendations: [],
        quickFixes: [],
        transformativeEdits: [],
        totalRecommendations: 0,
        estimatedTotalCost: 0,
        processingTime: Date.now() - startTime,
        confidence: 0,
        processed: false,
        error: errorMessage,
      };
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Generate edited image based on recommendation
   */
  async generateEditedImage(
    request: ImageGenerationRequest,
    userId: string = "anonymous"
  ): Promise<ImageGenerationResponse> {
    try {
      const startTime = Date.now();

      // Estimate cost
      const estimatedCost = this.estimateGenerationCost(request);

      // Check rate limits
      const rateLimitResult = await rateLimiter.checkGenerationLimit(
        userId,
        estimatedCost
      );

      if (!rateLimitResult.allowed) {
        throw new Error(`Rate limit exceeded: ${rateLimitResult.reason}`);
      }

      // Generate editing prompt
      const editingPrompt = this.generateEditingPrompt(request.recommendation);

      // Call OpenAI GPT-Image-1 API
      const editedImage = await this.callOpenAIImageAPI(
        request.originalImage,
        editingPrompt,
        request.quality,
        request.inputFidelity
      );

      const processingTime = Date.now() - startTime;
      const actualCost = this.calculateActualCost(request, processingTime);

      // Record successful generation
      rateLimiter.recordGenerationRequest(userId, actualCost, processingTime);

      return {
        success: true,
        editedImage,
        cost: actualCost,
        processingTime,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.error("❌ Image generation failed:", error);
      
      // Record failed request
      rateLimiter.recordFailedRequest(userId, errorMessage);

      return {
        success: false,
        cost: 0,
        processingTime: Date.now() - Date.now(),
        error: errorMessage,
      };
    }
  }

  /**
   * Execute editing analysis steps
   */
  private async executeEditingAnalysis(
    request: EditingRequest,
    onProgress?: (step: number, stepName: string, progress: number) => void
  ): Promise<EditingRecommendation[]> {
    const recommendations: EditingRecommendation[] = [];
    const steps = EDITING_ANALYSIS_STEPS;

    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];
      onProgress?.(step.id, step.name, (i / steps.length) * 100);

      const startTime = Date.now();

      try {
        console.log(`🔍 Starting editing analysis step ${step.id}: ${step.name}`);
        const stepRecommendations = await this.executeEditingStep(
          step.id,
          step.name,
          request
        );

        recommendations.push(...stepRecommendations);

        const processingTime = Date.now() - startTime;
        console.log(`📊 Editing Step ${step.id} (${step.name}):`, {
          recommendations: stepRecommendations.length,
          processingTime: `${processingTime}ms`
        });

        onProgress?.(step.id, step.name, ((i + 1) / steps.length) * 100);
      } catch (error) {
        console.error(`❌ Error in step ${step.id} (${step.name}):`, error);
        // Continue with other steps even if one fails
      }
    }

    return recommendations;
  }

  /**
   * Execute individual editing analysis step
   */
  private async executeEditingStep(
    stepId: number,
    stepName: string,
    request: EditingRequest
  ): Promise<EditingRecommendation[]> {
    const prompt = this.getEditingStepPrompt(stepId, request);

    console.log(`🤖 Calling OpenAI API for editing step ${stepId}`);

    const { text } = await generateText({
      model: openai("gpt-4o"),
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: prompt,
            },
            {
              type: "image",
              image: request.imageData,
            },
          ],
        },
      ],
      maxTokens: 2000,
      temperature: 0.3,
    });

    return this.parseEditingRecommendations(text, stepId);
  }

  /**
   * Generate editing step prompt
   */
  private getEditingStepPrompt(stepId: number, request: EditingRequest): string {
    const baseInstruction = `You are an expert photo editor specializing in dating profile optimization. Analyze this image and provide specific, actionable editing recommendations that will improve its dating appeal while maintaining the subject's natural appearance.

CRITICAL REQUIREMENTS:
- Never suggest changing how the subject looks (no facial alterations, body modifications)
- Focus on: lighting, composition, background, clothing, pose, camera angle, colors, style
- Provide specific, actionable editing instructions
- Estimate impact score (1-10) and difficulty level
- Consider cost-effectiveness

Response format (JSON):
{
  "recommendations": [
    {
      "type": "lighting|background|composition|color|style|clothing|expression|posture",
      "category": "quick_fix|advanced_edit|style_enhancement|composition_improvement",
      "title": "Short descriptive title",
      "description": "Detailed description of what to change",
      "impactScore": 1-10,
      "difficulty": "easy|medium|hard",
      "estimatedTime": minutes,
      "instructions": ["Step 1", "Step 2", "Step 3"],
      "toolsRecommended": ["Suggested editing app/tool"],
      "estimatedCost": 0.05-2.00,
      "confidence": 0-100
    }
  ]
}`;

    const stepPrompts = {
      1: `${baseInstruction}

STEP 1: TECHNICAL ASSESSMENT & QUICK FIXES
Analyze technical quality and identify immediate improvements:
- Image sharpness and clarity issues
- Basic lighting corrections (exposure, shadows, highlights)
- Color balance and saturation adjustments
- Cropping and framing improvements
- Basic noise reduction or enhancement needs

Focus on: Quick, low-cost fixes that provide immediate visual improvement.`,

      2: `${baseInstruction}

STEP 2: LIGHTING OPTIMIZATION
Analyze lighting conditions and improvement opportunities:
- Harsh shadows or unflattering lighting angles
- Exposure issues (overexposed/underexposed areas)
- Color temperature problems (too warm/cool)
- Lighting direction and quality
- Background lighting vs subject lighting balance

Focus on: Lighting adjustments that enhance the subject's appearance without changing facial features.`,

      3: `${baseInstruction}

STEP 3: COMPOSITION & FRAMING
Analyze composition and suggest improvements:
- Rule of thirds application
- Background elements and distractions
- Framing and crop optimization
- Angle and perspective improvements
- Visual balance and focal points

Focus on: Repositioning, cropping, and angle adjustments that improve visual appeal.`,

      4: `${baseInstruction}

STEP 4: COLOR & STYLE ENHANCEMENT
Analyze color palette and style opportunities:
- Color grading and tone adjustments
- Clothing color optimization
- Background color harmony
- Seasonal and style appropriateness
- Filter and preset applications

Focus on: Color and style improvements that enhance overall aesthetic appeal.`,

      5: `${baseInstruction}

STEP 5: ADVANCED EDITING OPPORTUNITIES
Identify sophisticated editing possibilities:
- Background replacement or enhancement
- Advanced lighting techniques
- Professional-grade color grading
- Artistic style applications
- Premium editing techniques

Focus on: High-impact professional edits that justify their cost.`,
    };

    return stepPrompts[stepId as keyof typeof stepPrompts] || stepPrompts[1];
  }

  /**
   * Parse editing recommendations from AI response
   */
  private parseEditingRecommendations(
    response: string,
    stepId: number
  ): EditingRecommendation[] {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error("No JSON found in response");
      }

      const parsed = JSON.parse(jsonMatch[0]);
      const recommendations = Array.isArray(parsed.recommendations) 
        ? parsed.recommendations 
        : [];

      return recommendations.map((rec: any, index: number) => ({
        id: `${stepId}-${index}`,
        type: rec.type || 'enhancement',
        category: rec.category || 'quick_fix',
        title: rec.title || 'Photo Enhancement',
        description: rec.description || 'Improve photo quality',
        impactScore: Math.max(1, Math.min(10, parseInt(rec.impactScore) || 5)),
        difficulty: rec.difficulty || 'medium',
        estimatedTime: parseInt(rec.estimatedTime) || 5,
        instructions: Array.isArray(rec.instructions) 
          ? rec.instructions.slice(0, 5) 
          : ['Apply enhancement'],
        toolsRecommended: Array.isArray(rec.toolsRecommended) 
          ? rec.toolsRecommended.slice(0, 3) 
          : ['Photo editing app'],
        estimatedCost: Math.max(0.05, Math.min(2.00, parseFloat(rec.estimatedCost) || 0.25)),
        confidence: Math.max(0, Math.min(100, parseInt(rec.confidence) || 80)),
        applied: false,
        dismissed: false,
        createdAt: new Date(),
      }));
    } catch (error) {
      console.error("🔧 Failed to parse editing recommendations:", error);
      console.log("📝 Raw response:", response);
      return [];
    }
  }

  /**
   * Categorize recommendations by type
   */
  private categorizeRecommendations(
    recommendations: EditingRecommendation[]
  ): { all: EditingRecommendation[]; quickFixes: EditingRecommendation[]; transformative: EditingRecommendation[] } {
    const quickFixes = recommendations.filter(
      rec => rec.category === 'quick_fix' && rec.estimatedCost <= 0.50
    );

    const transformative = recommendations.filter(
      rec => rec.category === 'advanced_edit' && rec.impactScore >= 7
    );

    return {
      all: recommendations,
      quickFixes,
      transformative,
    };
  }

  /**
   * Calculate total estimated cost
   */
  private calculateTotalCost(recommendations: EditingRecommendation[]): number {
    return recommendations.reduce((total, rec) => total + rec.estimatedCost, 0);
  }

  /**
   * Calculate overall confidence
   */
  private calculateOverallConfidence(recommendations: EditingRecommendation[]): number {
    if (recommendations.length === 0) return 0;
    
    const totalConfidence = recommendations.reduce((sum, rec) => sum + rec.confidence, 0);
    return Math.round(totalConfidence / recommendations.length);
  }

  /**
   * Generate editing prompt for OpenAI image generation
   */
  private generateEditingPrompt(recommendation: EditingRecommendation): string {
    const basePrompt = `Apply professional photo editing to improve this dating profile image. `;
    
    const typePrompts = {
      lighting: `Enhance lighting to be more flattering and natural. ${recommendation.description}`,
      background: `Improve or replace the background. ${recommendation.description}`,
      composition: `Optimize composition and framing. ${recommendation.description}`,
      color: `Adjust colors for better aesthetic appeal. ${recommendation.description}`,
      style: `Apply style enhancements. ${recommendation.description}`,
      clothing: `Optimize clothing appearance. ${recommendation.description}`,
      expression: `Enhance expression and mood. ${recommendation.description}`,
      posture: `Improve posture and body language. ${recommendation.description}`,
      enhancement: `Apply general enhancements. ${recommendation.description}`,
      object_removal: `Remove unwanted objects or distractions. ${recommendation.description}`,
    };

    const typePrompt = typePrompts[recommendation.type] || typePrompts.enhancement;
    
    return `${basePrompt}${typePrompt} Maintain the subject's natural appearance while improving the overall quality and appeal of the image.`;
  }

  /**
   * Call OpenAI GPT-Image-1 API for image editing
   */
  private async callOpenAIImageAPI(
    originalImage: string,
    prompt: string,
    quality: 'standard' | 'high' | 'ultra',
    inputFidelity: 'standard' | 'high'
  ): Promise<string> {
    // This is a placeholder for the actual OpenAI GPT-Image-1 API call
    // In a real implementation, you would use the OpenAI images API
    // For now, we'll simulate the response
    
    console.log("🎨 Calling OpenAI GPT-Image-1 API", {
      prompt: prompt.substring(0, 100) + "...",
      quality,
      inputFidelity
    });

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Return the original image for now (in real implementation, this would be the edited image)
    return originalImage;
  }

  /**
   * Estimate generation cost
   */
  private estimateGenerationCost(request: ImageGenerationRequest): number {
    const baseCosts = {
      standard: 0.04,
      high: 0.17,
      ultra: 0.34
    };

    const fidelityCosts = {
      standard: 0,
      high: 0.062
    };

    return baseCosts[request.quality] + fidelityCosts[request.inputFidelity];
  }

  /**
   * Calculate actual cost after generation
   */
  private calculateActualCost(request: ImageGenerationRequest, processingTime: number): number {
    const baseCost = this.estimateGenerationCost(request);
    
    // Add processing time factor (longer processing = higher cost)
    const timeMultiplier = Math.max(1, processingTime / 10000); // 10 seconds baseline
    
    return Math.round(baseCost * timeMultiplier * 100) / 100;
  }

  /**
   * Estimate time remaining for progress tracking
   */
  private estimateTimeRemaining(progress: number, startTime: number): number {
    if (progress === 0) return 0;
    
    const elapsed = Date.now() - startTime;
    const estimatedTotal = elapsed / (progress / 100);
    const remaining = estimatedTotal - elapsed;
    
    return Math.max(0, Math.round(remaining / 1000)); // Return in seconds
  }

  /**
   * Set current user ID for rate limiting
   */
  setUserId(userId: string): void {
    this.currentUserId = userId;
  }

  /**
   * Check if currently processing
   */
  isCurrentlyProcessing(): boolean {
    return this.isProcessing;
  }

  /**
   * Get service statistics
   */
  getServiceStats() {
    return {
      cache: imageEditingCache.getStats(),
      rateLimiter: rateLimiter.getGlobalStats(),
      isProcessing: this.isProcessing,
      apiKeyConfigured: !!this.apiKey,
    };
  }
}

// Singleton instance
export const imageEditingService = new ImageEditingService();