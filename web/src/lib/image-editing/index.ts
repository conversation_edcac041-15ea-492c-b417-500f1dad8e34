// Main entry point for image editing service
export { ImageEditingService, imageEditingService } from './image-editing-service';
export { ImageEditingCache } from './cache';
export { RateLimiter, rateLimiter } from './rate-limiter';
export { ImageEditingErrorHandler, imageEditingErrorHandler } from './error-handler';
export { ImageEditingLogger, imageEditingLogger } from './logger';
export { SecurityService, securityService } from './security';
export { ImageEditingQueueManager, imageEditingQueueManager } from './queue-manager';

// Export types
export type {
  ImageEditingRequest,
  ImageEditingResult,
  ImageEditingProgress,
  EditingRecommendation,
  EditingType,
  BatchEditingRequest,
  BatchEditingResult,
  UsageMetrics,
  ServiceHealth
} from './types';

// Export hooks for React integration
export { useImageEditing } from './hooks/use-image-editing';
export { useImageEditingQueue } from './hooks/use-image-editing-queue';
export { useImageEditingAnalytics } from './hooks/use-image-editing-analytics';