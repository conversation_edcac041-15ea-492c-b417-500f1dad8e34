export interface AnalysisStep {
  id: number;
  name: string;
  description: string;
  status: "pending" | "processing" | "completed" | "error";
  score?: number;
  insights?: string[];
  confidence?: number;
}

export interface StepResult {
  stepId: number;
  stepName: string;
  score: number;
  insights: string[];
  confidence: number;
  processingTime: number;
}

export interface AnalysisResult {
  fileName: string;
  preview: string;
  overallScore: number;
  steps: StepResult[];
  recommendations: string[];
  processed: boolean;
  error?: string;
}

export interface AnalysisProgress {
  fileName: string;
  currentStep: number;
  totalSteps: number;
  stepName: string;
  progress: number;
}

export interface ImageAnalysisRequest {
  fileName: string;
  imageData: string; // base64 encoded image
  mimeType: string;
}

export interface ImageAnalysisResponse {
  success: boolean;
  result?: AnalysisResult;
  error?: string;
  progress?: AnalysisProgress;
}

export interface StreamingAnalysisEvent {
  type: "progress" | "step-complete" | "analysis-complete" | "error";
  data: AnalysisProgress | StepResult | AnalysisResult | { error: string };
}

export const ANALYSIS_STEPS = [
  {
    id: 1,
    name: "Technical Quality",
    description: "Analyzing photo quality, lighting, and composition",
  },
  {
    id: 2,
    name: "Facial Analysis",
    description: "Evaluating facial features, expressions, and attractiveness",
  },
  {
    id: 3,
    name: "Physical Analysis",
    description: "Assessing body language, posture, and physical presentation",
  },
  {
    id: 4,
    name: "Style & Presentation",
    description: "Assessing clothing, setting, and overall presentation",
  },
  {
    id: 5,
    name: "Dating Profile Optimization",
    description: "Analyzing dating-specific appeal and suitability",
  },
  {
    id: 6,
    name: "Final Recommendations",
    description: "Synthesizing insights into actionable advice",
  },
] as const;

export const BIO_ANALYSIS_STEPS = [
  {
    id: 1,
    name: "Writing Quality",
    description: "Analyzing grammar, readability, and writing style",
  },
  {
    id: 2,
    name: "Personality Appeal",
    description: "Evaluating personality traits and attractiveness signals",
  },
  {
    id: 3,
    name: "Interest Analysis",
    description: "Assessing hobbies, interests, and lifestyle appeal",
  },
  {
    id: 4,
    name: "Dating Intent",
    description: "Analyzing clarity of dating goals and relationship signals",
  },
  {
    id: 5,
    name: "Engagement Factor",
    description: "Evaluating conversation starters and match appeal",
  },
] as const;

export interface BioAnalysisResult {
  originalBio: string;
  overallScore: number;
  steps: StepResult[];
  recommendations: string[];
  improvedBio?: string;
  processed: boolean;
  error?: string;
}

export interface BioAnalysisProgress {
  currentStep: number;
  totalSteps: number;
  stepName: string;
  progress: number;
}

// Image Editing Recommendation Types
export interface EditingRecommendation {
  id: string;
  type: 'lighting' | 'background' | 'composition' | 'color' | 'style' | 'object_removal' | 'enhancement' | 'clothing' | 'expression' | 'posture';
  category: 'quick_fix' | 'advanced_edit' | 'style_enhancement' | 'composition_improvement';
  title: string;
  description: string;
  impactScore: number; // 1-10 scale of potential improvement
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedTime: number; // in minutes
  instructions: string[];
  beforePreview?: string; // base64 image
  afterPreview?: string; // base64 image
  toolsRecommended: string[];
  estimatedCost: number; // in USD
  confidence: number; // AI confidence 0-100
  applied: boolean;
  dismissed: boolean;
  createdAt: Date;
}

export interface EditingAnalysisResult {
  fileName: string;
  originalImage: string; // base64
  canEdit: boolean;
  editingRecommendations: EditingRecommendation[];
  quickFixes: EditingRecommendation[];
  transformativeEdits: EditingRecommendation[];
  totalRecommendations: number;
  estimatedTotalCost: number;
  processingTime: number;
  confidence: number;
  processed: boolean;
  error?: string;
}

export interface EditingProgress {
  fileName: string;
  currentStep: number;
  totalSteps: number;
  stepName: string;
  progress: number;
  estimatedTimeRemaining?: number;
}

export interface EditingRequest {
  fileName: string;
  imageData: string; // base64 encoded image
  mimeType: string;
  analysisResults?: AnalysisResult; // Optional existing analysis results
  preferences?: {
    conservative: boolean; // Less dramatic changes
    style: 'natural' | 'enhanced' | 'professional' | 'artistic';
    maxBudget?: number; // Maximum cost in USD
    priorityTypes?: EditingRecommendation['type'][];
  };
}

export interface EditingResponse {
  success: boolean;
  result?: EditingAnalysisResult;
  error?: string;
  progress?: EditingProgress;
}

export interface EditingServiceConfig {
  onProgress?: (progress: EditingProgress) => void;
  onRecommendationComplete?: (recommendation: EditingRecommendation) => void;
  onComplete?: (result: EditingAnalysisResult) => void;
  onError?: (error: string) => void;
}

export interface ImageGenerationRequest {
  originalImage: string; // base64
  recommendation: EditingRecommendation;
  quality: 'standard' | 'high' | 'ultra';
  inputFidelity: 'standard' | 'high';
}

export interface ImageGenerationResponse {
  success: boolean;
  editedImage?: string; // base64
  cost: number;
  processingTime: number;
  error?: string;
}

export interface EditingCache {
  key: string;
  result: EditingAnalysisResult;
  timestamp: Date;
  ttl: number; // Time to live in milliseconds
}

export interface EditingUsageStats {
  userId: string;
  analysisCount: number;
  generationCount: number;
  totalCost: number;
  lastUsed: Date;
  dailyLimits: {
    analysis: number;
    generation: number;
    cost: number;
  };
}

export const EDITING_ANALYSIS_STEPS = [
  {
    id: 1,
    name: "Technical Assessment",
    description: "Analyzing image quality and editing potential",
  },
  {
    id: 2,
    name: "Lighting Analysis",
    description: "Evaluating lighting conditions and improvement opportunities",
  },
  {
    id: 3,
    name: "Composition Review",
    description: "Analyzing framing, angles, and compositional elements",
  },
  {
    id: 4,
    name: "Style & Color",
    description: "Assessing color balance, saturation, and style opportunities",
  },
  {
    id: 5,
    name: "Enhancement Opportunities",
    description: "Identifying potential improvements and editing recommendations",
  },
] as const;

export const EDITING_CATEGORIES = {
  quick_fix: {
    name: "Quick Fixes",
    description: "Simple adjustments that can be made easily",
    maxCost: 0.50,
    examples: ["Brightness adjustment", "Contrast enhancement", "Color correction"]
  },
  advanced_edit: {
    name: "Advanced Edits",
    description: "More complex modifications requiring AI processing",
    maxCost: 2.00,
    examples: ["Background replacement", "Object removal", "Facial enhancement"]
  },
  style_enhancement: {
    name: "Style Enhancement",
    description: "Artistic and style improvements",
    maxCost: 1.50,
    examples: ["Filter application", "Artistic effects", "Style transfer"]
  },
  composition_improvement: {
    name: "Composition Improvement",
    description: "Framing and compositional adjustments",
    maxCost: 1.00,
    examples: ["Crop optimization", "Angle adjustment", "Focus enhancement"]
  }
} as const;
