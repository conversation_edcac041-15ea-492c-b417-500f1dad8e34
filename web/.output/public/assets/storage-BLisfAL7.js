import{createLucideIcon as e}from"./createLucideIcon-JB7IMeGf.js";const t=e(`X`,[[`path`,{d:`M18 6 6 18`,key:`1bl5f8`}],[`path`,{d:`m6 6 12 12`,key:`d8bk6v`}]]),n=`TinderOpImageDB`,r=1,i=`images`,a=`tinderop_session`;var o=class{db=null;async init(){return new Promise((e,t)=>{let a=indexedDB.open(n,r);a.onerror=()=>t(a.error),a.onsuccess=()=>{this.db=a.result,e()},a.onupgradeneeded=e=>{let t=e.target.result;if(!t.objectStoreNames.contains(i)){let e=t.createObjectStore(i,{keyPath:`id`});e.createIndex(`sessionId`,`sessionId`,{unique:!1}),e.createIndex(`uploadedAt`,`uploadedAt`,{unique:!1})}}})}async storeImage(e,t){this.db||await this.init();let n=`${t}_${Date.now()}_${Math.random().toString(36).substring(2,11)}`,r={id:n,fileName:e.name,blob:e,mimeType:e.type,uploadedAt:Date.now(),sessionId:t};return new Promise((e,t)=>{let a=this.db.transaction([i],`readwrite`),o=a.objectStore(i),s=o.add(r);s.onsuccess=()=>e(n),s.onerror=()=>t(s.error)})}async getImage(e){return this.db||await this.init(),new Promise((t,n)=>{let r=this.db.transaction([i],`readonly`),a=r.objectStore(i),o=a.get(e);o.onsuccess=()=>t(o.result||null),o.onerror=()=>n(o.error)})}async getSessionImages(e){return this.db||await this.init(),new Promise((t,n)=>{let r=this.db.transaction([i],`readonly`),a=r.objectStore(i),o=a.index(`sessionId`),s=o.getAll(e);s.onsuccess=()=>t(s.result),s.onerror=()=>n(s.error)})}async deleteImage(e){return this.db||await this.init(),new Promise((t,n)=>{let r=this.db.transaction([i],`readwrite`),a=r.objectStore(i),o=a.delete(e);o.onsuccess=()=>t(),o.onerror=()=>n(o.error)})}async clearSession(e){this.db||await this.init();let t=await this.getSessionImages(e),n=t.map(e=>this.deleteImage(e.id));await Promise.all(n)}async cleanup(){this.db||await this.init();let e=Date.now()-24*60*60*1e3;return new Promise((t,n)=>{let r=this.db.transaction([i],`readwrite`),a=r.objectStore(i),o=a.index(`uploadedAt`),s=o.openCursor(IDBKeyRange.upperBound(e));s.onsuccess=e=>{let n=e.target.result;n?(n.delete(),n.continue()):t()},s.onerror=()=>n(s.error)})}},s=class e{static instance;currentSession=null;static getInstance(){return e.instance||=new e,e.instance}getCurrentSession(){if(this.currentSession)return this.currentSession;let e=localStorage.getItem(a);if(e)try{return this.currentSession=JSON.parse(e),this.currentSession}catch{console.warn(`Failed to parse stored session, creating new one`)}return this.currentSession={id:`session_${Date.now()}_${Math.random().toString(36).substring(2,11)}`,createdAt:Date.now(),imageIds:[],results:{}},this.saveSession(),this.currentSession}addImageToSession(e){let t=this.getCurrentSession();t.imageIds.includes(e)||(t.imageIds.push(e),this.saveSession())}removeImageFromSession(e){let t=this.getCurrentSession();t.imageIds=t.imageIds.filter(t=>t!==e),delete t.results[e],this.saveSession()}saveAnalysisResult(e,t){let n=this.getCurrentSession();n.results[e]=t,this.saveSession()}getAnalysisResult(e){let t=this.getCurrentSession();return t.results[e]}clearSession(){localStorage.removeItem(a),this.currentSession=null}saveSession(){this.currentSession&&localStorage.setItem(a,JSON.stringify(this.currentSession))}};const c=new o,l=s.getInstance();async function u(e){return new Promise((t,n)=>{let r=new FileReader;r.onload=()=>{let e=r.result;t(e.split(`,`)[1])},r.onerror=n,r.readAsDataURL(e)})}function d(e){return URL.createObjectURL(e)}function f(e){URL.revokeObjectURL(e)}async function p(){await c.init(),await c.cleanup()}export{t as X,u as convertBlobToBase64,d as createImagePreview,c as imageStorage,p as initStorage,f as revokeImagePreview,l as sessionManager};