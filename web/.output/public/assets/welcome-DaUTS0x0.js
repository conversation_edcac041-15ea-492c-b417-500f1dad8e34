import{SignIn as e,SignUp as t}from"./uiComponents-BgWMuAlY.js";import{Link as n,SignedIn as r,SignedOut as i,__toESM as a,require_jsx_runtime as o,require_react as s}from"./main-B4G73TvM.js";import{Card as c,CardContent as l,CardDescription as u,CardHeader as d,CardTitle as f}from"./card-Bzfaa5B9.js";import{Button as p}from"./createLucideIcon-JB7IMeGf.js";import{ArrowLeft as m}from"./arrow-left-CrPLcqAC.js";import{Heart as h,Star as g}from"./star-Dm2nqXPr.js";import{Sparkles as _}from"./sparkles-BbQK6jEu.js";var v=a(s()),y=a(o());function b({onSignIn:e,onSignUp:t}){return(0,y.jsxs)(c,{className:`shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm`,children:[(0,y.jsxs)(d,{className:`text-center pb-6`,children:[(0,y.jsx)(`div`,{className:`flex justify-center mb-4`,children:(0,y.jsxs)(`div`,{className:`relative`,children:[(0,y.jsx)(h,{className:`w-12 h-12 text-flame-red fill-current`}),(0,y.jsx)(_,{className:`w-4 h-4 text-sparks-pink absolute -top-1 -right-1`})]})}),(0,y.jsx)(f,{className:`text-h2-mobile md:text-h2 text-graphite-90`,children:`Welcome to TinderOP`}),(0,y.jsx)(u,{className:`text-body-md text-graphite-60`,children:`The AI-powered dating profile optimizer that helps you get more matches`})]}),(0,y.jsxs)(l,{className:`space-y-6`,children:[(0,y.jsxs)(`div`,{className:`flex flex-col space-y-4`,children:[(0,y.jsxs)(`div`,{className:`flex items-center space-x-3 text-sm text-graphite-60`,children:[(0,y.jsx)(g,{className:`w-4 h-4 text-sparks-pink`}),(0,y.jsx)(`span`,{children:`AI-powered photo analysis`})]}),(0,y.jsxs)(`div`,{className:`flex items-center space-x-3 text-sm text-graphite-60`,children:[(0,y.jsx)(_,{className:`w-4 h-4 text-sparks-pink`}),(0,y.jsx)(`span`,{children:`Bio optimization suggestions`})]}),(0,y.jsxs)(`div`,{className:`flex items-center space-x-3 text-sm text-graphite-60`,children:[(0,y.jsx)(h,{className:`w-4 h-4 text-sparks-pink`}),(0,y.jsx)(`span`,{children:`Increase your match rate`})]})]}),(0,y.jsxs)(`div`,{className:`space-y-4`,children:[(0,y.jsxs)(`div`,{className:`relative`,children:[(0,y.jsx)(`div`,{className:`absolute inset-0 flex items-center`,children:(0,y.jsx)(`div`,{className:`w-full border-t border-graphite-20`})}),(0,y.jsx)(`div`,{className:`relative flex justify-center text-sm`,children:(0,y.jsx)(`span`,{className:`px-2 bg-cloud-white text-graphite-60`,children:`Get started`})})]}),(0,y.jsxs)(`div`,{className:`grid grid-cols-2 gap-3`,children:[(0,y.jsx)(p,{variant:`primary`,className:`w-full`,onClick:t,children:`Sign Up`}),(0,y.jsx)(p,{variant:`secondary`,className:`w-full`,onClick:e,children:`Sign In`})]})]})]})]})}function x({onBack:t}){return(0,y.jsxs)(c,{className:`shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm`,children:[(0,y.jsxs)(d,{className:`text-center pb-6`,children:[(0,y.jsxs)(`div`,{className:`flex items-center justify-between mb-4`,children:[(0,y.jsx)(p,{variant:`ghost`,size:`icon`,onClick:t,className:`text-graphite-60 hover:text-graphite-90`,children:(0,y.jsx)(m,{className:`w-4 h-4`})}),(0,y.jsx)(`div`,{className:`flex-1 flex justify-center`,children:(0,y.jsx)(h,{className:`w-8 h-8 text-flame-red fill-current`})}),(0,y.jsx)(`div`,{className:`w-10`})]}),(0,y.jsx)(f,{className:`text-h2-mobile md:text-h2 text-graphite-90`,children:`Sign In`}),(0,y.jsx)(u,{className:`text-body-md text-graphite-60`,children:`Welcome back! Sign in to continue optimizing your profile`})]}),(0,y.jsx)(l,{children:(0,y.jsx)(`div`,{className:`flex justify-center`,children:(0,y.jsx)(e,{routing:`hash`,signUpUrl:`/welcome`,afterSignInUrl:`/dashboard`,appearance:{elements:{rootBox:`w-full`,card:`shadow-none border-0 bg-transparent`,headerTitle:`hidden`,headerSubtitle:`hidden`,socialButtonsBlockButton:`bg-gradient-primary text-cloud-white hover:bg-gradient-primary/90`,formButtonPrimary:`bg-gradient-primary text-cloud-white hover:bg-gradient-primary/90`,footerActionLink:`text-flame-red hover:text-flame-red/80`}}})})})]})}function S({onBack:e}){return(0,y.jsxs)(c,{className:`shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm`,children:[(0,y.jsxs)(d,{className:`text-center pb-6`,children:[(0,y.jsxs)(`div`,{className:`flex items-center justify-between mb-4`,children:[(0,y.jsx)(p,{variant:`ghost`,size:`icon`,onClick:e,className:`text-graphite-60 hover:text-graphite-90`,children:(0,y.jsx)(m,{className:`w-4 h-4`})}),(0,y.jsx)(`div`,{className:`flex-1 flex justify-center`,children:(0,y.jsx)(h,{className:`w-8 h-8 text-flame-red fill-current`})}),(0,y.jsx)(`div`,{className:`w-10`})]}),(0,y.jsx)(f,{className:`text-h2-mobile md:text-h2 text-graphite-90`,children:`Sign Up`}),(0,y.jsx)(u,{className:`text-body-md text-graphite-60`,children:`Create your account and start getting better matches today`})]}),(0,y.jsx)(l,{children:(0,y.jsx)(`div`,{className:`flex justify-center`,children:(0,y.jsx)(t,{routing:`hash`,signInUrl:`/welcome`,afterSignUpUrl:`/dashboard`,appearance:{elements:{rootBox:`w-full`,card:`shadow-none border-0 bg-transparent`,headerTitle:`hidden`,headerSubtitle:`hidden`,socialButtonsBlockButton:`bg-gradient-primary text-cloud-white hover:bg-gradient-primary/90`,formButtonPrimary:`bg-gradient-primary text-cloud-white hover:bg-gradient-primary/90`,footerActionLink:`text-flame-red hover:text-flame-red/80`}}})})})]})}function C(){return(0,y.jsxs)(c,{className:`shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm`,children:[(0,y.jsxs)(d,{className:`text-center pb-6`,children:[(0,y.jsx)(`div`,{className:`flex justify-center mb-4`,children:(0,y.jsxs)(`div`,{className:`relative`,children:[(0,y.jsx)(h,{className:`w-12 h-12 text-flame-red fill-current`}),(0,y.jsx)(_,{className:`w-4 h-4 text-sparks-pink absolute -top-1 -right-1`})]})}),(0,y.jsx)(f,{className:`text-h2-mobile md:text-h2 text-graphite-90`,children:`Welcome back!`}),(0,y.jsx)(u,{className:`text-body-md text-graphite-60`,children:`Ready to optimize your dating profile?`})]}),(0,y.jsxs)(l,{className:`space-y-4`,children:[(0,y.jsxs)(`div`,{className:`flex flex-col space-y-3`,children:[(0,y.jsx)(p,{asChild:!0,variant:`primary`,className:`w-full`,children:(0,y.jsx)(n,{to:`/dashboard`,children:`Go to Dashboard`})}),(0,y.jsx)(p,{asChild:!0,variant:`secondary`,className:`w-full`,children:(0,y.jsx)(n,{to:`/image-analyzer`,children:`Analyze Photos`})}),(0,y.jsx)(p,{asChild:!0,variant:`secondary`,className:`w-full`,children:(0,y.jsx)(n,{to:`/bio-analyzer`,children:`Optimize Bio`})})]}),(0,y.jsx)(`div`,{className:`text-center pt-4`,children:(0,y.jsx)(p,{asChild:!0,variant:`tertiary`,className:`text-sm`,children:(0,y.jsx)(n,{to:`/`,children:`Back to Home`})})})]})]})}const w=function(){let[e,t]=(0,v.useState)(!1),[n,a]=(0,v.useState)(!1);return(0,y.jsx)(`div`,{className:`min-h-screen bg-gradient-hero flex items-center justify-center p-4`,children:(0,y.jsxs)(`div`,{className:`w-full max-w-md`,children:[(0,y.jsxs)(i,{children:[!e&&!n&&(0,y.jsx)(b,{onSignIn:()=>t(!0),onSignUp:()=>a(!0)}),e&&(0,y.jsx)(x,{onBack:()=>t(!1)}),n&&(0,y.jsx)(S,{onBack:()=>a(!1)})]}),(0,y.jsx)(r,{children:(0,y.jsx)(C,{})})]})})};export{w as component};