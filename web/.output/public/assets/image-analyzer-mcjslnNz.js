import{Link as e,__toESM as t,require_jsx_runtime as n,require_react as r}from"./main-B4G73TvM.js";import"./dist-CgCb95MR.js";import{ANALYSIS_STEPS as i}from"./analysis-BhwX0Tn_.js";import{Button as a}from"./createLucideIcon-JB7IMeGf.js";import{Badge as o}from"./badge-B_rsQKkv.js";import"./trash-2-CkLpzZy2.js";import{ArrowLeft as s}from"./arrow-left-CrPLcqAC.js";import{Crown as c}from"./crown-BUU0WoJh.js";import{Database as l,FileUp as u,PrivacyNotice as d,useDropzone as f}from"./PrivacyNotice-C9kEPnp4.js";import{LoaderCircle as p}from"./dist-C61kQO-x.js";import{Shield as m}from"./shield-Ct2yHJj1.js";import{Sparkles as h}from"./sparkles-BbQK6jEu.js";import{X as g,createImagePreview as _,imageStorage as v,initStorage as y,revokeImagePreview as b,sessionManager as x}from"./storage-BLisfAL7.js";import{Progress as S}from"./progress-BIjJajwn.js";import{analysisService as C}from"./analysis-service-DxWJ7Psk.js";import"./dist-DGuqR-4V.js";var w=t(r()),T=t(n());const E=({result:e})=>{let t=e=>e>=80?`bg-gradient-primary`:e>=50?`bg-warning-amber`:`bg-error-crimson`;return(0,T.jsxs)(`div`,{className:`bg-white rounded-lg shadow-md overflow-hidden animate-fade-in`,children:[(0,T.jsx)(`img`,{src:e.preview||`/placeholder.svg`,alt:e.fileName,className:`w-full h-48 object-cover`}),(0,T.jsxs)(`div`,{className:`p-4`,children:[(0,T.jsxs)(`div`,{className:`flex justify-between items-center mb-3`,children:[(0,T.jsx)(`h3`,{className:`font-semibold text-graphite-90 truncate pr-2`,children:e.fileName}),(0,T.jsx)(`div`,{className:`px-3 py-1 text-sm font-bold text-white rounded-full ${t(e.overallScore)}`,children:e.overallScore})]}),(0,T.jsx)(`div`,{className:`mb-4 space-y-1`,children:e.steps.map(e=>(0,T.jsxs)(`div`,{className:`flex justify-between items-center text-xs`,children:[(0,T.jsx)(`span`,{className:`text-graphite-60`,children:e.stepName}),(0,T.jsx)(`span`,{className:`font-medium ${e.score>=70?`text-success-green`:e.score>=50?`text-warning-amber`:`text-error-crimson`}`,children:e.score})]},e.stepId))}),(0,T.jsx)(`ul`,{className:`space-y-2`,children:e.recommendations.slice(0,3).map((e,t)=>(0,T.jsxs)(`li`,{className:`flex items-start text-sm text-graphite-60`,children:[(0,T.jsx)(h,{className:`h-4 w-4 mr-2 mt-0.5 text-flame-red flex-shrink-0`}),(0,T.jsx)(`span`,{children:e})]},t))}),e.error&&(0,T.jsx)(`div`,{className:`mt-3 p-2 bg-error-crimson/10 rounded text-sm text-error-crimson`,children:e.error})]})]})},D=({image:e,progress:t})=>(0,T.jsxs)(`div`,{className:`bg-white rounded-lg shadow-md overflow-hidden`,children:[(0,T.jsx)(`img`,{src:e.preview,alt:e.fileName,className:`w-full h-48 object-cover`}),(0,T.jsxs)(`div`,{className:`p-4`,children:[(0,T.jsx)(`div`,{className:`h-6 w-3/4 rounded bg-gray-200 animate-pulse mb-4`}),t&&(0,T.jsxs)(`div`,{className:`space-y-2`,children:[(0,T.jsxs)(`div`,{className:`flex justify-between text-sm`,children:[(0,T.jsxs)(`span`,{className:`text-graphite-60`,children:[`Step `,t.currentStep,`/`,i.length]}),(0,T.jsxs)(`span`,{className:`text-flame-red font-medium`,children:[Math.round(t.progress),`%`]})]}),(0,T.jsx)(`div`,{className:`text-sm text-graphite-90 font-medium`,children:t.stepName}),(0,T.jsx)(S,{value:t.progress,className:`h-2`})]}),(0,T.jsx)(`div`,{className:`mt-4 space-y-2`,children:i.map(e=>(0,T.jsxs)(`div`,{className:`flex items-center text-sm ${t&&t.currentStep>e.id?`text-success-green`:t&&t.currentStep===e.id?`text-flame-red`:`text-gray-400`}`,children:[(0,T.jsx)(`div`,{className:`w-2 h-2 rounded-full mr-2 ${t&&t.currentStep>e.id?`bg-success-green`:t&&t.currentStep===e.id?`bg-flame-red animate-pulse`:`bg-gray-300`}`}),(0,T.jsx)(`span`,{children:e.name})]},e.id))})]})]});function O(){let[t,n]=(0,w.useState)([]),[r,S]=(0,w.useState)([]),[O,k]=(0,w.useState)(`idle`),[A,j]=(0,w.useState)({}),[M,N]=(0,w.useState)(!1);(0,w.useEffect)(()=>(y().then(()=>{N(!0),P()}),()=>{t.forEach(e=>b(e.preview))}),[]);let P=async()=>{try{let e=x.getCurrentSession(),t=await v.getSessionImages(e.id),r=t.map(e=>({id:e.id,fileName:e.fileName,preview:_(e.blob),storedImage:e}));n(r);let i=[];for(let e of r){let t=x.getAnalysisResult(e.id);t&&i.push(t)}i.length>0&&(S(i),k(`done`))}catch(e){console.error(`Failed to load session images:`,e)}},F=(0,w.useCallback)(async e=>{if(M)try{let r=x.getCurrentSession(),i=[];for(let n of e.slice(0,10-t.length)){let e=await v.storeImage(n,r.id);x.addImageToSession(e);let t=await v.getImage(e);t&&i.push({id:e,fileName:n.name,preview:_(t.blob),storedImage:t})}n(e=>[...e,...i])}catch(e){console.error(`Failed to store images:`,e)}},[t.length,M]),{getRootProps:I,getInputProps:L,isDragActive:R}=f({onDrop:F,accept:{"image/*":[]},maxFiles:10-t.length,disabled:!M||t.length>=10}),z=async e=>{try{let r=t.find(t=>t.id===e);r&&(b(r.preview),await v.deleteImage(e),x.removeImageFromSession(e),n(t=>t.filter(t=>t.id!==e)),S(e=>e.filter(e=>e.fileName!==r.fileName)))}catch(e){console.error(`Failed to remove image:`,e)}},B=async()=>{if(t.length===0)return;k(`processing`),S([]),j({});let e=[];try{for(let n of t)await C.analyzeImage(n.storedImage,{onProgress:e=>{j(t=>({...t,[n.id]:e}))},onComplete:t=>{x.saveAnalysisResult(n.id,t),e.push(t),S([...e])},onError:e=>{console.error(`Analysis failed for ${n.fileName}:`,e)}}),t.indexOf(n)<t.length-1&&await new Promise(e=>setTimeout(e,1e3));k(`done`)}catch(e){console.error(`Analysis failed:`,e),k(`idle`)}};return(0,T.jsxs)(`div`,{className:`min-h-screen bg-gray-50`,children:[(0,T.jsxs)(`header`,{className:`bg-cloud-white shadow-sm sticky top-0 z-10`,children:[(0,T.jsxs)(`div`,{className:`container mx-auto px-4 md:px-6 h-16 flex items-center justify-between`,children:[(0,T.jsxs)(e,{to:`/`,className:`flex items-center gap-2 text-graphite-60 hover:text-graphite-90`,children:[(0,T.jsx)(s,{className:`h-5 w-5`}),(0,T.jsx)(`span`,{className:`font-semibold`,children:`Back to Home`})]}),(0,T.jsx)(d,{})]}),O===`processing`&&(0,T.jsx)(`div`,{className:`h-1 bg-gray-200`,children:(0,T.jsx)(`div`,{className:`h-full bg-gradient-primary transition-all duration-300`,style:{width:`${Object.values(A).reduce((e,t)=>e+t.progress,0)/Math.max(Object.keys(A).length,1)}%`}})})]}),(0,T.jsx)(`main`,{className:`container mx-auto px-4 md:px-6 py-8`,children:(0,T.jsxs)(`div`,{className:`max-w-4xl mx-auto`,children:[(0,T.jsxs)(`div`,{className:`text-center`,children:[(0,T.jsx)(`h1`,{className:`text-h2-mobile md:text-h2`,children:`Image Analyzer`}),(0,T.jsx)(`p`,{className:`text-body-lg text-graphite-60 mt-2`,children:`Upload your photos to get AI-powered feedback.`}),(0,T.jsxs)(`div`,{className:`flex justify-center gap-2 mt-4`,children:[(0,T.jsxs)(o,{variant:`secondary`,className:`flex items-center gap-1`,children:[(0,T.jsx)(m,{className:`h-3 w-3`}),`Images not stored on servers`]}),(0,T.jsxs)(o,{variant:`outline`,className:`flex items-center gap-1`,children:[(0,T.jsx)(l,{className:`h-3 w-3`}),`Local processing only`]})]})]}),O!==`done`&&(0,T.jsxs)(`div`,{className:`mt-8`,children:[(0,T.jsxs)(`div`,{...I(),className:`w-full p-10 border-2 border-dashed rounded-lg text-center cursor-pointer transition-colors ${R?`border-flame-red bg-flame-red/10`:`border-flame-red/50 hover:bg-flame-red/5`}`,children:[(0,T.jsx)(`input`,{...L()}),(0,T.jsx)(u,{className:`mx-auto h-12 w-12 text-flame-red/80`}),(0,T.jsx)(`p`,{className:`mt-4 text-body-md text-graphite-60`,children:R?`Drop the files here...`:`Drag 'n' drop some files here, or click to select files`}),(0,T.jsx)(`p`,{className:`text-caption text-graphite-60/70 mt-1`,children:`Maximum 10 photos`})]}),t.length>0&&(0,T.jsxs)(`div`,{className:`mt-6`,children:[(0,T.jsxs)(`h3`,{className:`font-semibold`,children:[`Uploaded Photos (`,t.length,`/10)`]}),(0,T.jsx)(`ul`,{className:`mt-4 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4`,children:t.map(e=>(0,T.jsxs)(`li`,{className:`relative group`,children:[(0,T.jsx)(`img`,{src:e.preview,alt:e.fileName,className:`w-full h-32 object-cover rounded-md`}),(0,T.jsx)(`button`,{onClick:()=>z(e.id),className:`absolute top-1 right-1 bg-black/50 text-white rounded-full p-0.5 opacity-0 group-hover:opacity-100 transition-opacity`,disabled:O===`processing`,children:(0,T.jsx)(g,{className:`h-4 w-4`})})]},e.id))}),(0,T.jsx)(a,{size:`lg`,onClick:B,disabled:O===`processing`||!M,className:`w-full mt-8`,children:O===`processing`?(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)(p,{className:`mr-2 h-5 w-5 animate-spin`}),`Analyzing with AI...`]}):`Analyze ${t.length} Photo${t.length===1?``:`s`} with AI`})]})]}),O===`processing`&&(0,T.jsx)(`div`,{className:`mt-12 grid md:grid-cols-2 lg:grid-cols-3 gap-6`,children:t.map(e=>(0,T.jsx)(D,{image:e,progress:A[e.id]},e.id))}),O===`done`&&r.length>0&&(0,T.jsxs)(`div`,{className:`mt-12`,children:[(0,T.jsxs)(`div`,{className:`text-center mb-6`,children:[(0,T.jsx)(`h2`,{className:`text-xl font-semibold`,children:`Analysis Results`}),(0,T.jsxs)(`p`,{className:`text-graphite-60 mt-2`,children:[`Your photos have been analyzed using our `,i.length,`-step AI assessment`]})]}),(0,T.jsx)(`div`,{className:`grid md:grid-cols-2 lg:grid-cols-3 gap-6`,children:r.map(e=>(0,T.jsx)(E,{result:e},e.fileName))}),(0,T.jsxs)(`div`,{className:`mt-8 text-center space-y-4`,children:[(0,T.jsxs)(`div`,{className:`flex justify-center space-x-4`,children:[(0,T.jsx)(a,{onClick:()=>{k(`idle`),S([]),j({})},variant:`secondary`,children:`Analyze More Photos`}),(0,T.jsx)(a,{asChild:!0,size:`lg`,children:(0,T.jsxs)(e,{to:`/bio-analyzer`,children:[`Analyze Bio Next `,(0,T.jsx)(h,{className:`ml-2 h-5 w-5`})]})})]}),(0,T.jsxs)(`div`,{className:`pt-4 border-t`,children:[(0,T.jsx)(`p`,{className:`text-sm text-gray-600 mb-3`,children:`Want professional-grade analysis?`}),(0,T.jsx)(a,{asChild:!0,variant:`outline`,className:`border-purple-300 text-purple-700 hover:bg-purple-50`,children:(0,T.jsxs)(e,{to:`/image-analyzer-pro`,children:[`Try Advanced Analysis `,(0,T.jsx)(c,{className:`ml-2 h-4 w-4`})]})})]})]}),(0,T.jsx)(`div`,{className:`fixed bottom-8 right-8`,children:(0,T.jsx)(a,{asChild:!0,size:`lg`,className:`rounded-full shadow-lg`,children:(0,T.jsx)(e,{to:`/bio-analyzer`,children:(0,T.jsx)(h,{className:`h-5 w-5`})})})})]})]})})]})}const k=O;export{k as component};