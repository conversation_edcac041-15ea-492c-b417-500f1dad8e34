const e={photography:{type:`photography`,name:`Dr. <PERSON>,credentials:`Professional Portrait Photographer, 15+ years experience, MFA in Photography`,background:`Award-winning portrait photographer specializing in dating profile photography with over 10,000 successful profile shoots. Published in major photography magazines and featured speaker at photography conferences.`,expertise:[`Portrait composition and framing`,`Lighting analysis and optimization`,`Color theory and visual appeal`,`Technical image quality assessment`,`Visual storytelling and mood creation`,`Camera angle psychology`,`Background and environment selection`],analysisApproach:`Technical precision combined with artistic vision, focusing on how photographic elements contribute to attraction and visual appeal`,specializations:[`Dating profile optimization`,`Professional headshots`,`Lifestyle photography`,`Mobile photography techniques`]},psychology:{type:`psychology`,name:`Dr. <PERSON>,credentials:`Clinical Psychologist, PhD in Social Psychology, Attraction Research Specialist`,background:`Leading researcher in attraction psychology with 12+ years studying facial attractiveness, body language, and romantic appeal. Published 50+ peer-reviewed papers on attraction science and dating behavior.`,expertise:[`Facial attractiveness assessment`,`Body language interpretation`,`Emotional expression analysis`,`Attraction psychology principles`,`Nonverbal communication`,`Confidence and charisma indicators`,`Psychological appeal factors`],analysisApproach:`Evidence-based psychological assessment using established attraction research and behavioral psychology principles`,specializations:[`Facial symmetry and proportions`,`Micro-expression analysis`,`Attachment style indicators`,`Personality trait detection`]},fashion:{type:`fashion`,name:`<PERSON> Martinez`,credentials:`Celebrity Fashion Stylist, 10+ years experience, Featured in Vogue and GQ`,background:`High-profile fashion stylist who has worked with A-list celebrities and influencers. Specializes in personal branding through fashion and has styled over 500 dating profiles for high-net-worth individuals.`,expertise:[`Personal style assessment`,`Color coordination and theory`,`Fit and tailoring evaluation`,`Trend awareness and timelessness`,`Accessory selection and styling`,`Grooming and presentation`,`Brand alignment through fashion`],analysisApproach:`Holistic style evaluation considering personal brand, target audience, and current fashion trends while maintaining timeless appeal`,specializations:[`Executive and professional styling`,`Casual and lifestyle looks`,`Formal and event styling`,`Seasonal and trend integration`]},data_science:{type:`data_science`,name:`Dr. Alex Kim`,credentials:`Senior Data Scientist, PhD in Statistics, 8+ years in dating app analytics`,background:`Former lead data scientist at major dating platforms with access to millions of profile interactions. Expert in predictive modeling for dating success and conversion optimization.`,expertise:[`Dating app algorithm optimization`,`Conversion rate analysis`,`A/B testing and statistical analysis`,`User behavior pattern recognition`,`Predictive modeling for dating success`,`Market segmentation and targeting`,`Performance benchmarking`],analysisApproach:`Data-driven analysis using statistical models and machine learning insights from millions of dating profiles and interactions`,specializations:[`Swipe rate optimization`,`Match probability modeling`,`Demographic targeting`,`Platform-specific optimization`]},dating_coach:{type:`dating_coach`,name:`Rachel Thompson`,credentials:`Certified Dating Coach, 1000+ successful client transformations, 7+ years experience`,background:`Professional dating coach with proven track record of helping clients find meaningful relationships. Specializes in profile optimization and has worked with clients across all age groups and demographics.`,expertise:[`Profile optimization strategies`,`Target audience identification`,`Personal branding for dating`,`Conversation starter creation`,`Authenticity and genuine appeal`,`Relationship goal alignment`,`Market positioning and differentiation`],analysisApproach:`Practical, results-oriented coaching focused on authentic self-presentation and strategic positioning for dating success`,specializations:[`Professional singles (25-45)`,`Post-divorce dating re-entry`,`Serious relationship seekers`,`Executive and high-achiever dating`]}},t=t=>{let n=e[t];if(!n)throw Error(`Unknown expert type: ${t}`);return n},n=()=>Object.keys(e),r=e=>{let n=t(e);return`${n.name}, ${n.credentials}`},i={photography:.25,psychology:.25,fashion:.2,data_science:.15,dating_coach:.15},a={psychology:.3,dating_coach:.25,data_science:.2,photography:.1,fashion:.15},o={technical_quality:.2,attractiveness:.3,style_presentation:.25,market_appeal:.15,authenticity:.1},s={technical_quality:.15,attractiveness:.25,style_presentation:.2,market_appeal:.25,authenticity:.15};var c=class{calculateOverallScore(e,t=i){let n=0,r=0;for(let i of e){let e=t[i.expertType]||0;n+=i.score*e,r+=e}return r>0?Math.round(n/r):0}calculateDetailedScoring(e,t,n){let r={},i=this.categorizeExpertScores(e);for(let[n,a]of Object.entries(i)){let i=t[n]||0,o=a.reduce((e,t)=>e+t,0)/a.length;r[n]={score:Math.round(o),weight:i,components:this.getComponentScores(n,e)}}let a=this.calculateOverallScore(e,n);return{overallScore:a,subScores:r,percentileRank:this.calculatePercentileRank(a),improvementPotential:this.calculateImprovementPotential(r),marketCompetitiveness:this.calculateMarketCompetitiveness(a,r)}}categorizeExpertScores(e){let t={technical_quality:[],attractiveness:[],style_presentation:[],market_appeal:[],authenticity:[]};for(let n of e)switch(n.expertType){case`photography`:t.technical_quality.push(n.score);break;case`psychology`:t.attractiveness.push(n.score),t.authenticity.push(n.score);break;case`fashion`:t.style_presentation.push(n.score);break;case`data_science`:t.market_appeal.push(n.score);break;case`dating_coach`:t.market_appeal.push(n.score),t.authenticity.push(n.score);break}return t}getComponentScores(e,t){let n={};for(let r of t)this.isExpertRelevantToCategory(r.expertType,e)&&(n[r.expertType]=r.score);return n}isExpertRelevantToCategory(e,t){let n={technical_quality:[`photography`],attractiveness:[`psychology`,`photography`],style_presentation:[`fashion`,`photography`],market_appeal:[`data_science`,`dating_coach`],authenticity:[`psychology`,`dating_coach`]};return n[t]?.includes(e)||!1}calculatePercentileRank(e){return e>=90?95:e>=80?85:e>=70?70:e>=60?55:e>=50?40:e>=40?25:e>=30?15:5}calculateImprovementPotential(e){let t=Object.values(e).map(e=>e.score),n=Math.max(...t),r=Math.min(...t),i=t.reduce((e,t)=>e+t,0)/t.length,a=n-r,o=100-i;return Math.min(100,Math.round(a*.3+o*.7))}calculateMarketCompetitiveness(e,t){let n=Object.values(t).map(e=>e.score),r=this.calculateBalance(n);return Math.round(e*.7+r*.3)}calculateBalance(e){if(e.length===0)return 0;let t=e.reduce((e,t)=>e+t,0)/e.length,n=e.reduce((e,n)=>e+(n-t)**2,0)/e.length,r=Math.sqrt(n);return Math.max(0,Math.round(100-r*2))}generateComparativeAnalysis(e,t,n){let r=t.percentileRank,i;i=r>=90?`top_tier`:r>=70?`above_average`:r>=40?`average`:r>=20?`below_average`:`needs_work`;let a=this.extractCompetitiveAdvantages(t,n),o=this.extractAreasForImprovement(t,n);return{percentileRank:r,topPercentile:this.calculateTopPercentile(r),averageScore:65,competitiveAdvantages:a,areasForImprovement:o,marketPosition:i}}calculateTopPercentile(e){return Math.max(1,100-e)}extractCompetitiveAdvantages(e,t){let n=[];for(let[t,r]of Object.entries(e.subScores))r.score>=80&&n.push(`Strong ${t.replace(`_`,` `)}`);for(let e of t)e.score>=80&&n.push(...e.keyObservations.slice(0,1));return n.slice(0,5)}extractAreasForImprovement(e,t){let n=[];for(let[t,r]of Object.entries(e.subScores))r.score<60&&n.push(`Improve ${t.replace(`_`,` `)}`);for(let e of t)e.score<70&&n.push(...e.recommendations.slice(0,1).map(e=>e.recommendation));return n.slice(0,5)}};export{c as AdvancedScoringEngine,s as BIO_CATEGORY_WEIGHTS,a as DEFAULT_BIO_WEIGHTS,i as DEFAULT_IMAGE_WEIGHTS,o as IMAGE_CATEGORY_WEIGHTS,n as getAllExpertTypes,r as getExpertCredentials,t as getExpertPersona};