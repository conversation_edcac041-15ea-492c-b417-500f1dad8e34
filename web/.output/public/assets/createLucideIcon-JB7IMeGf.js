import{__toESM as e,clsx as t,cn as n,require_jsx_runtime as r,require_react as i}from"./main-B4G73TvM.js";var a=e(i(),1);function o(e,t){if(typeof e==`function`)return e(t);e!=null&&(e.current=t)}function s(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return!n&&typeof r==`function`&&(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];typeof n==`function`?n():o(e[t],null)}}}}var c=e(r(),1);function l(e){let t=d(e),n=a.forwardRef((e,n)=>{let{children:r,...i}=e,o=a.Children.toArray(r),s=o.find(p);if(s){let e=s.props.children,r=o.map(t=>t===s?a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null:t);return(0,c.jsx)(t,{...i,ref:n,children:a.isValidElement(e)?a.cloneElement(e,void 0,r):null})}return(0,c.jsx)(t,{...i,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}var u=l(`Slot`);function d(e){let t=a.forwardRef((e,t)=>{let{children:n,...r}=e;if(a.isValidElement(n)){let e=h(n),i=m(r,n.props);return n.type!==a.Fragment&&(i.ref=t?s(t,e):e),a.cloneElement(n,i)}return a.Children.count(n)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var f=Symbol(`radix.slottable`);function p(e){return a.isValidElement(e)&&typeof e.type==`function`&&`__radixId`in e.type&&e.type.__radixId===f}function m(e,t){let n={...t};for(let r in t){let i=e[r],a=t[r],o=/^on[A-Z]/.test(r);o?i&&a?n[r]=(...e)=>{let t=a(...e);return i(...e),t}:i&&(n[r]=i):r===`style`?n[r]={...i,...a}:r===`className`&&(n[r]=[i,a].filter(Boolean).join(` `))}return{...e,...n}}function h(e){let t=Object.getOwnPropertyDescriptor(e.props,`ref`)?.get,n=t&&`isReactWarning`in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,`ref`)?.get,n=t&&`isReactWarning`in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}const g=e=>typeof e==`boolean`?`${e}`:e===0?`0`:e,_=t,v=(e,t)=>n=>{var r;if(t?.variants==null)return _(e,n?.class,n?.className);let{variants:i,defaultVariants:a}=t,o=Object.keys(i).map(e=>{let t=n?.[e],r=a?.[e];if(t===null)return null;let o=g(t)||g(r);return i[e][o]}),s=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return r===void 0||(e[n]=r),e},{}),c=t==null?void 0:(r=t.compoundVariants)?.reduce((e,t)=>{let{class:n,className:r,...i}=t;return Object.entries(i).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...a,...s}[t]):{...a,...s}[t]===n})?[...e,n,r]:e},[]);return _(e,o,c,n?.class,n?.className)},y=v(`inline-flex items-center justify-center whitespace-nowrap rounded-md text-base font-semibold ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-38`,{variants:{variant:{primary:`bg-gradient-primary text-cloud-white shadow-button-primary hover:-translate-y-px hover:shadow-lg`,secondary:`border-2 border-flame-red text-flame-red bg-cloud-white hover:bg-flame-red/10`,tertiary:`text-flame-red hover:underline`,inverted:`bg-cloud-white text-flame-red hover:bg-cloud-white/90`,ghost:`hover:bg-accent hover:text-accent-foreground`,link:`text-primary underline-offset-4 hover:underline`},size:{default:`h-11 px-4 py-2`,sm:`h-9 rounded-md px-3 text-sm`,lg:`h-12 rounded-md px-6 py-3 text-lg`,icon:`h-10 w-10`}},defaultVariants:{variant:`primary`,size:`default`}}),b=a.forwardRef(({className:e,variant:t,size:r,asChild:i=!1,...a},o)=>{let s=i?u:`button`;return(0,c.jsx)(s,{className:n(y({variant:t,size:r,className:e})),ref:o,...a})});b.displayName=`Button`;
/**
* @license lucide-react v0.454.0 - ISC
*
* This source code is licensed under the ISC license.
* See the LICENSE file in the root directory of this source tree.
*/
const x=e=>e.replace(/([a-z0-9])([A-Z])/g,`$1-$2`).toLowerCase(),S=(...e)=>e.filter((e,t,n)=>!!e&&e.trim()!==``&&n.indexOf(e)===t).join(` `).trim();
/**
* @license lucide-react v0.454.0 - ISC
*
* This source code is licensed under the ISC license.
* See the LICENSE file in the root directory of this source tree.
*/
var C={xmlns:`http://www.w3.org/2000/svg`,width:24,height:24,viewBox:`0 0 24 24`,fill:`none`,stroke:`currentColor`,strokeWidth:2,strokeLinecap:`round`,strokeLinejoin:`round`};const w=(0,a.forwardRef)(({color:e=`currentColor`,size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:i=``,children:o,iconNode:s,...c},l)=>(0,a.createElement)(`svg`,{ref:l,...C,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:S(`lucide`,i),...c},[...s.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(o)?o:[o]])),T=(e,t)=>{let n=(0,a.forwardRef)(({className:n,...r},i)=>(0,a.createElement)(w,{ref:i,iconNode:t,className:S(`lucide-${x(e)}`,n),...r}));return n.displayName=`${e}`,n};export{b as Button,T as createLucideIcon,l as createSlot,v as cva};