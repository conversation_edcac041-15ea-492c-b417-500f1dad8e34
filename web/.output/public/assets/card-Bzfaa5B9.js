import{__toESM as e,cn as t,require_jsx_runtime as n,require_react as r}from"./main-B4G73TvM.js";var i=e(r()),a=e(n());const o=i.forwardRef(({className:e,...n},r)=>(0,a.jsx)(`div`,{ref:r,className:t(`rounded-lg border bg-card text-card-foreground shadow-sm`,e),...n}));o.displayName=`Card`;const s=i.forwardRef(({className:e,...n},r)=>(0,a.jsx)(`div`,{ref:r,className:t(`flex flex-col space-y-1.5 p-6`,e),...n}));s.displayName=`CardHeader`;const c=i.forwardRef(({className:e,...n},r)=>(0,a.jsx)(`div`,{ref:r,className:t(`text-2xl font-semibold leading-none tracking-tight`,e),...n}));c.displayName=`CardTitle`;const l=i.forwardRef(({className:e,...n},r)=>(0,a.jsx)(`div`,{ref:r,className:t(`text-sm text-muted-foreground`,e),...n}));l.displayName=`CardDescription`;const u=i.forwardRef(({className:e,...n},r)=>(0,a.jsx)(`div`,{ref:r,className:t(`p-6 pt-0`,e),...n}));u.displayName=`CardContent`;const d=i.forwardRef(({className:e,...n},r)=>(0,a.jsx)(`div`,{ref:r,className:t(`flex items-center p-6 pt-0`,e),...n}));d.displayName=`CardFooter`;export{o as Card,u as CardContent,l as CardDescription,s as CardHeader,c as CardTitle};