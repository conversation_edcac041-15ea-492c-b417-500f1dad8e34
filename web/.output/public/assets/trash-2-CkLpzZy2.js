import{__toESM as e,require_jsx_runtime as t,require_react as n,require_react_dom as r}from"./main-B4G73TvM.js";import{createLucideIcon as i}from"./createLucideIcon-JB7IMeGf.js";var a=e(n(),1);function o(e,t){if(typeof e==`function`)return e(t);e!=null&&(e.current=t)}function s(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return!n&&typeof r==`function`&&(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];typeof n==`function`?n():o(e[t],null)}}}}function c(...e){return a.useCallback(s(...e),e)}var l=e(t(),1),u=a.forwardRef((e,t)=>{let{children:n,...r}=e,i=a.Children.toArray(n),o=i.find(p);if(o){let e=o.props.children,n=i.map(t=>t===o?a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null:t);return(0,l.jsx)(d,{...r,ref:t,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,l.jsx)(d,{...r,ref:t,children:n})});u.displayName=`Slot`;var d=a.forwardRef((e,t)=>{let{children:n,...r}=e;if(a.isValidElement(n)){let e=h(n);return a.cloneElement(n,{...m(r,n.props),ref:t?s(t,e):e})}return a.Children.count(n)>1?a.Children.only(null):null});d.displayName=`SlotClone`;var f=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});function p(e){return a.isValidElement(e)&&e.type===f}function m(e,t){let n={...t};for(let r in t){let i=e[r],a=t[r],o=/^on[A-Z]/.test(r);o?i&&a?n[r]=(...e)=>{a(...e),i(...e)}:i&&(n[r]=i):r===`style`?n[r]={...i,...a}:r===`className`&&(n[r]=[i,a].filter(Boolean).join(` `))}return{...e,...n}}function h(e){let t=Object.getOwnPropertyDescriptor(e.props,`ref`)?.get,n=t&&`isReactWarning`in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,`ref`)?.get,n=t&&`isReactWarning`in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var g=e(r(),1),_=[`a`,`button`,`div`,`form`,`h2`,`h3`,`img`,`input`,`label`,`li`,`nav`,`ol`,`p`,`span`,`svg`,`ul`],v=_.reduce((e,t)=>{let n=a.forwardRef((e,n)=>{let{asChild:r,...i}=e,a=r?u:t;return typeof window<`u`&&(window[Symbol.for(`radix-ui`)]=!0),(0,l.jsx)(a,{...i,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function y(e,t){e&&g.flushSync(()=>e.dispatchEvent(t))}const b=i(`Trash2`,[[`path`,{d:`M3 6h18`,key:`d0wm0j`}],[`path`,{d:`M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6`,key:`4alrt4`}],[`path`,{d:`M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2`,key:`v07s0e`}],[`line`,{x1:`10`,x2:`10`,y1:`11`,y2:`17`,key:`1uufr5`}],[`line`,{x1:`14`,x2:`14`,y1:`11`,y2:`17`,key:`xtxkd`}]]);export{v as Primitive,b as Trash2,s as composeRefs,y as dispatchDiscreteCustomEvent,c as useComposedRefs};