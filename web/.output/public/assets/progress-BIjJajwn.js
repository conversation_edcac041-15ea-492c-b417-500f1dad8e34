import{__toESM as e,cn as t,require_jsx_runtime as n,require_react as r,require_react_dom as i}from"./main-B4G73TvM.js";import{createSlot as a}from"./createLucideIcon-JB7IMeGf.js";var o=e(r(),1),s=e(n(),1);function c(e,t=[]){let n=[];function r(t,r){let i=o.createContext(r),a=n.length;n=[...n,r];let c=t=>{let{scope:n,children:r,...c}=t,l=n?.[e]?.[a]||i,u=o.useMemo(()=>c,Object.values(c));return(0,s.jsx)(l.Provider,{value:u,children:r})};c.displayName=t+`Provider`;function l(n,s){let c=s?.[e]?.[a]||i,l=o.useContext(c);if(l)return l;if(r!==void 0)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}return[c,l]}let i=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return i.scopeName=e,[r,l(i,...t)]}function l(...e){let t=e[0];if(e.length===1)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e),a=i[`__scope${r}`];return{...t,...a}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}var u=e(i(),1),d=[`a`,`button`,`div`,`form`,`h2`,`h3`,`img`,`input`,`label`,`li`,`nav`,`ol`,`p`,`select`,`span`,`svg`,`ul`],f=d.reduce((e,t)=>{let n=a(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:i,...a}=e,o=i?n:t;return typeof window<`u`&&(window[Symbol.for(`radix-ui`)]=!0),(0,s.jsx)(o,{...a,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),p=`Progress`,m=100,[h,g]=c(p),[_,v]=h(p),y=o.forwardRef((e,t)=>{let{__scopeProgress:n,value:r=null,max:i,getValueLabel:a=S,...o}=e;(i||i===0)&&!T(i)&&console.error(D(`${i}`,`Progress`));let c=T(i)?i:m;r!==null&&!E(r,c)&&console.error(O(`${r}`,`Progress`));let l=E(r,c)?r:null,u=w(l)?a(l,c):void 0;return(0,s.jsx)(_,{scope:n,value:l,max:c,children:(0,s.jsx)(f.div,{"aria-valuemax":c,"aria-valuemin":0,"aria-valuenow":w(l)?l:void 0,"aria-valuetext":u,role:`progressbar`,"data-state":C(l,c),"data-value":l??void 0,"data-max":c,...o,ref:t})})});y.displayName=p;var b=`ProgressIndicator`,x=o.forwardRef((e,t)=>{let{__scopeProgress:n,...r}=e,i=v(b,n);return(0,s.jsx)(f.div,{"data-state":C(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...r,ref:t})});x.displayName=b;function S(e,t){return`${Math.round(e/t*100)}%`}function C(e,t){return e==null?`indeterminate`:e===t?`complete`:`loading`}function w(e){return typeof e==`number`}function T(e){return w(e)&&!isNaN(e)&&e>0}function E(e,t){return w(e)&&!isNaN(e)&&e<=t&&e>=0}function D(e,t){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${t}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${m}\`.`}function O(e,t){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${t}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${m} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var k=y,A=x;const j=o.forwardRef(({className:e,value:n,...r},i)=>(0,s.jsx)(k,{ref:i,className:t(`relative h-4 w-full overflow-hidden rounded-full bg-secondary`,e),...r,children:(0,s.jsx)(A,{className:`h-full w-full flex-1 bg-gradient-primary transition-all`,style:{transform:`translateX(-${100-(n||0)}%)`}})}));j.displayName=k.displayName;export{j as Progress};