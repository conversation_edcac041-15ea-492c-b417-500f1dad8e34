import{__commonJSMin as e,__toESM as t}from"./main-B4G73TvM.js";var n=`vercel.ai.error`,r=Symbol.for(n),i,a=class e extends Error{constructor({name:e,message:t,cause:n}){super(t),this[i]=!0,this.name=e,this.cause=n}static isInstance(t){return e.has<PERSON>arker(t,n)}static hasMarker(e,t){let n=Symbol.for(t);return typeof e==`object`&&!!e&&n in e&&typeof e[n]==`boolean`&&e[n]===!0}};i=r;var o=a,s=`AI_APICallError`,c=`vercel.ai.error.${s}`,l=Symbol.for(c),u,d=class extends o{constructor({message:e,url:t,requestBodyValues:n,statusCode:r,responseHeaders:i,responseBody:a,cause:o,isRetryable:c=r!=null&&(r===408||r===409||r===429||r>=500),data:l}){super({name:s,message:e,cause:o}),this[u]=!0,this.url=t,this.requestBodyValues=n,this.statusCode=r,this.responseHeaders=i,this.responseBody=a,this.isRetryable=c,this.data=l}static isInstance(e){return o.hasMarker(e,c)}};u=l;var f=`AI_EmptyResponseBodyError`,p=`vercel.ai.error.${f}`,m=Symbol.for(p),ee,te=class extends o{constructor({message:e=`Empty response body`}={}){super({name:f,message:e}),this[ee]=!0}static isInstance(e){return o.hasMarker(e,p)}};ee=m;function ne(e){return e==null?`unknown error`:typeof e==`string`?e:e instanceof Error?e.message:JSON.stringify(e)}var re=`AI_InvalidArgumentError`,ie=`vercel.ai.error.${re}`,ae=Symbol.for(ie),oe,h=class extends o{constructor({message:e,cause:t,argument:n}){super({name:re,message:e,cause:t}),this[oe]=!0,this.argument=n}static isInstance(e){return o.hasMarker(e,ie)}};oe=ae;var se=`AI_InvalidPromptError`,ce=`vercel.ai.error.${se}`,le=Symbol.for(ce),ue,g=class extends o{constructor({prompt:e,message:t,cause:n}){super({name:se,message:`Invalid prompt: ${t}`,cause:n}),this[ue]=!0,this.prompt=e}static isInstance(e){return o.hasMarker(e,ce)}};ue=le;var de=`AI_InvalidResponseDataError`,fe=`vercel.ai.error.${de}`,pe=Symbol.for(fe),me,he=class extends o{constructor({data:e,message:t=`Invalid response data: ${JSON.stringify(e)}.`}){super({name:de,message:t}),this[me]=!0,this.data=e}static isInstance(e){return o.hasMarker(e,fe)}};me=pe;var ge=`AI_JSONParseError`,_e=`vercel.ai.error.${ge}`,ve=Symbol.for(_e),ye,be=class extends o{constructor({text:e,cause:t}){super({name:ge,message:`JSON parsing failed: Text: ${e}.
Error message: ${ne(t)}`,cause:t}),this[ye]=!0,this.text=e}static isInstance(e){return o.hasMarker(e,_e)}};ye=ve;var xe=`AI_LoadAPIKeyError`,_=`vercel.ai.error.${xe}`,v=Symbol.for(_),Se,y=class extends o{constructor({message:e}){super({name:xe,message:e}),this[Se]=!0}static isInstance(e){return o.hasMarker(e,_)}};Se=v;var Ce=`AI_LoadSettingError`,we=`vercel.ai.error.${Ce}`,Te=Symbol.for(we),Ee;Ee=Te;var De=`AI_NoContentGeneratedError`,Oe=`vercel.ai.error.${De}`,ke=Symbol.for(Oe),Ae;Ae=ke;var b=`AI_NoSuchModelError`,je=`vercel.ai.error.${b}`,Me=Symbol.for(je),Ne;Ne=Me;var x=`AI_TooManyEmbeddingValuesForCallError`,Pe=`vercel.ai.error.${x}`,Fe=Symbol.for(Pe),Ie,Le=class extends o{constructor(e){super({name:x,message:`Too many values for a single embedding call. The ${e.provider} model "${e.modelId}" can only embed up to ${e.maxEmbeddingsPerCall} values per call, but ${e.values.length} values were provided.`}),this[Ie]=!0,this.provider=e.provider,this.modelId=e.modelId,this.maxEmbeddingsPerCall=e.maxEmbeddingsPerCall,this.values=e.values}static isInstance(e){return o.hasMarker(e,Pe)}};Ie=Fe;var Re=`AI_TypeValidationError`,ze=`vercel.ai.error.${Re}`,Be=Symbol.for(ze),Ve,He=class e extends o{constructor({value:e,cause:t}){super({name:Re,message:`Type validation failed: Value: ${JSON.stringify(e)}.
Error message: ${ne(t)}`,cause:t}),this[Ve]=!0,this.value=e}static isInstance(e){return o.hasMarker(e,ze)}static wrap({value:t,cause:n}){return e.isInstance(n)&&n.value===t?n:new e({value:t,cause:n})}};Ve=Be;var Ue=He,We=`AI_UnsupportedFunctionalityError`,Ge=`vercel.ai.error.${We}`,Ke=Symbol.for(Ge),qe,Je=class extends o{constructor({functionality:e,message:t=`'${e}' functionality not supported.`}){super({name:We,message:t}),this[qe]=!0,this.functionality=e}static isInstance(e){return o.hasMarker(e,Ge)}};qe=Ke;let Ye=(e,t=21)=>(n=t)=>{let r=``,i=n|0;for(;i--;)r+=e[Math.random()*e.length|0];return r};var Xe=e((exports,t)=>{let n=typeof Buffer<`u`,r=/"(?:_|\\u005[Ff])(?:_|\\u005[Ff])(?:p|\\u0070)(?:r|\\u0072)(?:o|\\u006[Ff])(?:t|\\u0074)(?:o|\\u006[Ff])(?:_|\\u005[Ff])(?:_|\\u005[Ff])"\s*:/,i=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/;function a(e,t,a){a==null&&typeof t==`object`&&t&&(a=t,t=void 0),n&&Buffer.isBuffer(e)&&(e=e.toString()),e&&e.charCodeAt(0)===65279&&(e=e.slice(1));let s=JSON.parse(e,t);if(typeof s!=`object`||!s)return s;let c=a&&a.protoAction||`error`,l=a&&a.constructorAction||`error`;if(c===`ignore`&&l===`ignore`)return s;if(c!==`ignore`&&l!==`ignore`){if(r.test(e)===!1&&i.test(e)===!1)return s}else if(c!==`ignore`&&l===`ignore`){if(r.test(e)===!1)return s}else if(i.test(e)===!1)return s;return o(s,{protoAction:c,constructorAction:l,safe:a&&a.safe})}function o(e,{protoAction:t=`error`,constructorAction:n=`error`,safe:r}={}){let i=[e];for(;i.length;){let e=i;i=[];for(let a of e){if(t!==`ignore`&&Object.prototype.hasOwnProperty.call(a,`__proto__`)){if(r===!0)return null;if(t===`error`)throw SyntaxError(`Object contains forbidden prototype property`);delete a.__proto__}if(n!==`ignore`&&Object.prototype.hasOwnProperty.call(a,`constructor`)&&Object.prototype.hasOwnProperty.call(a.constructor,`prototype`)){if(r===!0)return null;if(n===`error`)throw SyntaxError(`Object contains forbidden prototype property`);delete a.constructor}for(let e in a){let t=a[e];t&&typeof t==`object`&&i.push(t)}}}return e}function s(e,t,n){let r=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return a(e,t,n)}finally{Error.stackTraceLimit=r}}function c(e,t){let n=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return a(e,t,{safe:!0})}catch{return null}finally{Error.stackTraceLimit=n}}t.exports=s,t.exports.default=s,t.exports.parse=s,t.exports.safeParse=c,t.exports.scan=o}),Ze=t(Xe(),1);function Qe(...e){return e.reduce((e,t)=>({...e,...t??{}}),{})}function $e(e){return new ReadableStream({async pull(t){try{let{value:n,done:r}=await e.next();r?t.close():t.enqueue(n)}catch(e){t.error(e)}},cancel(){}})}async function et(e){return e==null?Promise.resolve():new Promise(t=>setTimeout(t,e))}function tt(){let e=``,t,n=[],r,i;function a(e,t){if(e===``){o(t);return}if(e.startsWith(`:`))return;let n=e.indexOf(`:`);if(n===-1){s(e,``);return}let r=e.slice(0,n),i=n+1,a=i<e.length&&e[i]===` `?e.slice(i+1):e.slice(i);s(r,a)}function o(e){n.length>0&&(e.enqueue({event:t,data:n.join(`
`),id:r,retry:i}),n=[],t=void 0,i=void 0)}function s(e,a){switch(e){case`event`:t=a;break;case`data`:n.push(a);break;case`id`:r=a;break;case`retry`:let e=parseInt(a,10);isNaN(e)||(i=e);break}}return new TransformStream({transform(t,n){let{lines:r,incompleteLine:i}=nt(e,t);e=i;for(let e=0;e<r.length;e++)a(r[e],n)},flush(t){a(e,t),o(t)}})}function nt(e,t){let n=[],r=e;for(let e=0;e<t.length;){let i=t[e++];i===`
`?(n.push(r),r=``):i===`\r`?(n.push(r),r=``,t[e]===`
`&&e++):r+=i}return{lines:n,incompleteLine:r}}function rt(e){let t={};return e.headers.forEach((e,n)=>{t[n]=e}),t}var it=({prefix:e,size:t=16,alphabet:n=`0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz`,separator:r=`-`}={})=>{let i=Ye(n,t);if(e==null)return i;if(n.includes(r))throw new h({argument:`separator`,message:`The separator "${r}" must not be part of the alphabet "${n}".`});return t=>`${e}${r}${i(t)}`},at=it();function ot(e){return e==null?`unknown error`:typeof e==`string`?e:e instanceof Error?e.message:JSON.stringify(e)}function st(e){return Object.fromEntries(Object.entries(e).filter(([e,t])=>t!=null))}function ct(e){return e instanceof Error&&(e.name===`AbortError`||e.name===`TimeoutError`)}function lt({apiKey:e,environmentVariableName:t,apiKeyParameterName:n=`apiKey`,description:r}){if(typeof e==`string`)return e;if(e!=null)throw new y({message:`${r} API key must be a string.`});if(typeof process>`u`)throw new y({message:`${r} API key is missing. Pass it using the '${n}' parameter. Environment variables is not supported in this environment.`});if(e={}[t],e==null)throw new y({message:`${r} API key is missing. Pass it using the '${n}' parameter or the ${t} environment variable.`});if(typeof e!=`string`)throw new y({message:`${r} API key must be a string. The value of the ${t} environment variable is not a string.`});return e}var ut=Symbol.for(`vercel.ai.validator`);function dt(e){return{[ut]:!0,validate:e}}function ft(e){return typeof e==`object`&&!!e&&ut in e&&e[ut]===!0&&`validate`in e}function pt(e){return ft(e)?e:mt(e)}function mt(e){return dt(t=>{let n=e.safeParse(t);return n.success?{success:!0,value:n.data}:{success:!1,error:n.error}})}function ht({value:e,schema:t}){let n=gt({value:e,schema:t});if(!n.success)throw Ue.wrap({value:e,cause:n.error});return n.value}function gt({value:e,schema:t}){let n=pt(t);try{if(n.validate==null)return{success:!0,value:e};let t=n.validate(e);return t.success?t:{success:!1,error:Ue.wrap({value:e,cause:t.error})}}catch(t){return{success:!1,error:Ue.wrap({value:e,cause:t})}}}function _t({text:e,schema:t}){try{let n=Ze.default.parse(e);return t==null?n:ht({value:n,schema:t})}catch(t){throw be.isInstance(t)||Ue.isInstance(t)?t:new be({text:e,cause:t})}}function vt({text:e,schema:t}){try{let n=Ze.default.parse(e);if(t==null)return{success:!0,value:n,rawValue:n};let r=gt({value:n,schema:t});return r.success?{...r,rawValue:n}:r}catch(t){return{success:!1,error:be.isInstance(t)?t:new be({text:e,cause:t})}}}function yt(e){try{return Ze.default.parse(e),!0}catch{return!1}}function bt({provider:e,providerOptions:t,schema:n}){if(t?.[e]==null)return;let r=gt({value:t[e],schema:n});if(!r.success)throw new h({argument:`providerOptions`,message:`invalid ${e} provider options`,cause:r.error});return r.value}var xt=()=>globalThis.fetch,St=async({url:e,headers:t,body:n,failedResponseHandler:r,successfulResponseHandler:i,abortSignal:a,fetch:o})=>wt({url:e,headers:{"Content-Type":`application/json`,...t},body:{content:JSON.stringify(n),values:n},failedResponseHandler:r,successfulResponseHandler:i,abortSignal:a,fetch:o}),Ct=async({url:e,headers:t,formData:n,failedResponseHandler:r,successfulResponseHandler:i,abortSignal:a,fetch:o})=>wt({url:e,headers:t,body:{content:n,values:Object.fromEntries(n.entries())},failedResponseHandler:r,successfulResponseHandler:i,abortSignal:a,fetch:o}),wt=async({url:e,headers:t={},body:n,successfulResponseHandler:r,failedResponseHandler:i,abortSignal:a,fetch:o=xt()})=>{try{let s=await o(e,{method:`POST`,headers:st(t),body:n.content,signal:a}),c=rt(s);if(!s.ok){let t;try{t=await i({response:s,url:e,requestBodyValues:n.values})}catch(t){throw ct(t)||d.isInstance(t)?t:new d({message:`Failed to process error response`,cause:t,statusCode:s.status,url:e,responseHeaders:c,requestBodyValues:n.values})}throw t.value}try{return await r({response:s,url:e,requestBodyValues:n.values})}catch(t){throw t instanceof Error&&(ct(t)||d.isInstance(t))?t:new d({message:`Failed to process successful response`,cause:t,statusCode:s.status,url:e,responseHeaders:c,requestBodyValues:n.values})}}catch(t){if(ct(t))throw t;if(t instanceof TypeError&&t.message===`fetch failed`){let r=t.cause;if(r!=null)throw new d({message:`Cannot connect to API: ${r.message}`,cause:r,url:e,requestBodyValues:n.values,isRetryable:!0})}throw t}},Tt=({errorSchema:e,errorToMessage:t,isRetryable:n})=>async({response:r,url:i,requestBodyValues:a})=>{let o=await r.text(),s=rt(r);if(o.trim()===``)return{responseHeaders:s,value:new d({message:r.statusText,url:i,requestBodyValues:a,statusCode:r.status,responseHeaders:s,responseBody:o,isRetryable:n?.(r)})};try{let c=_t({text:o,schema:e});return{responseHeaders:s,value:new d({message:t(c),url:i,requestBodyValues:a,statusCode:r.status,responseHeaders:s,responseBody:o,data:c,isRetryable:n?.(r,c)})}}catch{return{responseHeaders:s,value:new d({message:r.statusText,url:i,requestBodyValues:a,statusCode:r.status,responseHeaders:s,responseBody:o,isRetryable:n?.(r)})}}},Et=e=>async({response:t})=>{let n=rt(t);if(t.body==null)throw new te({});return{responseHeaders:n,value:t.body.pipeThrough(new TextDecoderStream).pipeThrough(tt()).pipeThrough(new TransformStream({transform({data:t},n){t!==`[DONE]`&&n.enqueue(vt({text:t,schema:e}))}}))}},Dt=e=>async({response:t,url:n,requestBodyValues:r})=>{let i=await t.text(),a=vt({text:i,schema:e}),o=rt(t);if(!a.success)throw new d({message:`Invalid JSON response`,cause:a.error,statusCode:t.status,responseHeaders:o,responseBody:i,url:n,requestBodyValues:r});return{responseHeaders:o,value:a.value,rawValue:a.rawValue}},Ot=()=>async({response:e,url:t,requestBodyValues:n})=>{let r=rt(e);if(!e.body)throw new d({message:`Response body is empty`,url:t,requestBodyValues:n,statusCode:e.status,responseHeaders:r,responseBody:void 0});try{let t=await e.arrayBuffer();return{responseHeaders:r,value:new Uint8Array(t)}}catch(i){throw new d({message:`Failed to read response as array buffer`,url:t,requestBodyValues:n,statusCode:e.status,responseHeaders:r,responseBody:void 0,cause:i})}},{btoa:kt,atob:At}=globalThis;function jt(e){let t=e.replace(/-/g,`+`).replace(/_/g,`/`),n=At(t);return Uint8Array.from(n,e=>e.codePointAt(0))}function Mt(e){let t=``;for(let n=0;n<e.length;n++)t+=String.fromCodePoint(e[n]);return kt(t)}function Nt(e){return e?.replace(/\/$/,``)}var S;(function(e){e.assertEqual=e=>{};function t(e){}e.assertIs=t;function n(e){throw Error()}e.assertNever=n,e.arrayToEnum=e=>{let t={};for(let n of e)t[n]=n;return t},e.getValidEnumValues=t=>{let n=e.objectKeys(t).filter(e=>typeof t[t[e]]!=`number`),r={};for(let e of n)r[e]=t[e];return e.objectValues(r)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys=typeof Object.keys==`function`?e=>Object.keys(e):e=>{let t=[];for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},e.find=(e,t)=>{for(let n of e)if(t(n))return n},e.isInteger=typeof Number.isInteger==`function`?e=>Number.isInteger(e):e=>typeof e==`number`&&Number.isFinite(e)&&Math.floor(e)===e;function r(e,t=` | `){return e.map(e=>typeof e==`string`?`'${e}'`:e).join(t)}e.joinValues=r,e.jsonStringifyReplacer=(e,t)=>typeof t==`bigint`?t.toString():t})(S||={});var Pt;(function(e){e.mergeShapes=(e,t)=>({...e,...t})})(Pt||={});const C=S.arrayToEnum([`string`,`nan`,`number`,`integer`,`float`,`boolean`,`date`,`bigint`,`symbol`,`function`,`undefined`,`null`,`array`,`object`,`unknown`,`promise`,`void`,`never`,`map`,`set`]),Ft=e=>{let t=typeof e;switch(t){case`undefined`:return C.undefined;case`string`:return C.string;case`number`:return Number.isNaN(e)?C.nan:C.number;case`boolean`:return C.boolean;case`function`:return C.function;case`bigint`:return C.bigint;case`symbol`:return C.symbol;case`object`:return Array.isArray(e)?C.array:e===null?C.null:e.then&&typeof e.then==`function`&&e.catch&&typeof e.catch==`function`?C.promise:typeof Map<`u`&&e instanceof Map?C.map:typeof Set<`u`&&e instanceof Set?C.set:typeof Date<`u`&&e instanceof Date?C.date:C.object;default:return C.unknown}},w=S.arrayToEnum([`invalid_type`,`invalid_literal`,`custom`,`invalid_union`,`invalid_union_discriminator`,`invalid_enum_value`,`unrecognized_keys`,`invalid_arguments`,`invalid_return_type`,`invalid_date`,`invalid_string`,`too_small`,`too_big`,`invalid_intersection_types`,`not_multiple_of`,`not_finite`]);var T=class e extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name=`ZodError`,this.issues=e}format(e){let t=e||function(e){return e.message},n={_errors:[]},r=e=>{for(let i of e.issues)if(i.code===`invalid_union`)i.unionErrors.map(r);else if(i.code===`invalid_return_type`)r(i.returnTypeError);else if(i.code===`invalid_arguments`)r(i.argumentsError);else if(i.path.length===0)n._errors.push(t(i));else{let e=n,r=0;for(;r<i.path.length;){let n=i.path[r],a=r===i.path.length-1;a?(e[n]=e[n]||{_errors:[]},e[n]._errors.push(t(i))):e[n]=e[n]||{_errors:[]},e=e[n],r++}}};return r(this),n}static assert(t){if(!(t instanceof e))throw Error(`Not a ZodError: ${t}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,S.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=e=>e.message){let t={},n=[];for(let r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}};T.create=e=>{let t=new T(e);return t};const It=(e,t)=>{let n;switch(e.code){case w.invalid_type:n=e.received===C.undefined?`Required`:`Expected ${e.expected}, received ${e.received}`;break;case w.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(e.expected,S.jsonStringifyReplacer)}`;break;case w.unrecognized_keys:n=`Unrecognized key(s) in object: ${S.joinValues(e.keys,`, `)}`;break;case w.invalid_union:n=`Invalid input`;break;case w.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${S.joinValues(e.options)}`;break;case w.invalid_enum_value:n=`Invalid enum value. Expected ${S.joinValues(e.options)}, received '${e.received}'`;break;case w.invalid_arguments:n=`Invalid function arguments`;break;case w.invalid_return_type:n=`Invalid function return type`;break;case w.invalid_date:n=`Invalid date`;break;case w.invalid_string:typeof e.validation==`object`?`includes`in e.validation?(n=`Invalid input: must include "${e.validation.includes}"`,typeof e.validation.position==`number`&&(n=`${n} at one or more positions greater than or equal to ${e.validation.position}`)):`startsWith`in e.validation?n=`Invalid input: must start with "${e.validation.startsWith}"`:`endsWith`in e.validation?n=`Invalid input: must end with "${e.validation.endsWith}"`:S.assertNever(e.validation):n=e.validation===`regex`?`Invalid`:`Invalid ${e.validation}`;break;case w.too_small:n=e.type===`array`?`Array must contain ${e.exact?`exactly`:e.inclusive?`at least`:`more than`} ${e.minimum} element(s)`:e.type===`string`?`String must contain ${e.exact?`exactly`:e.inclusive?`at least`:`over`} ${e.minimum} character(s)`:e.type===`number`?`Number must be ${e.exact?`exactly equal to `:e.inclusive?`greater than or equal to `:`greater than `}${e.minimum}`:e.type===`date`?`Date must be ${e.exact?`exactly equal to `:e.inclusive?`greater than or equal to `:`greater than `}${new Date(Number(e.minimum))}`:`Invalid input`;break;case w.too_big:n=e.type===`array`?`Array must contain ${e.exact?`exactly`:e.inclusive?`at most`:`less than`} ${e.maximum} element(s)`:e.type===`string`?`String must contain ${e.exact?`exactly`:e.inclusive?`at most`:`under`} ${e.maximum} character(s)`:e.type===`number`?`Number must be ${e.exact?`exactly`:e.inclusive?`less than or equal to`:`less than`} ${e.maximum}`:e.type===`bigint`?`BigInt must be ${e.exact?`exactly`:e.inclusive?`less than or equal to`:`less than`} ${e.maximum}`:e.type===`date`?`Date must be ${e.exact?`exactly`:e.inclusive?`smaller than or equal to`:`smaller than`} ${new Date(Number(e.maximum))}`:`Invalid input`;break;case w.custom:n=`Invalid input`;break;case w.invalid_intersection_types:n=`Intersection results could not be merged`;break;case w.not_multiple_of:n=`Number must be a multiple of ${e.multipleOf}`;break;case w.not_finite:n=`Number must be finite`;break;default:n=t.defaultError,S.assertNever(e)}return{message:n}};var Lt=It;let Rt=Lt;function zt(){return Rt}const Bt=e=>{let{data:t,path:n,errorMaps:r,issueData:i}=e,a=[...n,...i.path||[]],o={...i,path:a};if(i.message!==void 0)return{...i,path:a,message:i.message};let s=``,c=r.filter(e=>!!e).slice().reverse();for(let e of c)s=e(o,{data:t,defaultError:s}).message;return{...i,path:a,message:s}};function E(e,t){let n=zt(),r=Bt({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,n,n===Lt?void 0:Lt].filter(e=>!!e)});e.common.issues.push(r)}var D=class e{constructor(){this.value=`valid`}dirty(){this.value===`valid`&&(this.value=`dirty`)}abort(){this.value!==`aborted`&&(this.value=`aborted`)}static mergeArray(e,t){let n=[];for(let r of t){if(r.status===`aborted`)return O;r.status===`dirty`&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(t,n){let r=[];for(let e of n){let t=await e.key,n=await e.value;r.push({key:t,value:n})}return e.mergeObjectSync(t,r)}static mergeObjectSync(e,t){let n={};for(let r of t){let{key:t,value:i}=r;if(t.status===`aborted`||i.status===`aborted`)return O;t.status===`dirty`&&e.dirty(),i.status===`dirty`&&e.dirty(),t.value!==`__proto__`&&(i.value!==void 0||r.alwaysSet)&&(n[t.value]=i.value)}return{status:e.value,value:n}}};const O=Object.freeze({status:`aborted`}),Vt=e=>({status:`dirty`,value:e}),k=e=>({status:`valid`,value:e}),Ht=e=>e.status===`aborted`,Ut=e=>e.status===`dirty`,Wt=e=>e.status===`valid`,Gt=e=>typeof Promise<`u`&&e instanceof Promise;var A;(function(e){e.errToObj=e=>typeof e==`string`?{message:e}:e||{},e.toString=e=>typeof e==`string`?e:e?.message})(A||={});var j=class{constructor(e,t,n,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=r}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}};const Kt=(e,t)=>{if(Wt(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error(`Validation failed but no issues detected.`);return{success:!1,get error(){if(this._error)return this._error;let t=new T(e.common.issues);return this._error=t,this._error}}};function M(e){if(!e)return{};let{errorMap:t,invalid_type_error:n,required_error:r,description:i}=e;if(t&&(n||r))throw Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);if(t)return{errorMap:t,description:i};let a=(t,i)=>{let{message:a}=e;return t.code===`invalid_enum_value`?{message:a??i.defaultError}:i.data===void 0?{message:a??r??i.defaultError}:t.code===`invalid_type`?{message:a??n??i.defaultError}:{message:i.defaultError}};return{errorMap:a,description:i}}var N=class{get description(){return this._def.description}_getType(e){return Ft(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:Ft(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new D,ctx:{common:e.parent.common,data:e.data,parsedType:Ft(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(Gt(t))throw Error(`Synchronous parse encountered promise.`);return t}_parseAsync(e){let t=this._parse(e);return Promise.resolve(t)}parse(e,t){let n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){let n={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ft(e)},r=this._parseSync({data:e,path:n.path,parent:n});return Kt(n,r)}"~validate"(e){let t={common:{issues:[],async:!!this[`~standard`].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ft(e)};if(!this[`~standard`].async)try{let n=this._parseSync({data:e,path:[],parent:t});return Wt(n)?{value:n.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes(`encountered`)&&(this[`~standard`].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>Wt(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){let n={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ft(e)},r=this._parse({data:e,path:n.path,parent:n}),i=await(Gt(r)?r:Promise.resolve(r));return Kt(n,i)}refine(e,t){let n=e=>typeof t==`string`||t===void 0?{message:t}:typeof t==`function`?t(e):t;return this._refinement((t,r)=>{let i=e(t),a=()=>r.addIssue({code:w.custom,...n(t)});return typeof Promise<`u`&&i instanceof Promise?i.then(e=>e?!0:(a(),!1)):i?!0:(a(),!1)})}refinement(e,t){return this._refinement((n,r)=>e(n)?!0:(r.addIssue(typeof t==`function`?t(n,r):t),!1))}_refinement(e){return new I({schema:this,typeName:R.ZodEffects,effect:{type:`refinement`,refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this[`~standard`]={version:1,vendor:`zod`,validate:e=>this[`~validate`](e)}}optional(){return L.create(this,this._def)}nullable(){return Jn.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return jn.create(this)}promise(){return qn.create(this,this._def)}or(e){return Nn.create([this,e],this._def)}and(e){return In.create(this,e,this._def)}transform(e){return new I({...M(this._def),schema:this,typeName:R.ZodEffects,effect:{type:`transform`,transform:e}})}default(e){let t=typeof e==`function`?e:()=>e;return new Yn({...M(this._def),innerType:this,defaultValue:t,typeName:R.ZodDefault})}brand(){return new $n({typeName:R.ZodBranded,type:this,...M(this._def)})}catch(e){let t=typeof e==`function`?e:()=>e;return new Xn({...M(this._def),innerType:this,catchValue:t,typeName:R.ZodCatch})}describe(e){let t=this.constructor;return new t({...this._def,description:e})}pipe(e){return er.create(this,e)}readonly(){return tr.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}};const qt=/^c[^\s-]{8,}$/i,Jt=/^[0-9a-z]+$/,Yt=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Xt=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Zt=/^[a-z0-9_-]{21}$/i,Qt=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,$t=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,en=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,tn=`^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$`;let nn;const rn=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,an=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,on=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,sn=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,cn=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,ln=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,un=`((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))`,dn=RegExp(`^${un}$`);function fn(e){let t=`[0-5]\\d`;e.precision?t=`${t}\\.\\d{${e.precision}}`:e.precision??(t=`${t}(\\.\\d+)?`);let n=e.precision?`+`:`?`;return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${n}`}function pn(e){return RegExp(`^${fn(e)}$`)}function mn(e){let t=`${un}T${fn(e)}`,n=[];return n.push(e.local?`Z?`:`Z`),e.offset&&n.push(`([+-]\\d{2}:?\\d{2})`),t=`${t}(${n.join(`|`)})`,RegExp(`^${t}$`)}function hn(e,t){return!!((t===`v4`||!t)&&rn.test(e)||(t===`v6`||!t)&&on.test(e))}function gn(e,t){if(!Qt.test(e))return!1;try{let[n]=e.split(`.`),r=n.replace(/-/g,`+`).replace(/_/g,`/`).padEnd(n.length+(4-n.length%4)%4,`=`),i=JSON.parse(atob(r));return!(typeof i!=`object`||!i||`typ`in i&&i?.typ!==`JWT`||!i.alg||t&&i.alg!==t)}catch{return!1}}function _n(e,t){return!!((t===`v4`||!t)&&an.test(e)||(t===`v6`||!t)&&sn.test(e))}var vn=class e extends N{_parse(e){this._def.coerce&&(e.data=String(e.data));let t=this._getType(e);if(t!==C.string){let t=this._getOrReturnCtx(e);return E(t,{code:w.invalid_type,expected:C.string,received:t.parsedType}),O}let n=new D,r;for(let t of this._def.checks)if(t.kind===`min`)e.data.length<t.value&&(r=this._getOrReturnCtx(e,r),E(r,{code:w.too_small,minimum:t.value,type:`string`,inclusive:!0,exact:!1,message:t.message}),n.dirty());else if(t.kind===`max`)e.data.length>t.value&&(r=this._getOrReturnCtx(e,r),E(r,{code:w.too_big,maximum:t.value,type:`string`,inclusive:!0,exact:!1,message:t.message}),n.dirty());else if(t.kind===`length`){let i=e.data.length>t.value,a=e.data.length<t.value;(i||a)&&(r=this._getOrReturnCtx(e,r),i?E(r,{code:w.too_big,maximum:t.value,type:`string`,inclusive:!0,exact:!0,message:t.message}):a&&E(r,{code:w.too_small,minimum:t.value,type:`string`,inclusive:!0,exact:!0,message:t.message}),n.dirty())}else if(t.kind===`email`)en.test(e.data)||(r=this._getOrReturnCtx(e,r),E(r,{validation:`email`,code:w.invalid_string,message:t.message}),n.dirty());else if(t.kind===`emoji`)nn||=new RegExp(tn,`u`),nn.test(e.data)||(r=this._getOrReturnCtx(e,r),E(r,{validation:`emoji`,code:w.invalid_string,message:t.message}),n.dirty());else if(t.kind===`uuid`)Xt.test(e.data)||(r=this._getOrReturnCtx(e,r),E(r,{validation:`uuid`,code:w.invalid_string,message:t.message}),n.dirty());else if(t.kind===`nanoid`)Zt.test(e.data)||(r=this._getOrReturnCtx(e,r),E(r,{validation:`nanoid`,code:w.invalid_string,message:t.message}),n.dirty());else if(t.kind===`cuid`)qt.test(e.data)||(r=this._getOrReturnCtx(e,r),E(r,{validation:`cuid`,code:w.invalid_string,message:t.message}),n.dirty());else if(t.kind===`cuid2`)Jt.test(e.data)||(r=this._getOrReturnCtx(e,r),E(r,{validation:`cuid2`,code:w.invalid_string,message:t.message}),n.dirty());else if(t.kind===`ulid`)Yt.test(e.data)||(r=this._getOrReturnCtx(e,r),E(r,{validation:`ulid`,code:w.invalid_string,message:t.message}),n.dirty());else if(t.kind===`url`)try{new URL(e.data)}catch{r=this._getOrReturnCtx(e,r),E(r,{validation:`url`,code:w.invalid_string,message:t.message}),n.dirty()}else if(t.kind===`regex`){t.regex.lastIndex=0;let i=t.regex.test(e.data);i||(r=this._getOrReturnCtx(e,r),E(r,{validation:`regex`,code:w.invalid_string,message:t.message}),n.dirty())}else if(t.kind===`trim`)e.data=e.data.trim();else if(t.kind===`includes`)e.data.includes(t.value,t.position)||(r=this._getOrReturnCtx(e,r),E(r,{code:w.invalid_string,validation:{includes:t.value,position:t.position},message:t.message}),n.dirty());else if(t.kind===`toLowerCase`)e.data=e.data.toLowerCase();else if(t.kind===`toUpperCase`)e.data=e.data.toUpperCase();else if(t.kind===`startsWith`)e.data.startsWith(t.value)||(r=this._getOrReturnCtx(e,r),E(r,{code:w.invalid_string,validation:{startsWith:t.value},message:t.message}),n.dirty());else if(t.kind===`endsWith`)e.data.endsWith(t.value)||(r=this._getOrReturnCtx(e,r),E(r,{code:w.invalid_string,validation:{endsWith:t.value},message:t.message}),n.dirty());else if(t.kind===`datetime`){let i=mn(t);i.test(e.data)||(r=this._getOrReturnCtx(e,r),E(r,{code:w.invalid_string,validation:`datetime`,message:t.message}),n.dirty())}else if(t.kind===`date`){let i=dn;i.test(e.data)||(r=this._getOrReturnCtx(e,r),E(r,{code:w.invalid_string,validation:`date`,message:t.message}),n.dirty())}else if(t.kind===`time`){let i=pn(t);i.test(e.data)||(r=this._getOrReturnCtx(e,r),E(r,{code:w.invalid_string,validation:`time`,message:t.message}),n.dirty())}else t.kind===`duration`?$t.test(e.data)||(r=this._getOrReturnCtx(e,r),E(r,{validation:`duration`,code:w.invalid_string,message:t.message}),n.dirty()):t.kind===`ip`?hn(e.data,t.version)||(r=this._getOrReturnCtx(e,r),E(r,{validation:`ip`,code:w.invalid_string,message:t.message}),n.dirty()):t.kind===`jwt`?gn(e.data,t.alg)||(r=this._getOrReturnCtx(e,r),E(r,{validation:`jwt`,code:w.invalid_string,message:t.message}),n.dirty()):t.kind===`cidr`?_n(e.data,t.version)||(r=this._getOrReturnCtx(e,r),E(r,{validation:`cidr`,code:w.invalid_string,message:t.message}),n.dirty()):t.kind===`base64`?cn.test(e.data)||(r=this._getOrReturnCtx(e,r),E(r,{validation:`base64`,code:w.invalid_string,message:t.message}),n.dirty()):t.kind===`base64url`?ln.test(e.data)||(r=this._getOrReturnCtx(e,r),E(r,{validation:`base64url`,code:w.invalid_string,message:t.message}),n.dirty()):S.assertNever(t);return{status:n.value,value:e.data}}_regex(e,t,n){return this.refinement(t=>e.test(t),{validation:t,code:w.invalid_string,...A.errToObj(n)})}_addCheck(t){return new e({...this._def,checks:[...this._def.checks,t]})}email(e){return this._addCheck({kind:`email`,...A.errToObj(e)})}url(e){return this._addCheck({kind:`url`,...A.errToObj(e)})}emoji(e){return this._addCheck({kind:`emoji`,...A.errToObj(e)})}uuid(e){return this._addCheck({kind:`uuid`,...A.errToObj(e)})}nanoid(e){return this._addCheck({kind:`nanoid`,...A.errToObj(e)})}cuid(e){return this._addCheck({kind:`cuid`,...A.errToObj(e)})}cuid2(e){return this._addCheck({kind:`cuid2`,...A.errToObj(e)})}ulid(e){return this._addCheck({kind:`ulid`,...A.errToObj(e)})}base64(e){return this._addCheck({kind:`base64`,...A.errToObj(e)})}base64url(e){return this._addCheck({kind:`base64url`,...A.errToObj(e)})}jwt(e){return this._addCheck({kind:`jwt`,...A.errToObj(e)})}ip(e){return this._addCheck({kind:`ip`,...A.errToObj(e)})}cidr(e){return this._addCheck({kind:`cidr`,...A.errToObj(e)})}datetime(e){return typeof e==`string`?this._addCheck({kind:`datetime`,precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:`datetime`,precision:e?.precision===void 0?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...A.errToObj(e?.message)})}date(e){return this._addCheck({kind:`date`,message:e})}time(e){return typeof e==`string`?this._addCheck({kind:`time`,precision:null,message:e}):this._addCheck({kind:`time`,precision:e?.precision===void 0?null:e?.precision,...A.errToObj(e?.message)})}duration(e){return this._addCheck({kind:`duration`,...A.errToObj(e)})}regex(e,t){return this._addCheck({kind:`regex`,regex:e,...A.errToObj(t)})}includes(e,t){return this._addCheck({kind:`includes`,value:e,position:t?.position,...A.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:`startsWith`,value:e,...A.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:`endsWith`,value:e,...A.errToObj(t)})}min(e,t){return this._addCheck({kind:`min`,value:e,...A.errToObj(t)})}max(e,t){return this._addCheck({kind:`max`,value:e,...A.errToObj(t)})}length(e,t){return this._addCheck({kind:`length`,value:e,...A.errToObj(t)})}nonempty(e){return this.min(1,A.errToObj(e))}trim(){return new e({...this._def,checks:[...this._def.checks,{kind:`trim`}]})}toLowerCase(){return new e({...this._def,checks:[...this._def.checks,{kind:`toLowerCase`}]})}toUpperCase(){return new e({...this._def,checks:[...this._def.checks,{kind:`toUpperCase`}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind===`datetime`)}get isDate(){return!!this._def.checks.find(e=>e.kind===`date`)}get isTime(){return!!this._def.checks.find(e=>e.kind===`time`)}get isDuration(){return!!this._def.checks.find(e=>e.kind===`duration`)}get isEmail(){return!!this._def.checks.find(e=>e.kind===`email`)}get isURL(){return!!this._def.checks.find(e=>e.kind===`url`)}get isEmoji(){return!!this._def.checks.find(e=>e.kind===`emoji`)}get isUUID(){return!!this._def.checks.find(e=>e.kind===`uuid`)}get isNANOID(){return!!this._def.checks.find(e=>e.kind===`nanoid`)}get isCUID(){return!!this._def.checks.find(e=>e.kind===`cuid`)}get isCUID2(){return!!this._def.checks.find(e=>e.kind===`cuid2`)}get isULID(){return!!this._def.checks.find(e=>e.kind===`ulid`)}get isIP(){return!!this._def.checks.find(e=>e.kind===`ip`)}get isCIDR(){return!!this._def.checks.find(e=>e.kind===`cidr`)}get isBase64(){return!!this._def.checks.find(e=>e.kind===`base64`)}get isBase64url(){return!!this._def.checks.find(e=>e.kind===`base64url`)}get minLength(){let e=null;for(let t of this._def.checks)t.kind===`min`&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)t.kind===`max`&&(e===null||t.value<e)&&(e=t.value);return e}};vn.create=e=>new vn({checks:[],typeName:R.ZodString,coerce:e?.coerce??!1,...M(e)});function yn(e,t){let n=(e.toString().split(`.`)[1]||``).length,r=(t.toString().split(`.`)[1]||``).length,i=n>r?n:r,a=Number.parseInt(e.toFixed(i).replace(`.`,``)),o=Number.parseInt(t.toFixed(i).replace(`.`,``));return a%o/10**i}var bn=class e extends N{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){this._def.coerce&&(e.data=Number(e.data));let t=this._getType(e);if(t!==C.number){let t=this._getOrReturnCtx(e);return E(t,{code:w.invalid_type,expected:C.number,received:t.parsedType}),O}let n,r=new D;for(let t of this._def.checks)if(t.kind===`int`)S.isInteger(e.data)||(n=this._getOrReturnCtx(e,n),E(n,{code:w.invalid_type,expected:`integer`,received:`float`,message:t.message}),r.dirty());else if(t.kind===`min`){let i=t.inclusive?e.data<t.value:e.data<=t.value;i&&(n=this._getOrReturnCtx(e,n),E(n,{code:w.too_small,minimum:t.value,type:`number`,inclusive:t.inclusive,exact:!1,message:t.message}),r.dirty())}else if(t.kind===`max`){let i=t.inclusive?e.data>t.value:e.data>=t.value;i&&(n=this._getOrReturnCtx(e,n),E(n,{code:w.too_big,maximum:t.value,type:`number`,inclusive:t.inclusive,exact:!1,message:t.message}),r.dirty())}else t.kind===`multipleOf`?yn(e.data,t.value)!==0&&(n=this._getOrReturnCtx(e,n),E(n,{code:w.not_multiple_of,multipleOf:t.value,message:t.message}),r.dirty()):t.kind===`finite`?Number.isFinite(e.data)||(n=this._getOrReturnCtx(e,n),E(n,{code:w.not_finite,message:t.message}),r.dirty()):S.assertNever(t);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit(`min`,e,!0,A.toString(t))}gt(e,t){return this.setLimit(`min`,e,!1,A.toString(t))}lte(e,t){return this.setLimit(`max`,e,!0,A.toString(t))}lt(e,t){return this.setLimit(`max`,e,!1,A.toString(t))}setLimit(t,n,r,i){return new e({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:A.toString(i)}]})}_addCheck(t){return new e({...this._def,checks:[...this._def.checks,t]})}int(e){return this._addCheck({kind:`int`,message:A.toString(e)})}positive(e){return this._addCheck({kind:`min`,value:0,inclusive:!1,message:A.toString(e)})}negative(e){return this._addCheck({kind:`max`,value:0,inclusive:!1,message:A.toString(e)})}nonpositive(e){return this._addCheck({kind:`max`,value:0,inclusive:!0,message:A.toString(e)})}nonnegative(e){return this._addCheck({kind:`min`,value:0,inclusive:!0,message:A.toString(e)})}multipleOf(e,t){return this._addCheck({kind:`multipleOf`,value:e,message:A.toString(t)})}finite(e){return this._addCheck({kind:`finite`,message:A.toString(e)})}safe(e){return this._addCheck({kind:`min`,inclusive:!0,value:-(2**53-1),message:A.toString(e)})._addCheck({kind:`max`,inclusive:!0,value:2**53-1,message:A.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)t.kind===`min`&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)t.kind===`max`&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind===`int`||e.kind===`multipleOf`&&S.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let n of this._def.checks)if(n.kind===`finite`||n.kind===`int`||n.kind===`multipleOf`)return!0;else n.kind===`min`?(t===null||n.value>t)&&(t=n.value):n.kind===`max`&&(e===null||n.value<e)&&(e=n.value);return Number.isFinite(t)&&Number.isFinite(e)}};bn.create=e=>new bn({checks:[],typeName:R.ZodNumber,coerce:e?.coerce||!1,...M(e)});var xn=class e extends N{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}let t=this._getType(e);if(t!==C.bigint)return this._getInvalidInput(e);let n,r=new D;for(let t of this._def.checks)if(t.kind===`min`){let i=t.inclusive?e.data<t.value:e.data<=t.value;i&&(n=this._getOrReturnCtx(e,n),E(n,{code:w.too_small,type:`bigint`,minimum:t.value,inclusive:t.inclusive,message:t.message}),r.dirty())}else if(t.kind===`max`){let i=t.inclusive?e.data>t.value:e.data>=t.value;i&&(n=this._getOrReturnCtx(e,n),E(n,{code:w.too_big,type:`bigint`,maximum:t.value,inclusive:t.inclusive,message:t.message}),r.dirty())}else t.kind===`multipleOf`?e.data%t.value!==BigInt(0)&&(n=this._getOrReturnCtx(e,n),E(n,{code:w.not_multiple_of,multipleOf:t.value,message:t.message}),r.dirty()):S.assertNever(t);return{status:r.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return E(t,{code:w.invalid_type,expected:C.bigint,received:t.parsedType}),O}gte(e,t){return this.setLimit(`min`,e,!0,A.toString(t))}gt(e,t){return this.setLimit(`min`,e,!1,A.toString(t))}lte(e,t){return this.setLimit(`max`,e,!0,A.toString(t))}lt(e,t){return this.setLimit(`max`,e,!1,A.toString(t))}setLimit(t,n,r,i){return new e({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:A.toString(i)}]})}_addCheck(t){return new e({...this._def,checks:[...this._def.checks,t]})}positive(e){return this._addCheck({kind:`min`,value:BigInt(0),inclusive:!1,message:A.toString(e)})}negative(e){return this._addCheck({kind:`max`,value:BigInt(0),inclusive:!1,message:A.toString(e)})}nonpositive(e){return this._addCheck({kind:`max`,value:BigInt(0),inclusive:!0,message:A.toString(e)})}nonnegative(e){return this._addCheck({kind:`min`,value:BigInt(0),inclusive:!0,message:A.toString(e)})}multipleOf(e,t){return this._addCheck({kind:`multipleOf`,value:e,message:A.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)t.kind===`min`&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)t.kind===`max`&&(e===null||t.value<e)&&(e=t.value);return e}};xn.create=e=>new xn({checks:[],typeName:R.ZodBigInt,coerce:e?.coerce??!1,...M(e)});var Sn=class extends N{_parse(e){this._def.coerce&&(e.data=!!e.data);let t=this._getType(e);if(t!==C.boolean){let t=this._getOrReturnCtx(e);return E(t,{code:w.invalid_type,expected:C.boolean,received:t.parsedType}),O}return k(e.data)}};Sn.create=e=>new Sn({typeName:R.ZodBoolean,coerce:e?.coerce||!1,...M(e)});var Cn=class e extends N{_parse(e){this._def.coerce&&(e.data=new Date(e.data));let t=this._getType(e);if(t!==C.date){let t=this._getOrReturnCtx(e);return E(t,{code:w.invalid_type,expected:C.date,received:t.parsedType}),O}if(Number.isNaN(e.data.getTime())){let t=this._getOrReturnCtx(e);return E(t,{code:w.invalid_date}),O}let n=new D,r;for(let t of this._def.checks)t.kind===`min`?e.data.getTime()<t.value&&(r=this._getOrReturnCtx(e,r),E(r,{code:w.too_small,message:t.message,inclusive:!0,exact:!1,minimum:t.value,type:`date`}),n.dirty()):t.kind===`max`?e.data.getTime()>t.value&&(r=this._getOrReturnCtx(e,r),E(r,{code:w.too_big,message:t.message,inclusive:!0,exact:!1,maximum:t.value,type:`date`}),n.dirty()):S.assertNever(t);return{status:n.value,value:new Date(e.data.getTime())}}_addCheck(t){return new e({...this._def,checks:[...this._def.checks,t]})}min(e,t){return this._addCheck({kind:`min`,value:e.getTime(),message:A.toString(t)})}max(e,t){return this._addCheck({kind:`max`,value:e.getTime(),message:A.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)t.kind===`min`&&(e===null||t.value>e)&&(e=t.value);return e==null?null:new Date(e)}get maxDate(){let e=null;for(let t of this._def.checks)t.kind===`max`&&(e===null||t.value<e)&&(e=t.value);return e==null?null:new Date(e)}};Cn.create=e=>new Cn({checks:[],coerce:e?.coerce||!1,typeName:R.ZodDate,...M(e)});var wn=class extends N{_parse(e){let t=this._getType(e);if(t!==C.symbol){let t=this._getOrReturnCtx(e);return E(t,{code:w.invalid_type,expected:C.symbol,received:t.parsedType}),O}return k(e.data)}};wn.create=e=>new wn({typeName:R.ZodSymbol,...M(e)});var Tn=class extends N{_parse(e){let t=this._getType(e);if(t!==C.undefined){let t=this._getOrReturnCtx(e);return E(t,{code:w.invalid_type,expected:C.undefined,received:t.parsedType}),O}return k(e.data)}};Tn.create=e=>new Tn({typeName:R.ZodUndefined,...M(e)});var En=class extends N{_parse(e){let t=this._getType(e);if(t!==C.null){let t=this._getOrReturnCtx(e);return E(t,{code:w.invalid_type,expected:C.null,received:t.parsedType}),O}return k(e.data)}};En.create=e=>new En({typeName:R.ZodNull,...M(e)});var Dn=class extends N{constructor(){super(...arguments),this._any=!0}_parse(e){return k(e.data)}};Dn.create=e=>new Dn({typeName:R.ZodAny,...M(e)});var On=class extends N{constructor(){super(...arguments),this._unknown=!0}_parse(e){return k(e.data)}};On.create=e=>new On({typeName:R.ZodUnknown,...M(e)});var kn=class extends N{_parse(e){let t=this._getOrReturnCtx(e);return E(t,{code:w.invalid_type,expected:C.never,received:t.parsedType}),O}};kn.create=e=>new kn({typeName:R.ZodNever,...M(e)});var An=class extends N{_parse(e){let t=this._getType(e);if(t!==C.undefined){let t=this._getOrReturnCtx(e);return E(t,{code:w.invalid_type,expected:C.void,received:t.parsedType}),O}return k(e.data)}};An.create=e=>new An({typeName:R.ZodVoid,...M(e)});var jn=class e extends N{_parse(e){let{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==C.array)return E(t,{code:w.invalid_type,expected:C.array,received:t.parsedType}),O;if(r.exactLength!==null){let e=t.data.length>r.exactLength.value,i=t.data.length<r.exactLength.value;(e||i)&&(E(t,{code:e?w.too_big:w.too_small,minimum:i?r.exactLength.value:void 0,maximum:e?r.exactLength.value:void 0,type:`array`,inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(r.minLength!==null&&t.data.length<r.minLength.value&&(E(t,{code:w.too_small,minimum:r.minLength.value,type:`array`,inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),r.maxLength!==null&&t.data.length>r.maxLength.value&&(E(t,{code:w.too_big,maximum:r.maxLength.value,type:`array`,inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((e,n)=>r.type._parseAsync(new j(t,e,t.path,n)))).then(e=>D.mergeArray(n,e));let i=[...t.data].map((e,n)=>r.type._parseSync(new j(t,e,t.path,n)));return D.mergeArray(n,i)}get element(){return this._def.type}min(t,n){return new e({...this._def,minLength:{value:t,message:A.toString(n)}})}max(t,n){return new e({...this._def,maxLength:{value:t,message:A.toString(n)}})}length(t,n){return new e({...this._def,exactLength:{value:t,message:A.toString(n)}})}nonempty(e){return this.min(1,e)}};jn.create=(e,t)=>new jn({type:e,minLength:null,maxLength:null,exactLength:null,typeName:R.ZodArray,...M(t)});function Mn(e){if(e instanceof P){let t={};for(let n in e.shape){let r=e.shape[n];t[n]=L.create(Mn(r))}return new P({...e._def,shape:()=>t})}else if(e instanceof jn)return new jn({...e._def,type:Mn(e.element)});else if(e instanceof L)return L.create(Mn(e.unwrap()));else if(e instanceof Jn)return Jn.create(Mn(e.unwrap()));else if(e instanceof Ln)return Ln.create(e.items.map(e=>Mn(e)));else return e}var P=class e extends N{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;let e=this._def.shape(),t=S.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){let t=this._getType(e);if(t!==C.object){let t=this._getOrReturnCtx(e);return E(t,{code:w.invalid_type,expected:C.object,received:t.parsedType}),O}let{status:n,ctx:r}=this._processInputParams(e),{shape:i,keys:a}=this._getCached(),o=[];if(!(this._def.catchall instanceof kn&&this._def.unknownKeys===`strip`))for(let e in r.data)a.includes(e)||o.push(e);let s=[];for(let e of a){let t=i[e],n=r.data[e];s.push({key:{status:`valid`,value:e},value:t._parse(new j(r,n,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof kn){let e=this._def.unknownKeys;if(e===`passthrough`)for(let e of o)s.push({key:{status:`valid`,value:e},value:{status:`valid`,value:r.data[e]}});else if(e===`strict`)o.length>0&&(E(r,{code:w.unrecognized_keys,keys:o}),n.dirty());else if(e!==`strip`)throw Error(`Internal ZodObject error: invalid unknownKeys value.`)}else{let e=this._def.catchall;for(let t of o){let n=r.data[t];s.push({key:{status:`valid`,value:t},value:e._parse(new j(r,n,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of s){let n=await t.key,r=await t.value;e.push({key:n,value:r,alwaysSet:t.alwaysSet})}return e}).then(e=>D.mergeObjectSync(n,e)):D.mergeObjectSync(n,s)}get shape(){return this._def.shape()}strict(t){return A.errToObj,new e({...this._def,unknownKeys:`strict`,...t===void 0?{}:{errorMap:(e,n)=>{let r=this._def.errorMap?.(e,n).message??n.defaultError;return e.code===`unrecognized_keys`?{message:A.errToObj(t).message??r}:{message:r}}}})}strip(){return new e({...this._def,unknownKeys:`strip`})}passthrough(){return new e({...this._def,unknownKeys:`passthrough`})}extend(t){return new e({...this._def,shape:()=>({...this._def.shape(),...t})})}merge(t){let n=new e({unknownKeys:t._def.unknownKeys,catchall:t._def.catchall,shape:()=>({...this._def.shape(),...t._def.shape()}),typeName:R.ZodObject});return n}setKey(e,t){return this.augment({[e]:t})}catchall(t){return new e({...this._def,catchall:t})}pick(t){let n={};for(let e of S.objectKeys(t))t[e]&&this.shape[e]&&(n[e]=this.shape[e]);return new e({...this._def,shape:()=>n})}omit(t){let n={};for(let e of S.objectKeys(this.shape))t[e]||(n[e]=this.shape[e]);return new e({...this._def,shape:()=>n})}deepPartial(){return Mn(this)}partial(t){let n={};for(let e of S.objectKeys(this.shape)){let r=this.shape[e];t&&!t[e]?n[e]=r:n[e]=r.optional()}return new e({...this._def,shape:()=>n})}required(t){let n={};for(let e of S.objectKeys(this.shape))if(t&&!t[e])n[e]=this.shape[e];else{let t=this.shape[e],r=t;for(;r instanceof L;)r=r._def.innerType;n[e]=r}return new e({...this._def,shape:()=>n})}keyof(){return Wn(S.objectKeys(this.shape))}};P.create=(e,t)=>new P({shape:()=>e,unknownKeys:`strip`,catchall:kn.create(),typeName:R.ZodObject,...M(t)}),P.strictCreate=(e,t)=>new P({shape:()=>e,unknownKeys:`strict`,catchall:kn.create(),typeName:R.ZodObject,...M(t)}),P.lazycreate=(e,t)=>new P({shape:e,unknownKeys:`strip`,catchall:kn.create(),typeName:R.ZodObject,...M(t)});var Nn=class extends N{_parse(e){let{ctx:t}=this._processInputParams(e),n=this._def.options;function r(e){for(let t of e)if(t.result.status===`valid`)return t.result;for(let n of e)if(n.result.status===`dirty`)return t.common.issues.push(...n.ctx.common.issues),n.result;let n=e.map(e=>new T(e.ctx.common.issues));return E(t,{code:w.invalid_union,unionErrors:n}),O}if(t.common.async)return Promise.all(n.map(async e=>{let n={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:n}),ctx:n}})).then(r);{let e,r=[];for(let i of n){let n={...t,common:{...t.common,issues:[]},parent:null},a=i._parseSync({data:t.data,path:t.path,parent:n});if(a.status===`valid`)return a;a.status===`dirty`&&!e&&(e={result:a,ctx:n}),n.common.issues.length&&r.push(n.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let i=r.map(e=>new T(e));return E(t,{code:w.invalid_union,unionErrors:i}),O}}get options(){return this._def.options}};Nn.create=(e,t)=>new Nn({options:e,typeName:R.ZodUnion,...M(t)});const F=e=>e instanceof Hn?F(e.schema):e instanceof I?F(e.innerType()):e instanceof Un?[e.value]:e instanceof Gn?e.options:e instanceof Kn?S.objectValues(e.enum):e instanceof Yn?F(e._def.innerType):e instanceof Tn?[void 0]:e instanceof En?[null]:e instanceof L?[void 0,...F(e.unwrap())]:e instanceof Jn?[null,...F(e.unwrap())]:e instanceof $n||e instanceof tr?F(e.unwrap()):e instanceof Xn?F(e._def.innerType):[];var Pn=class e extends N{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==C.object)return E(t,{code:w.invalid_type,expected:C.object,received:t.parsedType}),O;let n=this.discriminator,r=t.data[n],i=this.optionsMap.get(r);return i?t.common.async?i._parseAsync({data:t.data,path:t.path,parent:t}):i._parseSync({data:t.data,path:t.path,parent:t}):(E(t,{code:w.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),O)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(t,n,r){let i=new Map;for(let e of n){let n=F(e.shape[t]);if(!n.length)throw Error(`A discriminator value for key \`${t}\` could not be extracted from all schema options`);for(let r of n){if(i.has(r))throw Error(`Discriminator property ${String(t)} has duplicate value ${String(r)}`);i.set(r,e)}}return new e({typeName:R.ZodDiscriminatedUnion,discriminator:t,options:n,optionsMap:i,...M(r)})}};function Fn(e,t){let n=Ft(e),r=Ft(t);if(e===t)return{valid:!0,data:e};if(n===C.object&&r===C.object){let n=S.objectKeys(t),r=S.objectKeys(e).filter(e=>n.indexOf(e)!==-1),i={...e,...t};for(let n of r){let r=Fn(e[n],t[n]);if(!r.valid)return{valid:!1};i[n]=r.data}return{valid:!0,data:i}}else if(n===C.array&&r===C.array){if(e.length!==t.length)return{valid:!1};let n=[];for(let r=0;r<e.length;r++){let i=e[r],a=t[r],o=Fn(i,a);if(!o.valid)return{valid:!1};n.push(o.data)}return{valid:!0,data:n}}else if(n===C.date&&r===C.date&&+e==+t)return{valid:!0,data:e};else return{valid:!1}}var In=class extends N{_parse(e){let{status:t,ctx:n}=this._processInputParams(e),r=(e,r)=>{if(Ht(e)||Ht(r))return O;let i=Fn(e.value,r.value);return i.valid?((Ut(e)||Ut(r))&&t.dirty(),{status:t.value,value:i.data}):(E(n,{code:w.invalid_intersection_types}),O)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([e,t])=>r(e,t)):r(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}};In.create=(e,t,n)=>new In({left:e,right:t,typeName:R.ZodIntersection,...M(n)});var Ln=class e extends N{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==C.array)return E(n,{code:w.invalid_type,expected:C.array,received:n.parsedType}),O;if(n.data.length<this._def.items.length)return E(n,{code:w.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:`array`}),O;let r=this._def.rest;!r&&n.data.length>this._def.items.length&&(E(n,{code:w.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:`array`}),t.dirty());let i=[...n.data].map((e,t)=>{let r=this._def.items[t]||this._def.rest;return r?r._parse(new j(n,e,n.path,t)):null}).filter(e=>!!e);return n.common.async?Promise.all(i).then(e=>D.mergeArray(t,e)):D.mergeArray(t,i)}get items(){return this._def.items}rest(t){return new e({...this._def,rest:t})}};Ln.create=(e,t)=>{if(!Array.isArray(e))throw Error(`You must pass an array of schemas to z.tuple([ ... ])`);return new Ln({items:e,typeName:R.ZodTuple,rest:null,...M(t)})};var Rn=class e extends N{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==C.object)return E(n,{code:w.invalid_type,expected:C.object,received:n.parsedType}),O;let r=[],i=this._def.keyType,a=this._def.valueType;for(let e in n.data)r.push({key:i._parse(new j(n,e,n.path,e)),value:a._parse(new j(n,n.data[e],n.path,e)),alwaysSet:e in n.data});return n.common.async?D.mergeObjectAsync(t,r):D.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(t,n,r){return n instanceof N?new e({keyType:t,valueType:n,typeName:R.ZodRecord,...M(r)}):new e({keyType:vn.create(),valueType:t,typeName:R.ZodRecord,...M(n)})}},zn=class extends N{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==C.map)return E(n,{code:w.invalid_type,expected:C.map,received:n.parsedType}),O;let r=this._def.keyType,i=this._def.valueType,a=[...n.data.entries()].map(([e,t],a)=>({key:r._parse(new j(n,e,n.path,[a,`key`])),value:i._parse(new j(n,t,n.path,[a,`value`]))}));if(n.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let n of a){let r=await n.key,i=await n.value;if(r.status===`aborted`||i.status===`aborted`)return O;(r.status===`dirty`||i.status===`dirty`)&&t.dirty(),e.set(r.value,i.value)}return{status:t.value,value:e}})}else{let e=new Map;for(let n of a){let r=n.key,i=n.value;if(r.status===`aborted`||i.status===`aborted`)return O;(r.status===`dirty`||i.status===`dirty`)&&t.dirty(),e.set(r.value,i.value)}return{status:t.value,value:e}}}};zn.create=(e,t,n)=>new zn({valueType:t,keyType:e,typeName:R.ZodMap,...M(n)});var Bn=class e extends N{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==C.set)return E(n,{code:w.invalid_type,expected:C.set,received:n.parsedType}),O;let r=this._def;r.minSize!==null&&n.data.size<r.minSize.value&&(E(n,{code:w.too_small,minimum:r.minSize.value,type:`set`,inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),r.maxSize!==null&&n.data.size>r.maxSize.value&&(E(n,{code:w.too_big,maximum:r.maxSize.value,type:`set`,inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());let i=this._def.valueType;function a(e){let n=new Set;for(let r of e){if(r.status===`aborted`)return O;r.status===`dirty`&&t.dirty(),n.add(r.value)}return{status:t.value,value:n}}let o=[...n.data.values()].map((e,t)=>i._parse(new j(n,e,n.path,t)));return n.common.async?Promise.all(o).then(e=>a(e)):a(o)}min(t,n){return new e({...this._def,minSize:{value:t,message:A.toString(n)}})}max(t,n){return new e({...this._def,maxSize:{value:t,message:A.toString(n)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}};Bn.create=(e,t)=>new Bn({valueType:e,minSize:null,maxSize:null,typeName:R.ZodSet,...M(t)});var Vn=class e extends N{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==C.function)return E(t,{code:w.invalid_type,expected:C.function,received:t.parsedType}),O;function n(e,n){return Bt({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,zt(),Lt].filter(e=>!!e),issueData:{code:w.invalid_arguments,argumentsError:n}})}function r(e,n){return Bt({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,zt(),Lt].filter(e=>!!e),issueData:{code:w.invalid_return_type,returnTypeError:n}})}let i={errorMap:t.common.contextualErrorMap},a=t.data;if(this._def.returns instanceof qn){let e=this;return k(async function(...t){let o=new T([]),s=await e._def.args.parseAsync(t,i).catch(e=>{throw o.addIssue(n(t,e)),o}),c=await Reflect.apply(a,this,s),l=await e._def.returns._def.type.parseAsync(c,i).catch(e=>{throw o.addIssue(r(c,e)),o});return l})}else{let e=this;return k(function(...t){let o=e._def.args.safeParse(t,i);if(!o.success)throw new T([n(t,o.error)]);let s=Reflect.apply(a,this,o.data),c=e._def.returns.safeParse(s,i);if(!c.success)throw new T([r(s,c.error)]);return c.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...t){return new e({...this._def,args:Ln.create(t).rest(On.create())})}returns(t){return new e({...this._def,returns:t})}implement(e){let t=this.parse(e);return t}strictImplement(e){let t=this.parse(e);return t}static create(t,n,r){return new e({args:t||Ln.create([]).rest(On.create()),returns:n||On.create(),typeName:R.ZodFunction,...M(r)})}},Hn=class extends N{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e),n=this._def.getter();return n._parse({data:t.data,path:t.path,parent:t})}};Hn.create=(e,t)=>new Hn({getter:e,typeName:R.ZodLazy,...M(t)});var Un=class extends N{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return E(t,{received:t.data,code:w.invalid_literal,expected:this._def.value}),O}return{status:`valid`,value:e.data}}get value(){return this._def.value}};Un.create=(e,t)=>new Un({value:e,typeName:R.ZodLiteral,...M(t)});function Wn(e,t){return new Gn({values:e,typeName:R.ZodEnum,...M(t)})}var Gn=class e extends N{_parse(e){if(typeof e.data!=`string`){let t=this._getOrReturnCtx(e),n=this._def.values;return E(t,{expected:S.joinValues(n),received:t.parsedType,code:w.invalid_type}),O}if(this._cache||=new Set(this._def.values),!this._cache.has(e.data)){let t=this._getOrReturnCtx(e),n=this._def.values;return E(t,{received:t.data,code:w.invalid_enum_value,options:n}),O}return k(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(t,n=this._def){return e.create(t,{...this._def,...n})}exclude(t,n=this._def){return e.create(this.options.filter(e=>!t.includes(e)),{...this._def,...n})}};Gn.create=Wn;var Kn=class extends N{_parse(e){let t=S.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==C.string&&n.parsedType!==C.number){let e=S.objectValues(t);return E(n,{expected:S.joinValues(e),received:n.parsedType,code:w.invalid_type}),O}if(this._cache||=new Set(S.getValidEnumValues(this._def.values)),!this._cache.has(e.data)){let e=S.objectValues(t);return E(n,{received:n.data,code:w.invalid_enum_value,options:e}),O}return k(e.data)}get enum(){return this._def.values}};Kn.create=(e,t)=>new Kn({values:e,typeName:R.ZodNativeEnum,...M(t)});var qn=class extends N{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==C.promise&&t.common.async===!1)return E(t,{code:w.invalid_type,expected:C.promise,received:t.parsedType}),O;let n=t.parsedType===C.promise?t.data:Promise.resolve(t.data);return k(n.then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}};qn.create=(e,t)=>new qn({type:e,typeName:R.ZodPromise,...M(t)});var I=class extends N{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===R.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:n}=this._processInputParams(e),r=this._def.effect||null,i={addIssue:e=>{E(n,e),e.fatal?t.abort():t.dirty()},get path(){return n.path}};if(i.addIssue=i.addIssue.bind(i),r.type===`preprocess`){let e=r.transform(n.data,i);if(n.common.async)return Promise.resolve(e).then(async e=>{if(t.value===`aborted`)return O;let r=await this._def.schema._parseAsync({data:e,path:n.path,parent:n});return r.status===`aborted`?O:r.status===`dirty`||t.value===`dirty`?Vt(r.value):r});{if(t.value===`aborted`)return O;let r=this._def.schema._parseSync({data:e,path:n.path,parent:n});return r.status===`aborted`?O:r.status===`dirty`||t.value===`dirty`?Vt(r.value):r}}if(r.type===`refinement`){let e=e=>{let t=r.refinement(e,i);if(n.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error(`Async refinement encountered during synchronous parse operation. Use .parseAsync instead.`);return e};if(n.common.async===!1){let r=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return r.status===`aborted`?O:(r.status===`dirty`&&t.dirty(),e(r.value),{status:t.value,value:r.value})}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(n=>n.status===`aborted`?O:(n.status===`dirty`&&t.dirty(),e(n.value).then(()=>({status:t.value,value:n.value}))))}if(r.type===`transform`)if(n.common.async===!1){let e=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!Wt(e))return O;let a=r.transform(e.value,i);if(a instanceof Promise)throw Error(`Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.`);return{status:t.value,value:a}}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(e=>Wt(e)?Promise.resolve(r.transform(e.value,i)).then(e=>({status:t.value,value:e})):O);S.assertNever(r)}};I.create=(e,t,n)=>new I({schema:e,typeName:R.ZodEffects,effect:t,...M(n)}),I.createWithPreprocess=(e,t,n)=>new I({schema:t,effect:{type:`preprocess`,transform:e},typeName:R.ZodEffects,...M(n)});var L=class extends N{_parse(e){let t=this._getType(e);return t===C.undefined?k(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}};L.create=(e,t)=>new L({innerType:e,typeName:R.ZodOptional,...M(t)});var Jn=class extends N{_parse(e){let t=this._getType(e);return t===C.null?k(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}};Jn.create=(e,t)=>new Jn({innerType:e,typeName:R.ZodNullable,...M(t)});var Yn=class extends N{_parse(e){let{ctx:t}=this._processInputParams(e),n=t.data;return t.parsedType===C.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}};Yn.create=(e,t)=>new Yn({innerType:e,typeName:R.ZodDefault,defaultValue:typeof t.default==`function`?t.default:()=>t.default,...M(t)});var Xn=class extends N{_parse(e){let{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return Gt(r)?r.then(e=>({status:`valid`,value:e.status===`valid`?e.value:this._def.catchValue({get error(){return new T(n.common.issues)},input:n.data})})):{status:`valid`,value:r.status===`valid`?r.value:this._def.catchValue({get error(){return new T(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}};Xn.create=(e,t)=>new Xn({innerType:e,typeName:R.ZodCatch,catchValue:typeof t.catch==`function`?t.catch:()=>t.catch,...M(t)});var Zn=class extends N{_parse(e){let t=this._getType(e);if(t!==C.nan){let t=this._getOrReturnCtx(e);return E(t,{code:w.invalid_type,expected:C.nan,received:t.parsedType}),O}return{status:`valid`,value:e.data}}};Zn.create=e=>new Zn({typeName:R.ZodNaN,...M(e)});const Qn=Symbol(`zod_brand`);var $n=class extends N{_parse(e){let{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}},er=class e extends N{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.common.async){let e=async()=>{let e=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return e.status===`aborted`?O:e.status===`dirty`?(t.dirty(),Vt(e.value)):this._def.out._parseAsync({data:e.value,path:n.path,parent:n})};return e()}else{let e=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return e.status===`aborted`?O:e.status===`dirty`?(t.dirty(),{status:`dirty`,value:e.value}):this._def.out._parseSync({data:e.value,path:n.path,parent:n})}}static create(t,n){return new e({in:t,out:n,typeName:R.ZodPipeline})}},tr=class extends N{_parse(e){let t=this._def.innerType._parse(e),n=e=>(Wt(e)&&(e.value=Object.freeze(e.value)),e);return Gt(t)?t.then(e=>n(e)):n(t)}unwrap(){return this._def.innerType}};tr.create=(e,t)=>new tr({innerType:e,typeName:R.ZodReadonly,...M(t)});function nr(e,t){let n=typeof e==`function`?e(t):typeof e==`string`?{message:e}:e,r=typeof n==`string`?{message:n}:n;return r}function rr(e,t={},n){return e?Dn.create().superRefine((r,i)=>{let a=e(r);if(a instanceof Promise)return a.then(e=>{if(!e){let e=nr(t,r),a=e.fatal??n??!0;i.addIssue({code:`custom`,...e,fatal:a})}});if(!a){let e=nr(t,r),a=e.fatal??n??!0;i.addIssue({code:`custom`,...e,fatal:a})}}):Dn.create()}const ir={object:P.lazycreate};var R;(function(e){e.ZodString=`ZodString`,e.ZodNumber=`ZodNumber`,e.ZodNaN=`ZodNaN`,e.ZodBigInt=`ZodBigInt`,e.ZodBoolean=`ZodBoolean`,e.ZodDate=`ZodDate`,e.ZodSymbol=`ZodSymbol`,e.ZodUndefined=`ZodUndefined`,e.ZodNull=`ZodNull`,e.ZodAny=`ZodAny`,e.ZodUnknown=`ZodUnknown`,e.ZodNever=`ZodNever`,e.ZodVoid=`ZodVoid`,e.ZodArray=`ZodArray`,e.ZodObject=`ZodObject`,e.ZodUnion=`ZodUnion`,e.ZodDiscriminatedUnion=`ZodDiscriminatedUnion`,e.ZodIntersection=`ZodIntersection`,e.ZodTuple=`ZodTuple`,e.ZodRecord=`ZodRecord`,e.ZodMap=`ZodMap`,e.ZodSet=`ZodSet`,e.ZodFunction=`ZodFunction`,e.ZodLazy=`ZodLazy`,e.ZodLiteral=`ZodLiteral`,e.ZodEnum=`ZodEnum`,e.ZodEffects=`ZodEffects`,e.ZodNativeEnum=`ZodNativeEnum`,e.ZodOptional=`ZodOptional`,e.ZodNullable=`ZodNullable`,e.ZodDefault=`ZodDefault`,e.ZodCatch=`ZodCatch`,e.ZodPromise=`ZodPromise`,e.ZodBranded=`ZodBranded`,e.ZodPipeline=`ZodPipeline`,e.ZodReadonly=`ZodReadonly`})(R||={});const ar=(e,t={message:`Input not instance of ${e.name}`})=>rr(t=>t instanceof e,t),z=vn.create,or=bn.create,sr=Zn.create,cr=xn.create,lr=Sn.create,ur=Cn.create,dr=wn.create,fr=Tn.create,pr=En.create,mr=Dn.create,hr=On.create,gr=kn.create,_r=An.create,B=jn.create,V=P.create,vr=P.strictCreate,H=Nn.create,yr=Pn.create,br=In.create,xr=Ln.create,Sr=Rn.create,Cr=zn.create,wr=Bn.create,Tr=Vn.create,Er=Hn.create,U=Un.create,Dr=Gn.create,Or=Kn.create,kr=qn.create,Ar=I.create,W=L.create,jr=Jn.create,Mr=I.createWithPreprocess,Nr=er.create,Pr=Symbol(`Let zodToJsonSchema decide on which parser to use`),Fr={name:void 0,$refStrategy:`root`,basePath:[`#`],effectStrategy:`input`,pipeStrategy:`all`,dateStrategy:`format:date-time`,mapStrategy:`entries`,removeAdditionalStrategy:`passthrough`,allowedAdditionalProperties:!0,rejectedAdditionalProperties:!1,definitionPath:`definitions`,target:`jsonSchema7`,strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:`escape`,applyRegexFlags:!1,emailStrategy:`format:email`,base64Strategy:`contentEncoding:base64`,nameStrategy:`ref`,openAiAnyTypeName:`OpenAiAnyType`},Ir=e=>typeof e==`string`?{...Fr,name:e}:{...Fr,...e},Lr=e=>{let t=Ir(e),n=t.name===void 0?t.basePath:[...t.basePath,t.definitionPath,t.name];return{...t,flags:{hasReferencedOpenAiAnyType:!1},currentPath:n,propertyPath:void 0,seen:new Map(Object.entries(t.definitions).map(([e,n])=>[n._def,{def:n._def,path:[...t.basePath,t.definitionPath,e],jsonSchema:void 0}]))}};function Rr(e,t,n,r){r?.errorMessages&&n&&(e.errorMessage={...e.errorMessage,[t]:n})}function G(e,t,n,r,i){e[t]=n,Rr(e,t,r,i)}const zr=(e,t)=>{let n=0;for(;n<e.length&&n<t.length&&e[n]===t[n];n++);return[(e.length-n).toString(),...t.slice(n)].join(`/`)};function K(e){if(e.target!==`openAi`)return{};let t=[...e.basePath,e.definitionPath,e.openAiAnyTypeName];return e.flags.hasReferencedOpenAiAnyType=!0,{$ref:e.$refStrategy===`relative`?zr(t,e.currentPath):t.join(`/`)}}function Br(e,t){let n={type:`array`};return e.type?._def&&e.type?._def?.typeName!==R.ZodAny&&(n.items=X(e.type._def,{...t,currentPath:[...t.currentPath,`items`]})),e.minLength&&G(n,`minItems`,e.minLength.value,e.minLength.message,t),e.maxLength&&G(n,`maxItems`,e.maxLength.value,e.maxLength.message,t),e.exactLength&&(G(n,`minItems`,e.exactLength.value,e.exactLength.message,t),G(n,`maxItems`,e.exactLength.value,e.exactLength.message,t)),n}function Vr(e,t){let n={type:`integer`,format:`int64`};if(!e.checks)return n;for(let r of e.checks)switch(r.kind){case`min`:t.target===`jsonSchema7`?r.inclusive?G(n,`minimum`,r.value,r.message,t):G(n,`exclusiveMinimum`,r.value,r.message,t):(r.inclusive||(n.exclusiveMinimum=!0),G(n,`minimum`,r.value,r.message,t));break;case`max`:t.target===`jsonSchema7`?r.inclusive?G(n,`maximum`,r.value,r.message,t):G(n,`exclusiveMaximum`,r.value,r.message,t):(r.inclusive||(n.exclusiveMaximum=!0),G(n,`maximum`,r.value,r.message,t));break;case`multipleOf`:G(n,`multipleOf`,r.value,r.message,t);break}return n}function Hr(){return{type:`boolean`}}function Ur(e,t){return X(e.type._def,t)}const Wr=(e,t)=>X(e.innerType._def,t);function Gr(e,t,n){let r=n??t.dateStrategy;if(Array.isArray(r))return{anyOf:r.map((n,r)=>Gr(e,t,n))};switch(r){case`string`:case`format:date-time`:return{type:`string`,format:`date-time`};case`format:date`:return{type:`string`,format:`date`};case`integer`:return Kr(e,t)}}const Kr=(e,t)=>{let n={type:`integer`,format:`unix-time`};if(t.target===`openApi3`)return n;for(let r of e.checks)switch(r.kind){case`min`:G(n,`minimum`,r.value,r.message,t);break;case`max`:G(n,`maximum`,r.value,r.message,t);break}return n};function qr(e,t){return{...X(e.innerType._def,t),default:e.defaultValue()}}function Jr(e,t){return t.effectStrategy===`input`?X(e.schema._def,t):K(t)}function Yr(e){return{type:`string`,enum:Array.from(e.values)}}const Xr=e=>`type`in e&&e.type===`string`?!1:`allOf`in e;function Zr(e,t){let n=[X(e.left._def,{...t,currentPath:[...t.currentPath,`allOf`,`0`]}),X(e.right._def,{...t,currentPath:[...t.currentPath,`allOf`,`1`]})].filter(e=>!!e),r=t.target===`jsonSchema2019-09`?{unevaluatedProperties:!1}:void 0,i=[];return n.forEach(e=>{if(Xr(e))i.push(...e.allOf),e.unevaluatedProperties===void 0&&(r=void 0);else{let t=e;if(`additionalProperties`in e&&e.additionalProperties===!1){let{additionalProperties:n,...r}=e;t=r}else r=void 0;i.push(t)}}),i.length?{allOf:i,...r}:void 0}function Qr(e,t){let n=typeof e.value;return n!==`bigint`&&n!==`number`&&n!==`boolean`&&n!==`string`?{type:Array.isArray(e.value)?`array`:`object`}:t.target===`openApi3`?{type:n===`bigint`?`integer`:n,enum:[e.value]}:{type:n===`bigint`?`integer`:n,const:e.value}}let $r;const q={cuid:/^[cC][^\s-]{8,}$/,cuid2:/^[0-9a-z]+$/,ulid:/^[0-9A-HJKMNP-TV-Z]{26}$/,email:/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,emoji:()=>($r===void 0&&($r=RegExp(`^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$`,`u`)),$r),uuid:/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/,ipv4:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ipv4Cidr:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6:/^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,ipv6Cidr:/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64:/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64url:/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,nanoid:/^[a-zA-Z0-9_-]{21}$/,jwt:/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/};function ei(e,t){let n={type:`string`};if(e.checks)for(let r of e.checks)switch(r.kind){case`min`:G(n,`minLength`,typeof n.minLength==`number`?Math.max(n.minLength,r.value):r.value,r.message,t);break;case`max`:G(n,`maxLength`,typeof n.maxLength==`number`?Math.min(n.maxLength,r.value):r.value,r.message,t);break;case`email`:switch(t.emailStrategy){case`format:email`:J(n,`email`,r.message,t);break;case`format:idn-email`:J(n,`idn-email`,r.message,t);break;case`pattern:zod`:Y(n,q.email,r.message,t);break}break;case`url`:J(n,`uri`,r.message,t);break;case`uuid`:J(n,`uuid`,r.message,t);break;case`regex`:Y(n,r.regex,r.message,t);break;case`cuid`:Y(n,q.cuid,r.message,t);break;case`cuid2`:Y(n,q.cuid2,r.message,t);break;case`startsWith`:Y(n,RegExp(`^${ti(r.value,t)}`),r.message,t);break;case`endsWith`:Y(n,RegExp(`${ti(r.value,t)}$`),r.message,t);break;case`datetime`:J(n,`date-time`,r.message,t);break;case`date`:J(n,`date`,r.message,t);break;case`time`:J(n,`time`,r.message,t);break;case`duration`:J(n,`duration`,r.message,t);break;case`length`:G(n,`minLength`,typeof n.minLength==`number`?Math.max(n.minLength,r.value):r.value,r.message,t),G(n,`maxLength`,typeof n.maxLength==`number`?Math.min(n.maxLength,r.value):r.value,r.message,t);break;case`includes`:Y(n,RegExp(ti(r.value,t)),r.message,t);break;case`ip`:r.version!==`v6`&&J(n,`ipv4`,r.message,t),r.version!==`v4`&&J(n,`ipv6`,r.message,t);break;case`base64url`:Y(n,q.base64url,r.message,t);break;case`jwt`:Y(n,q.jwt,r.message,t);break;case`cidr`:r.version!==`v6`&&Y(n,q.ipv4Cidr,r.message,t),r.version!==`v4`&&Y(n,q.ipv6Cidr,r.message,t);break;case`emoji`:Y(n,q.emoji(),r.message,t);break;case`ulid`:Y(n,q.ulid,r.message,t);break;case`base64`:switch(t.base64Strategy){case`format:binary`:J(n,`binary`,r.message,t);break;case`contentEncoding:base64`:G(n,`contentEncoding`,`base64`,r.message,t);break;case`pattern:zod`:Y(n,q.base64,r.message,t);break}break;case`nanoid`:Y(n,q.nanoid,r.message,t);case`toLowerCase`:case`toUpperCase`:case`trim`:break;default:(e=>{})(r)}return n}function ti(e,t){return t.patternStrategy===`escape`?ri(e):e}const ni=new Set(`ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789`);function ri(e){let t=``;for(let n=0;n<e.length;n++)ni.has(e[n])||(t+=`\\`),t+=e[n];return t}function J(e,t,n,r){e.format||e.anyOf?.some(e=>e.format)?(e.anyOf||=[],e.format&&(e.anyOf.push({format:e.format,...e.errorMessage&&r.errorMessages&&{errorMessage:{format:e.errorMessage.format}}}),delete e.format,e.errorMessage&&(delete e.errorMessage.format,Object.keys(e.errorMessage).length===0&&delete e.errorMessage)),e.anyOf.push({format:t,...n&&r.errorMessages&&{errorMessage:{format:n}}})):G(e,`format`,t,n,r)}function Y(e,t,n,r){e.pattern||e.allOf?.some(e=>e.pattern)?(e.allOf||=[],e.pattern&&(e.allOf.push({pattern:e.pattern,...e.errorMessage&&r.errorMessages&&{errorMessage:{pattern:e.errorMessage.pattern}}}),delete e.pattern,e.errorMessage&&(delete e.errorMessage.pattern,Object.keys(e.errorMessage).length===0&&delete e.errorMessage)),e.allOf.push({pattern:ii(t,r),...n&&r.errorMessages&&{errorMessage:{pattern:n}}})):G(e,`pattern`,ii(t,r),n,r)}function ii(e,t){if(!t.applyRegexFlags||!e.flags)return e.source;let n={i:e.flags.includes(`i`),m:e.flags.includes(`m`),s:e.flags.includes(`s`)},r=n.i?e.source.toLowerCase():e.source,i=``,a=!1,o=!1,s=!1;for(let e=0;e<r.length;e++){if(a){i+=r[e],a=!1;continue}if(n.i){if(o){if(r[e].match(/[a-z]/)){s?(i+=r[e],i+=`${r[e-2]}-${r[e]}`.toUpperCase(),s=!1):r[e+1]===`-`&&r[e+2]?.match(/[a-z]/)?(i+=r[e],s=!0):i+=`${r[e]}${r[e].toUpperCase()}`;continue}}else if(r[e].match(/[a-z]/)){i+=`[${r[e]}${r[e].toUpperCase()}]`;continue}}if(n.m){if(r[e]===`^`){i+=`(^|(?<=[\r
]))`;continue}else if(r[e]===`$`){i+=`($|(?=[\r
]))`;continue}}if(n.s&&r[e]===`.`){i+=o?`${r[e]}\r\n`:`[${r[e]}\r\n]`;continue}i+=r[e],r[e]===`\\`?a=!0:o&&r[e]===`]`?o=!1:!o&&r[e]===`[`&&(o=!0)}try{new RegExp(i)}catch{return console.warn(`Could not convert regex pattern at ${t.currentPath.join(`/`)} to a flag-independent form! Falling back to the flag-ignorant source`),e.source}return i}function ai(e,t){if(t.target===`openAi`&&console.warn(`Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead.`),t.target===`openApi3`&&e.keyType?._def.typeName===R.ZodEnum)return{type:`object`,required:e.keyType._def.values,properties:e.keyType._def.values.reduce((n,r)=>({...n,[r]:X(e.valueType._def,{...t,currentPath:[...t.currentPath,`properties`,r]})??K(t)}),{}),additionalProperties:t.rejectedAdditionalProperties};let n={type:`object`,additionalProperties:X(e.valueType._def,{...t,currentPath:[...t.currentPath,`additionalProperties`]})??t.allowedAdditionalProperties};if(t.target===`openApi3`)return n;if(e.keyType?._def.typeName===R.ZodString&&e.keyType._def.checks?.length){let{type:r,...i}=ei(e.keyType._def,t);return{...n,propertyNames:i}}else if(e.keyType?._def.typeName===R.ZodEnum)return{...n,propertyNames:{enum:e.keyType._def.values}};else if(e.keyType?._def.typeName===R.ZodBranded&&e.keyType._def.type._def.typeName===R.ZodString&&e.keyType._def.type._def.checks?.length){let{type:r,...i}=Ur(e.keyType._def,t);return{...n,propertyNames:i}}return n}function oi(e,t){if(t.mapStrategy===`record`)return ai(e,t);let n=X(e.keyType._def,{...t,currentPath:[...t.currentPath,`items`,`items`,`0`]})||K(t),r=X(e.valueType._def,{...t,currentPath:[...t.currentPath,`items`,`items`,`1`]})||K(t);return{type:`array`,maxItems:125,items:{type:`array`,items:[n,r],minItems:2,maxItems:2}}}function si(e){let t=e.values,n=Object.keys(e.values).filter(e=>typeof t[t[e]]!=`number`),r=n.map(e=>t[e]),i=Array.from(new Set(r.map(e=>typeof e)));return{type:i.length===1?i[0]===`string`?`string`:`number`:[`string`,`number`],enum:r}}function ci(e){return e.target===`openAi`?void 0:{not:K({...e,currentPath:[...e.currentPath,`not`]})}}function li(e){return e.target===`openApi3`?{enum:[`null`],nullable:!0}:{type:`null`}}const ui={ZodString:`string`,ZodNumber:`number`,ZodBigInt:`integer`,ZodBoolean:`boolean`,ZodNull:`null`};function di(e,t){if(t.target===`openApi3`)return fi(e,t);let n=e.options instanceof Map?Array.from(e.options.values()):e.options;if(n.every(e=>e._def.typeName in ui&&(!e._def.checks||!e._def.checks.length))){let e=n.reduce((e,t)=>{let n=ui[t._def.typeName];return n&&!e.includes(n)?[...e,n]:e},[]);return{type:e.length>1?e:e[0]}}else if(n.every(e=>e._def.typeName===`ZodLiteral`&&!e.description)){let e=n.reduce((e,t)=>{let n=typeof t._def.value;switch(n){case`string`:case`number`:case`boolean`:return[...e,n];case`bigint`:return[...e,`integer`];case`object`:if(t._def.value===null)return[...e,`null`];case`symbol`:case`undefined`:case`function`:default:return e}},[]);if(e.length===n.length){let t=e.filter((e,t,n)=>n.indexOf(e)===t);return{type:t.length>1?t:t[0],enum:n.reduce((e,t)=>e.includes(t._def.value)?e:[...e,t._def.value],[])}}}else if(n.every(e=>e._def.typeName===`ZodEnum`))return{type:`string`,enum:n.reduce((e,t)=>[...e,...t._def.values.filter(t=>!e.includes(t))],[])};return fi(e,t)}const fi=(e,t)=>{let n=(e.options instanceof Map?Array.from(e.options.values()):e.options).map((e,n)=>X(e._def,{...t,currentPath:[...t.currentPath,`anyOf`,`${n}`]})).filter(e=>!!e&&(!t.strictUnions||typeof e==`object`&&Object.keys(e).length>0));return n.length?{anyOf:n}:void 0};function pi(e,t){if([`ZodString`,`ZodNumber`,`ZodBigInt`,`ZodBoolean`,`ZodNull`].includes(e.innerType._def.typeName)&&(!e.innerType._def.checks||!e.innerType._def.checks.length))return t.target===`openApi3`?{type:ui[e.innerType._def.typeName],nullable:!0}:{type:[ui[e.innerType._def.typeName],`null`]};if(t.target===`openApi3`){let n=X(e.innerType._def,{...t,currentPath:[...t.currentPath]});return n&&`$ref`in n?{allOf:[n],nullable:!0}:n&&{...n,nullable:!0}}let n=X(e.innerType._def,{...t,currentPath:[...t.currentPath,`anyOf`,`0`]});return n&&{anyOf:[n,{type:`null`}]}}function mi(e,t){let n={type:`number`};if(!e.checks)return n;for(let r of e.checks)switch(r.kind){case`int`:n.type=`integer`,Rr(n,`type`,r.message,t);break;case`min`:t.target===`jsonSchema7`?r.inclusive?G(n,`minimum`,r.value,r.message,t):G(n,`exclusiveMinimum`,r.value,r.message,t):(r.inclusive||(n.exclusiveMinimum=!0),G(n,`minimum`,r.value,r.message,t));break;case`max`:t.target===`jsonSchema7`?r.inclusive?G(n,`maximum`,r.value,r.message,t):G(n,`exclusiveMaximum`,r.value,r.message,t):(r.inclusive||(n.exclusiveMaximum=!0),G(n,`maximum`,r.value,r.message,t));break;case`multipleOf`:G(n,`multipleOf`,r.value,r.message,t);break}return n}function hi(e,t){let n=t.target===`openAi`,r={type:`object`,properties:{}},i=[],a=e.shape();for(let e in a){let o=a[e];if(o===void 0||o._def===void 0)continue;let s=_i(o);s&&n&&(o._def.typeName===`ZodOptional`&&(o=o._def.innerType),o.isNullable()||(o=o.nullable()),s=!1);let c=X(o._def,{...t,currentPath:[...t.currentPath,`properties`,e],propertyPath:[...t.currentPath,`properties`,e]});if(c===void 0)continue;r.properties[e]=c,s||i.push(e)}i.length&&(r.required=i);let o=gi(e,t);return o!==void 0&&(r.additionalProperties=o),r}function gi(e,t){if(e.catchall._def.typeName!==`ZodNever`)return X(e.catchall._def,{...t,currentPath:[...t.currentPath,`additionalProperties`]});switch(e.unknownKeys){case`passthrough`:return t.allowedAdditionalProperties;case`strict`:return t.rejectedAdditionalProperties;case`strip`:return t.removeAdditionalStrategy===`strict`?t.allowedAdditionalProperties:t.rejectedAdditionalProperties}}function _i(e){try{return e.isOptional()}catch{return!0}}const vi=(e,t)=>{if(t.currentPath.toString()===t.propertyPath?.toString())return X(e.innerType._def,t);let n=X(e.innerType._def,{...t,currentPath:[...t.currentPath,`anyOf`,`1`]});return n?{anyOf:[{not:K(t)},n]}:K(t)},yi=(e,t)=>{if(t.pipeStrategy===`input`)return X(e.in._def,t);if(t.pipeStrategy===`output`)return X(e.out._def,t);let n=X(e.in._def,{...t,currentPath:[...t.currentPath,`allOf`,`0`]}),r=X(e.out._def,{...t,currentPath:[...t.currentPath,`allOf`,n?`1`:`0`]});return{allOf:[n,r].filter(e=>e!==void 0)}};function bi(e,t){return X(e.type._def,t)}function xi(e,t){let n=X(e.valueType._def,{...t,currentPath:[...t.currentPath,`items`]}),r={type:`array`,uniqueItems:!0,items:n};return e.minSize&&G(r,`minItems`,e.minSize.value,e.minSize.message,t),e.maxSize&&G(r,`maxItems`,e.maxSize.value,e.maxSize.message,t),r}function Si(e,t){return e.rest?{type:`array`,minItems:e.items.length,items:e.items.map((e,n)=>X(e._def,{...t,currentPath:[...t.currentPath,`items`,`${n}`]})).reduce((e,t)=>t===void 0?e:[...e,t],[]),additionalItems:X(e.rest._def,{...t,currentPath:[...t.currentPath,`additionalItems`]})}:{type:`array`,minItems:e.items.length,maxItems:e.items.length,items:e.items.map((e,n)=>X(e._def,{...t,currentPath:[...t.currentPath,`items`,`${n}`]})).reduce((e,t)=>t===void 0?e:[...e,t],[])}}function Ci(e){return{not:K(e)}}function wi(e){return K(e)}const Ti=(e,t)=>X(e.innerType._def,t),Ei=(e,t,n)=>{switch(t){case R.ZodString:return ei(e,n);case R.ZodNumber:return mi(e,n);case R.ZodObject:return hi(e,n);case R.ZodBigInt:return Vr(e,n);case R.ZodBoolean:return Hr();case R.ZodDate:return Gr(e,n);case R.ZodUndefined:return Ci(n);case R.ZodNull:return li(n);case R.ZodArray:return Br(e,n);case R.ZodUnion:case R.ZodDiscriminatedUnion:return di(e,n);case R.ZodIntersection:return Zr(e,n);case R.ZodTuple:return Si(e,n);case R.ZodRecord:return ai(e,n);case R.ZodLiteral:return Qr(e,n);case R.ZodEnum:return Yr(e);case R.ZodNativeEnum:return si(e);case R.ZodNullable:return pi(e,n);case R.ZodOptional:return vi(e,n);case R.ZodMap:return oi(e,n);case R.ZodSet:return xi(e,n);case R.ZodLazy:return()=>e.getter()._def;case R.ZodPromise:return bi(e,n);case R.ZodNaN:case R.ZodNever:return ci(n);case R.ZodEffects:return Jr(e,n);case R.ZodAny:return K(n);case R.ZodUnknown:return wi(n);case R.ZodDefault:return qr(e,n);case R.ZodBranded:return Ur(e,n);case R.ZodReadonly:return Ti(e,n);case R.ZodCatch:return Wr(e,n);case R.ZodPipeline:return yi(e,n);case R.ZodFunction:case R.ZodVoid:case R.ZodSymbol:return;default:return(e=>void 0)(t)}};function X(e,t,n=!1){let r=t.seen.get(e);if(t.override){let i=t.override?.(e,t,r,n);if(i!==Pr)return i}if(r&&!n){let e=Di(r,t);if(e!==void 0)return e}let i={def:e,path:t.currentPath,jsonSchema:void 0};t.seen.set(e,i);let a=Ei(e,e.typeName,t),o=typeof a==`function`?X(a(),t):a;if(o&&Oi(e,t,o),t.postProcess){let n=t.postProcess(o,e,t);return i.jsonSchema=o,n}return i.jsonSchema=o,o}const Di=(e,t)=>{switch(t.$refStrategy){case`root`:return{$ref:e.path.join(`/`)};case`relative`:return{$ref:zr(t.currentPath,e.path)};case`none`:case`seen`:return e.path.length<t.currentPath.length&&e.path.every((e,n)=>t.currentPath[n]===e)?(console.warn(`Recursive reference detected at ${t.currentPath.join(`/`)}! Defaulting to any`),K(t)):t.$refStrategy===`seen`?K(t):void 0}},Oi=(e,t,n)=>(e.description&&(n.description=e.description,t.markdownDescription&&(n.markdownDescription=e.description)),n),ki=(e,t)=>{let n=Lr(t),r=typeof t==`object`&&t.definitions?Object.entries(t.definitions).reduce((e,[t,r])=>({...e,[t]:X(r._def,{...n,currentPath:[...n.basePath,n.definitionPath,t]},!0)??K(n)}),{}):void 0,i=typeof t==`string`?t:t?.nameStrategy===`title`?void 0:t?.name,a=X(e._def,i===void 0?n:{...n,currentPath:[...n.basePath,n.definitionPath,i]},!1)??K(n),o=typeof t==`object`&&t.name!==void 0&&t.nameStrategy===`title`?t.name:void 0;o!==void 0&&(a.title=o),n.flags.hasReferencedOpenAiAnyType&&(r||={},r[n.openAiAnyTypeName]||(r[n.openAiAnyTypeName]={type:[`string`,`number`,`integer`,`boolean`,`array`,`null`],items:{$ref:n.$refStrategy===`relative`?`1`:[...n.basePath,n.definitionPath,n.openAiAnyTypeName].join(`/`)}}));let s=i===void 0?r?{...a,[n.definitionPath]:r}:a:{$ref:[...n.$refStrategy===`relative`?[]:n.basePath,n.definitionPath,i].join(`/`),[n.definitionPath]:{...r,[i]:a}};return n.target===`jsonSchema7`?s.$schema=`http://json-schema.org/draft-07/schema#`:(n.target===`jsonSchema2019-09`||n.target===`openAi`)&&(s.$schema=`https://json-schema.org/draft/2019-09/schema#`),n.target===`openAi`&&(`anyOf`in s||`oneOf`in s||`allOf`in s||`type`in s&&Array.isArray(s.type))&&console.warn(`Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property.`),s};var Ai=ki,ji={code:`0`,name:`text`,parse:e=>{if(typeof e!=`string`)throw Error(`"text" parts expect a string value.`);return{type:`text`,value:e}}},Mi={code:`3`,name:`error`,parse:e=>{if(typeof e!=`string`)throw Error(`"error" parts expect a string value.`);return{type:`error`,value:e}}},Ni={code:`4`,name:`assistant_message`,parse:e=>{if(typeof e!=`object`||!e||!(`id`in e)||!(`role`in e)||!(`content`in e)||typeof e.id!=`string`||typeof e.role!=`string`||e.role!==`assistant`||!Array.isArray(e.content)||!e.content.every(e=>typeof e==`object`&&!!e&&`type`in e&&e.type===`text`&&`text`in e&&e.text!=null&&typeof e.text==`object`&&`value`in e.text&&typeof e.text.value==`string`))throw Error(`"assistant_message" parts expect an object with an "id", "role", and "content" property.`);return{type:`assistant_message`,value:e}}},Pi={code:`5`,name:`assistant_control_data`,parse:e=>{if(typeof e!=`object`||!e||!(`threadId`in e)||!(`messageId`in e)||typeof e.threadId!=`string`||typeof e.messageId!=`string`)throw Error(`"assistant_control_data" parts expect an object with a "threadId" and "messageId" property.`);return{type:`assistant_control_data`,value:{threadId:e.threadId,messageId:e.messageId}}}},Fi={code:`6`,name:`data_message`,parse:e=>{if(typeof e!=`object`||!e||!(`role`in e)||!(`data`in e)||typeof e.role!=`string`||e.role!==`data`)throw Error(`"data_message" parts expect an object with a "role" and "data" property.`);return{type:`data_message`,value:e}}},Ii=[ji,Mi,Ni,Pi,Fi],Li={[ji.code]:ji,[Mi.code]:Mi,[Ni.code]:Ni,[Pi.code]:Pi,[Fi.code]:Fi},Ri={[ji.name]:ji.code,[Mi.name]:Mi.code,[Ni.name]:Ni.code,[Pi.name]:Pi.code,[Fi.name]:Fi.code},zi=Ii.map(e=>e.code);function Bi(e){let t=[`ROOT`],n=-1,r=null;function i(e,i,a){switch(e){case`"`:n=i,t.pop(),t.push(a),t.push(`INSIDE_STRING`);break;case`f`:case`t`:case`n`:n=i,r=i,t.pop(),t.push(a),t.push(`INSIDE_LITERAL`);break;case`-`:t.pop(),t.push(a),t.push(`INSIDE_NUMBER`);break;case`0`:case`1`:case`2`:case`3`:case`4`:case`5`:case`6`:case`7`:case`8`:case`9`:n=i,t.pop(),t.push(a),t.push(`INSIDE_NUMBER`);break;case`{`:n=i,t.pop(),t.push(a),t.push(`INSIDE_OBJECT_START`);break;case`[`:n=i,t.pop(),t.push(a),t.push(`INSIDE_ARRAY_START`);break}}function a(e,r){switch(e){case`,`:t.pop(),t.push(`INSIDE_OBJECT_AFTER_COMMA`);break;case`}`:n=r,t.pop();break}}function o(e,r){switch(e){case`,`:t.pop(),t.push(`INSIDE_ARRAY_AFTER_COMMA`);break;case`]`:n=r,t.pop();break}}for(let s=0;s<e.length;s++){let c=e[s],l=t[t.length-1];switch(l){case`ROOT`:i(c,s,`FINISH`);break;case`INSIDE_OBJECT_START`:switch(c){case`"`:t.pop(),t.push(`INSIDE_OBJECT_KEY`);break;case`}`:n=s,t.pop();break}break;case`INSIDE_OBJECT_AFTER_COMMA`:switch(c){case`"`:t.pop(),t.push(`INSIDE_OBJECT_KEY`);break}break;case`INSIDE_OBJECT_KEY`:switch(c){case`"`:t.pop(),t.push(`INSIDE_OBJECT_AFTER_KEY`);break}break;case`INSIDE_OBJECT_AFTER_KEY`:switch(c){case`:`:t.pop(),t.push(`INSIDE_OBJECT_BEFORE_VALUE`);break}break;case`INSIDE_OBJECT_BEFORE_VALUE`:i(c,s,`INSIDE_OBJECT_AFTER_VALUE`);break;case`INSIDE_OBJECT_AFTER_VALUE`:a(c,s);break;case`INSIDE_STRING`:switch(c){case`"`:t.pop(),n=s;break;case`\\`:t.push(`INSIDE_STRING_ESCAPE`);break;default:n=s}break;case`INSIDE_ARRAY_START`:switch(c){case`]`:n=s,t.pop();break;default:n=s,i(c,s,`INSIDE_ARRAY_AFTER_VALUE`);break}break;case`INSIDE_ARRAY_AFTER_VALUE`:switch(c){case`,`:t.pop(),t.push(`INSIDE_ARRAY_AFTER_COMMA`);break;case`]`:n=s,t.pop();break;default:n=s;break}break;case`INSIDE_ARRAY_AFTER_COMMA`:i(c,s,`INSIDE_ARRAY_AFTER_VALUE`);break;case`INSIDE_STRING_ESCAPE`:t.pop(),n=s;break;case`INSIDE_NUMBER`:switch(c){case`0`:case`1`:case`2`:case`3`:case`4`:case`5`:case`6`:case`7`:case`8`:case`9`:n=s;break;case`e`:case`E`:case`-`:case`.`:break;case`,`:t.pop(),t[t.length-1]===`INSIDE_ARRAY_AFTER_VALUE`&&o(c,s),t[t.length-1]===`INSIDE_OBJECT_AFTER_VALUE`&&a(c,s);break;case`}`:t.pop(),t[t.length-1]===`INSIDE_OBJECT_AFTER_VALUE`&&a(c,s);break;case`]`:t.pop(),t[t.length-1]===`INSIDE_ARRAY_AFTER_VALUE`&&o(c,s);break;default:t.pop();break}break;case`INSIDE_LITERAL`:{let i=e.substring(r,s+1);!`false`.startsWith(i)&&!`true`.startsWith(i)&&!`null`.startsWith(i)?(t.pop(),t[t.length-1]===`INSIDE_OBJECT_AFTER_VALUE`?a(c,s):t[t.length-1]===`INSIDE_ARRAY_AFTER_VALUE`&&o(c,s)):n=s;break}}}let s=e.slice(0,n+1);for(let n=t.length-1;n>=0;n--){let i=t[n];switch(i){case`INSIDE_STRING`:s+=`"`;break;case`INSIDE_OBJECT_KEY`:case`INSIDE_OBJECT_AFTER_KEY`:case`INSIDE_OBJECT_AFTER_COMMA`:case`INSIDE_OBJECT_START`:case`INSIDE_OBJECT_BEFORE_VALUE`:case`INSIDE_OBJECT_AFTER_VALUE`:s+=`}`;break;case`INSIDE_ARRAY_START`:case`INSIDE_ARRAY_AFTER_COMMA`:case`INSIDE_ARRAY_AFTER_VALUE`:s+=`]`;break;case`INSIDE_LITERAL`:{let t=e.substring(r,e.length);`true`.startsWith(t)?s+=`true`.slice(t.length):`false`.startsWith(t)?s+=`false`.slice(t.length):`null`.startsWith(t)&&(s+=`null`.slice(t.length))}}}return s}function Vi(e){if(e===void 0)return{value:void 0,state:`undefined-input`};let t=vt({text:e});return t.success?{value:t.value,state:`successful-parse`}:(t=vt({text:Bi(e)}),t.success?{value:t.value,state:`repaired-parse`}:{value:void 0,state:`failed-parse`})}var Hi={code:`0`,name:`text`,parse:e=>{if(typeof e!=`string`)throw Error(`"text" parts expect a string value.`);return{type:`text`,value:e}}},Ui={code:`2`,name:`data`,parse:e=>{if(!Array.isArray(e))throw Error(`"data" parts expect an array value.`);return{type:`data`,value:e}}},Wi={code:`3`,name:`error`,parse:e=>{if(typeof e!=`string`)throw Error(`"error" parts expect a string value.`);return{type:`error`,value:e}}},Gi={code:`8`,name:`message_annotations`,parse:e=>{if(!Array.isArray(e))throw Error(`"message_annotations" parts expect an array value.`);return{type:`message_annotations`,value:e}}},Ki={code:`9`,name:`tool_call`,parse:e=>{if(typeof e!=`object`||!e||!(`toolCallId`in e)||typeof e.toolCallId!=`string`||!(`toolName`in e)||typeof e.toolName!=`string`||!(`args`in e)||typeof e.args!=`object`)throw Error(`"tool_call" parts expect an object with a "toolCallId", "toolName", and "args" property.`);return{type:`tool_call`,value:e}}},qi={code:`a`,name:`tool_result`,parse:e=>{if(typeof e!=`object`||!e||!(`toolCallId`in e)||typeof e.toolCallId!=`string`||!(`result`in e))throw Error(`"tool_result" parts expect an object with a "toolCallId" and a "result" property.`);return{type:`tool_result`,value:e}}},Ji={code:`b`,name:`tool_call_streaming_start`,parse:e=>{if(typeof e!=`object`||!e||!(`toolCallId`in e)||typeof e.toolCallId!=`string`||!(`toolName`in e)||typeof e.toolName!=`string`)throw Error(`"tool_call_streaming_start" parts expect an object with a "toolCallId" and "toolName" property.`);return{type:`tool_call_streaming_start`,value:e}}},Yi={code:`c`,name:`tool_call_delta`,parse:e=>{if(typeof e!=`object`||!e||!(`toolCallId`in e)||typeof e.toolCallId!=`string`||!(`argsTextDelta`in e)||typeof e.argsTextDelta!=`string`)throw Error(`"tool_call_delta" parts expect an object with a "toolCallId" and "argsTextDelta" property.`);return{type:`tool_call_delta`,value:e}}},Xi={code:`d`,name:`finish_message`,parse:e=>{if(typeof e!=`object`||!e||!(`finishReason`in e)||typeof e.finishReason!=`string`)throw Error(`"finish_message" parts expect an object with a "finishReason" property.`);let t={finishReason:e.finishReason};return`usage`in e&&e.usage!=null&&typeof e.usage==`object`&&`promptTokens`in e.usage&&`completionTokens`in e.usage&&(t.usage={promptTokens:typeof e.usage.promptTokens==`number`?e.usage.promptTokens:NaN,completionTokens:typeof e.usage.completionTokens==`number`?e.usage.completionTokens:NaN}),{type:`finish_message`,value:t}}},Zi={code:`e`,name:`finish_step`,parse:e=>{if(typeof e!=`object`||!e||!(`finishReason`in e)||typeof e.finishReason!=`string`)throw Error(`"finish_step" parts expect an object with a "finishReason" property.`);let t={finishReason:e.finishReason,isContinued:!1};return`usage`in e&&e.usage!=null&&typeof e.usage==`object`&&`promptTokens`in e.usage&&`completionTokens`in e.usage&&(t.usage={promptTokens:typeof e.usage.promptTokens==`number`?e.usage.promptTokens:NaN,completionTokens:typeof e.usage.completionTokens==`number`?e.usage.completionTokens:NaN}),`isContinued`in e&&typeof e.isContinued==`boolean`&&(t.isContinued=e.isContinued),{type:`finish_step`,value:t}}},Qi={code:`f`,name:`start_step`,parse:e=>{if(typeof e!=`object`||!e||!(`messageId`in e)||typeof e.messageId!=`string`)throw Error(`"start_step" parts expect an object with an "id" property.`);return{type:`start_step`,value:{messageId:e.messageId}}}},$i={code:`g`,name:`reasoning`,parse:e=>{if(typeof e!=`string`)throw Error(`"reasoning" parts expect a string value.`);return{type:`reasoning`,value:e}}},ea={code:`h`,name:`source`,parse:e=>{if(typeof e!=`object`||!e)throw Error(`"source" parts expect a Source object.`);return{type:`source`,value:e}}},ta={code:`i`,name:`redacted_reasoning`,parse:e=>{if(typeof e!=`object`||!e||!(`data`in e)||typeof e.data!=`string`)throw Error(`"redacted_reasoning" parts expect an object with a "data" property.`);return{type:`redacted_reasoning`,value:{data:e.data}}}},na={code:`j`,name:`reasoning_signature`,parse:e=>{if(typeof e!=`object`||!e||!(`signature`in e)||typeof e.signature!=`string`)throw Error(`"reasoning_signature" parts expect an object with a "signature" property.`);return{type:`reasoning_signature`,value:{signature:e.signature}}}},ra={code:`k`,name:`file`,parse:e=>{if(typeof e!=`object`||!e||!(`data`in e)||typeof e.data!=`string`||!(`mimeType`in e)||typeof e.mimeType!=`string`)throw Error(`"file" parts expect an object with a "data" and "mimeType" property.`);return{type:`file`,value:e}}},ia=[Hi,Ui,Wi,Gi,Ki,qi,Ji,Yi,Xi,Zi,Qi,$i,ea,ta,na,ra],aa=Object.fromEntries(ia.map(e=>[e.code,e])),oa=Object.fromEntries(ia.map(e=>[e.name,e.code])),sa=ia.map(e=>e.code);function ca(e,t){let n=ia.find(t=>t.name===e);if(!n)throw Error(`Invalid stream part type: ${e}`);return`${n.code}:${JSON.stringify(t)}
`}var la=10,ua=10;function da(e,t){var n;let r=(n=t?.useReferences)??!1;return pa(Ai(e,{$refStrategy:r?`root`:`none`,target:`jsonSchema7`}),{validate:t=>{let n=e.safeParse(t);return n.success?{success:!0,value:n.data}:{success:!1,error:n.error}}})}var fa=Symbol.for(`vercel.ai.schema`);function pa(e,{validate:t}={}){return{[fa]:!0,_type:void 0,[ut]:!0,jsonSchema:e,validate:t}}function ma(e){return typeof e==`object`&&!!e&&fa in e&&e[fa]===!0&&`jsonSchema`in e&&`validate`in e}function ha(e){return ma(e)?e:da(e)}var ga=typeof globalThis==`object`?globalThis:typeof self==`object`?self:typeof window==`object`?window:typeof global==`object`?global:{},_a=`1.9.0`,va=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function ya(e){var t=new Set([e]),n=new Set,r=e.match(va);if(!r)return function(){return!1};var i={major:+r[1],minor:+r[2],patch:+r[3],prerelease:r[4]};if(i.prerelease!=null)return function(t){return t===e};function a(e){return n.add(e),!1}function o(e){return t.add(e),!0}return function(e){if(t.has(e))return!0;if(n.has(e))return!1;var r=e.match(va);if(!r)return a(e);var s={major:+r[1],minor:+r[2],patch:+r[3],prerelease:r[4]};return s.prerelease!=null||i.major!==s.major?a(e):i.major===0?i.minor===s.minor&&i.patch<=s.patch?o(e):a(e):i.minor<=s.minor?o(e):a(e)}}var ba=ya(_a),xa=_a.split(`.`)[0],Sa=Symbol.for(`opentelemetry.js.api.`+xa),Ca=ga;function wa(e,t,n,r){var i;r===void 0&&(r=!1);var a=Ca[Sa]=(i=Ca[Sa])??{version:_a};if(!r&&a[e]){var o=Error(`@opentelemetry/api: Attempted duplicate registration of API: `+e);return n.error(o.stack||o.message),!1}if(a.version!==_a){var o=Error(`@opentelemetry/api: Registration of version v`+a.version+` for `+e+` does not match previously registered API v`+_a);return n.error(o.stack||o.message),!1}return a[e]=t,n.debug(`@opentelemetry/api: Registered a global for `+e+` v`+_a+`.`),!0}function Ta(e){var t,n,r=(t=Ca[Sa])?.version;if(!(!r||!ba(r)))return(n=Ca[Sa])?.[e]}function Ea(e,t){t.debug(`@opentelemetry/api: Unregistering a global for `+e+` v`+_a+`.`);var n=Ca[Sa];n&&delete n[e]}var Da=function(e,t){var n=typeof Symbol==`function`&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(e){o={error:e}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(o)throw o.error}}return a},Oa=function(e,t,n){if(n||arguments.length===2)for(var r=0,i=t.length,a;r<i;r++)(a||!(r in t))&&(a||=Array.prototype.slice.call(t,0,r),a[r]=t[r]);return e.concat(a||Array.prototype.slice.call(t))},ka=function(){function e(e){this._namespace=e.namespace||`DiagComponentLogger`}return e.prototype.debug=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Aa(`debug`,this._namespace,e)},e.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Aa(`error`,this._namespace,e)},e.prototype.info=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Aa(`info`,this._namespace,e)},e.prototype.warn=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Aa(`warn`,this._namespace,e)},e.prototype.verbose=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Aa(`verbose`,this._namespace,e)},e}();function Aa(e,t,n){var r=Ta(`diag`);if(r)return n.unshift(t),r[e].apply(r,Oa([],Da(n),!1))}var Z;(function(e){e[e.NONE=0]=`NONE`,e[e.ERROR=30]=`ERROR`,e[e.WARN=50]=`WARN`,e[e.INFO=60]=`INFO`,e[e.DEBUG=70]=`DEBUG`,e[e.VERBOSE=80]=`VERBOSE`,e[e.ALL=9999]=`ALL`})(Z||={});function ja(e,t){e<Z.NONE?e=Z.NONE:e>Z.ALL&&(e=Z.ALL),t||={};function n(n,r){var i=t[n];return typeof i==`function`&&e>=r?i.bind(t):function(){}}return{error:n(`error`,Z.ERROR),warn:n(`warn`,Z.WARN),info:n(`info`,Z.INFO),debug:n(`debug`,Z.DEBUG),verbose:n(`verbose`,Z.VERBOSE)}}var Ma=function(e,t){var n=typeof Symbol==`function`&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(e){o={error:e}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(o)throw o.error}}return a},Na=function(e,t,n){if(n||arguments.length===2)for(var r=0,i=t.length,a;r<i;r++)(a||!(r in t))&&(a||=Array.prototype.slice.call(t,0,r),a[r]=t[r]);return e.concat(a||Array.prototype.slice.call(t))},Pa=`diag`,Fa=function(){function e(){function e(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=Ta(`diag`);if(r)return r[e].apply(r,Na([],Ma(t),!1))}}var t=this,n=function(e,n){var r,i,a;if(n===void 0&&(n={logLevel:Z.INFO}),e===t){var o=Error(`Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation`);return t.error((r=o.stack)??o.message),!1}typeof n==`number`&&(n={logLevel:n});var s=Ta(`diag`),c=ja((i=n.logLevel)??Z.INFO,e);if(s&&!n.suppressOverrideMessage){var l=(a=Error().stack)??`<failed to generate stacktrace>`;s.warn(`Current logger will be overwritten from `+l),c.warn(`Current logger will overwrite one already registered from `+l)}return wa(`diag`,c,t,!0)};t.setLogger=n,t.disable=function(){Ea(Pa,t)},t.createComponentLogger=function(e){return new ka(e)},t.verbose=e(`verbose`),t.debug=e(`debug`),t.info=e(`info`),t.warn=e(`warn`),t.error=e(`error`)}return e.instance=function(){return this._instance||=new e,this._instance},e}();function Ia(e){return Symbol.for(e)}var La=function(){function e(t){var n=this;n._currentContext=t?new Map(t):new Map,n.getValue=function(e){return n._currentContext.get(e)},n.setValue=function(t,r){var i=new e(n._currentContext);return i._currentContext.set(t,r),i},n.deleteValue=function(t){var r=new e(n._currentContext);return r._currentContext.delete(t),r}}return e}(),Ra=new La,za=function(e,t){var n=typeof Symbol==`function`&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(e){o={error:e}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(o)throw o.error}}return a},Ba=function(e,t,n){if(n||arguments.length===2)for(var r=0,i=t.length,a;r<i;r++)(a||!(r in t))&&(a||=Array.prototype.slice.call(t,0,r),a[r]=t[r]);return e.concat(a||Array.prototype.slice.call(t))},Va=function(){function e(){}return e.prototype.active=function(){return Ra},e.prototype.with=function(e,t,n){for(var r=[],i=3;i<arguments.length;i++)r[i-3]=arguments[i];return t.call.apply(t,Ba([n],za(r),!1))},e.prototype.bind=function(e,t){return t},e.prototype.enable=function(){return this},e.prototype.disable=function(){return this},e}(),Ha=function(e,t){var n=typeof Symbol==`function`&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),i,a=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)a.push(i.value)}catch(e){o={error:e}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(o)throw o.error}}return a},Ua=function(e,t,n){if(n||arguments.length===2)for(var r=0,i=t.length,a;r<i;r++)(a||!(r in t))&&(a||=Array.prototype.slice.call(t,0,r),a[r]=t[r]);return e.concat(a||Array.prototype.slice.call(t))},Wa=`context`,Ga=new Va,Ka=function(){function e(){}return e.getInstance=function(){return this._instance||=new e,this._instance},e.prototype.setGlobalContextManager=function(e){return wa(Wa,e,Fa.instance())},e.prototype.active=function(){return this._getContextManager().active()},e.prototype.with=function(e,t,n){for(var r,i=[],a=3;a<arguments.length;a++)i[a-3]=arguments[a];return(r=this._getContextManager()).with.apply(r,Ua([e,t,n],Ha(i),!1))},e.prototype.bind=function(e,t){return this._getContextManager().bind(e,t)},e.prototype._getContextManager=function(){return Ta(Wa)||Ga},e.prototype.disable=function(){this._getContextManager().disable(),Ea(Wa,Fa.instance())},e}(),qa;(function(e){e[e.NONE=0]=`NONE`,e[e.SAMPLED=1]=`SAMPLED`})(qa||={});var Ja=`0000000000000000`,Ya=`00000000000000000000000000000000`,Xa={traceId:Ya,spanId:Ja,traceFlags:qa.NONE},Za=function(){function e(e){e===void 0&&(e=Xa),this._spanContext=e}return e.prototype.spanContext=function(){return this._spanContext},e.prototype.setAttribute=function(e,t){return this},e.prototype.setAttributes=function(e){return this},e.prototype.addEvent=function(e,t){return this},e.prototype.addLink=function(e){return this},e.prototype.addLinks=function(e){return this},e.prototype.setStatus=function(e){return this},e.prototype.updateName=function(e){return this},e.prototype.end=function(e){},e.prototype.isRecording=function(){return!1},e.prototype.recordException=function(e,t){},e}(),Qa=Ia(`OpenTelemetry Context Key SPAN`);function $a(e){return e.getValue(Qa)||void 0}function eo(){return $a(Ka.getInstance().active())}function to(e,t){return e.setValue(Qa,t)}function no(e){return e.deleteValue(Qa)}function ro(e,t){return to(e,new Za(t))}function io(e){var t;return(t=$a(e))?.spanContext()}var ao=/^([0-9a-f]{32})$/i,oo=/^[0-9a-f]{16}$/i;function so(e){return ao.test(e)&&e!==Ya}function co(e){return oo.test(e)&&e!==Ja}function lo(e){return so(e.traceId)&&co(e.spanId)}function uo(e){return new Za(e)}var fo=Ka.getInstance(),po=function(){function e(){}return e.prototype.startSpan=function(e,t,n){n===void 0&&(n=fo.active());var r=!!t?.root;if(r)return new Za;var i=n&&io(n);return mo(i)&&lo(i)?new Za(i):new Za},e.prototype.startActiveSpan=function(e,t,n,r){var i,a,o;if(!(arguments.length<2)){arguments.length===2?o=t:arguments.length===3?(i=t,o=n):(i=t,a=n,o=r);var s=a??fo.active(),c=this.startSpan(e,i,s),l=to(s,c);return fo.with(l,o,void 0,c)}},e}();function mo(e){return typeof e==`object`&&typeof e.spanId==`string`&&typeof e.traceId==`string`&&typeof e.traceFlags==`number`}var ho=new po,go=function(){function e(e,t,n,r){this._provider=e,this.name=t,this.version=n,this.options=r}return e.prototype.startSpan=function(e,t,n){return this._getTracer().startSpan(e,t,n)},e.prototype.startActiveSpan=function(e,t,n,r){var i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)},e.prototype._getTracer=function(){if(this._delegate)return this._delegate;var e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):ho},e}(),_o=function(){function e(){}return e.prototype.getTracer=function(e,t,n){return new po},e}(),vo=new _o,yo=function(){function e(){}return e.prototype.getTracer=function(e,t,n){var r;return(r=this.getDelegateTracer(e,t,n))??new go(this,e,t,n)},e.prototype.getDelegate=function(){var e;return(e=this._delegate)??vo},e.prototype.setDelegate=function(e){this._delegate=e},e.prototype.getDelegateTracer=function(e,t,n){var r;return(r=this._delegate)?.getTracer(e,t,n)},e}(),bo;(function(e){e[e.UNSET=0]=`UNSET`,e[e.OK=1]=`OK`,e[e.ERROR=2]=`ERROR`})(bo||={});var xo=`trace`,So=function(){function e(){this._proxyTracerProvider=new yo,this.wrapSpanContext=uo,this.isSpanContextValid=lo,this.deleteSpan=no,this.getSpan=$a,this.getActiveSpan=eo,this.getSpanContext=io,this.setSpan=to,this.setSpanContext=ro}return e.getInstance=function(){return this._instance||=new e,this._instance},e.prototype.setGlobalTracerProvider=function(e){var t=wa(xo,this._proxyTracerProvider,Fa.instance());return t&&this._proxyTracerProvider.setDelegate(e),t},e.prototype.getTracerProvider=function(){return Ta(xo)||this._proxyTracerProvider},e.prototype.getTracer=function(e,t){return this.getTracerProvider().getTracer(e,t)},e.prototype.disable=function(){Ea(xo,Fa.instance()),this._proxyTracerProvider=new yo},e}(),Co=So.getInstance(),wo=Object.defineProperty,To=(e,t)=>{for(var n in t)wo(e,n,{get:t[n],enumerable:!0})};function Eo(e,{contentType:t,dataStreamVersion:n}){let r=new Headers(e??{});return r.has(`Content-Type`)||r.set(`Content-Type`,t),n!==void 0&&r.set(`X-Vercel-AI-Data-Stream`,n),r}var Do=`AI_InvalidArgumentError`,Oo=`vercel.ai.error.${Do}`,ko=Symbol.for(Oo),Ao,Q=class extends o{constructor({parameter:e,value:t,message:n}){super({name:Do,message:`Invalid argument for parameter ${e}: ${n}`}),this[Ao]=!0,this.parameter=e,this.value=t}static isInstance(e){return o.hasMarker(e,Oo)}};Ao=ko;var jo=`AI_RetryError`,Mo=`vercel.ai.error.${jo}`,No=Symbol.for(Mo),Po,Fo=class extends o{constructor({message:e,reason:t,errors:n}){super({name:jo,message:e}),this[Po]=!0,this.reason=t,this.errors=n,this.lastError=n[n.length-1]}static isInstance(e){return o.hasMarker(e,Mo)}};Po=No;var Io=({maxRetries:e=2,initialDelayInMs:t=2e3,backoffFactor:n=2}={})=>async r=>Lo(r,{maxRetries:e,delayInMs:t,backoffFactor:n});async function Lo(e,{maxRetries:t,delayInMs:n,backoffFactor:r},i=[]){try{return await e()}catch(a){if(ct(a)||t===0)throw a;let o=ot(a),s=[...i,a],c=s.length;if(c>t)throw new Fo({message:`Failed after ${c} attempts. Last error: ${o}`,reason:`maxRetriesExceeded`,errors:s});if(a instanceof Error&&d.isInstance(a)&&a.isRetryable===!0&&c<=t)return await et(n),Lo(e,{maxRetries:t,delayInMs:r*n,backoffFactor:r},s);throw c===1?a:new Fo({message:`Failed after ${c} attempts with non-retryable error: '${o}'`,reason:`errorNotRetryable`,errors:s})}}function Ro({maxRetries:e}){if(e!=null){if(!Number.isInteger(e))throw new Q({parameter:`maxRetries`,value:e,message:`maxRetries must be an integer`});if(e<0)throw new Q({parameter:`maxRetries`,value:e,message:`maxRetries must be >= 0`})}let t=e??2;return{maxRetries:t,retry:Io({maxRetries:t})}}function zo({operationId:e,telemetry:t}){return{"operation.name":`${e}${t?.functionId==null?``:` ${t.functionId}`}`,"resource.name":t?.functionId,"ai.operationId":e,"ai.telemetry.functionId":t?.functionId}}function Bo({model:e,settings:t,telemetry:n,headers:r}){var i;return{"ai.model.provider":e.provider,"ai.model.id":e.modelId,...Object.entries(t).reduce((e,[t,n])=>(e[`ai.settings.${t}`]=n,e),{}),...Object.entries((i=n?.metadata)??{}).reduce((e,[t,n])=>(e[`ai.telemetry.metadata.${t}`]=n,e),{}),...Object.entries(r??{}).reduce((e,[t,n])=>(n!==void 0&&(e[`ai.request.headers.${t}`]=n),e),{})}}var Vo={startSpan(){return Ho},startActiveSpan(e,t,n,r){if(typeof t==`function`)return t(Ho);if(typeof n==`function`)return n(Ho);if(typeof r==`function`)return r(Ho)}},Ho={spanContext(){return Uo},setAttribute(){return this},setAttributes(){return this},addEvent(){return this},addLink(){return this},addLinks(){return this},setStatus(){return this},updateName(){return this},end(){return this},isRecording(){return!1},recordException(){return this}},Uo={traceId:``,spanId:``,traceFlags:0};function Wo({isEnabled:e=!1,tracer:t}={}){return e?t||Co.getTracer(`ai`):Vo}function Go({name:e,tracer:t,attributes:n,fn:r,endWhenDone:i=!0}){return t.startActiveSpan(e,{attributes:n},async e=>{try{let t=await r(e);return i&&e.end(),t}catch(t){try{t instanceof Error?(e.recordException({name:t.name,message:t.message,stack:t.stack}),e.setStatus({code:bo.ERROR,message:t.message})):e.setStatus({code:bo.ERROR})}finally{e.end()}throw t}})}function Ko({telemetry:e,attributes:t}){return e?.isEnabled===!0?Object.entries(t).reduce((t,[n,r])=>{if(r===void 0)return t;if(typeof r==`object`&&`input`in r&&typeof r.input==`function`){if(e?.recordInputs===!1)return t;let i=r.input();return i===void 0?t:{...t,[n]:i}}if(typeof r==`object`&&`output`in r&&typeof r.output==`function`){if(e?.recordOutputs===!1)return t;let i=r.output();return i===void 0?t:{...t,[n]:i}}return{...t,[n]:r}},{}):{}}var qo=`AI_NoImageGeneratedError`,Jo=`vercel.ai.error.${qo}`,Yo=Symbol.for(Jo),Xo;Xo=Yo;var Zo=class{constructor({data:e,mimeType:t}){let n=e instanceof Uint8Array;this.base64Data=n?void 0:e,this.uint8ArrayData=n?e:void 0,this.mimeType=t}get base64(){return this.base64Data??=Mt(this.uint8ArrayData),this.base64Data}get uint8Array(){return this.uint8ArrayData??=jt(this.base64Data),this.uint8ArrayData}},Qo=[{mimeType:`image/gif`,bytesPrefix:[71,73,70],base64Prefix:`R0lG`},{mimeType:`image/png`,bytesPrefix:[137,80,78,71],base64Prefix:`iVBORw`},{mimeType:`image/jpeg`,bytesPrefix:[255,216],base64Prefix:`/9j/`},{mimeType:`image/webp`,bytesPrefix:[82,73,70,70],base64Prefix:`UklGRg`},{mimeType:`image/bmp`,bytesPrefix:[66,77],base64Prefix:`Qk`},{mimeType:`image/tiff`,bytesPrefix:[73,73,42,0],base64Prefix:`SUkqAA`},{mimeType:`image/tiff`,bytesPrefix:[77,77,0,42],base64Prefix:`TU0AKg`},{mimeType:`image/avif`,bytesPrefix:[0,0,0,32,102,116,121,112,97,118,105,102],base64Prefix:`AAAAIGZ0eXBhdmlm`},{mimeType:`image/heic`,bytesPrefix:[0,0,0,32,102,116,121,112,104,101,105,99],base64Prefix:`AAAAIGZ0eXBoZWlj`}],$o=e=>{let t=typeof e==`string`?jt(e):e,n=(t[6]&127)<<21|(t[7]&127)<<14|(t[8]&127)<<7|t[9]&127;return t.slice(n+10)};function es(e){let t=typeof e==`string`&&e.startsWith(`SUQz`)||typeof e!=`string`&&e.length>10&&e[0]===73&&e[1]===68&&e[2]===51;return t?$o(e):e}function ts({data:e,signatures:t}){let n=es(e);for(let e of t)if(typeof n==`string`?n.startsWith(e.base64Prefix):n.length>=e.bytesPrefix.length&&e.bytesPrefix.every((e,t)=>n[t]===e))return e.mimeType}var ns=`AI_NoObjectGeneratedError`,rs=`vercel.ai.error.${ns}`,os=Symbol.for(rs),ss,cs=class extends o{constructor({message:e=`No object generated.`,cause:t,text:n,response:r,usage:i,finishReason:a}){super({name:ns,message:e,cause:t}),this[ss]=!0,this.text=n,this.response=r,this.usage=i,this.finishReason=a}static isInstance(e){return o.hasMarker(e,rs)}};ss=os;var ls=`AI_DownloadError`,us=`vercel.ai.error.${ls}`,ds=Symbol.for(us),fs,ps=class extends o{constructor({url:e,statusCode:t,statusText:n,cause:r,message:i=r==null?`Failed to download ${e}: ${t} ${n}`:`Failed to download ${e}: ${r}`}){super({name:ls,message:i,cause:r}),this[fs]=!0,this.url=e,this.statusCode=t,this.statusText=n}static isInstance(e){return o.hasMarker(e,us)}};fs=ds;async function ms({url:e}){var t;let n=e.toString();try{let e=await fetch(n);if(!e.ok)throw new ps({url:n,statusCode:e.status,statusText:e.statusText});return{data:new Uint8Array(await e.arrayBuffer()),mimeType:(t=e.headers.get(`content-type`))??void 0}}catch(e){throw ps.isInstance(e)?e:new ps({url:n,cause:e})}}var hs=`AI_InvalidDataContentError`,gs=`vercel.ai.error.${hs}`,_s=Symbol.for(gs),vs,ys=class extends o{constructor({content:e,cause:t,message:n=`Invalid data content. Expected a base64 string, Uint8Array, ArrayBuffer, or Buffer, but got ${typeof e}.`}){super({name:hs,message:n,cause:t}),this[vs]=!0,this.content=e}static isInstance(e){return o.hasMarker(e,gs)}};vs=_s;var bs=H([z(),ar(Uint8Array),ar(ArrayBuffer),rr(e=>{var t,n;return(n=(t=globalThis.Buffer)?.isBuffer(e))??!1},{message:`Must be a Buffer`})]);function xs(e){return typeof e==`string`?e:e instanceof ArrayBuffer?Mt(new Uint8Array(e)):Mt(e)}function Ss(e){if(e instanceof Uint8Array)return e;if(typeof e==`string`)try{return jt(e)}catch(t){throw new ys({message:`Invalid data content. Content string is not a base64-encoded media.`,content:e,cause:t})}if(e instanceof ArrayBuffer)return new Uint8Array(e);throw new ys({content:e})}function Cs(e){try{return new TextDecoder().decode(e)}catch{throw Error(`Error decoding Uint8Array to text`)}}var ws=`AI_InvalidMessageRoleError`,Ts=`vercel.ai.error.${ws}`,Es=Symbol.for(Ts),Ds,Os=class extends o{constructor({role:e,message:t=`Invalid message role: '${e}'. Must be one of: "system", "user", "assistant", "tool".`}){super({name:ws,message:t}),this[Ds]=!0,this.role=e}static isInstance(e){return o.hasMarker(e,Ts)}};Ds=Es;function ks(e){try{let[t,n]=e.split(`,`);return{mimeType:t.split(`;`)[0].split(`:`)[1],base64Content:n}}catch{return{mimeType:void 0,base64Content:void 0}}}async function As({prompt:e,modelSupportsImageUrls:t=!0,modelSupportsUrl:n=()=>!1,downloadImplementation:r=ms}){let i=await Ms(e.messages,r,t,n);return[...e.system==null?[]:[{role:`system`,content:e.system}],...e.messages.map(e=>js(e,i))]}function js(e,t){var n,r,i,a,o,s;let c=e.role;switch(c){case`system`:return{role:`system`,content:e.content,providerMetadata:(n=e.providerOptions)??e.experimental_providerMetadata};case`user`:return typeof e.content==`string`?{role:`user`,content:[{type:`text`,text:e.content}],providerMetadata:(r=e.providerOptions)??e.experimental_providerMetadata}:{role:`user`,content:e.content.map(e=>Ns(e,t)).filter(e=>e.type!==`text`||e.text!==``),providerMetadata:(i=e.providerOptions)??e.experimental_providerMetadata};case`assistant`:return typeof e.content==`string`?{role:`assistant`,content:[{type:`text`,text:e.content}],providerMetadata:(a=e.providerOptions)??e.experimental_providerMetadata}:{role:`assistant`,content:e.content.filter(e=>e.type!==`text`||e.text!==``).map(e=>{var t;let n=(t=e.providerOptions)??e.experimental_providerMetadata;switch(e.type){case`file`:return{type:`file`,data:e.data instanceof URL?e.data:xs(e.data),filename:e.filename,mimeType:e.mimeType,providerMetadata:n};case`reasoning`:return{type:`reasoning`,text:e.text,signature:e.signature,providerMetadata:n};case`redacted-reasoning`:return{type:`redacted-reasoning`,data:e.data,providerMetadata:n};case`text`:return{type:`text`,text:e.text,providerMetadata:n};case`tool-call`:return{type:`tool-call`,toolCallId:e.toolCallId,toolName:e.toolName,args:e.args,providerMetadata:n}}}),providerMetadata:(o=e.providerOptions)??e.experimental_providerMetadata};case`tool`:return{role:`tool`,content:e.content.map(e=>{var t;return{type:`tool-result`,toolCallId:e.toolCallId,toolName:e.toolName,result:e.result,content:e.experimental_content,isError:e.isError,providerMetadata:(t=e.providerOptions)??e.experimental_providerMetadata}}),providerMetadata:(s=e.providerOptions)??e.experimental_providerMetadata};default:{let e=c;throw new Os({role:e})}}}async function Ms(e,t,n,r){let i=e.filter(e=>e.role===`user`).map(e=>e.content).filter(e=>Array.isArray(e)).flat().filter(e=>e.type===`image`||e.type===`file`).filter(e=>!(e.type===`image`&&n===!0)).map(e=>e.type===`image`?e.image:e.data).map(e=>typeof e==`string`&&(e.startsWith(`http:`)||e.startsWith(`https:`))?new URL(e):e).filter(e=>e instanceof URL).filter(e=>!r(e)),a=await Promise.all(i.map(async e=>({url:e,data:await t({url:e})})));return Object.fromEntries(a.map(({url:e,data:t})=>[e.toString(),t]))}function Ns(e,t){var n,r,i,a;if(e.type===`text`)return{type:`text`,text:e.text,providerMetadata:(n=e.providerOptions)??e.experimental_providerMetadata};let o=e.mimeType,s,c,l,u=e.type;switch(u){case`image`:s=e.image;break;case`file`:s=e.data;break;default:throw Error(`Unsupported part type: ${u}`)}try{c=typeof s==`string`?new URL(s):s}catch{c=s}if(c instanceof URL)if(c.protocol===`data:`){let{mimeType:e,base64Content:t}=ks(c.toString());if(e==null||t==null)throw Error(`Invalid data URL format in part ${u}`);o=e,l=Ss(t)}else{let e=t[c.toString()];e?(l=e.data,o??=e.mimeType):l=c}else l=Ss(c);switch(u){case`image`:return l instanceof Uint8Array&&(o=(r=ts({data:l,signatures:Qo}))??o),{type:`image`,image:l,mimeType:o,providerMetadata:(i=e.providerOptions)??e.experimental_providerMetadata};case`file`:if(o==null)throw Error(`Mime type is missing for file part`);return{type:`file`,data:l instanceof Uint8Array?xs(l):l,filename:e.filename,mimeType:o,providerMetadata:(a=e.providerOptions)??e.experimental_providerMetadata}}}function Ps({maxTokens:e,temperature:t,topP:n,topK:r,presencePenalty:i,frequencyPenalty:a,stopSequences:o,seed:s}){if(e!=null){if(!Number.isInteger(e))throw new Q({parameter:`maxTokens`,value:e,message:`maxTokens must be an integer`});if(e<1)throw new Q({parameter:`maxTokens`,value:e,message:`maxTokens must be >= 1`})}if(t!=null&&typeof t!=`number`)throw new Q({parameter:`temperature`,value:t,message:`temperature must be a number`});if(n!=null&&typeof n!=`number`)throw new Q({parameter:`topP`,value:n,message:`topP must be a number`});if(r!=null&&typeof r!=`number`)throw new Q({parameter:`topK`,value:r,message:`topK must be a number`});if(i!=null&&typeof i!=`number`)throw new Q({parameter:`presencePenalty`,value:i,message:`presencePenalty must be a number`});if(a!=null&&typeof a!=`number`)throw new Q({parameter:`frequencyPenalty`,value:a,message:`frequencyPenalty must be a number`});if(s!=null&&!Number.isInteger(s))throw new Q({parameter:`seed`,value:s,message:`seed must be an integer`});return{maxTokens:e,temperature:t??0,topP:n,topK:r,presencePenalty:i,frequencyPenalty:a,stopSequences:o!=null&&o.length>0?o:void 0,seed:s}}function Fs(e){var t,n,r;let i=[];for(let a of e){let e;try{e=new URL(a.url)}catch{throw Error(`Invalid URL: ${a.url}`)}switch(e.protocol){case`http:`:case`https:`:if((t=a.contentType)?.startsWith(`image/`))i.push({type:`image`,image:e});else{if(!a.contentType)throw Error(`If the attachment is not an image, it must specify a content type`);i.push({type:`file`,data:e,mimeType:a.contentType})}break;case`data:`:{let e,t,o;try{[e,t]=a.url.split(`,`),o=e.split(`;`)[0].split(`:`)[1]}catch{throw Error(`Error processing data URL: ${a.url}`)}if(o==null||t==null)throw Error(`Invalid data URL format: ${a.url}`);if((n=a.contentType)?.startsWith(`image/`))i.push({type:`image`,image:Ss(t)});else if((r=a.contentType)?.startsWith(`text/`))i.push({type:`text`,text:Cs(Ss(t))});else{if(!a.contentType)throw Error(`If the attachment is not an image or text, it must specify a content type`);i.push({type:`file`,data:t,mimeType:a.contentType})}break}default:throw Error(`Unsupported URL protocol: ${e.protocol}`)}}return i}var Is=`AI_MessageConversionError`,Ls=`vercel.ai.error.${Is}`,Rs=Symbol.for(Ls),zs,Bs=class extends o{constructor({originalMessage:e,message:t}){super({name:Is,message:t}),this[zs]=!0,this.originalMessage=e}static isInstance(e){return o.hasMarker(e,Ls)}};zs=Rs;function Vs(e,t){var n,r;let i=(n=t?.tools)??{},a=[];for(let t=0;t<e.length;t++){let n=e[t],s=t===e.length-1,{role:c,content:l,experimental_attachments:u}=n;switch(c){case`system`:a.push({role:`system`,content:l});break;case`user`:if(n.parts==null)a.push({role:`user`,content:u?[{type:`text`,text:l},...Fs(u)]:l});else{let e=n.parts.filter(e=>e.type===`text`).map(e=>({type:`text`,text:e.text}));a.push({role:`user`,content:u?[...e,...Fs(u)]:e})}break;case`assistant`:{if(n.parts!=null){let e=function(){let e=[];for(let t of c)switch(t.type){case`file`:case`text`:e.push(t);break;case`reasoning`:for(let n of t.details)switch(n.type){case`text`:e.push({type:`reasoning`,text:n.text,signature:n.signature});break;case`redacted`:e.push({type:`redacted-reasoning`,data:n.data});break}break;case`tool-invocation`:e.push({type:`tool-call`,toolCallId:t.toolInvocation.toolCallId,toolName:t.toolInvocation.toolName,args:t.toolInvocation.args});break;default:{let e=t;throw Error(`Unsupported part: ${e}`)}}a.push({role:`assistant`,content:e});let r=c.filter(e=>e.type===`tool-invocation`).map(e=>e.toolInvocation);r.length>0&&a.push({role:`tool`,content:r.map(e=>{if(!(`result`in e))throw new Bs({originalMessage:n,message:`ToolInvocation must have a result: `+JSON.stringify(e)});let{toolCallId:t,toolName:r,result:a}=e,o=i[r];return o?.experimental_toToolResultContent==null?{type:`tool-result`,toolCallId:t,toolName:r,result:a}:{type:`tool-result`,toolCallId:t,toolName:r,result:o.experimental_toToolResultContent(a),experimental_content:o.experimental_toToolResultContent(a)}})}),c=[],s=!1,t++};var o=e;let t=0,s=!1,c=[];for(let i of n.parts)switch(i.type){case`text`:s&&e(),c.push(i);break;case`file`:case`reasoning`:c.push(i);break;case`tool-invocation`:((r=i.toolInvocation.step)??0)!==t&&e(),c.push(i),s=!0;break}e();break}let e=n.toolInvocations;if(e==null||e.length===0){a.push({role:`assistant`,content:l});break}let t=e.reduce((e,t)=>{var n;return Math.max(e,(n=t.step)??0)},0);for(let r=0;r<=t;r++){let t=e.filter(e=>{var t;return((t=e.step)??0)===r});t.length!==0&&(a.push({role:`assistant`,content:[...s&&l&&r===0?[{type:`text`,text:l}]:[],...t.map(({toolCallId:e,toolName:t,args:n})=>({type:`tool-call`,toolCallId:e,toolName:t,args:n}))]}),a.push({role:`tool`,content:t.map(e=>{if(!(`result`in e))throw new Bs({originalMessage:n,message:`ToolInvocation must have a result: `+JSON.stringify(e)});let{toolCallId:t,toolName:r,result:a}=e,o=i[r];return o?.experimental_toToolResultContent==null?{type:`tool-result`,toolCallId:t,toolName:r,result:a}:{type:`tool-result`,toolCallId:t,toolName:r,result:o.experimental_toToolResultContent(a),experimental_content:o.experimental_toToolResultContent(a)}})}))}l&&!s&&a.push({role:`assistant`,content:l});break}case`data`:break;default:{let e=c;throw new Bs({originalMessage:n,message:`Unsupported role: ${e}`})}}}return a}var Hs=Er(()=>H([pr(),z(),or(),lr(),Sr(z(),Hs),B(Hs)])),$=Sr(z(),Sr(z(),Hs)),Us=B(H([V({type:U(`text`),text:z()}),V({type:U(`image`),data:z(),mimeType:z().optional()})])),Ws=V({type:U(`text`),text:z(),providerOptions:$.optional(),experimental_providerMetadata:$.optional()}),Gs=V({type:U(`image`),image:H([bs,ar(URL)]),mimeType:z().optional(),providerOptions:$.optional(),experimental_providerMetadata:$.optional()}),Ks=V({type:U(`file`),data:H([bs,ar(URL)]),filename:z().optional(),mimeType:z(),providerOptions:$.optional(),experimental_providerMetadata:$.optional()}),qs=V({type:U(`reasoning`),text:z(),providerOptions:$.optional(),experimental_providerMetadata:$.optional()}),Js=V({type:U(`redacted-reasoning`),data:z(),providerOptions:$.optional(),experimental_providerMetadata:$.optional()}),Ys=V({type:U(`tool-call`),toolCallId:z(),toolName:z(),args:hr(),providerOptions:$.optional(),experimental_providerMetadata:$.optional()}),Xs=V({type:U(`tool-result`),toolCallId:z(),toolName:z(),result:hr(),content:Us.optional(),isError:lr().optional(),providerOptions:$.optional(),experimental_providerMetadata:$.optional()}),Zs=V({role:U(`system`),content:z(),providerOptions:$.optional(),experimental_providerMetadata:$.optional()}),Qs=V({role:U(`user`),content:H([z(),B(H([Ws,Gs,Ks]))]),providerOptions:$.optional(),experimental_providerMetadata:$.optional()}),$s=V({role:U(`assistant`),content:H([z(),B(H([Ws,Ks,qs,Js,Ys]))]),providerOptions:$.optional(),experimental_providerMetadata:$.optional()}),ec=V({role:U(`tool`),content:B(Xs),providerOptions:$.optional(),experimental_providerMetadata:$.optional()}),tc=H([Zs,Qs,$s,ec]);function nc({prompt:e,tools:t}){if(e.prompt==null&&e.messages==null)throw new g({prompt:e,message:`prompt or messages must be defined`});if(e.prompt!=null&&e.messages!=null)throw new g({prompt:e,message:`prompt and messages cannot be defined at the same time`});if(e.system!=null&&typeof e.system!=`string`)throw new g({prompt:e,message:`system must be a string`});if(e.prompt!=null){if(typeof e.prompt!=`string`)throw new g({prompt:e,message:`prompt must be a string`});return{type:`prompt`,system:e.system,messages:[{role:`user`,content:e.prompt}]}}if(e.messages!=null){let n=rc(e.messages),r=n===`ui-messages`?Vs(e.messages,{tools:t}):e.messages;if(r.length===0)throw new g({prompt:e,message:`messages must not be empty`});let i=gt({value:r,schema:B(tc)});if(!i.success)throw new g({prompt:e,message:[`message must be a CoreMessage or a UI message`,`Validation error: ${i.error.message}`].join(`
`),cause:i.error});return{type:`messages`,messages:r,system:e.system}}throw Error(`unreachable`)}function rc(e){if(!Array.isArray(e))throw new g({prompt:e,message:[`messages must be an array of CoreMessage or UIMessage`,`Received non-array value: ${JSON.stringify(e)}`].join(`
`),cause:e});if(e.length===0)return`messages`;let t=e.map(ic);if(t.some(e=>e===`has-ui-specific-parts`))return`ui-messages`;let n=t.findIndex(e=>e!==`has-core-specific-parts`&&e!==`message`);if(n===-1)return`messages`;throw new g({prompt:e,message:[`messages must be an array of CoreMessage or UIMessage`,`Received message of type: "${t[n]}" at index ${n}`,`messages[${n}]: ${JSON.stringify(e[n])}`].join(`
`),cause:e})}function ic(e){return typeof e==`object`&&e&&(e.role===`function`||e.role===`data`||`toolInvocations`in e||`parts`in e||`experimental_attachments`in e)?`has-ui-specific-parts`:typeof e==`object`&&e&&`content`in e&&(Array.isArray(e.content)||`experimental_providerMetadata`in e||`providerOptions`in e)?`has-core-specific-parts`:typeof e==`object`&&e&&`role`in e&&`content`in e&&typeof e.content==`string`&&[`system`,`user`,`assistant`,`tool`].includes(e.role)?`message`:`other`}function ac({promptTokens:e,completionTokens:t}){return{promptTokens:e,completionTokens:t,totalTokens:e+t}}function oc(e,t){return{promptTokens:e.promptTokens+t.promptTokens,completionTokens:e.completionTokens+t.completionTokens,totalTokens:e.totalTokens+t.totalTokens}}var sc=`JSON schema:`,cc=`You MUST answer with a JSON object that matches the JSON schema above.`,lc=`You MUST answer with JSON.`;function uc({prompt:e,schema:t,schemaPrefix:n=t==null?void 0:sc,schemaSuffix:r=t==null?lc:cc}){return[e!=null&&e.length>0?e:void 0,e!=null&&e.length>0?``:void 0,n,t==null?void 0:JSON.stringify(t),r].filter(e=>e!=null).join(`
`)}function dc(e){let t=e.map(e=>({...e,content:typeof e.content==`string`?e.content:e.content.map(fc)}));return JSON.stringify(t)}function fc(e){return e.type===`image`?{...e,image:e.image instanceof Uint8Array?xs(e.image):e.image}:e}var pc=it({prefix:`aiobj`,size:24}),mc=it({prefix:`aiobj`,size:24}),hc=`AI_NoOutputSpecifiedError`,gc=`vercel.ai.error.${hc}`,_c=Symbol.for(gc),vc,yc=class extends o{constructor({message:e=`No output specified.`}={}){super({name:hc,message:e}),this[vc]=!0}static isInstance(e){return o.hasMarker(e,gc)}};vc=_c;var bc=`AI_ToolExecutionError`,xc=`vercel.ai.error.${bc}`,Sc=Symbol.for(xc),Cc,wc=class extends o{constructor({toolArgs:e,toolName:t,toolCallId:n,cause:r,message:i=`Error executing tool ${t}: ${ne(r)}`}){super({name:bc,message:i,cause:r}),this[Cc]=!0,this.toolArgs=e,this.toolName=t,this.toolCallId=n}static isInstance(e){return o.hasMarker(e,xc)}};Cc=Sc;function Tc(e){return e!=null&&Object.keys(e).length>0}function Ec({tools:e,toolChoice:t,activeTools:n}){if(!Tc(e))return{tools:void 0,toolChoice:void 0};let r=n==null?Object.entries(e):Object.entries(e).filter(([e])=>n.includes(e));return{tools:r.map(([e,t])=>{let n=t.type;switch(n){case void 0:case`function`:return{type:`function`,name:e,description:t.description,parameters:ha(t.parameters).jsonSchema};case`provider-defined`:return{type:`provider-defined`,name:e,id:t.id,args:t.args};default:{let e=n;throw Error(`Unsupported tool type: ${e}`)}}}),toolChoice:t==null?{type:`auto`}:typeof t==`string`?{type:t}:{type:`tool`,toolName:t.toolName}}}var Dc=/^([\s\S]*?)(\s+)(\S*)$/;function Oc(e){let t=e.match(Dc);return t?{prefix:t[1],whitespace:t[2],suffix:t[3]}:void 0}function kc(e){let t=Oc(e);return t?t.prefix+t.whitespace:e}var Ac=`AI_InvalidToolArgumentsError`,jc=`vercel.ai.error.${Ac}`,Mc=Symbol.for(jc),Nc,Pc=class extends o{constructor({toolArgs:e,toolName:t,cause:n,message:r=`Invalid arguments for tool ${t}: ${ne(n)}`}){super({name:Ac,message:r,cause:n}),this[Nc]=!0,this.toolArgs=e,this.toolName=t}static isInstance(e){return o.hasMarker(e,jc)}};Nc=Mc;var Fc=`AI_NoSuchToolError`,Ic=`vercel.ai.error.${Fc}`,Lc=Symbol.for(Ic),Rc,zc=class extends o{constructor({toolName:e,availableTools:t=void 0,message:n=`Model tried to call unavailable tool '${e}'. ${t===void 0?`No tools are available.`:`Available tools: ${t.join(`, `)}.`}`}){super({name:Fc,message:n}),this[Rc]=!0,this.toolName=e,this.availableTools=t}static isInstance(e){return o.hasMarker(e,Ic)}};Rc=Lc;var Bc=`AI_ToolCallRepairError`,Vc=`vercel.ai.error.${Bc}`,Hc=Symbol.for(Vc),Uc,Wc=class extends o{constructor({cause:e,originalError:t,message:n=`Error repairing tool call: ${ne(e)}`}){super({name:Bc,message:n,cause:e}),this[Uc]=!0,this.originalError=t}static isInstance(e){return o.hasMarker(e,Vc)}};Uc=Hc;async function Gc({toolCall:e,tools:t,repairToolCall:n,system:r,messages:i}){if(t==null)throw new zc({toolName:e.toolName});try{return await Kc({toolCall:e,tools:t})}catch(a){if(n==null||!(zc.isInstance(a)||Pc.isInstance(a)))throw a;let o=null;try{o=await n({toolCall:e,tools:t,parameterSchema:({toolName:e})=>ha(t[e].parameters).jsonSchema,system:r,messages:i,error:a})}catch(e){throw new Wc({cause:e,originalError:a})}if(o==null)throw a;return await Kc({toolCall:o,tools:t})}}async function Kc({toolCall:e,tools:t}){let n=e.toolName,r=t[n];if(r==null)throw new zc({toolName:e.toolName,availableTools:Object.keys(t)});let i=ha(r.parameters),a=e.args.trim()===``?gt({value:{},schema:i}):vt({text:e.args,schema:i});if(a.success===!1)throw new Pc({toolName:n,toolArgs:e.args,cause:a.error});return{type:`tool-call`,toolCallId:e.toolCallId,toolName:n,args:a.value}}function qc(e){let t=e.filter(e=>e.type===`text`).map(e=>e.text).join(``);return t.length>0?t:void 0}function Jc({text:e=``,files:t,reasoning:n,tools:r,toolCalls:i,toolResults:a,messageId:o,generateMessageId:s}){let c=[],l=[];return n.length>0&&l.push(...n.map(e=>e.type===`text`?{...e,type:`reasoning`}:{...e,type:`redacted-reasoning`})),t.length>0&&l.push(...t.map(e=>({type:`file`,data:e.base64,mimeType:e.mimeType}))),e.length>0&&l.push({type:`text`,text:e}),i.length>0&&l.push(...i),l.length>0&&c.push({role:`assistant`,content:l,id:o}),a.length>0&&c.push({role:`tool`,id:s(),content:a.map(e=>{let t=r[e.toolName];return t?.experimental_toToolResultContent==null?{type:`tool-result`,toolCallId:e.toolCallId,toolName:e.toolName,result:e.result}:{type:`tool-result`,toolCallId:e.toolCallId,toolName:e.toolName,result:t.experimental_toToolResultContent(e.result),experimental_content:t.experimental_toToolResultContent(e.result)}})}),c}var Yc=it({prefix:`aitxt`,size:24}),Xc=it({prefix:`msg`,size:24});async function Zc({model:e,tools:t,toolChoice:n,system:r,prompt:i,messages:a,maxRetries:o,abortSignal:s,headers:c,maxSteps:l=1,experimental_generateMessageId:u=Xc,experimental_output:d,experimental_continueSteps:f=!1,experimental_telemetry:p,experimental_providerMetadata:m,providerOptions:ee=m,experimental_activeTools:te,experimental_prepareStep:ne,experimental_repairToolCall:re,_internal:{generateId:ie=Yc,currentDate:ae=()=>new Date}={},onStepFinish:oe,...h}){var se;if(l<1)throw new Q({parameter:`maxSteps`,value:l,message:`maxSteps must be at least 1`});let{maxRetries:ce,retry:le}=Ro({maxRetries:o}),ue=Bo({model:e,telemetry:p,headers:c,settings:{...h,maxRetries:ce}}),g=nc({prompt:{system:(se=d?.injectIntoSystemPrompt({system:r,model:e}))??r,prompt:i,messages:a},tools:t}),de=Wo(p);return Go({name:`ai.generateText`,attributes:Ko({telemetry:p,attributes:{...zo({operationId:`ai.generateText`,telemetry:p}),...ue,"ai.model.provider":e.provider,"ai.model.id":e.modelId,"ai.prompt":{input:()=>JSON.stringify({system:r,prompt:i,messages:a})},"ai.settings.maxSteps":l}}),tracer:de,fn:async i=>{var a,o,m,se,ce,fe,pe,me,he,ge,_e,ve,ye,be;let xe=Ps(h),_,v=[],Se=[],y=[],Ce=0,we=[],Te=``,Ee=[],De=[],Oe={completionTokens:0,promptTokens:0,totalTokens:0},ke=`initial`;do{let i=Ce===0?g.type:`messages`,ve=[...g.messages,...we],ye=await ne?.({model:e,steps:De,maxSteps:l,stepNumber:Ce}),be=(a=ye?.toolChoice)??n,Ae=(o=ye?.experimental_activeTools)??te,b=(m=ye?.model)??e,je=await As({prompt:{type:i,system:g.system,messages:ve},modelSupportsImageUrls:b.supportsImageUrls,modelSupportsUrl:(se=b.supportsUrl)?.bind(b)}),Me={type:`regular`,...Ec({tools:t,toolChoice:be,activeTools:Ae})};_=await le(()=>Go({name:`ai.generateText.doGenerate`,attributes:Ko({telemetry:p,attributes:{...zo({operationId:`ai.generateText.doGenerate`,telemetry:p}),...ue,"ai.model.provider":b.provider,"ai.model.id":b.modelId,"ai.prompt.format":{input:()=>i},"ai.prompt.messages":{input:()=>dc(je)},"ai.prompt.tools":{input:()=>{var e;return(e=Me.tools)?.map(e=>JSON.stringify(e))}},"ai.prompt.toolChoice":{input:()=>Me.toolChoice==null?void 0:JSON.stringify(Me.toolChoice)},"gen_ai.system":b.provider,"gen_ai.request.model":b.modelId,"gen_ai.request.frequency_penalty":h.frequencyPenalty,"gen_ai.request.max_tokens":h.maxTokens,"gen_ai.request.presence_penalty":h.presencePenalty,"gen_ai.request.stop_sequences":h.stopSequences,"gen_ai.request.temperature":h.temperature,"gen_ai.request.top_k":h.topK,"gen_ai.request.top_p":h.topP}}),tracer:de,fn:async t=>{var n,r,a,o,l,u;let f=await b.doGenerate({mode:Me,...xe,inputFormat:i,responseFormat:d?.responseFormat({model:e}),prompt:je,providerMetadata:ee,abortSignal:s,headers:c}),m={id:(r=(n=f.response)?.id)??ie(),timestamp:(o=(a=f.response)?.timestamp)??ae(),modelId:(u=(l=f.response)?.modelId)??b.modelId};return t.setAttributes(Ko({telemetry:p,attributes:{"ai.response.finishReason":f.finishReason,"ai.response.text":{output:()=>f.text},"ai.response.toolCalls":{output:()=>JSON.stringify(f.toolCalls)},"ai.response.id":m.id,"ai.response.model":m.modelId,"ai.response.timestamp":m.timestamp.toISOString(),"ai.usage.promptTokens":f.usage.promptTokens,"ai.usage.completionTokens":f.usage.completionTokens,"gen_ai.response.finish_reasons":[f.finishReason],"gen_ai.response.id":m.id,"gen_ai.response.model":m.modelId,"gen_ai.usage.input_tokens":f.usage.promptTokens,"gen_ai.usage.output_tokens":f.usage.completionTokens}})),{...f,response:m}}})),v=await Promise.all(((ce=_.toolCalls)??[]).map(e=>Gc({toolCall:e,tools:t,repairToolCall:re,system:r,messages:ve}))),Se=t==null?[]:await Qc({toolCalls:v,tools:t,tracer:de,telemetry:p,messages:ve,abortSignal:s});let Ne=ac(_.usage);Oe=oc(Oe,Ne);let x=`done`;++Ce<l&&(f&&_.finishReason===`length`&&v.length===0?x=`continue`:v.length>0&&Se.length===v.length&&(x=`tool-result`));let Pe=(fe=_.text)??``,Fe=ke===`continue`&&Te.trimEnd()!==Te?Pe.trimStart():Pe,Ie=x===`continue`?kc(Fe):Fe;if(Te=x===`continue`||ke===`continue`?Te+Ie:Ie,y=el(_.reasoning),Ee.push(...(pe=_.sources)??[]),ke===`continue`){let e=we[we.length-1];typeof e.content==`string`?e.content+=Ie:e.content.push({text:Ie,type:`text`})}else we.push(...Jc({text:Te,files:tl(_.files),reasoning:el(_.reasoning),tools:t??{},toolCalls:v,toolResults:Se,messageId:u(),generateMessageId:u}));let Le={stepType:ke,text:Ie,reasoning:qc(y),reasoningDetails:y,files:tl(_.files),sources:(me=_.sources)??[],toolCalls:v,toolResults:Se,finishReason:_.finishReason,usage:Ne,warnings:_.warnings,logprobs:_.logprobs,request:(he=_.request)??{},response:{..._.response,headers:(ge=_.rawResponse)?.headers,body:(_e=_.rawResponse)?.body,messages:structuredClone(we)},providerMetadata:_.providerMetadata,experimental_providerMetadata:_.providerMetadata,isContinued:x===`continue`};De.push(Le),await oe?.(Le),ke=x}while(ke!==`done`);return i.setAttributes(Ko({telemetry:p,attributes:{"ai.response.finishReason":_.finishReason,"ai.response.text":{output:()=>_.text},"ai.response.toolCalls":{output:()=>JSON.stringify(_.toolCalls)},"ai.usage.promptTokens":_.usage.promptTokens,"ai.usage.completionTokens":_.usage.completionTokens}})),new $c({text:Te,files:tl(_.files),reasoning:qc(y),reasoningDetails:y,sources:Ee,outputResolver:()=>{if(d==null)throw new yc;return d.parseOutput({text:Te},{response:_.response,usage:Oe,finishReason:_.finishReason})},toolCalls:v,toolResults:Se,finishReason:_.finishReason,usage:Oe,warnings:_.warnings,request:(ve=_.request)??{},response:{..._.response,headers:(ye=_.rawResponse)?.headers,body:(be=_.rawResponse)?.body,messages:we},logprobs:_.logprobs,steps:De,providerMetadata:_.providerMetadata})}})}async function Qc({toolCalls:e,tools:t,tracer:n,telemetry:r,messages:i,abortSignal:a}){let o=await Promise.all(e.map(async({toolCallId:e,toolName:o,args:s})=>{let c=t[o];if(c?.execute==null)return;let l=await Go({name:`ai.toolCall`,attributes:Ko({telemetry:r,attributes:{...zo({operationId:`ai.toolCall`,telemetry:r}),"ai.toolCall.name":o,"ai.toolCall.id":e,"ai.toolCall.args":{output:()=>JSON.stringify(s)}}}),tracer:n,fn:async t=>{try{let n=await c.execute(s,{toolCallId:e,messages:i,abortSignal:a});try{t.setAttributes(Ko({telemetry:r,attributes:{"ai.toolCall.result":{output:()=>JSON.stringify(n)}}}))}catch{}return n}catch(t){throw new wc({toolCallId:e,toolName:o,toolArgs:s,cause:t})}}});return{type:`tool-result`,toolCallId:e,toolName:o,args:s,result:l}}));return o.filter(e=>e!=null)}var $c=class{constructor(e){this.text=e.text,this.files=e.files,this.reasoning=e.reasoning,this.reasoningDetails=e.reasoningDetails,this.toolCalls=e.toolCalls,this.toolResults=e.toolResults,this.finishReason=e.finishReason,this.usage=e.usage,this.warnings=e.warnings,this.request=e.request,this.response=e.response,this.steps=e.steps,this.experimental_providerMetadata=e.providerMetadata,this.providerMetadata=e.providerMetadata,this.logprobs=e.logprobs,this.outputResolver=e.outputResolver,this.sources=e.sources}get experimental_output(){return this.outputResolver()}};function el(e){return e==null?[]:typeof e==`string`?[{type:`text`,text:e}]:e}function tl(e){var t;return(t=e?.map(e=>new Zo(e)))??[]}var nl={};To(nl,{object:()=>fl,text:()=>dl});var rl=`AI_InvalidStreamPartError`,il=`vercel.ai.error.${rl}`,al=Symbol.for(il),ol;ol=al;var sl=`AI_MCPClientError`,cl=`vercel.ai.error.${sl}`,ll=Symbol.for(cl),ul;ul=ll;var dl=()=>({type:`text`,responseFormat:()=>({type:`text`}),injectIntoSystemPrompt({system:e}){return e},parsePartial({text:e}){return{partial:e}},parseOutput({text:e}){return e}}),fl=({schema:e})=>{let t=ha(e);return{type:`object`,responseFormat:({model:e})=>({type:`json`,schema:e.supportsStructuredOutputs?t.jsonSchema:void 0}),injectIntoSystemPrompt({system:e,model:n}){return n.supportsStructuredOutputs?e:uc({prompt:e,schema:t.jsonSchema})},parsePartial({text:e}){let t=Vi(e);switch(t.state){case`failed-parse`:case`undefined-input`:return;case`repaired-parse`:case`successful-parse`:return{partial:t.value};default:{let e=t.state;throw Error(`Unsupported parse state: ${e}`)}}},parseOutput({text:e},n){let r=vt({text:e});if(!r.success)throw new cs({message:`No object generated: could not parse the response.`,cause:r.error,text:e,response:n.response,usage:n.usage,finishReason:n.finishReason});let i=gt({value:r.value,schema:t});if(!i.success)throw new cs({message:`No object generated: response did not match schema.`,cause:i.error,text:e,response:n.response,usage:n.usage,finishReason:n.finishReason});return i.value}}};function pl(e,t){let n=e.getReader(),r=t.getReader(),i,a,o=!1,s=!1;async function c(e){try{i??=n.read();let t=await i;i=void 0,t.done?e.close():e.enqueue(t.value)}catch(t){e.error(t)}}async function l(e){try{a??=r.read();let t=await a;a=void 0,t.done?e.close():e.enqueue(t.value)}catch(t){e.error(t)}}return new ReadableStream({async pull(e){try{if(o){await l(e);return}if(s){await c(e);return}i??=n.read(),a??=r.read();let{result:t,reader:u}=await Promise.race([i.then(e=>({result:e,reader:n})),a.then(e=>({result:e,reader:r}))]);t.done||e.enqueue(t.value),u===n?(i=void 0,t.done&&(await l(e),o=!0)):(a=void 0,t.done&&(s=!0,await c(e)))}catch(t){e.error(t)}},cancel(){n.cancel(),r.cancel()}})}var ml=it({prefix:`aitxt`,size:24}),hl=it({prefix:`msg`,size:24}),gl=`AI_NoSuchProviderError`,_l=`vercel.ai.error.${gl}`,vl=Symbol.for(_l),yl;yl=vl;var bl=V({name:z(),version:z()}).passthrough(),xl=V({_meta:W(V({}).passthrough())}).passthrough(),Sl=xl,Cl=V({method:z(),params:W(xl)}),wl=V({experimental:W(V({}).passthrough()),logging:W(V({}).passthrough()),prompts:W(V({listChanged:W(lr())}).passthrough()),resources:W(V({subscribe:W(lr()),listChanged:W(lr())}).passthrough()),tools:W(V({listChanged:W(lr())}).passthrough())}).passthrough(),Tl=Sl.extend({protocolVersion:z(),capabilities:wl,serverInfo:bl,instructions:W(z())}),El=Sl.extend({nextCursor:W(z())}),Dl=V({name:z(),description:W(z()),inputSchema:V({type:U(`object`),properties:W(V({}).passthrough())}).passthrough()}).passthrough(),Ol=El.extend({tools:B(Dl)}),kl=V({type:U(`text`),text:z()}).passthrough(),Al=V({type:U(`image`),data:z().base64(),mimeType:z()}).passthrough(),jl=V({uri:z(),mimeType:W(z())}).passthrough(),Ml=jl.extend({text:z()}),Nl=jl.extend({blob:z().base64()}),Pl=V({type:U(`resource`),resource:H([Ml,Nl])}).passthrough(),Fl=Sl.extend({content:B(H([kl,Al,Pl])),isError:lr().default(!1).optional()}).or(Sl.extend({toolResult:hr()})),Il=`2.0`,Ll=V({jsonrpc:U(Il),id:H([z(),or().int()])}).merge(Cl).strict(),Rl=V({jsonrpc:U(Il),id:H([z(),or().int()]),result:Sl}).strict(),zl=V({jsonrpc:U(Il),id:H([z(),or().int()]),error:V({code:or().int(),message:z(),data:W(hr())})}).strict(),Bl=V({jsonrpc:U(Il)}).merge(V({method:z(),params:W(xl)})).strict(),Vl=H([Ll,Bl,Rl,zl]),Hl={};To(Hl,{mergeIntoDataStream:()=>ql,toDataStream:()=>Gl,toDataStreamResponse:()=>Kl});function Ul(e={}){let t=new TextEncoder,n=``;return new TransformStream({async start(){e.onStart&&await e.onStart()},async transform(r,i){i.enqueue(t.encode(r)),n+=r,e.onToken&&await e.onToken(r),e.onText&&typeof r==`string`&&await e.onText(r)},async flush(){e.onCompletion&&await e.onCompletion(n),e.onFinal&&await e.onFinal(n)}})}function Wl(e,t){return e.pipeThrough(new TransformStream({transform:async(e,t)=>{var n;if(typeof e==`string`){t.enqueue(e);return}if(`event`in e){e.event===`on_chat_model_stream`&&Jl((n=e.data)?.chunk,t);return}Jl(e,t)}})).pipeThrough(Ul(t)).pipeThrough(new TextDecoderStream).pipeThrough(new TransformStream({transform:async(e,t)=>{t.enqueue(ca(`text`,e))}}))}function Gl(e,t){return Wl(e,t).pipeThrough(new TextEncoderStream)}function Kl(e,t){var n;let r=Wl(e,t?.callbacks).pipeThrough(new TextEncoderStream),i=t?.data,a=t?.init,o=i?pl(i.stream,r):r;return new Response(o,{status:(n=a?.status)??200,statusText:a?.statusText,headers:Eo(a?.headers,{contentType:`text/plain; charset=utf-8`,dataStreamVersion:`v1`})})}function ql(e,t){t.dataStream.merge(Wl(e,t.callbacks))}function Jl(e,t){if(typeof e.content==`string`)t.enqueue(e.content);else{let n=e.content;for(let e of n)e.type===`text`&&t.enqueue(e.text)}}var Yl={};To(Yl,{mergeIntoDataStream:()=>$l,toDataStream:()=>Zl,toDataStreamResponse:()=>Ql});function Xl(e,t){let n=eu();return $e(e[Symbol.asyncIterator]()).pipeThrough(new TransformStream({async transform(e,t){t.enqueue(n(e.delta))}})).pipeThrough(Ul(t)).pipeThrough(new TextDecoderStream).pipeThrough(new TransformStream({transform:async(e,t)=>{t.enqueue(ca(`text`,e))}}))}function Zl(e,t){return Xl(e,t).pipeThrough(new TextEncoderStream)}function Ql(e,t={}){var n;let{init:r,data:i,callbacks:a}=t,o=Xl(e,a).pipeThrough(new TextEncoderStream),s=i?pl(i.stream,o):o;return new Response(s,{status:(n=r?.status)??200,statusText:r?.statusText,headers:Eo(r?.headers,{contentType:`text/plain; charset=utf-8`,dataStreamVersion:`v1`})})}function $l(e,t){t.dataStream.merge(Xl(e,t.callbacks))}function eu(){let e=!0;return t=>(e&&(t=t.trimStart(),t&&(e=!1)),t)}var tu=15*1e3;export{g as InvalidPromptError,he as InvalidResponseDataError,Le as TooManyEmbeddingValuesForCallError,Je as UnsupportedFunctionalityError,mr as anyType,B as arrayType,lr as booleanType,Qe as combineHeaders,jt as convertBase64ToUint8Array,Mt as convertUint8ArrayToBase64,Ot as createBinaryResponseHandler,Et as createEventSourceResponseHandler,Tt as createJsonErrorResponseHandler,Dt as createJsonResponseHandler,yr as discriminatedUnionType,Dr as enumType,at as generateId,Zc as generateText,yt as isParsableJson,U as literalType,lt as loadApiKey,or as numberType,V as objectType,bt as parseProviderOptions,Ct as postFormDataToApi,St as postJsonToApi,Sr as recordType,z as stringType,H as unionType,hr as unknownType,Nt as withoutTrailingSlash};