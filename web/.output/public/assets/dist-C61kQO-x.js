import{InvalidPromptError as e,InvalidResponseDataError as t,UnsupportedFunctionalityError as n,anyType as r,arrayType as i,combineHeaders as a,convertUint8ArrayToBase64 as o,createEventSourceResponseHandler as s,createJsonErrorResponseHandler as c,createJsonResponseHandler as l,enumType as u,generateId as d,isParsableJson as f,literalType as p,loadApiKey as m,numberType as h,objectType as g,postJsonToApi as _,recordType as v,stringType as y,unionType as b,unknownType as x,withoutTrailingSlash as S}from"./dist-CgCb95MR.js";import{createLucideIcon as C}from"./createLucideIcon-JB7IMeGf.js";const w=C(`LoaderCircle`,[[`path`,{d:`M21 12a9 9 0 1 1-6.219-8.56`,key:`13zald`}]]);var T=Object.defineProperty,E=Object.defineProperties,D=Object.getOwnPropertyDescriptors,O=Object.getOwnPropertySymbols,k=Object.prototype.hasOwnProperty,A=Object.prototype.propertyIsEnumerable,j=(e,t,n)=>t in e?T(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,M=(e,t)=>{for(var n in t||={})k.call(t,n)&&j(e,n,t[n]);if(O)for(var n of O(t))A.call(t,n)&&j(e,n,t[n]);return e},N=(e,t)=>E(e,D(t)),P=(e,t)=>{var n={};for(var r in e)k.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&O)for(var r of O(e))t.indexOf(r)<0&&A.call(e,r)&&(n[r]=e[r]);return n},F=g({type:p(`reasoning.summary`),summary:y()}),I=g({type:p(`reasoning.encrypted`),data:y()}),L=g({type:p(`reasoning.text`),text:y().nullish(),signature:y().nullish()}),R=b([F,I,L]),z=b([R,x().transform(()=>null)]),B=i(z).transform(e=>e.filter(e=>!!e));function V(e){var t,n,r;let i=e?.anthropic,a=e?.openrouter;return(r=(n=(t=a?.cacheControl)??a?.cache_control)??i?.cacheControl)??i?.cache_control}function H(e){var t,n,r;let i=[];for(let{role:a,content:s,providerMetadata:c}of e)switch(a){case`system`:i.push({role:`system`,content:s,cache_control:V(c)});break;case`user`:{if(s.length===1&&(t=s[0])?.type===`text`){i.push({role:`user`,content:s[0].text,cache_control:(n=V(c))??V(s[0].providerMetadata)});break}let e=V(c),r=s.map(t=>{var n,r,i,a;let s=(n=V(t.providerMetadata))??e;switch(t.type){case`text`:return{type:`text`,text:t.text,cache_control:s};case`image`:return{type:`image_url`,image_url:{url:t.image instanceof URL?t.image.toString():`data:${(r=t.mimeType)??`image/jpeg`};base64,${o(t.image)}`},cache_control:s};case`file`:return{type:`file`,file:{filename:String((a=(i=t.providerMetadata)?.openrouter)?.filename),file_data:t.data instanceof Uint8Array?`data:${t.mimeType};base64,${o(t.data)}`:`data:${t.mimeType};base64,${t.data}`},cache_control:s};default:{let e=t;throw Error(`Unsupported content part type: ${e}`)}}});i.push({role:`user`,content:r});break}case`assistant`:{let e=``,t=``,n=[],r=[];for(let i of s)switch(i.type){case`text`:e+=i.text;break;case`tool-call`:r.push({id:i.toolCallId,type:`function`,function:{name:i.toolName,arguments:JSON.stringify(i.args)}});break;case`reasoning`:t+=i.text,n.push({type:`reasoning.text`,text:i.text,signature:i.signature});break;case`redacted-reasoning`:n.push({type:`reasoning.encrypted`,data:i.data});break;case`file`:break;default:{let e=i;throw Error(`Unsupported part: ${e}`)}}i.push({role:`assistant`,content:e,tool_calls:r.length>0?r:void 0,reasoning:t||void 0,reasoning_details:n.length>0?n:void 0,cache_control:V(c)});break}case`tool`:for(let e of s)i.push({role:`tool`,tool_call_id:e.toolCallId,content:JSON.stringify(e.result),cache_control:(r=V(c))??V(e.providerMetadata)});break;default:{let e=a;throw Error(`Unsupported role: ${e}`)}}return i}function U(e){var t,n;return(n=(t=e?.content)?.map(({token:e,logprob:t,top_logprobs:n})=>({token:e,logprob:t,topLogprobs:n?n.map(({token:e,logprob:t})=>({token:e,logprob:t})):[]})))??void 0}function W(e){switch(e){case`stop`:return`stop`;case`length`:return`length`;case`content_filter`:return`content-filter`;case`function_call`:case`tool_calls`:return`tool-calls`;default:return`unknown`}}var G=g({error:g({code:b([y(),h()]).nullable(),message:y(),type:y().nullable(),param:r().nullable()})}),K=c({errorSchema:G,errorToMessage:e=>e.error.message});function q(e){return`parameters`in e}var J=class{constructor(e,t,n){this.specificationVersion=`v1`,this.defaultObjectGenerationMode=`tool`,this.modelId=e,this.settings=t,this.config=n}get provider(){return this.config.provider}getArgs({mode:e,prompt:t,maxTokens:r,temperature:i,topP:a,frequencyPenalty:o,presencePenalty:s,seed:c,stopSequences:l,responseFormat:u,topK:d,providerMetadata:f}){var p;let m=e.type,h=(p=f?.openrouter)??{},g=M(M(M({model:this.modelId,models:this.settings.models,logit_bias:this.settings.logitBias,logprobs:this.settings.logprobs===!0||typeof this.settings.logprobs==`number`?!0:void 0,top_logprobs:typeof this.settings.logprobs==`number`?this.settings.logprobs:typeof this.settings.logprobs==`boolean`&&this.settings.logprobs?0:void 0,user:this.settings.user,parallel_tool_calls:this.settings.parallelToolCalls,max_tokens:r,temperature:i,top_p:a,frequency_penalty:o,presence_penalty:s,seed:c,stop:l,response_format:u,top_k:d,messages:H(t),include_reasoning:this.settings.includeReasoning,reasoning:this.settings.reasoning,usage:this.settings.usage},this.config.extraBody),this.settings.extraBody),h);switch(m){case`regular`:return M(M({},g),ee(e));case`object-json`:return N(M({},g),{response_format:{type:`json_object`}});case`object-tool`:return N(M({},g),{tool_choice:{type:`function`,function:{name:e.tool.name}},tools:[{type:`function`,function:{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters}}]});default:{let e=m;throw new n({functionality:`${e} mode`})}}}async doGenerate(e){var t,n,r,i,o,s,c,u,f;let p=this.getArgs(e),{responseHeaders:m,value:h}=await _({url:this.config.url({path:`/chat/completions`,modelId:this.modelId}),headers:a(this.config.headers(),e.headers),body:p,failedResponseHandler:K,successfulResponseHandler:l(X),abortSignal:e.abortSignal,fetch:this.config.fetch}),g=p,{messages:v}=g,y=P(g,[`messages`]),b=h.choices[0];if(!b)throw Error(`No choice in response`);let x=h.usage?{promptTokens:(t=h.usage.prompt_tokens)??0,completionTokens:(n=h.usage.completion_tokens)??0}:{promptTokens:0,completionTokens:0},S={};h.usage&&(r=this.settings.usage)?.include&&(S.openrouter={usage:{promptTokens:h.usage.prompt_tokens,promptTokensDetails:h.usage.prompt_tokens_details?{cachedTokens:(i=h.usage.prompt_tokens_details.cached_tokens)??0}:void 0,completionTokens:h.usage.completion_tokens,completionTokensDetails:h.usage.completion_tokens_details?{reasoningTokens:(o=h.usage.completion_tokens_details.reasoning_tokens)??0}:void 0,cost:h.usage.cost,totalTokens:(s=h.usage.total_tokens)??0}});let C=Object.keys(S).length>0,w=(c=b.message.reasoning_details)??[],T=w.length>0?w.map(e=>{var t;switch(e.type){case`reasoning.text`:if(e.text)return{type:`text`,text:e.text,signature:(t=e.signature)??void 0};break;case`reasoning.summary`:if(e.summary)return{type:`text`,text:e.summary};break;case`reasoning.encrypted`:if(e.data)return{type:`redacted`,data:e.data};break;default:}return null}).filter(e=>e!==null):b.message.reasoning?[{type:`text`,text:b.message.reasoning}]:[];return M({response:{id:h.id,modelId:h.model},text:(u=b.message.content)??void 0,reasoning:T,toolCalls:(f=b.message.tool_calls)?.map(e=>{var t;return{toolCallType:`function`,toolCallId:(t=e.id)??d(),toolName:e.function.name,args:e.function.arguments}}),finishReason:W(b.finish_reason),usage:x,rawCall:{rawPrompt:v,rawSettings:y},rawResponse:{headers:m},warnings:[],logprobs:U(b.logprobs)},C?{providerMetadata:S}:{})}async doStream(e){var n,r;let i=this.getArgs(e),{responseHeaders:o,value:c}=await _({url:this.config.url({path:`/chat/completions`,modelId:this.modelId}),headers:a(this.config.headers(),e.headers),body:N(M({},i),{stream:!0,stream_options:this.config.compatibility===`strict`?M({include_usage:!0},(n=this.settings.usage)?.include?{include_usage:!0}:{}):void 0}),failedResponseHandler:K,successfulResponseHandler:s(Z),abortSignal:e.abortSignal,fetch:this.config.fetch}),l=i,{messages:u}=l,p=P(l,[`messages`]),m=[],h=`other`,g={promptTokens:NaN,completionTokens:NaN},v,y={},b=!!(r=this.settings.usage)?.include;return{stream:c.pipeThrough(new TransformStream({transform(e,n){var r,i,a,o,s,c,l,u,p,_,b,x,S,C;if(!e.success){h=`error`,n.enqueue({type:`error`,error:e.error});return}let w=e.value;if(`error`in w){h=`error`,n.enqueue({type:`error`,error:w.error});return}w.id&&n.enqueue({type:`response-metadata`,id:w.id}),w.model&&n.enqueue({type:`response-metadata`,modelId:w.model}),w.usage!=null&&(g={promptTokens:w.usage.prompt_tokens,completionTokens:w.usage.completion_tokens},y.promptTokens=w.usage.prompt_tokens,w.usage.prompt_tokens_details&&(y.promptTokensDetails={cachedTokens:(r=w.usage.prompt_tokens_details.cached_tokens)??0}),y.completionTokens=w.usage.completion_tokens,w.usage.completion_tokens_details&&(y.completionTokensDetails={reasoningTokens:(i=w.usage.completion_tokens_details.reasoning_tokens)??0}),y.cost=w.usage.cost,y.totalTokens=w.usage.total_tokens);let T=w.choices[0];if(T?.finish_reason!=null&&(h=W(T.finish_reason)),T?.delta==null)return;let E=T.delta;if(E.content!=null&&n.enqueue({type:`text-delta`,textDelta:E.content}),E.reasoning!=null&&n.enqueue({type:`reasoning`,textDelta:E.reasoning}),E.reasoning_details&&E.reasoning_details.length>0)for(let e of E.reasoning_details)switch(e.type){case`reasoning.text`:e.text&&n.enqueue({type:`reasoning`,textDelta:e.text}),e.signature&&n.enqueue({type:`reasoning-signature`,signature:e.signature});break;case`reasoning.encrypted`:e.data&&n.enqueue({type:`redacted-reasoning`,data:e.data});break;case`reasoning.summary`:e.summary&&n.enqueue({type:`reasoning`,textDelta:e.summary});break;default:break}let D=U(T?.logprobs);if(D?.length&&(v===void 0&&(v=[]),v.push(...D)),E.tool_calls!=null)for(let e of E.tool_calls){let r=e.index;if(m[r]==null){if(e.type!==`function`)throw new t({data:e,message:`Expected 'function' type.`});if(e.id==null)throw new t({data:e,message:`Expected 'id' to be a string.`});if((a=e.function)?.name==null)throw new t({data:e,message:`Expected 'function.name' to be a string.`});m[r]={id:e.id,type:`function`,function:{name:e.function.name,arguments:(o=e.function.arguments)??``},sent:!1};let i=m[r];if(i==null)throw Error(`Tool call is missing`);(s=i.function)?.name!=null&&(c=i.function)?.arguments!=null&&f(i.function.arguments)&&(n.enqueue({type:`tool-call-delta`,toolCallType:`function`,toolCallId:i.id,toolName:i.function.name,argsTextDelta:i.function.arguments}),n.enqueue({type:`tool-call`,toolCallType:`function`,toolCallId:(l=i.id)??d(),toolName:i.function.name,args:i.function.arguments}),i.sent=!0);continue}let i=m[r];if(i==null)throw Error(`Tool call is missing`);(u=e.function)?.arguments!=null&&(i.function.arguments+=(_=(p=e.function)?.arguments)??``),n.enqueue({type:`tool-call-delta`,toolCallType:`function`,toolCallId:i.id,toolName:i.function.name,argsTextDelta:(b=e.function.arguments)??``}),(x=i.function)?.name!=null&&(S=i.function)?.arguments!=null&&f(i.function.arguments)&&(n.enqueue({type:`tool-call`,toolCallType:`function`,toolCallId:(C=i.id)??d(),toolName:i.function.name,args:i.function.arguments}),i.sent=!0)}},flush(e){var t;if(h===`tool-calls`)for(let n of m)n.sent||(e.enqueue({type:`tool-call`,toolCallType:`function`,toolCallId:(t=n.id)??d(),toolName:n.function.name,args:f(n.function.arguments)?n.function.arguments:`{}`}),n.sent=!0);let n={};b&&(y.totalTokens!==void 0||y.cost!==void 0||y.promptTokensDetails!==void 0||y.completionTokensDetails!==void 0)&&(n.openrouter={usage:y});let r=Object.keys(n).length>0&&b;e.enqueue(M({type:`finish`,finishReason:h,logprobs:v,usage:g},r?{providerMetadata:n}:{}))}})),rawCall:{rawPrompt:u,rawSettings:p},rawResponse:{headers:o},warnings:[]}}},Y=g({id:y().optional(),model:y().optional(),usage:g({prompt_tokens:h(),prompt_tokens_details:g({cached_tokens:h()}).nullish(),completion_tokens:h(),completion_tokens_details:g({reasoning_tokens:h()}).nullish(),total_tokens:h(),cost:h().optional()}).nullish()}),X=Y.extend({choices:i(g({message:g({role:p(`assistant`),content:y().nullable().optional(),reasoning:y().nullable().optional(),reasoning_details:B.nullish(),tool_calls:i(g({id:y().optional().nullable(),type:p(`function`),function:g({name:y(),arguments:y()})})).optional()}),index:h(),logprobs:g({content:i(g({token:y(),logprob:h(),top_logprobs:i(g({token:y(),logprob:h()}))})).nullable()}).nullable().optional(),finish_reason:y().optional().nullable()}))}),Z=b([Y.extend({choices:i(g({delta:g({role:u([`assistant`]).optional(),content:y().nullish(),reasoning:y().nullish().optional(),reasoning_details:B.nullish(),tool_calls:i(g({index:h(),id:y().nullish(),type:p(`function`).optional(),function:g({name:y().nullish(),arguments:y().nullish()})})).nullish()}).nullish(),logprobs:g({content:i(g({token:y(),logprob:h(),top_logprobs:i(g({token:y(),logprob:h()}))})).nullable()}).nullish(),finish_reason:y().nullable().optional(),index:h()}))}),G]);function ee(e){var t;let n=(t=e.tools)?.length?e.tools:void 0;if(n==null)return{tools:void 0,tool_choice:void 0};let r=n.map(e=>q(e)?{type:`function`,function:{name:e.name,description:e.description,parameters:e.parameters}}:{type:`function`,function:{name:e.name}}),i=e.toolChoice;if(i==null)return{tools:r,tool_choice:void 0};let a=i.type;switch(a){case`auto`:case`none`:case`required`:return{tools:r,tool_choice:a};case`tool`:return{tools:r,tool_choice:{type:`function`,function:{name:i.toolName}}};default:{let e=a;throw Error(`Unsupported tool choice type: ${e}`)}}}function te({prompt:t,inputFormat:r,user:i=`user`,assistant:a=`assistant`}){if(r===`prompt`&&t.length===1&&t[0]&&t[0].role===`user`&&t[0].content.length===1&&t[0].content[0]&&t[0].content[0].type===`text`)return{prompt:t[0].content[0].text};let o=``;t[0]&&t[0].role===`system`&&(o+=`${t[0].content}

`,t=t.slice(1));for(let{role:r,content:s}of t)switch(r){case`system`:throw new e({message:"Unexpected system message in prompt: ${content}",prompt:t});case`user`:{let e=s.map(e=>{switch(e.type){case`text`:return e.text;case`image`:throw new n({functionality:`images`});case`file`:throw new n({functionality:`file attachments`});default:{let t=e;throw Error(`Unsupported content type: ${t}`)}}}).join(``);o+=`${i}:
${e}

`;break}case`assistant`:{let e=s.map(e=>{switch(e.type){case`text`:return e.text;case`tool-call`:throw new n({functionality:`tool-call messages`});case`reasoning`:throw new n({functionality:`reasoning messages`});case`redacted-reasoning`:throw new n({functionality:`redacted reasoning messages`});case`file`:throw new n({functionality:`file attachments`});default:{let t=e;throw Error(`Unsupported content type: ${t}`)}}}).join(``);o+=`${a}:
${e}

`;break}case`tool`:throw new n({functionality:`tool messages`});default:{let e=r;throw Error(`Unsupported role: ${e}`)}}return o+=`${a}:
`,{prompt:o}}function Q(e){return e?.tokens.map((t,n)=>{var r,i;return{token:t,logprob:(r=e.token_logprobs[n])??0,topLogprobs:e.top_logprobs?Object.entries((i=e.top_logprobs[n])??{}).map(([e,t])=>({token:e,logprob:t})):[]}})}var ne=class{constructor(e,t,n){this.specificationVersion=`v1`,this.defaultObjectGenerationMode=void 0,this.modelId=e,this.settings=t,this.config=n}get provider(){return this.config.provider}getArgs({mode:e,inputFormat:t,prompt:r,maxTokens:i,temperature:a,topP:o,frequencyPenalty:s,presencePenalty:c,seed:l,responseFormat:u,topK:d,stopSequences:f,providerMetadata:p}){var m,h;let g=e.type,_=(m=p?.openrouter)??{},{prompt:v}=te({prompt:r,inputFormat:t}),y=M(M(M({model:this.modelId,models:this.settings.models,logit_bias:this.settings.logitBias,logprobs:typeof this.settings.logprobs==`number`?this.settings.logprobs:typeof this.settings.logprobs==`boolean`&&this.settings.logprobs?0:void 0,suffix:this.settings.suffix,user:this.settings.user,max_tokens:i,temperature:a,top_p:o,frequency_penalty:s,presence_penalty:c,seed:l,stop:f,response_format:u,top_k:d,prompt:v,include_reasoning:this.settings.includeReasoning,reasoning:this.settings.reasoning},this.config.extraBody),this.settings.extraBody),_);switch(g){case`regular`:if((h=e.tools)?.length)throw new n({functionality:`tools`});if(e.toolChoice)throw new n({functionality:`toolChoice`});return y;case`object-json`:throw new n({functionality:`object-json mode`});case`object-tool`:throw new n({functionality:`object-tool mode`});default:{let e=g;throw new n({functionality:`${e} mode`})}}}async doGenerate(e){var t,n,r,i,o;let s=this.getArgs(e),{responseHeaders:c,value:u}=await _({url:this.config.url({path:`/completions`,modelId:this.modelId}),headers:a(this.config.headers(),e.headers),body:s,failedResponseHandler:K,successfulResponseHandler:l($),abortSignal:e.abortSignal,fetch:this.config.fetch}),d=s,{prompt:f}=d,p=P(d,[`prompt`]);if(`error`in u)throw Error(`${u.error.message}`);let m=u.choices[0];if(!m)throw Error(`No choice in OpenRouter completion response`);return{response:{id:u.id,modelId:u.model},text:(t=m.text)??``,reasoning:m.reasoning||void 0,usage:{promptTokens:(r=(n=u.usage)?.prompt_tokens)??0,completionTokens:(o=(i=u.usage)?.completion_tokens)??0},finishReason:W(m.finish_reason),logprobs:Q(m.logprobs),rawCall:{rawPrompt:f,rawSettings:p},rawResponse:{headers:c},warnings:[]}}async doStream(e){let t=this.getArgs(e),{responseHeaders:n,value:r}=await _({url:this.config.url({path:`/completions`,modelId:this.modelId}),headers:a(this.config.headers(),e.headers),body:N(M({},this.getArgs(e)),{stream:!0,stream_options:this.config.compatibility===`strict`?{include_usage:!0}:void 0}),failedResponseHandler:K,successfulResponseHandler:s($),abortSignal:e.abortSignal,fetch:this.config.fetch}),i=t,{prompt:o}=i,c=P(i,[`prompt`]),l=`other`,u={promptTokens:NaN,completionTokens:NaN},d;return{stream:r.pipeThrough(new TransformStream({transform(e,t){if(!e.success){l=`error`,t.enqueue({type:`error`,error:e.error});return}let n=e.value;if(`error`in n){l=`error`,t.enqueue({type:`error`,error:n.error});return}n.usage!=null&&(u={promptTokens:n.usage.prompt_tokens,completionTokens:n.usage.completion_tokens});let r=n.choices[0];r?.finish_reason!=null&&(l=W(r.finish_reason)),r?.text!=null&&t.enqueue({type:`text-delta`,textDelta:r.text});let i=Q(r?.logprobs);i?.length&&(d===void 0&&(d=[]),d.push(...i))},flush(e){e.enqueue({type:`finish`,finishReason:l,logprobs:d,usage:u})}})),rawCall:{rawPrompt:o,rawSettings:c},rawResponse:{headers:n},warnings:[]}}},$=b([g({id:y().optional(),model:y().optional(),choices:i(g({text:y(),reasoning:y().nullish().optional(),reasoning_details:B.nullish(),finish_reason:y().nullish(),index:h(),logprobs:g({tokens:i(y()),token_logprobs:i(h()),top_logprobs:i(v(y(),h())).nullable()}).nullable().optional()})),usage:g({prompt_tokens:h(),completion_tokens:h()}).optional().nullable()}),G]);function re(e={}){var t,n,r;let i=(n=S((t=e.baseURL)??e.baseUrl))??`https://openrouter.ai/api/v1`,a=(r=e.compatibility)??`compatible`,o=()=>M({Authorization:`Bearer ${m({apiKey:e.apiKey,environmentVariableName:`OPENROUTER_API_KEY`,description:`OpenRouter`})}`},e.headers),s=(t,n={})=>new J(t,n,{provider:`openrouter.chat`,url:({path:e})=>`${i}${e}`,headers:o,compatibility:a,fetch:e.fetch,extraBody:e.extraBody}),c=(t,n={})=>new ne(t,n,{provider:`openrouter.completion`,url:({path:e})=>`${i}${e}`,headers:o,compatibility:a,fetch:e.fetch,extraBody:e.extraBody}),l=(e,t)=>{if(new.target)throw Error(`The OpenRouter model function cannot be called with the new keyword.`);return e===`openai/gpt-3.5-turbo-instruct`?c(e,t):s(e,t)},u=(e,t)=>l(e,t);return u.languageModel=l,u.chat=s,u.completion=c,u}var ie=re({compatibility:`strict`});export{w as LoaderCircle,ie as openrouter};