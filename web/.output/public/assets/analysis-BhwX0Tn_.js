const e=[{id:1,name:`Technical Quality`,description:`Analyzing photo quality, lighting, and composition`},{id:2,name:`Facial Analysis`,description:`Evaluating facial features, expressions, and attractiveness`},{id:3,name:`Physical Analysis`,description:`Assessing body language, posture, and physical presentation`},{id:4,name:`Style & Presentation`,description:`Assessing clothing, setting, and overall presentation`},{id:5,name:`Dating Profile Optimization`,description:`Analyzing dating-specific appeal and suitability`},{id:6,name:`Final Recommendations`,description:`Synthesizing insights into actionable advice`}],t=[{id:1,name:`Writing Quality`,description:`Analyzing grammar, readability, and writing style`},{id:2,name:`Personality Appeal`,description:`Evaluating personality traits and attractiveness signals`},{id:3,name:`Interest Analysis`,description:`Assessing hobbies, interests, and lifestyle appeal`},{id:4,name:`Dating Intent`,description:`Analyzing clarity of dating goals and relationship signals`},{id:5,name:`Engagement Factor`,description:`Evaluating conversation starters and match appeal`}],n=[{id:1,name:`Technical Assessment`,description:`Analyzing image quality and editing potential`},{id:2,name:`Lighting Analysis`,description:`Evaluating lighting conditions and improvement opportunities`},{id:3,name:`Composition Review`,description:`Analyzing framing, angles, and compositional elements`},{id:4,name:`Style & Color`,description:`Assessing color balance, saturation, and style opportunities`},{id:5,name:`Enhancement Opportunities`,description:`Identifying potential improvements and editing recommendations`}];export{e as ANALYSIS_STEPS,t as BIO_ANALYSIS_STEPS,n as EDITING_ANALYSIS_STEPS};