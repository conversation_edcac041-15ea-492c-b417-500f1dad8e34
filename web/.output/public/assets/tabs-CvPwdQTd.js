import{__toESM as e,cn as t,require_jsx_runtime as n,require_react as r,require_react_dom as i}from"./main-B4G73TvM.js";import{createSlot as a}from"./createLucideIcon-JB7IMeGf.js";function o(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),n===!1||!r.defaultPrevented)return t?.(r)}}var s=e(r(),1),c=e(n(),1);function l(e,t=[]){let n=[];function r(t,r){let i=s.createContext(r),a=n.length;n=[...n,r];let o=t=>{let{scope:n,children:r,...o}=t,l=n?.[e]?.[a]||i,u=s.useMemo(()=>o,Object.values(o));return(0,c.jsx)(l.Provider,{value:u,children:r})};o.displayName=t+`Provider`;function l(n,o){let c=o?.[e]?.[a]||i,l=s.useContext(c);if(l)return l;if(r!==void 0)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}return[o,l]}let i=()=>{let t=n.map(e=>s.createContext(e));return function(n){let r=n?.[e]||t;return s.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return i.scopeName=e,[r,u(i,...t)]}function u(...e){let t=e[0];if(e.length===1)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e),a=i[`__scope${r}`];return{...t,...a}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}function d(e,t){if(typeof e==`function`)return e(t);e!=null&&(e.current=t)}function f(...e){return t=>{let n=!1,r=e.map(e=>{let r=d(e,t);return!n&&typeof r==`function`&&(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];typeof n==`function`?n():d(e[t],null)}}}}function p(...e){return s.useCallback(f(...e),e)}function m(e){let t=e+`CollectionProvider`,[n,r]=l(t),[i,o]=n(t,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:t,children:n}=e,r=s.useRef(null),a=s.useRef(new Map).current;return(0,c.jsx)(i,{scope:t,itemMap:a,collectionRef:r,children:n})};u.displayName=t;let d=e+`CollectionSlot`,f=a(d),m=s.forwardRef((e,t)=>{let{scope:n,children:r}=e,i=o(d,n),a=p(t,i.collectionRef);return(0,c.jsx)(f,{ref:a,children:r})});m.displayName=d;let h=e+`CollectionItemSlot`,g=`data-radix-collection-item`,_=a(h),v=s.forwardRef((e,t)=>{let{scope:n,children:r,...i}=e,a=s.useRef(null),l=p(t,a),u=o(h,n);return s.useEffect(()=>(u.itemMap.set(a,{ref:a,...i}),()=>void u.itemMap.delete(a))),(0,c.jsx)(_,{[g]:``,ref:l,children:r})});v.displayName=h;function y(t){let n=o(e+`CollectionConsumer`,t),r=s.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${g}]`)),r=Array.from(n.itemMap.values()),i=r.sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current));return i},[n.collectionRef,n.itemMap]);return r}return[{Provider:u,Slot:m,ItemSlot:v},y,r]}var h=globalThis?.document?s.useLayoutEffect:()=>{},g=s.useId||(()=>void 0),_=0;function v(e){let[t,n]=s.useState(g());return h(()=>{e||n(e=>e??String(_++))},[e]),e||(t?`radix-${t}`:``)}var y=e(i(),1),b=[`a`,`button`,`div`,`form`,`h2`,`h3`,`img`,`input`,`label`,`li`,`nav`,`ol`,`p`,`select`,`span`,`svg`,`ul`],x=b.reduce((e,t)=>{let n=a(`Primitive.${t}`),r=s.forwardRef((e,r)=>{let{asChild:i,...a}=e,o=i?n:t;return typeof window<`u`&&(window[Symbol.for(`radix-ui`)]=!0),(0,c.jsx)(o,{...a,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function ee(e){let t=s.useRef(e);return s.useEffect(()=>{t.current=e}),s.useMemo(()=>(...e)=>t.current?.(...e),[])}var S=globalThis?.document?s.useLayoutEffect:()=>{},C=s.useInsertionEffect||S;function w({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,o]=T({defaultProp:t,onChange:n}),c=e!==void 0,l=c?e:i;{let t=s.useRef(e!==void 0);s.useEffect(()=>{let e=t.current;if(e!==c){let t=e?`controlled`:`uncontrolled`,n=c?`controlled`:`uncontrolled`;console.warn(`${r} is changing from ${t} to ${n}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}let u=s.useCallback(t=>{if(c){let n=E(t)?t(e):t;n!==e&&o.current?.(n)}else a(t)},[c,e,a,o]);return[l,u]}function T({defaultProp:e,onChange:t}){let[n,r]=s.useState(e),i=s.useRef(n),a=s.useRef(t);return C(()=>{a.current=t},[t]),s.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}function E(e){return typeof e==`function`}var D=Symbol(`RADIX:SYNC_STATE`),O=s.createContext(void 0);function k(e){let t=s.useContext(O);return e||t||`ltr`}var A=`rovingFocusGroup.onEntryFocus`,te={bubbles:!1,cancelable:!0},j=`RovingFocusGroup`,[M,N,ne]=m(j),[re,P]=l(j,[ne]),[ie,ae]=re(j),F=s.forwardRef((e,t)=>(0,c.jsx)(M.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,c.jsx)(M.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,c.jsx)(oe,{...e,ref:t})})}));F.displayName=j;var oe=s.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:i=!1,dir:a,currentTabStopId:l,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:d,onEntryFocus:f,preventScrollOnEntryFocus:m=!1,...h}=e,g=s.useRef(null),_=p(t,g),v=k(a),[y,b]=w({prop:l,defaultProp:u??null,onChange:d,caller:j}),[S,C]=s.useState(!1),T=ee(f),E=N(n),D=s.useRef(!1),[O,M]=s.useState(0);return s.useEffect(()=>{let e=g.current;if(e)return e.addEventListener(A,T),()=>e.removeEventListener(A,T)},[T]),(0,c.jsx)(ie,{scope:n,orientation:r,dir:v,loop:i,currentTabStopId:y,onItemFocus:s.useCallback(e=>b(e),[b]),onItemShiftTab:s.useCallback(()=>C(!0),[]),onFocusableItemAdd:s.useCallback(()=>M(e=>e+1),[]),onFocusableItemRemove:s.useCallback(()=>M(e=>e-1),[]),children:(0,c.jsx)(x.div,{tabIndex:S||O===0?-1:0,"data-orientation":r,...h,ref:_,style:{outline:`none`,...e.style},onMouseDown:o(e.onMouseDown,()=>{D.current=!0}),onFocus:o(e.onFocus,e=>{let t=!D.current;if(e.target===e.currentTarget&&t&&!S){let t=new CustomEvent(A,te);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=E().filter(e=>e.focusable),t=e.find(e=>e.active),n=e.find(e=>e.id===y),r=[t,n,...e].filter(Boolean),i=r.map(e=>e.ref.current);R(i,m)}}D.current=!1}),onBlur:o(e.onBlur,()=>C(!1))})})}),I=`RovingFocusGroupItem`,L=s.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:i=!1,tabStopId:a,children:l,...u}=e,d=v(),f=a||d,p=ae(I,n),m=p.currentTabStopId===f,h=N(n),{onFocusableItemAdd:g,onFocusableItemRemove:_,currentTabStopId:y}=p;return s.useEffect(()=>{if(r)return g(),()=>_()},[r,g,_]),(0,c.jsx)(M.ItemSlot,{scope:n,id:f,focusable:r,active:i,children:(0,c.jsx)(x.span,{tabIndex:m?0:-1,"data-orientation":p.orientation,...u,ref:t,onMouseDown:o(e.onMouseDown,e=>{r?p.onItemFocus(f):e.preventDefault()}),onFocus:o(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:o(e.onKeyDown,e=>{if(e.key===`Tab`&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=le(e,p.orientation,p.dir);if(t!==void 0){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=h().filter(e=>e.focusable),r=n.map(e=>e.ref.current);if(t===`last`)r.reverse();else if(t===`prev`||t===`next`){t===`prev`&&r.reverse();let n=r.indexOf(e.currentTarget);r=p.loop?ue(r,n+1):r.slice(n+1)}setTimeout(()=>R(r))}}),children:typeof l==`function`?l({isCurrentTabStop:m,hasTabStop:y!=null}):l})})});L.displayName=I;var se={ArrowLeft:`prev`,ArrowUp:`prev`,ArrowRight:`next`,ArrowDown:`next`,PageUp:`first`,Home:`first`,PageDown:`last`,End:`last`};function ce(e,t){return t===`rtl`?e===`ArrowLeft`?`ArrowRight`:e===`ArrowRight`?`ArrowLeft`:e:e}function le(e,t,n){let r=ce(e.key,n);if(!(t===`vertical`&&[`ArrowLeft`,`ArrowRight`].includes(r))&&!(t===`horizontal`&&[`ArrowUp`,`ArrowDown`].includes(r)))return se[r]}function R(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function ue(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var de=F,fe=L;function z(e,t){if(typeof e==`function`)return e(t);e!=null&&(e.current=t)}function pe(...e){return t=>{let n=!1,r=e.map(e=>{let r=z(e,t);return!n&&typeof r==`function`&&(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];typeof n==`function`?n():z(e[t],null)}}}}function me(...e){return s.useCallback(pe(...e),e)}var B=globalThis?.document?s.useLayoutEffect:()=>{};function he(e,t){return s.useReducer((e,n)=>{let r=t[e][n];return r??e},e)}var ge=e=>{let{present:t,children:n}=e,r=_e(t),i=typeof n==`function`?n({present:r.isPresent}):s.Children.only(n),a=me(r.ref,ve(i)),o=typeof n==`function`;return o||r.isPresent?s.cloneElement(i,{ref:a}):null};ge.displayName=`Presence`;function _e(e){let[t,n]=s.useState(),r=s.useRef(null),i=s.useRef(e),a=s.useRef(`none`),o=e?`mounted`:`unmounted`,[c,l]=he(o,{mounted:{UNMOUNT:`unmounted`,ANIMATION_OUT:`unmountSuspended`},unmountSuspended:{MOUNT:`mounted`,ANIMATION_END:`unmounted`},unmounted:{MOUNT:`mounted`}});return s.useEffect(()=>{let e=V(r.current);a.current=c===`mounted`?e:`none`},[c]),B(()=>{let t=r.current,n=i.current,o=n!==e;if(o){let r=a.current,o=V(t);if(e)l(`MOUNT`);else if(o===`none`||t?.display===`none`)l(`UNMOUNT`);else{let e=r!==o;l(n&&e?`ANIMATION_OUT`:`UNMOUNT`)}i.current=e}},[e,l]),B(()=>{if(t){let e,n=t.ownerDocument.defaultView??window,o=a=>{let o=V(r.current),s=o.includes(a.animationName);if(a.target===t&&s&&(l(`ANIMATION_END`),!i.current)){let r=t.style.animationFillMode;t.style.animationFillMode=`forwards`,e=n.setTimeout(()=>{t.style.animationFillMode===`forwards`&&(t.style.animationFillMode=r)})}},s=e=>{e.target===t&&(a.current=V(r.current))};return t.addEventListener(`animationstart`,s),t.addEventListener(`animationcancel`,o),t.addEventListener(`animationend`,o),()=>{n.clearTimeout(e),t.removeEventListener(`animationstart`,s),t.removeEventListener(`animationcancel`,o),t.removeEventListener(`animationend`,o)}}else l(`ANIMATION_END`)},[t,l]),{isPresent:[`mounted`,`unmountSuspended`].includes(c),ref:s.useCallback(e=>{r.current=e?getComputedStyle(e):null,n(e)},[])}}function V(e){return e?.animationName||`none`}function ve(e){let t=Object.getOwnPropertyDescriptor(e.props,`ref`)?.get,n=t&&`isReactWarning`in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,`ref`)?.get,n=t&&`isReactWarning`in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var H=`Tabs`,[ye,be]=l(H,[P]),U=P(),[xe,W]=ye(H),G=s.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:i,defaultValue:a,orientation:o=`horizontal`,dir:s,activationMode:l=`automatic`,...u}=e,d=k(s),[f,p]=w({prop:r,onChange:i,defaultProp:a??``,caller:H});return(0,c.jsx)(xe,{scope:n,baseId:v(),value:f,onValueChange:p,orientation:o,dir:d,activationMode:l,children:(0,c.jsx)(x.div,{dir:d,"data-orientation":o,...u,ref:t})})});G.displayName=H;var K=`TabsList`,q=s.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...i}=e,a=W(K,n),o=U(n);return(0,c.jsx)(de,{asChild:!0,...o,orientation:a.orientation,dir:a.dir,loop:r,children:(0,c.jsx)(x.div,{role:`tablist`,"aria-orientation":a.orientation,...i,ref:t})})});q.displayName=K;var J=`TabsTrigger`,Y=s.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:i=!1,...a}=e,s=W(J,n),l=U(n),u=Q(s.baseId,r),d=$(s.baseId,r),f=r===s.value;return(0,c.jsx)(fe,{asChild:!0,...l,focusable:!i,active:f,children:(0,c.jsx)(x.button,{type:`button`,role:`tab`,"aria-selected":f,"aria-controls":d,"data-state":f?`active`:`inactive`,"data-disabled":i?``:void 0,disabled:i,id:u,...a,ref:t,onMouseDown:o(e.onMouseDown,e=>{!i&&e.button===0&&e.ctrlKey===!1?s.onValueChange(r):e.preventDefault()}),onKeyDown:o(e.onKeyDown,e=>{[` `,`Enter`].includes(e.key)&&s.onValueChange(r)}),onFocus:o(e.onFocus,()=>{let e=s.activationMode!==`manual`;!f&&!i&&e&&s.onValueChange(r)})})})});Y.displayName=J;var X=`TabsContent`,Z=s.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,forceMount:i,children:a,...o}=e,l=W(X,n),u=Q(l.baseId,r),d=$(l.baseId,r),f=r===l.value,p=s.useRef(f);return s.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,c.jsx)(ge,{present:i||f,children:({present:n})=>(0,c.jsx)(x.div,{"data-state":f?`active`:`inactive`,"data-orientation":l.orientation,role:`tabpanel`,"aria-labelledby":u,hidden:!n,id:d,tabIndex:0,...o,ref:t,style:{...e.style,animationDuration:p.current?`0s`:void 0},children:n&&a})})});Z.displayName=X;function Q(e,t){return`${e}-trigger-${t}`}function $(e,t){return`${e}-content-${t}`}var Se=G,Ce=q,we=Y,Te=Z;const Ee=Se,De=s.forwardRef(({className:e,...n},r)=>(0,c.jsx)(Ce,{ref:r,className:t(`inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground`,e),...n}));De.displayName=Ce.displayName;const Oe=s.forwardRef(({className:e,...n},r)=>(0,c.jsx)(we,{ref:r,className:t(`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-base font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-gradient-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm`,e),...n}));Oe.displayName=we.displayName;const ke=s.forwardRef(({className:e,...n},r)=>(0,c.jsx)(Te,{ref:r,className:t(`mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2`,e),...n}));ke.displayName=Te.displayName;export{Ee as Tabs,ke as TabsContent,De as TabsList,Oe as TabsTrigger};