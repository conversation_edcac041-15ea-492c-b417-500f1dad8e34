const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/welcome-DaUTS0x0.js","assets/uiComponents-BgWMuAlY.js","assets/createLucideIcon-JB7IMeGf.js","assets/arrow-left-CrPLcqAC.js","assets/star-Dm2nqXPr.js","assets/sparkles-BbQK6jEu.js","assets/card-Bzfaa5B9.js","assets/routes-BLsNgnxf.js","assets/trending-up-BvkHvnfP.js","assets/image-editor-test-BwhwjrcH.js","assets/image-editing-service-Ck8uvW7j.js","assets/dist-CgCb95MR.js","assets/analysis-BhwX0Tn_.js","assets/dist-DGuqR-4V.js","assets/trash-2-CkLpzZy2.js","assets/progress-BIjJajwn.js","assets/slider-DbQhj1b4.js","assets/tabs-CvPwdQTd.js","assets/download-D5v-g3Ed.js","assets/settings-Tged_CGs.js","assets/badge-B_rsQKkv.js","assets/image-analyzer-pro-BSNjoE5E.js","assets/dist-C61kQO-x.js","assets/PrivacyNotice-C9kEPnp4.js","assets/shield-Ct2yHJj1.js","assets/storage-BLisfAL7.js","assets/brain-wUBLYMm0.js","assets/camera-P6IreDGA.js","assets/crown-BUU0WoJh.js","assets/advanced-scoring-CKP2w-nj.js","assets/image-analyzer-mcjslnNz.js","assets/analysis-service-DxWJ7Psk.js","assets/dashboard-BBZPk5g4.js","assets/ProtectedRoute-DIpB4JWB.js","assets/bio-analyzer-pro-DWKz7lQ3.js","assets/textarea-BtYj4OS8.js","assets/user-DxSNRym-.js","assets/bio-analyzer-DgsoUF1K.js","assets/analysis-comparison-Cnr7fkj1.js","assets/account-settings-C00NADvv.js"])))=>i.map(i=>d[i]);
var e=Object.create,t=Object.defineProperty,n=Object.getOwnPropertyDescriptor,r=Object.getOwnPropertyNames,i=Object.getPrototypeOf,a=Object.prototype.hasOwnProperty,o=(e,t)=>()=>(e&&(t=e(e=0)),t),s=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),c=(e,n)=>{for(var r in n)t(e,r,{get:n[r],enumerable:!0})},l=(e,i,o,s)=>{if(i&&typeof i==`object`||typeof i==`function`)for(var c=r(i),l=0,u=c.length,d;l<u;l++)d=c[l],!a.call(e,d)&&d!==o&&t(e,d,{get:(e=>i[e]).bind(null,d),enumerable:!(s=n(i,d))||s.enumerable});return e},u=(n,r,a)=>(a=n==null?{}:e(i(n)),l(r||!n||!n.__esModule?t(a,`default`,{value:n,enumerable:!0}):a,n)),d=e=>l(t({},`__esModule`,{value:!0}),e),f=!0,p=`Invariant failed`;function m(e,t){if(!e){if(f)throw Error(p);var n=typeof t==`function`?t():t,r=n?`${p}: ${n}`:p;throw Error(r)}}const h=new WeakMap,g=new WeakMap,_={current:[]};let v=!1,y=0;const b=new Set,x=new Map;function S(e){let t=Array.from(e).sort((e,t)=>e instanceof te&&e.options.deps.includes(t)?1:t instanceof te&&t.options.deps.includes(e)?-1:0);for(let e of t){if(_.current.includes(e))continue;_.current.push(e),e.recompute();let t=g.get(e);if(t)for(let e of t){let t=h.get(e);if(!t)continue;S(t)}}}function C(e){e.listeners.forEach(t=>t({prevVal:e.prevState,currentVal:e.state}))}function w(e){e.listeners.forEach(t=>t({prevVal:e.prevState,currentVal:e.state}))}function T(e){if(y>0&&!x.has(e)&&x.set(e,e.prevState),b.add(e),!(y>0)&&!v)try{for(v=!0;b.size>0;){let e=Array.from(b);b.clear();for(let t of e){let e=x.get(t)??t.prevState;t.prevState=e,C(t)}for(let t of e){let e=h.get(t);if(!e)continue;_.current.push(t),S(e)}for(let t of e){let e=h.get(t);if(!e)continue;for(let t of e)w(t)}}}finally{v=!1,_.current=[],x.clear()}}function E(e){y++;try{e()}finally{if(y--,y===0){let e=Array.from(b)[0];e&&T(e)}}}function ee(e){return typeof e==`function`}var D=class{constructor(e,t){this.listeners=new Set,this.subscribe=e=>{var t,n;this.listeners.add(e);let r=(n=(t=this.options)?.onSubscribe)?.call(t,e,this);return()=>{this.listeners.delete(e),r?.()}},this.prevState=e,this.state=e,this.options=t}setState(e){var t,n,r;this.prevState=this.state,(t=this.options)?.updateFn?this.state=this.options.updateFn(this.prevState)(e):ee(e)?this.state=e(this.prevState):this.state=e,(r=(n=this.options)?.onUpdate)==null||r.call(n),T(this)}},te=class e{constructor(t){this.listeners=new Set,this._subscriptions=[],this.lastSeenDepValues=[],this.getDepVals=()=>{let e=[],t=[];for(let n of this.options.deps)e.push(n.prevState),t.push(n.state);return this.lastSeenDepValues=t,{prevDepVals:e,currDepVals:t,prevVal:this.prevState??void 0}},this.recompute=()=>{var e,t;this.prevState=this.state;let{prevDepVals:n,currDepVals:r,prevVal:i}=this.getDepVals();this.state=this.options.fn({prevDepVals:n,currDepVals:r,prevVal:i}),(t=(e=this.options).onUpdate)==null||t.call(e)},this.checkIfRecalculationNeededDeeply=()=>{for(let t of this.options.deps)t instanceof e&&t.checkIfRecalculationNeededDeeply();let t=!1,n=this.lastSeenDepValues,{currDepVals:r}=this.getDepVals();for(let e=0;e<r.length;e++)if(r[e]!==n[e]){t=!0;break}t&&this.recompute()},this.mount=()=>(this.registerOnGraph(),this.checkIfRecalculationNeededDeeply(),()=>{this.unregisterFromGraph();for(let e of this._subscriptions)e()}),this.subscribe=e=>{var t,n;this.listeners.add(e);let r=(n=(t=this.options).onSubscribe)?.call(t,e,this);return()=>{this.listeners.delete(e),r?.()}},this.options=t,this.state=t.fn({prevDepVals:void 0,prevVal:void 0,currDepVals:this.getDepVals().currDepVals})}registerOnGraph(t=this.options.deps){for(let n of t)if(n instanceof e)n.registerOnGraph(),this.registerOnGraph(n.options.deps);else if(n instanceof D){let e=h.get(n);e||(e=new Set,h.set(n,e)),e.add(this);let t=g.get(this);t||(t=new Set,g.set(this,t)),t.add(n)}}unregisterFromGraph(t=this.options.deps){for(let n of t)if(n instanceof e)this.unregisterFromGraph(n.options.deps);else if(n instanceof D){let e=h.get(n);e&&e.delete(this);let t=g.get(this);t&&t.delete(n)}}};function ne(e){return e[e.length-1]}function re(e){return typeof e==`function`}function O(e,t){return re(e)?e(t):e}function k(e,t){return t.reduce((t,n)=>(t[n]=e[n],t),{})}function A(e,t){if(e===t)return e;let n=t,r=se(e)&&se(n);if(r||ie(e)&&ie(n)){let t=r?e:Object.keys(e).concat(Object.getOwnPropertySymbols(e)),i=t.length,a=r?n:Object.keys(n).concat(Object.getOwnPropertySymbols(n)),o=a.length,s=r?[]:{},c=0;for(let i=0;i<o;i++){let o=r?i:a[i];(!r&&t.includes(o)||r)&&e[o]===void 0&&n[o]===void 0?(s[o]=void 0,c++):(s[o]=A(e[o],n[o]),s[o]===e[o]&&e[o]!==void 0&&c++)}return i===o&&c===i?e:s}return n}function ie(e){return ae(e)&&Object.getOwnPropertyNames(e).length===Object.keys(e).length}function ae(e){if(!oe(e))return!1;let t=e.constructor;if(t===void 0)return!0;let n=t.prototype;return!(!oe(n)||!n.hasOwnProperty(`isPrototypeOf`))}function oe(e){return Object.prototype.toString.call(e)===`[object Object]`}function se(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function j(e,t){let n=Object.keys(e);return t&&(n=n.filter(t=>e[t]!==void 0)),n}function M(e,t,n){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(ae(e)&&ae(t)){let r=n?.ignoreUndefined??!0,i=j(e,r),a=j(t,r);return!n?.partial&&i.length!==a.length?!1:a.every(r=>M(e[r],t[r],n))}return Array.isArray(e)&&Array.isArray(t)&&e.length===t.length?!e.some((e,r)=>!M(e,t[r],n)):!1}function ce(e){let t,n,r=new Promise((e,r)=>{t=e,n=r});return r.status=`pending`,r.resolve=n=>{r.status=`resolved`,r.value=n,t(n),e?.(n)},r.reject=e=>{r.status=`rejected`,n(e)},r}function le(e){return typeof e?.message==`string`?e.message.startsWith(`Failed to fetch dynamically imported module`)||e.message.startsWith(`error loading dynamically imported module`)||e.message.startsWith(`Importing a module script failed`):!1}function ue(e){return{id:e.i,__beforeLoadContext:e.b,loaderData:e.l,status:e.s,ssr:e.ssr,updatedAt:e.u,error:e.e}}async function de(e){var t,n,r;m((t=window.$_TSR)?.router,`Expected to find a dehydrated data on window.$_TSR.router, but we did not. Please file an issue!`);let{manifest:i,dehydratedData:a,lastMatchId:o}=window.$_TSR.router;e.ssr={manifest:i};let s=e.matchRoutes(e.state.location),c=Promise.all(s.map(t=>{let n=e.looseRoutesById[t.routeId];return e.loadRouteChunk(n)}));function l(t){let n=e.looseRoutesById[t.routeId],r=n.options.pendingMinMs??e.options.defaultPendingMinMs;if(r){let n=ce();t.minPendingPromise=n,t._forcePending=!0,setTimeout(()=>{n.resolve(),e.updateMatch(t.id,e=>({...e,minPendingPromise:void 0,_forcePending:void 0}))},r)}}let u;s.forEach(e=>{let t=window.$_TSR.router.matches.find(t=>t.i===e.id);if(!t){Object.assign(e,{dehydrated:!1,ssr:!1});return}Object.assign(e,ue(t)),e.ssr===!1?e._dehydrated=!1:e._dehydrated=!0,(e.ssr===`data-only`||e.ssr===!1)&&u===void 0&&(u=e.index,l(e))}),e.__store.setState(e=>({...e,matches:s})),await(r=(n=e.options).hydrate)?.call(n,a),await Promise.all(e.state.matches.map(async t=>{var n,r,i,a,o,c;let l=e.looseRoutesById[t.routeId],u=e.state.matches[t.index-1],d=u?.context??e.options.context??{},f={deps:t.loaderDeps,params:t.params,context:d,location:e.state.location,navigate:t=>e.navigate({...t,_fromLocation:e.state.location}),buildLocation:e.buildLocation,cause:t.cause,abortController:t.abortController,preload:!1,matches:s};t.__routeContext=(r=(n=l.options).context)?.call(n,f)??{},t.context={...d,...t.__routeContext,...t.__beforeLoadContext};let p={matches:e.state.matches,match:t,params:t.params,loaderData:t.loaderData},m=await(a=(i=l.options).head)?.call(i,p),h=await(c=(o=l.options).scripts)?.call(o,p);t.meta=m?.meta,t.links=m?.links,t.headScripts=m?.scripts,t.styles=m?.styles,t.scripts=h}));let d=s[s.length-1].id!==o,f=s.some(e=>e.ssr===!1);if(!f&&!d)return s.forEach(e=>{e._dehydrated=void 0}),c;let p=Promise.resolve().then(()=>e.load()).catch(e=>{console.error(`Error during router hydration:`,e)});if(d){let t=s[1];m(t,`Expected to find a match below the root match in SPA mode.`),l(t),t._displayPending=!0,t.displayPendingPromise=p,p.then(()=>{E(()=>{e.__store.state.status===`pending`&&e.__store.setState(e=>({...e,status:`idle`,resolvedLocation:e.location})),e.updateMatch(t.id,e=>({...e,_displayPending:void 0,displayPendingPromise:void 0}))})})}return c}const fe=`__TSR_index`,pe=`popstate`,me=`beforeunload`;function he(e){let t=e.getLocation(),n=new Set,r=r=>{t=e.getLocation(),n.forEach(e=>e({location:t,action:r}))},i=n=>{e.notifyOnIndexChange??!0?r(n):t=e.getLocation()},a=async({task:n,navigateOpts:r,...i})=>{var a,o;let s=r?.ignoreBlocker??!1;if(s){n();return}let c=(a=e.getBlockers)?.call(e)??[],l=i.type===`PUSH`||i.type===`REPLACE`;if(typeof document<`u`&&c.length&&l)for(let n of c){let r=ye(i.path,i.state),a=await n.blockerFn({currentLocation:t,nextLocation:r,action:i.type});if(a){(o=e.onBlocked)==null||o.call(e);return}}n()};return{get location(){return t},get length(){return e.getLength()},subscribers:n,subscribe:e=>(n.add(e),()=>{n.delete(e)}),push:(n,i,o)=>{let s=t.state[fe];i=ge(s+1,i),a({task:()=>{e.pushState(n,i),r({type:`PUSH`})},navigateOpts:o,type:`PUSH`,path:n,state:i})},replace:(n,i,o)=>{let s=t.state[fe];i=ge(s,i),a({task:()=>{e.replaceState(n,i),r({type:`REPLACE`})},navigateOpts:o,type:`REPLACE`,path:n,state:i})},go:(t,n)=>{a({task:()=>{e.go(t),i({type:`GO`,index:t})},navigateOpts:n,type:`GO`})},back:t=>{a({task:()=>{e.back(t?.ignoreBlocker??!1),i({type:`BACK`})},navigateOpts:t,type:`BACK`})},forward:t=>{a({task:()=>{e.forward(t?.ignoreBlocker??!1),i({type:`FORWARD`})},navigateOpts:t,type:`FORWARD`})},canGoBack:()=>t.state[fe]!==0,createHref:t=>e.createHref(t),block:t=>{var n;if(!e.setBlockers)return()=>{};let r=(n=e.getBlockers)?.call(e)??[];return e.setBlockers([...r,t]),()=>{var n,r;let i=(n=e.getBlockers)?.call(e)??[];(r=e.setBlockers)==null||r.call(e,i.filter(e=>e!==t))}},flush:()=>{var t;return(t=e.flush)?.call(e)},destroy:()=>{var t;return(t=e.destroy)?.call(e)},notify:r}}function ge(e,t){t||={};let n=be();return{...t,key:n,__TSR_key:n,[fe]:e}}function _e(e){var t,n;let r=e?.window??(typeof document<`u`?window:void 0),i=r.history.pushState,a=r.history.replaceState,o=[],s=()=>o,c=e=>o=e,l=e?.createHref??(e=>e),u=e?.parseLocation??(()=>ye(`${r.location.pathname}${r.location.search}${r.location.hash}`,r.history.state));if(!(t=r.history.state)?.__TSR_key&&!(n=r.history.state)?.key){let e=be();r.history.replaceState({[fe]:0,key:e,__TSR_key:e},``)}let d=u(),f,p=!1,m=!1,h=!1,g=!1,_=()=>d,v,y,b=()=>{v&&(T._ignoreSubscribers=!0,(v.isPush?r.history.pushState:r.history.replaceState)(v.state,``,v.href),T._ignoreSubscribers=!1,v=void 0,y=void 0,f=void 0)},x=(e,t,n)=>{let r=l(t);y||(f=d),d=ye(t,n),v={href:r,state:n,isPush:v?.isPush||e===`push`},y||=Promise.resolve().then(()=>b())},S=e=>{d=u(),T.notify({type:e})},C=async()=>{if(m){m=!1;return}let e=u(),t=e.state[fe]-d.state[fe],n=t===1,i=t===-1,a=!n&&!i||p;p=!1;let o=a?`GO`:i?`BACK`:`FORWARD`,c=a?{type:`GO`,index:t}:{type:i?`BACK`:`FORWARD`};if(h)h=!1;else{let t=s();if(typeof document<`u`&&t.length)for(let n of t){let t=await n.blockerFn({currentLocation:d,nextLocation:e,action:o});if(t){m=!0,r.history.go(1),T.notify(c);return}}}d=u(),T.notify(c)},w=e=>{if(g){g=!1;return}let t=!1,n=s();if(typeof document<`u`&&n.length)for(let e of n){let n=e.enableBeforeUnload??!0;if(n===!0){t=!0;break}if(typeof n==`function`&&n()===!0){t=!0;break}}if(t)return e.preventDefault(),e.returnValue=``},T=he({getLocation:_,getLength:()=>r.history.length,pushState:(e,t)=>x(`push`,e,t),replaceState:(e,t)=>x(`replace`,e,t),back:e=>(e&&(h=!0),g=!0,r.history.back()),forward:e=>{e&&(h=!0),g=!0,r.history.forward()},go:e=>{p=!0,r.history.go(e)},createHref:e=>l(e),flush:b,destroy:()=>{r.history.pushState=i,r.history.replaceState=a,r.removeEventListener(me,w,{capture:!0}),r.removeEventListener(pe,C)},onBlocked:()=>{f&&d!==f&&(d=f)},getBlockers:s,setBlockers:c,notifyOnIndexChange:!1});return r.addEventListener(me,w,{capture:!0}),r.addEventListener(pe,C),r.history.pushState=function(...e){let t=i.apply(r.history,e);return T._ignoreSubscribers||S(`PUSH`),t},r.history.replaceState=function(...e){let t=a.apply(r.history,e);return T._ignoreSubscribers||S(`REPLACE`),t},T}function ve(e={initialEntries:[`/`]}){let t=e.initialEntries,n=e.initialIndex?Math.min(Math.max(e.initialIndex,0),t.length-1):t.length-1,r=t.map((e,t)=>ge(t,void 0)),i=()=>ye(t[n],r[n]);return he({getLocation:i,getLength:()=>t.length,pushState:(e,i)=>{n<t.length-1&&(t.splice(n+1),r.splice(n+1)),r.push(i),t.push(e),n=Math.max(t.length-1,0)},replaceState:(e,i)=>{r[n]=i,t[n]=e},back:()=>{n=Math.max(n-1,0)},forward:()=>{n=Math.min(n+1,t.length-1)},go:e=>{n=Math.min(Math.max(n+e,0),t.length-1)},createHref:e=>e})}function ye(e,t){let n=e.indexOf(`#`),r=e.indexOf(`?`),i=be();return{href:e,pathname:e.substring(0,n>0?r>0?Math.min(n,r):n:r>0?r:e.length),hash:n>-1?e.substring(n):``,search:r>-1?e.slice(r,n===-1?void 0:n):``,state:t||{[fe]:0,key:i,__TSR_key:i}}}function be(){return(Math.random()+1).toString(36).substring(7)}function xe(e){return Se(e.filter(e=>e!==void 0).join(`/`))}function Se(e){return e.replace(/\/{2,}/g,`/`)}function Ce(e){return e===`/`?e:e.replace(/^\/{1,}/,``)}function we(e){return e===`/`?e:e.replace(/\/{1,}$/,``)}function Te(e){return we(Ce(e))}function Ee(e,t){return e?.endsWith(`/`)&&e!==`/`&&e!==`${t}/`?e.slice(0,-1):e}function De(e,t,n){return Ee(e,n)===Ee(t,n)}function Oe({basepath:e,base:t,to:n,trailingSlash:r=`never`,caseSensitive:i}){var a,o;t=Re(e,t,i),n=Re(e,n,i);let s=Pe(t),c=Pe(n);s.length>1&&(a=ne(s))?.value===`/`&&s.pop(),c.forEach((e,t)=>{e.value===`/`?t?t===c.length-1&&s.push(e):s=[e]:e.value===`..`?s.pop():e.value===`.`||s.push(e)}),s.length>1&&((o=ne(s))?.value===`/`?r===`never`&&s.pop():r===`always`&&s.push({type:`pathname`,value:`/`}));let l=s.map(e=>{if(e.type===`param`){let t=e.value.substring(1);if(e.prefixSegment&&e.suffixSegment)return`${e.prefixSegment}{$${t}}${e.suffixSegment}`;if(e.prefixSegment)return`${e.prefixSegment}{$${t}}`;if(e.suffixSegment)return`{$${t}}${e.suffixSegment}`}if(e.type===`optional-param`){let t=e.value.substring(1);return e.prefixSegment&&e.suffixSegment?`${e.prefixSegment}{-$${t}}${e.suffixSegment}`:e.prefixSegment?`${e.prefixSegment}{-$${t}}`:e.suffixSegment?`{-$${t}}${e.suffixSegment}`:`{-$${t}}`}if(e.type===`wildcard`){if(e.prefixSegment&&e.suffixSegment)return`${e.prefixSegment}{$}${e.suffixSegment}`;if(e.prefixSegment)return`${e.prefixSegment}{$}`;if(e.suffixSegment)return`{$}${e.suffixSegment}`}return e.value}),u=xe([e,...l]);return Se(u)}const ke=/^\$.{1,}$/,Ae=/^(.*?)\{(\$[a-zA-Z_$][a-zA-Z0-9_$]*)\}(.*)$/,je=/^(.*?)\{-(\$[a-zA-Z_$][a-zA-Z0-9_$]*)\}(.*)$/,Me=/^\$$/,Ne=/^(.*?)\{\$\}(.*)$/;function Pe(e){if(!e)return[];e=Se(e);let t=[];if(e.slice(0,1)===`/`&&(e=e.substring(1),t.push({type:`pathname`,value:`/`})),!e)return t;let n=e.split(`/`).filter(Boolean);return t.push(...n.map(e=>{let t=e.match(Ne);if(t){let e=t[1],n=t[2];return{type:`wildcard`,value:`$`,prefixSegment:e||void 0,suffixSegment:n||void 0}}let n=e.match(je);if(n){let e=n[1],t=n[2],r=n[3];return{type:`optional-param`,value:t,prefixSegment:e||void 0,suffixSegment:r||void 0}}let r=e.match(Ae);if(r){let e=r[1],t=r[2],n=r[3];return{type:`param`,value:``+t,prefixSegment:e||void 0,suffixSegment:n||void 0}}if(ke.test(e)){let t=e.substring(1);return{type:`param`,value:`$`+t,prefixSegment:void 0,suffixSegment:void 0}}return Me.test(e)?{type:`wildcard`,value:`$`,prefixSegment:void 0,suffixSegment:void 0}:{type:`pathname`,value:e.includes(`%25`)?e.split(`%25`).map(e=>decodeURI(e)).join(`%25`):decodeURI(e)}})),e.slice(-1)===`/`&&(e=e.substring(1),t.push({type:`pathname`,value:`/`})),t}function Fe({path:e,params:t,leaveWildcards:n,leaveParams:r,decodeCharMap:i}){let a=Pe(e);function o(e){let n=t[e],r=typeof n==`string`;return[`*`,`_splat`].includes(e)?r?encodeURI(n):n:r?Ie(n,i):n}let s=!1,c={},l=xe(a.map(e=>{if(e.type===`wildcard`){c._splat=t._splat;let r=e.prefixSegment||``,i=e.suffixSegment||``;if(!(`_splat`in t))return s=!0,n?`${r}${e.value}${i}`:r||i?`${r}${i}`:void 0;let a=o(`_splat`);return n?`${r}${e.value}${a??``}${i}`:`${r}${a}${i}`}if(e.type===`param`){let n=e.value.substring(1);!s&&!(n in t)&&(s=!0),c[n]=t[n];let i=e.prefixSegment||``,a=e.suffixSegment||``;if(r){let t=o(e.value);return`${i}${e.value}${t??``}${a}`}return`${i}${o(n)??`undefined`}${a}`}if(e.type===`optional-param`){let n=e.value.substring(1),i=e.prefixSegment||``,a=e.suffixSegment||``;if(!(n in t)||t[n]==null)return i||a?`${i}${a}`:void 0;if(c[n]=t[n],r){let t=o(e.value);return`${i}${e.value}${t??``}${a}`}return`${i}${o(n)??``}${a}`}return e.value}));return{usedParams:c,interpolatedPath:l,isMissingParams:s}}function Ie(e,t){let n=encodeURIComponent(e);if(t)for(let[e,r]of t)n=n.replaceAll(e,r);return n}function Le(e,t,n){let r=ze(e,t,n);if(!(n.to&&!r))return r??{}}function Re(e,t,n=!1){let r=n?e:e.toLowerCase(),i=n?t:t.toLowerCase();switch(!0){case r===`/`:return t;case i===r:return``;case t.length<e.length:return t;case i[r.length]!==`/`:return t;case i.startsWith(r):return t.slice(e.length);default:return t}}function ze(e,t,n){if(e!==`/`&&!t.startsWith(e))return;t=Re(e,t,n.caseSensitive);let r=Re(e,`${n.to??`$`}`,n.caseSensitive),i=Pe(t),a=Pe(r);t.startsWith(`/`)||i.unshift({type:`pathname`,value:`/`}),r.startsWith(`/`)||a.unshift({type:`pathname`,value:`/`});let o={},s=(()=>{var e,t;let r=0,s=0;for(;r<i.length||s<a.length;){let c=i[r],l=a[s],u=r>=i.length-1,d=s>=a.length-1;if(l){if(l.type===`wildcard`){let t=i.slice(r),n;if(l.prefixSegment||l.suffixSegment){if(!c)return!1;let r=l.prefixSegment||``,a=l.suffixSegment||``,o=c.value;if(`prefixSegment`in l&&!o.startsWith(r)||`suffixSegment`in l&&!(e=i[i.length-1])?.value.endsWith(a))return!1;let s=decodeURI(xe(t.map(e=>e.value)));r&&s.startsWith(r)&&(s=s.slice(r.length)),a&&s.endsWith(a)&&(s=s.slice(0,s.length-a.length)),n=s}else n=decodeURI(xe(t.map(e=>e.value)));return o[`*`]=n,o._splat=n,!0}if(l.type===`pathname`){if(l.value===`/`&&!c?.value){s++;continue}if(c){if(n.caseSensitive){if(l.value!==c.value)return!1}else if(l.value.toLowerCase()!==c.value.toLowerCase())return!1;r++,s++;continue}else return!1}if(l.type===`param`){if(!c||c.value===`/`)return!1;let e=``,t=!1;if(l.prefixSegment||l.suffixSegment){let n=l.prefixSegment||``,r=l.suffixSegment||``,i=c.value;if(n&&!i.startsWith(n)||r&&!i.endsWith(r))return!1;let a=i;n&&a.startsWith(n)&&(a=a.slice(n.length)),r&&a.endsWith(r)&&(a=a.slice(0,a.length-r.length)),e=decodeURIComponent(a),t=!0}else e=decodeURIComponent(c.value),t=!0;t&&(o[l.value.substring(1)]=e,r++),s++;continue}if(l.type===`optional-param`){if(!c){s++;continue}if(c.value===`/`){s++;continue}let e=``,t=!1;if(l.prefixSegment||l.suffixSegment){let n=l.prefixSegment||``,r=l.suffixSegment||``,i=c.value;if((!n||i.startsWith(n))&&(!r||i.endsWith(r))){let a=i;n&&a.startsWith(n)&&(a=a.slice(n.length)),r&&a.endsWith(r)&&(a=a.slice(0,a.length-r.length)),e=decodeURIComponent(a),t=!0}}else{let n=!0;for(let e=s+1;e<a.length;e++){let t=a[e];if(t?.type===`pathname`&&t.value===c.value){n=!1;break}if(t?.type===`param`||t?.type===`wildcard`)break}n&&(e=decodeURIComponent(c.value),t=!0)}t&&(o[l.value.substring(1)]=e,r++),s++;continue}}if(!u&&d)return o[`**`]=xe(i.slice(r+1).map(e=>e.value)),!!n.fuzzy&&l?.value!==`/`;if(r<i.length&&s>=a.length)return!1;if(s<a.length&&r>=i.length){for(let e=s;e<a.length;e++)if((t=a[e])?.type!==`optional-param`)return!1;break}break}return!0})();return s?o:void 0}function Be(e){return!!e?.isNotFound}function Ve(){try{if(typeof window<`u`&&typeof window.sessionStorage==`object`)return window.sessionStorage}catch{return}}const He=`tsr-scroll-restoration-v1_3`,Ue=(e,t)=>{let n;return(...r)=>{n||=setTimeout(()=>{e(...r),n=null},t)}};function We(){let e=Ve();if(!e)return;let t=e.getItem(He),n=t?JSON.parse(t):{};return{state:n,set:t=>(n=O(t,n)||n,e.setItem(He,JSON.stringify(n)))}}const Ge=We(),Ke=e=>e.state.__TSR_key||e.href;function qe(e){let t=[],n;for(;n=e.parentNode;)t.unshift(`${e.tagName}:nth-child(${[].indexOf.call(n.children,e)+1})`),e=n;return`${t.join(` > `)}`.toLowerCase()}let Je=!1;function Ye(e,t,n,r,i){var a;let o;try{o=JSON.parse(sessionStorage.getItem(e)||`{}`)}catch(e){console.error(e);return}let s=t||(a=window.history.state)?.key,c=o[s];Je=!0,(()=>{if(r&&c){for(let e in c){let t=c[e];if(e===`window`)window.scrollTo({top:t.scrollY,left:t.scrollX,behavior:n});else if(e){let n=document.querySelector(e);n&&(n.scrollLeft=t.scrollX,n.scrollTop=t.scrollY)}}return}let e=window.location.hash.split(`#`)[1];if(e){let t=(window.history.state||{}).__hashScrollIntoViewOptions??!0;if(t){let n=document.getElementById(e);n&&n.scrollIntoView(t)}return}[`window`,...i?.filter(e=>e!==`window`)??[]].forEach(e=>{let t=e===`window`?window:typeof e==`function`?e():document.querySelector(e);t&&t.scrollTo({top:0,left:0,behavior:n})})})(),Je=!1}function Xe(e,t){if(Ge===void 0)return;let n=t??e.options.scrollRestoration??!1;if(n&&(e.isScrollRestoring=!0),typeof document>`u`||e.isScrollRestorationSetup)return;e.isScrollRestorationSetup=!0,Je=!1;let r=e.options.getScrollRestorationKey||Ke;window.history.scrollRestoration=`manual`;let i=t=>{if(Je||!e.isScrollRestoring)return;let n=``;if(t.target===document||t.target===window)n=`window`;else{let e=t.target.getAttribute(`data-scroll-restoration-id`);n=e?`[data-scroll-restoration-id="${e}"]`:qe(t.target)}let i=r(e.state.location);Ge.set(e=>{let t=e[i]=e[i]||{},r=t[n]=t[n]||{};if(n===`window`)r.scrollX=window.scrollX||0,r.scrollY=window.scrollY||0;else if(n){let e=document.querySelector(n);e&&(r.scrollX=e.scrollLeft||0,r.scrollY=e.scrollTop||0)}return e})};typeof document<`u`&&document.addEventListener(`scroll`,Ue(i,100),!0),e.subscribe(`onRendered`,t=>{let n=r(t.toLocation);if(!e.resetNextScroll){e.resetNextScroll=!0;return}Ye(He,n,e.options.scrollRestorationBehavior||void 0,e.isScrollRestoring||void 0,e.options.scrollToTopSelectors||void 0),e.isScrollRestoring&&Ge.set(e=>(e[n]=e[n]||{},e))})}function Ze(e){if(typeof document<`u`&&document.querySelector){let t=e.state.location.state.__hashScrollIntoViewOptions??!0;if(t&&e.state.location.hash!==``){let n=document.getElementById(e.state.location.hash);n&&n.scrollIntoView(t)}}}function Qe(e,t){let n=Object.entries(e).flatMap(([e,t])=>Array.isArray(t)?t.map(t=>[e,String(t)]):[[e,String(t)]]),r=new URLSearchParams(n);return(t||``)+r.toString()}function $e(e){return e?e===`false`?!1:e===`true`?!0:e*0==0&&+e+``===e?+e:e:``}function et(e,t){let n=t?e.slice(t.length):e,r=new URLSearchParams(n),i=[...r.entries()];return i.reduce((e,[t,n])=>{let r=e[t];return r==null?e[t]=$e(n):e[t]=Array.isArray(r)?[...r,$e(n)]:[r,$e(n)],e},{})}const tt=rt(JSON.parse),nt=it(JSON.stringify,JSON.parse);function rt(e){return t=>{t.substring(0,1)===`?`&&(t=t.substring(1));let n=et(t);for(let t in n){let r=n[t];if(typeof r==`string`)try{n[t]=e(r)}catch{}}return n}}function it(e,t){function n(n){if(typeof n==`object`&&n)try{return e(n)}catch{}else if(typeof n==`string`&&typeof t==`function`)try{return t(n),e(n)}catch{}return n}return e=>{e={...e},Object.keys(e).forEach(t=>{let r=e[t];r===void 0||r===void 0?delete e[t]:e[t]=n(r)});let t=Qe(e).toString();return t?`?${t}`:``}}const at=`__root__`;function ot(e){if(e.statusCode=e.statusCode||e.code||307,!e.reloadDocument)try{new URL(`${e.href}`),e.reloadDocument=!0}catch{}let t=new Headers(e.headers||{});e.href&&t.get(`Location`)===null&&t.set(`Location`,e.href);let n=new Response(null,{status:e.statusCode,headers:t});if(n.options=e,e.throw)throw n;return n}function st(e){return e instanceof Response&&!!e.options}function ct(e){if(e instanceof Error){let t={name:e.name,message:e.message};return t}return{data:e}}function lt(e){let t=e.resolvedLocation,n=e.location,r=t?.pathname!==n.pathname,i=t?.href!==n.href,a=t?.hash!==n.hash;return{fromLocation:t,toLocation:n,pathChanged:r,hrefChanged:i,hashChanged:a}}var ut=class{constructor(e){this.tempLocationKey=`${Math.round(Math.random()*1e7)}`,this.resetNextScroll=!0,this.shouldViewTransition=void 0,this.isViewTransitionTypesSupported=void 0,this.subscribers=new Set,this.isScrollRestoring=!1,this.isScrollRestorationSetup=!1,this.startTransition=e=>e(),this.update=e=>{var t;e.notFoundRoute&&console.warn(`The notFoundRoute API is deprecated and will be removed in the next major version. See https://tanstack.com/router/v1/docs/framework/react/guide/not-found-errors#migrating-from-notfoundroute for more info.`);let n=this.options;this.options={...this.options,...e},this.isServer=this.options.isServer??typeof document>`u`,this.pathParamsDecodeCharMap=this.options.pathParamsAllowedCharacters?new Map(this.options.pathParamsAllowedCharacters.map(e=>[encodeURIComponent(e),e])):void 0,(!this.basepath||e.basepath&&e.basepath!==n.basepath)&&(e.basepath===void 0||e.basepath===``||e.basepath===`/`?this.basepath=`/`:this.basepath=`/${Te(e.basepath)}`),(!this.history||this.options.history&&this.options.history!==this.history)&&(this.history=this.options.history??(this.isServer?ve({initialEntries:[this.basepath||`/`]}):_e()),this.latestLocation=this.parseLocation()),this.options.routeTree!==this.routeTree&&(this.routeTree=this.options.routeTree,this.buildRouteTree()),this.__store||(this.__store=new D(pt(this.latestLocation),{onUpdate:()=>{this.__store.state={...this.state,cachedMatches:this.state.cachedMatches.filter(e=>![`redirected`].includes(e.status))}}}),Xe(this)),typeof window<`u`&&`CSS`in window&&typeof(t=window.CSS)?.supports==`function`&&(this.isViewTransitionTypesSupported=window.CSS.supports(`selector(:active-view-transition-type(a)`))},this.buildRouteTree=()=>{let{routesById:e,routesByPath:t,flatRoutes:n}=_t({routeTree:this.routeTree,initRoute:(e,t)=>{e.init({originalIndex:t})}});this.routesById=e,this.routesByPath=t,this.flatRoutes=n;let r=this.options.notFoundRoute;r&&(r.init({originalIndex:99999999999}),this.routesById[r.id]=r)},this.subscribe=(e,t)=>{let n={eventType:e,fn:t};return this.subscribers.add(n),()=>{this.subscribers.delete(n)}},this.emit=e=>{this.subscribers.forEach(t=>{t.eventType===e.type&&t.fn(e)})},this.parseLocation=(e,t)=>{let n=({pathname:t,search:n,hash:r,state:i})=>{let a=this.options.parseSearch(n),o=this.options.stringifySearch(a);return{pathname:t,searchStr:o,search:A(e?.search,a),hash:r.split(`#`).reverse()[0]??``,href:`${t}${o}${r}`,state:A(e?.state,i)}},r=n(t??this.history.location),{__tempLocation:i,__tempKey:a}=r.state;if(i&&(!a||a===this.tempLocationKey)){let e=n(i);return e.state.key=r.state.key,e.state.__TSR_key=r.state.__TSR_key,delete e.state.__tempLocation,{...e,maskedLocation:r}}return r},this.resolvePathWithBase=(e,t)=>{let n=Oe({basepath:this.basepath,base:e,to:Se(t),trailingSlash:this.options.trailingSlash,caseSensitive:this.options.caseSensitive});return n},this.matchRoutes=(e,t,n)=>typeof e==`string`?this.matchRoutesInternal({pathname:e,search:t},n):this.matchRoutesInternal(e,t),this.getMatchedRoutes=(e,t)=>vt({pathname:e,routePathname:t,basepath:this.basepath,caseSensitive:this.options.caseSensitive,routesByPath:this.routesByPath,routesById:this.routesById,flatRoutes:this.flatRoutes}),this.cancelMatch=e=>{let t=this.getMatch(e);t&&(t.abortController.abort(),this.updateMatch(e,e=>(clearTimeout(e.pendingTimeout),{...e,pendingTimeout:void 0})))},this.cancelMatches=()=>{var e;(e=this.state.pendingMatches)==null||e.forEach(e=>{this.cancelMatch(e.id)})},this.buildLocation=e=>{let t=(t={})=>{var n;let r=t._fromLocation||this.latestLocation,i=this.matchRoutes(r,{_buildLocation:!0}),a=ne(i),o=a.fullPath,s=t.to?this.resolvePathWithBase(o,`${t.to}`):this.resolvePathWithBase(o,`.`),c=!!t.to&&!this.comparePaths(t.to.toString(),o)&&!this.comparePaths(s,o);t.unsafeRelative===`path`?o=r.pathname:c&&t.from&&(o=t.from);let l=a.search,u={...a.params},d=t.to?this.resolvePathWithBase(o,`${t.to}`):this.resolvePathWithBase(o,`.`),f=t.params===!1||t.params===null?{}:(t.params??!0)===!0?u:{...u,...O(t.params,u)},p=Fe({path:d,params:f??{}}).interpolatedPath,m=this.matchRoutes(p,{},{_buildLocation:!0}).map(e=>this.looseRoutesById[e.routeId]);Object.keys(f).length>0&&m.map(e=>{var t;return(t=e.options.params)?.stringify??e.options.stringifyParams}).filter(Boolean).forEach(e=>{f={...f,...e(f)}});let h=Fe({path:d,params:f??{},leaveWildcards:!1,leaveParams:e.leaveParams,decodeCharMap:this.pathParamsDecodeCharMap}).interpolatedPath,g=l;if(e._includeValidateSearch&&(n=this.options.search)?.strict){let e={};m.forEach(t=>{try{t.options.validateSearch&&(e={...e,...mt(t.options.validateSearch,{...e,...g})??{}})}catch{}}),g=e}g=yt({search:g,dest:t,destRoutes:m,_includeValidateSearch:e._includeValidateSearch}),g=A(l,g);let _=this.options.stringifySearch(g),v=t.hash===!0?r.hash:t.hash?O(t.hash,r.hash):void 0,y=v?`#${v}`:``,b=t.state===!0?r.state:t.state?O(t.state,r.state):{};return b=A(r.state,b),{pathname:h,search:g,searchStr:_,state:b,hash:v??``,href:`${h}${_}${y}`,unmaskOnReload:t.unmaskOnReload}},n=(n={},r)=>{var i;let a=t(n),o=r?t(r):void 0;if(!o){let n={},s=(i=this.options.routeMasks)?.find(e=>{let t=Le(this.basepath,a.pathname,{to:e.from,caseSensitive:!1,fuzzy:!1});return t?(n=t,!0):!1});if(s){let{from:i,...a}=s;r={...k(e,[`from`]),...a,params:n},o=t(r)}}if(o){let e=t(r);a.maskedLocation=e}return a};return e.mask?n(e,{...k(e,[`from`]),...e.mask}):n(e)},this.commitLocation=({viewTransition:e,ignoreBlocker:t,...n})=>{let r=()=>{let e=[`key`,`__TSR_key`,`__TSR_index`,`__hashScrollIntoViewOptions`];e.forEach(e=>{n.state[e]=this.latestLocation.state[e]});let t=M(n.state,this.latestLocation.state);return e.forEach(e=>{delete n.state[e]}),t},i=this.latestLocation.href===n.href,a=this.commitLocationPromise;if(this.commitLocationPromise=ce(()=>{a?.resolve()}),i&&r())this.load();else{let{maskedLocation:r,hashScrollIntoView:i,...a}=n;r&&(a={...r,state:{...r.state,__tempKey:void 0,__tempLocation:{...a,search:a.searchStr,state:{...a.state,__tempKey:void 0,__tempLocation:void 0,__TSR_key:void 0,key:void 0}}}},(a.unmaskOnReload??this.options.unmaskOnReload??!1)&&(a.state.__tempKey=this.tempLocationKey)),a.state.__hashScrollIntoViewOptions=i??this.options.defaultHashScrollIntoView??!0,this.shouldViewTransition=e,this.history[n.replace?`replace`:`push`](a.href,a.state,{ignoreBlocker:t})}return this.resetNextScroll=n.resetScroll??!0,this.history.subscribers.size||this.load(),this.commitLocationPromise},this.buildAndCommitLocation=({replace:e,resetScroll:t,hashScrollIntoView:n,viewTransition:r,ignoreBlocker:i,href:a,...o}={})=>{if(a){let t=this.history.location.state.__TSR_index,n=ye(a,{__TSR_index:e?t:t+1});o.to=n.pathname,o.search=this.options.parseSearch(n.search),o.hash=n.hash.slice(1)}let s=this.buildLocation({...o,_includeValidateSearch:!0});return this.commitLocation({...s,viewTransition:r,replace:e,resetScroll:t,hashScrollIntoView:n,ignoreBlocker:i})},this.navigate=({to:e,reloadDocument:t,href:n,...r})=>{if(!t&&n)try{new URL(`${n}`),t=!0}catch{}if(t){if(!n){let t=this.buildLocation({to:e,...r});n=this.history.createHref(t.href)}r.replace?window.location.replace(n):window.location.href=n;return}return this.buildAndCommitLocation({...r,href:n,to:e,_isNavigate:!0})},this.beforeLoad=()=>{if(this.cancelMatches(),this.latestLocation=this.parseLocation(this.latestLocation),this.isServer){let e=this.buildLocation({to:this.latestLocation.pathname,search:!0,params:!0,hash:!0,state:!0,_includeValidateSearch:!0}),t=e=>{try{return encodeURI(decodeURI(e))}catch{return e}};if(Te(t(this.latestLocation.href))!==Te(t(e.href)))throw ot({href:e.href})}let e=this.matchRoutes(this.latestLocation);this.__store.setState(t=>({...t,status:`pending`,isLoading:!0,location:this.latestLocation,pendingMatches:e,cachedMatches:t.cachedMatches.filter(t=>!e.find(e=>e.id===t.id))}))},this.load=async e=>{let t,n,r;for(r=new Promise(i=>{this.startTransition(async()=>{var a;try{this.beforeLoad();let t=this.latestLocation,n=this.state.resolvedLocation;this.state.redirect||this.emit({type:`onBeforeNavigate`,...lt({resolvedLocation:n,location:t})}),this.emit({type:`onBeforeLoad`,...lt({resolvedLocation:n,location:t})}),await this.loadMatches({sync:e?.sync,matches:this.state.pendingMatches,location:t,onReady:async()=>{this.startViewTransition(async()=>{let e,t,n;E(()=>{this.__store.setState(r=>{let i=r.matches,a=r.pendingMatches||r.matches;return e=i.filter(e=>!a.find(t=>t.id===e.id)),t=a.filter(e=>!i.find(t=>t.id===e.id)),n=i.filter(e=>a.find(t=>t.id===e.id)),{...r,isLoading:!1,loadedAt:Date.now(),matches:a,pendingMatches:void 0,cachedMatches:[...r.cachedMatches,...e.filter(e=>e.status!==`error`)]}}),this.clearExpiredCache()}),[[e,`onLeave`],[t,`onEnter`],[n,`onStay`]].forEach(([e,t])=>{e.forEach(e=>{var n,r;(r=(n=this.looseRoutesById[e.routeId].options)[t])==null||r.call(n,e)})})})}})}catch(e){st(e)?(t=e,this.isServer||this.navigate({...t.options,replace:!0,ignoreBlocker:!0})):Be(e)&&(n=e),this.__store.setState(e=>({...e,statusCode:t?t.status:n?404:e.matches.some(e=>e.status===`error`)?500:200,redirect:t}))}this.latestLoadPromise===r&&((a=this.commitLocationPromise)==null||a.resolve(),this.latestLoadPromise=void 0,this.commitLocationPromise=void 0),i()})}),this.latestLoadPromise=r,await r;this.latestLoadPromise&&r!==this.latestLoadPromise;)await this.latestLoadPromise;this.hasNotFoundMatch()&&this.__store.setState(e=>({...e,statusCode:404}))},this.startViewTransition=e=>{let t=this.shouldViewTransition??this.options.defaultViewTransition;if(delete this.shouldViewTransition,t&&typeof document<`u`&&`startViewTransition`in document&&typeof document.startViewTransition==`function`){let n;if(typeof t==`object`&&this.isViewTransitionTypesSupported){let r=this.latestLocation,i=this.state.resolvedLocation,a=typeof t.types==`function`?t.types(lt({resolvedLocation:i,location:r})):t.types;n={update:e,types:a}}else n=e;document.startViewTransition(n)}else e()},this.updateMatch=(e,t)=>{var n;let r,i=(n=this.state.pendingMatches)?.find(t=>t.id===e),a=this.state.matches.find(t=>t.id===e),o=this.state.cachedMatches.find(t=>t.id===e),s=i?`pendingMatches`:a?`matches`:o?`cachedMatches`:``;return s&&this.__store.setState(n=>{var i;return{...n,[s]:(i=n[s])?.map(n=>n.id===e?r=t(n):n)}}),r},this.getMatch=e=>[...this.state.cachedMatches,...this.state.pendingMatches??[],...this.state.matches].find(t=>t.id===e),this.loadMatches=async({location:e,matches:t,preload:n,onReady:r,updateMatch:i=this.updateMatch,sync:a})=>{let o,s=!1,c=async()=>{s||(s=!0,await r?.())},l=e=>!!(n&&!this.state.matches.find(t=>t.id===e));!this.isServer&&this.state.matches.find(e=>e._forcePending)&&c();let u=(n,r)=>{var a,o,c;if(st(r)||Be(r)){if(st(r)&&r.redirectHandled&&!r.options.reloadDocument)throw r;if((a=n.beforeLoadPromise)==null||a.resolve(),(o=n.loaderPromise)==null||o.resolve(),i(n.id,e=>({...e,status:st(r)?`redirected`:Be(r)?`notFound`:`error`,isFetching:!1,error:r,beforeLoadPromise:void 0,loaderPromise:void 0})),r.routeId||=n.routeId,(c=n.loadPromise)==null||c.resolve(),st(r))throw s=!0,r.options._fromLocation=e,r.redirectHandled=!0,r=this.resolveRedirect(r),r;if(Be(r))throw this._handleNotFound(t,r,{updateMatch:i}),r}},d=e=>{let t=this.getMatch(e);return!!(!this.isServer&&t._dehydrated||this.isServer&&t.ssr===!1)};try{await new Promise((n,s)=>{(async()=>{var f,p,m,h;try{let s=(e,n,r)=>{var a,s;let{id:c,routeId:l}=t[e],d=this.looseRoutesById[l];if(n instanceof Promise)throw n;n.routerCode=r,o??=e,u(this.getMatch(c),n);try{(s=(a=d.options).onError)==null||s.call(a,n)}catch(e){n=e,u(this.getMatch(c),n)}i(c,e=>{var t,r;return(t=e.beforeLoadPromise)==null||t.resolve(),(r=e.loadPromise)==null||r.resolve(),{...e,error:n,status:`error`,isFetching:!1,updatedAt:Date.now(),abortController:new AbortController,beforeLoadPromise:void 0}})};for(let[n,{id:a,routeId:o}]of t.entries()){let g=this.getMatch(a),_=(f=t[n-1])?.id,v=_?this.getMatch(_):void 0,y=this.looseRoutesById[o],b=y.options.pendingMs??this.options.defaultPendingMs;if(this.isServer){let n;if(this.isShell())n=a===at;else{let r=this.options.defaultSsr??!0;if(v?.ssr===!1)n=!1;else{let i;if(y.options.ssr===void 0)i=r;else if(typeof y.options.ssr==`function`){let n=function(e,t){return t?{status:`error`,error:t}:{status:`success`,value:e}},{search:o,params:s}=this.getMatch(a),c={search:n(o,g.searchError),params:n(s,g.paramsError),location:e,matches:t.map(e=>({index:e.index,pathname:e.pathname,fullPath:e.fullPath,staticData:e.staticData,id:e.id,routeId:e.routeId,search:n(e.search,e.searchError),params:n(e.params,e.paramsError),ssr:e.ssr}))};i=await y.options.ssr(c)??r}else i=y.options.ssr;n=i===!0&&v?.ssr===`data-only`?`data-only`:i}}i(a,e=>({...e,ssr:n}))}if(d(a))continue;let x=!!(r&&!this.isServer&&!l(a)&&(y.options.loader||y.options.beforeLoad||gt(y))&&typeof b==`number`&&b!==1/0&&(y.options.pendingComponent??(p=this.options)?.defaultPendingComponent)),S=!0,C=()=>{if(x&&this.getMatch(a).pendingTimeout===void 0){let e=setTimeout(()=>{try{c()}catch{}},b);i(a,t=>({...t,pendingTimeout:e}))}};if(g.beforeLoadPromise||g.loaderPromise){C(),await g.beforeLoadPromise;let e=this.getMatch(a);e.status===`error`?S=!0:e.preload&&(e.status===`redirected`||e.status===`notFound`)&&u(e,e.error)}if(S){try{i(a,e=>{let t=e.loadPromise;return{...e,loadPromise:ce(()=>{t?.resolve()}),beforeLoadPromise:ce()}});let{paramsError:r,searchError:o}=this.getMatch(a);r&&s(n,r,`PARSE_PARAMS`),o&&s(n,o,`VALIDATE_SEARCH`),C();let c=new AbortController,u=v?.context??this.options.context??{};i(a,e=>({...e,isFetching:`beforeLoad`,fetchCount:e.fetchCount+1,abortController:c,context:{...u,...e.__routeContext}}));let{search:d,params:f,context:p,cause:g}=this.getMatch(a),_=l(a),b={search:d,abortController:c,params:f,preload:_,context:p,location:e,navigate:t=>this.navigate({...t,_fromLocation:e}),buildLocation:this.buildLocation,cause:_?`preload`:g,matches:t},x=await(h=(m=y.options).beforeLoad)?.call(m,b);(st(x)||Be(x))&&s(n,x,`BEFORE_LOAD`),i(a,e=>({...e,__beforeLoadContext:x,context:{...u,...e.__routeContext,...x},abortController:c}))}catch(e){s(n,e,`BEFORE_LOAD`)}i(a,e=>{var t;return(t=e.beforeLoadPromise)==null||t.resolve(),{...e,beforeLoadPromise:void 0,isFetching:!1}})}}let g=t.slice(0,o),_=[];g.forEach(({id:n,routeId:r},o)=>{_.push((async()=>{let s=!1,c=!1,f=this.looseRoutesById[r],p=async()=>{var e,r,i,a,o,s;let c=this.getMatch(n);if(!c)return;let l={matches:t,match:c,params:c.params,loaderData:c.loaderData},u=await(r=(e=f.options).head)?.call(e,l),d=u?.meta,p=u?.links,m=u?.scripts,h=u?.styles,g=await(a=(i=f.options).scripts)?.call(i,l),_=await(s=(o=f.options).headers)?.call(o,l);return{meta:d,links:p,headScripts:m,headers:_,scripts:g,styles:h}},m=async()=>{let e=this.getMatch(n);e.minPendingPromise&&await e.minPendingPromise},h=this.getMatch(n);if(d(n)){if(this.isServer){let e=await p();return i(n,t=>({...t,...e})),this.getMatch(n)}}else if(h.loaderPromise){if(h.status===`success`&&!a&&!h.preload)return this.getMatch(n);await h.loaderPromise;let e=this.getMatch(n);e.error&&u(e,e.error)}else{let t=_[o-1],r=()=>{let{params:r,loaderDeps:i,abortController:a,context:o,cause:s}=this.getMatch(n),c=l(n);return{params:r,deps:i,preload:!!c,parentMatchPromise:t,abortController:a,context:o,location:e,navigate:t=>this.navigate({...t,_fromLocation:e}),cause:c?`preload`:s,route:f}},d=Date.now()-this.getMatch(n).updatedAt,h=l(n),g=h?f.options.preloadStaleTime??this.options.defaultPreloadStaleTime??3e4:f.options.staleTime??this.options.defaultStaleTime??0,v=f.options.shouldReload,y=typeof v==`function`?v(r()):v;i(n,e=>({...e,loaderPromise:ce(),preload:!!h&&!this.state.matches.find(e=>e.id===n)}));let b=async()=>{var e,t,a,o;try{try{(!this.isServer||this.isServer&&this.getMatch(n).ssr===!0)&&this.loadRouteChunk(f),i(n,e=>({...e,isFetching:`loader`}));let a=await(t=(e=f.options).loader)?.call(e,r());u(this.getMatch(n),a),i(n,e=>({...e,loaderData:a})),await f._lazyPromise;let o=await p();await m(),await f._componentsPromise,i(n,e=>({...e,error:void 0,status:`success`,isFetching:!1,updatedAt:Date.now(),...o}))}catch(e){let t=e;await m(),u(this.getMatch(n),e);try{(o=(a=f.options).onError)==null||o.call(a,e)}catch(e){t=e,u(this.getMatch(n),e)}let r=await p();i(n,e=>({...e,error:t,status:`error`,isFetching:!1,...r}))}}catch(e){let t=await p();i(n,e=>({...e,loaderPromise:void 0,...t})),u(this.getMatch(n),e)}},{status:x,invalid:S}=this.getMatch(n);if(s=x===`success`&&(S||(y??d>g)),!(h&&f.options.preload===!1))if(s&&!a)c=!0,(async()=>{try{await b();let{loaderPromise:e,loadPromise:t}=this.getMatch(n);e?.resolve(),t?.resolve(),i(n,e=>({...e,loaderPromise:void 0}))}catch(e){st(e)&&await this.navigate(e.options)}})();else if(x!==`success`||s&&a)await b();else{let e=await p();i(n,t=>({...t,...e}))}}if(!c){let{loaderPromise:e,loadPromise:t}=this.getMatch(n);e?.resolve(),t?.resolve()}return i(n,e=>(clearTimeout(e.pendingTimeout),{...e,isFetching:c?e.isFetching:!1,loaderPromise:c?e.loaderPromise:void 0,invalid:!1,pendingTimeout:void 0,_dehydrated:void 0})),this.getMatch(n)})())}),await Promise.all(_),n()}catch(e){s(e)}})()}),await c()}catch(e){if(st(e)||Be(e))throw Be(e)&&!n&&await c(),e}return t},this.invalidate=e=>{let t=t=>{var n;return(n=e?.filter)?.call(e,t)??!0?{...t,invalid:!0,...e?.forcePending||t.status===`error`?{status:`pending`,error:void 0}:{}}:t};return this.__store.setState(e=>{var n;return{...e,matches:e.matches.map(t),cachedMatches:e.cachedMatches.map(t),pendingMatches:(n=e.pendingMatches)?.map(t)}}),this.shouldViewTransition=!1,this.load({sync:e?.sync})},this.resolveRedirect=e=>(e.options.href||(e.options.href=this.buildLocation(e.options).href,e.headers.set(`Location`,e.options.href)),e.headers.get(`Location`)||e.headers.set(`Location`,e.options.href),e),this.clearCache=e=>{let t=e?.filter;t===void 0?this.__store.setState(e=>({...e,cachedMatches:[]})):this.__store.setState(e=>({...e,cachedMatches:e.cachedMatches.filter(e=>!t(e))}))},this.clearExpiredCache=()=>{let e=e=>{let t=this.looseRoutesById[e.routeId];if(!t.options.loader)return!0;let n=(e.preload?t.options.preloadGcTime??this.options.defaultPreloadGcTime:t.options.gcTime??this.options.defaultGcTime)??5*60*1e3;return!(e.status!==`error`&&Date.now()-e.updatedAt<n)};this.clearCache({filter:e})},this.loadRouteChunk=e=>(e._lazyPromise===void 0&&(e.lazyFn?e._lazyPromise=e.lazyFn().then(t=>{let{id:n,...r}=t.options;Object.assign(e.options,r)}):e._lazyPromise=Promise.resolve()),e._componentsPromise===void 0&&(e._componentsPromise=e._lazyPromise.then(()=>Promise.all(ht.map(async t=>{let n=e.options[t];n?.preload&&await n.preload()})))),e._componentsPromise),this.preloadRoute=async e=>{let t=this.buildLocation(e),n=this.matchRoutes(t,{throwOnError:!0,preload:!0,dest:e}),r=new Set([...this.state.matches,...this.state.pendingMatches??[]].map(e=>e.id)),i=new Set([...r,...this.state.cachedMatches.map(e=>e.id)]);E(()=>{n.forEach(e=>{i.has(e.id)||this.__store.setState(t=>({...t,cachedMatches:[...t.cachedMatches,e]}))})});try{return n=await this.loadMatches({matches:n,location:t,preload:!0,updateMatch:(e,t)=>{r.has(e)?n=n.map(n=>n.id===e?t(n):n):this.updateMatch(e,t)}}),n}catch(e){if(st(e))return e.options.reloadDocument?void 0:await this.preloadRoute({...e.options,_fromLocation:t});Be(e)||console.error(e);return}},this.matchRoute=(e,t)=>{let n={...e,to:e.to?this.resolvePathWithBase(e.from||``,e.to):void 0,params:e.params||{},leaveParams:!0},r=this.buildLocation(n);if(t?.pending&&this.state.status!==`pending`)return!1;let i=t?.pending===void 0?!this.state.isLoading:t.pending,a=i?this.latestLocation:this.state.resolvedLocation||this.state.location,o=Le(this.basepath,a.pathname,{...t,to:r.pathname});return!o||e.params&&!M(o,e.params,{partial:!0})?!1:o&&(t?.includeSearch??!0)?M(a.search,r.search,{partial:!0})?o:!1:o},this._handleNotFound=(e,t,{updateMatch:n=this.updateMatch}={})=>{var r;let i=this.routesById[t.routeId??``]??this.routeTree,a={};for(let t of e)a[t.routeId]=t;!i.options.notFoundComponent&&(r=this.options)?.defaultNotFoundComponent&&(i.options.notFoundComponent=this.options.defaultNotFoundComponent),m(i.options.notFoundComponent,`No notFoundComponent found. Please set a notFoundComponent on your route or provide a defaultNotFoundComponent to the router.`);let o=a[i.id];m(o,`Could not find match for route: `+i.id),n(o.id,e=>({...e,status:`notFound`,error:t,isFetching:!1})),t.routerCode===`BEFORE_LOAD`&&i.parentRoute&&(t.routeId=i.parentRoute.id,this._handleNotFound(e,t,{updateMatch:n}))},this.hasNotFoundMatch=()=>this.__store.state.matches.some(e=>e.status===`notFound`||e.globalNotFound),this.update({defaultPreloadDelay:50,defaultPendingMs:1e3,defaultPendingMinMs:500,context:void 0,...e,caseSensitive:e.caseSensitive??!1,notFoundMode:e.notFoundMode??`fuzzy`,stringifySearch:e.stringifySearch??nt,parseSearch:e.parseSearch??tt}),typeof document<`u`&&(self.__TSR_ROUTER__=this)}isShell(){return this.options.isShell}get state(){return this.__store.state}get looseRoutesById(){return this.routesById}matchRoutesInternal(e,t){var n;let{foundRoute:r,matchedRoutes:i,routeParams:a}=this.getMatchedRoutes(e.pathname,(n=t?.dest)?.to),o=!1;(r?r.path!==`/`&&a[`**`]:we(e.pathname))&&(this.options.notFoundRoute?i.push(this.options.notFoundRoute):o=!0);let s=(()=>{if(o){if(this.options.notFoundMode!==`root`)for(let e=i.length-1;e>=0;e--){let t=i[e];if(t.children)return t.id}return at}})(),c=i.map(e=>{var n;let r,i=(n=e.options.params)?.parse??e.options.parseParams;if(i)try{let e=i(a);Object.assign(a,e)}catch(e){if(r=new ft(e.message,{cause:e}),t?.throwOnError)throw r;return r}}),l=[],u=e=>{let t=e?.id,n=t?e.context??this.options.context??{}:this.options.context??{};return n};return i.forEach((n,r)=>{var i,o;let d=l[r-1],[f,p,m]=(()=>{let r=d?.search??e.search,i=d?._strictSearch??{};try{let e=mt(n.options.validateSearch,{...r})??{};return[{...r,...e},{...i,...e},void 0]}catch(e){let n=e;if(e instanceof dt||(n=new dt(e.message,{cause:e})),t?.throwOnError)throw n;return[r,{},n]}})(),h=(o=(i=n.options).loaderDeps)?.call(i,{search:f})??``,g=h?JSON.stringify(h):``,{usedParams:_,interpolatedPath:v}=Fe({path:n.fullPath,params:a,decodeCharMap:this.pathParamsDecodeCharMap}),y=Fe({path:n.id,params:a,leaveWildcards:!0,decodeCharMap:this.pathParamsDecodeCharMap}).interpolatedPath+g,b=this.getMatch(y),x=this.state.matches.find(e=>e.routeId===n.id),S=x?`stay`:`enter`,C;if(b)C={...b,cause:S,params:x?A(x.params,a):a,_strictParams:_,search:A(x?x.search:b.search,f),_strictSearch:p};else{let e=n.options.loader||n.options.beforeLoad||n.lazyFn||gt(n)?`pending`:`success`;C={id:y,index:r,routeId:n.id,params:x?A(x.params,a):a,_strictParams:_,pathname:xe([this.basepath,v]),updatedAt:Date.now(),search:x?A(x.search,f):f,_strictSearch:p,searchError:void 0,status:e,isFetching:!1,error:void 0,paramsError:c[r],__routeContext:{},__beforeLoadContext:void 0,context:{},abortController:new AbortController,fetchCount:0,cause:S,loaderDeps:x?A(x.loaderDeps,h):h,invalid:!1,preload:!1,links:void 0,scripts:void 0,headScripts:void 0,meta:void 0,staticData:n.options.staticData||{},loadPromise:ce(),fullPath:n.fullPath}}t?.preload||(C.globalNotFound=s===n.id),C.searchError=m;let w=u(d);C.context={...w,...C.__routeContext,...C.__beforeLoadContext},l.push(C)}),l.forEach((n,r)=>{var i,a;let o=this.looseRoutesById[n.routeId],s=this.getMatch(n.id);if(!s&&t?._buildLocation!==!0){let t=l[r-1],s=u(t),c={deps:n.loaderDeps,params:n.params,context:s,location:e,navigate:t=>this.navigate({...t,_fromLocation:e}),buildLocation:this.buildLocation,cause:n.cause,abortController:n.abortController,preload:!!n.preload,matches:l};n.__routeContext=(a=(i=o.options).context)?.call(i,c)??{},n.context={...s,...n.__routeContext,...n.__beforeLoadContext}}}),l}comparePaths(e,t){return e.replace(/(.+)\/$/,`$1`)===t.replace(/(.+)\/$/,`$1`)}},dt=class extends Error{},ft=class extends Error{};function pt(e){return{loadedAt:0,isLoading:!1,isTransitioning:!1,status:`idle`,resolvedLocation:void 0,location:e,matches:[],pendingMatches:[],cachedMatches:[],statusCode:200}}function mt(e,t){if(e==null)return{};if(`~standard`in e){let n=e[`~standard`].validate(t);if(n instanceof Promise)throw new dt(`Async validation not supported`);if(n.issues)throw new dt(JSON.stringify(n.issues,void 0,2),{cause:n});return n.value}return`parse`in e?e.parse(t):typeof e==`function`?e(t):{}}const ht=[`component`,`errorComponent`,`pendingComponent`,`notFoundComponent`];function gt(e){var t;for(let n of ht)if((t=e.options[n])?.preload)return!0;return!1}function _t({routeTree:e,initRoute:t}){let n={},r={},i=e=>{e.forEach((e,a)=>{t?.(e,a);let o=n[e.id];if(m(!o,`Duplicate routes found with id: ${String(e.id)}`),n[e.id]=e,!e.isRoot&&e.path){let t=we(e.fullPath);(!r[t]||e.fullPath.endsWith(`/`))&&(r[t]=e)}let s=e.children;s?.length&&i(s)})};i([e]);let a=[],o=Object.values(n);o.forEach((e,t)=>{var n;if(e.isRoot||!e.path)return;let r=Ce(e.fullPath),i=Pe(r);for(;i.length>1&&(n=i[0])?.value===`/`;)i.shift();let o=i.map(e=>e.value===`/`?.75:e.type===`param`?e.prefixSegment&&e.suffixSegment?.55:e.prefixSegment?.52:e.suffixSegment?.51:.5:e.type===`optional-param`?e.prefixSegment&&e.suffixSegment?.45:e.prefixSegment?.42:e.suffixSegment?.41:.4:e.type===`wildcard`?e.prefixSegment&&e.suffixSegment?.3:e.prefixSegment?.27:e.suffixSegment?.26:.25:1);a.push({child:e,trimmed:r,parsed:i,index:t,scores:o})});let s=a.sort((e,t)=>{let n=Math.min(e.scores.length,t.scores.length);for(let r=0;r<n;r++)if(e.scores[r]!==t.scores[r])return t.scores[r]-e.scores[r];if(e.scores.length!==t.scores.length){let n=e.parsed.filter(e=>e.type===`optional-param`).length,r=t.parsed.filter(e=>e.type===`optional-param`).length;return n===r?t.scores.length-e.scores.length:n-r}for(let r=0;r<n;r++)if(e.parsed[r].value!==t.parsed[r].value)return e.parsed[r].value>t.parsed[r].value?1:-1;return e.index-t.index}).map((e,t)=>(e.child.rank=t,e.child));return{routesById:n,routesByPath:r,flatRoutes:s}}function vt({pathname:e,routePathname:t,basepath:n,caseSensitive:r,routesByPath:i,routesById:a,flatRoutes:o}){let s={},c=we(e),l=e=>{var t;let i=Le(n,c,{to:e.fullPath,caseSensitive:(t=e.options)?.caseSensitive??r,fuzzy:!1});return i},u=t===void 0?void 0:i[t];u?s=l(u):u=o.find(e=>{let t=l(e);return t?(s=t,!0):!1});let d=u||a[at],f=[d];for(;d.parentRoute;)d=d.parentRoute,f.unshift(d);return{matchedRoutes:f,routeParams:s,foundRoute:u}}function yt({search:e,dest:t,destRoutes:n,_includeValidateSearch:r}){let i=n.reduce((e,t)=>{var n;let i=[];if(`search`in t.options)(n=t.options.search)?.middlewares&&i.push(...t.options.search.middlewares);else if(t.options.preSearchFilters||t.options.postSearchFilters){let e=({search:e,next:n})=>{let r=e;`preSearchFilters`in t.options&&t.options.preSearchFilters&&(r=t.options.preSearchFilters.reduce((e,t)=>t(e),e));let i=n(r);return`postSearchFilters`in t.options&&t.options.postSearchFilters?t.options.postSearchFilters.reduce((e,t)=>t(e),i):i};i.push(e)}if(r&&t.options.validateSearch){let e=({search:e,next:n})=>{let r=n(e);try{let e={...r,...mt(t.options.validateSearch,r)??{}};return e}catch{return r}};i.push(e)}return e.concat(i)},[])??[],a=({search:e})=>t.search?t.search===!0?e:O(t.search,e):{};i.push(a);let o=(e,t)=>{if(e>=i.length)return t;let n=i[e],r=t=>o(e+1,t);return n({search:t,next:r})};return o(0,e)}const bt=Symbol.for(`TSR_DEFERRED_PROMISE`);function xt(e,t){let n=e;return n[bt]?n:(n[bt]={status:`pending`},n.then(e=>{n[bt].status=`success`,n[bt].data=e}).catch(e=>{n[bt].status=`error`,n[bt].error={data:(t?.serializeError??ct)(e),__isServerError:!0}}),n)}const St=`Error preloading route! ☝️`;var Ct=class{constructor(e){if(this.init=e=>{var t,n;this.originalIndex=e.originalIndex;let r=this.options,i=!r?.path&&!r?.id;this.parentRoute=(n=(t=this.options).getParentRoute)?.call(t),i?this._path=at:this.parentRoute||m(!1,`Child Route instances must pass a 'getParentRoute: () => ParentRoute' option that returns a Route instance.`);let a=i?at:r?.path;a&&a!==`/`&&(a=Ce(a));let o=r?.id||a,s=i?at:xe([this.parentRoute.id===at?``:this.parentRoute.id,o]);a===at&&(a=`/`),s!==at&&(s=xe([`/`,s]));let c=s===at?`/`:xe([this.parentRoute.fullPath,a]);this._path=a,this._id=s,this._fullPath=c,this._to=c},this.clone=e=>{this._path=e._path,this._id=e._id,this._fullPath=e._fullPath,this._to=e._to,this.options.getParentRoute=e.options.getParentRoute,this.children=e.children},this.addChildren=e=>this._addFileChildren(e),this._addFileChildren=e=>(Array.isArray(e)&&(this.children=e),typeof e==`object`&&e&&(this.children=Object.values(e)),this),this._addFileTypes=()=>this,this.updateLoader=e=>(Object.assign(this.options,e),this),this.update=e=>(Object.assign(this.options,e),this),this.lazy=e=>(this.lazyFn=e,this),this.options=e||{},this.isRoot=!e?.getParentRoute,e?.id&&e?.path)throw Error(`Route cannot have both an 'id' and a 'path' option.`)}get to(){return this._to}get id(){return this._id}get path(){return this._path}get fullPath(){return this._fullPath}},wt=class extends Ct{constructor(e){super(e)}};const Tt=`modulepreload`,Et=function(e){return`/`+e},Dt={},Ot=function(e,t,n){let r=Promise.resolve();if(t&&t.length>0){let e=document.getElementsByTagName(`link`),i=document.querySelector(`meta[property=csp-nonce]`),a=i?.nonce||i?.getAttribute(`nonce`);function o(e){return Promise.all(e.map(e=>Promise.resolve(e).then(e=>({status:`fulfilled`,value:e}),e=>({status:`rejected`,reason:e}))))}r=o(t.map(t=>{if(t=Et(t,n),t in Dt)return;Dt[t]=!0;let r=t.endsWith(`.css`),i=r?`[rel="stylesheet"]`:``,o=!!n;if(o)for(let n=e.length-1;n>=0;n--){let i=e[n];if(i.href===t&&(!r||i.rel===`stylesheet`))return}else if(document.querySelector(`link[href="${t}"]${i}`))return;let s=document.createElement(`link`);if(s.rel=r?`stylesheet`:Tt,r||(s.as=`script`),s.crossOrigin=``,s.href=t,a&&s.setAttribute(`nonce`,a),document.head.appendChild(s),r)return new Promise((e,n)=>{s.addEventListener(`load`,e),s.addEventListener(`error`,()=>n(Error(`Unable to preload CSS for ${t}`)))})}))}function i(e){let t=new Event(`vite:preloadError`,{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return r.then(t=>{for(let e of t||[]){if(e.status!==`rejected`)continue;i(e.reason)}return e().catch(i)})};var kt=!0;function At(e,t){if(!kt){if(e)return;var n=`Warning: `+t;typeof console<`u`&&console.warn(n);try{throw Error(n)}catch{}}}var jt=At,Mt=s(exports=>{var t=Symbol.for(`react.transitional.element`),n=Symbol.for(`react.fragment`);function r(e,n,r){var i=null;if(r!==void 0&&(i=``+r),n.key!==void 0&&(i=``+n.key),`key`in n)for(var a in r={},n)a!==`key`&&(r[a]=n[a]);else r=n;return n=r.ref,{$$typeof:t,type:e,key:i,ref:n===void 0?null:n,props:r}}exports.Fragment=n,exports.jsx=r,exports.jsxs=r}),Nt=s((exports,t)=>{t.exports=Mt()}),Pt=s(exports=>{var t=Symbol.for(`react.transitional.element`),n=Symbol.for(`react.portal`),r=Symbol.for(`react.fragment`),i=Symbol.for(`react.strict_mode`),a=Symbol.for(`react.profiler`),o=Symbol.for(`react.consumer`),s=Symbol.for(`react.context`),c=Symbol.for(`react.forward_ref`),l=Symbol.for(`react.suspense`),u=Symbol.for(`react.memo`),d=Symbol.for(`react.lazy`),f=Symbol.iterator;function p(e){return typeof e!=`object`||!e?null:(e=f&&e[f]||e[`@@iterator`],typeof e==`function`?e:null)}var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,g={};function _(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}_.prototype.isReactComponent={},_.prototype.setState=function(e,t){if(typeof e!=`object`&&typeof e!=`function`&&e!=null)throw Error(`takes an object of state variables to update or a function which returns an object of state variables.`);this.updater.enqueueSetState(this,e,t,`setState`)},_.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,`forceUpdate`)};function v(){}v.prototype=_.prototype;function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}var b=y.prototype=new v;b.constructor=y,h(b,_.prototype),b.isPureReactComponent=!0;var x=Array.isArray,S={H:null,A:null,T:null,S:null,V:null},C=Object.prototype.hasOwnProperty;function w(e,n,r,i,a,o){return r=o.ref,{$$typeof:t,type:e,key:n,ref:r===void 0?null:r,props:o}}function T(e,t){return w(e.type,t,void 0,void 0,void 0,e.props)}function E(e){return typeof e==`object`&&!!e&&e.$$typeof===t}function ee(e){var t={"=":`=0`,":":`=2`};return`$`+e.replace(/[=:]/g,function(e){return t[e]})}var D=/\/+/g;function te(e,t){return typeof e==`object`&&e&&e.key!=null?ee(``+e.key):t.toString(36)}function ne(){}function re(e){switch(e.status){case`fulfilled`:return e.value;case`rejected`:throw e.reason;default:switch(typeof e.status==`string`?e.then(ne,ne):(e.status=`pending`,e.then(function(t){e.status===`pending`&&(e.status=`fulfilled`,e.value=t)},function(t){e.status===`pending`&&(e.status=`rejected`,e.reason=t)})),e.status){case`fulfilled`:return e.value;case`rejected`:throw e.reason}}throw e}function O(e,r,i,a,o){var s=typeof e;(s===`undefined`||s===`boolean`)&&(e=null);var c=!1;if(e===null)c=!0;else switch(s){case`bigint`:case`string`:case`number`:c=!0;break;case`object`:switch(e.$$typeof){case t:case n:c=!0;break;case d:return c=e._init,O(c(e._payload),r,i,a,o)}}if(c)return o=o(e),c=a===``?`.`+te(e,0):a,x(o)?(i=``,c!=null&&(i=c.replace(D,`$&/`)+`/`),O(o,r,i,``,function(e){return e})):o!=null&&(E(o)&&(o=T(o,i+(o.key==null||e&&e.key===o.key?``:(``+o.key).replace(D,`$&/`)+`/`)+c)),r.push(o)),1;c=0;var l=a===``?`.`:a+`:`;if(x(e))for(var u=0;u<e.length;u++)a=e[u],s=l+te(a,u),c+=O(a,r,i,s,o);else if(u=p(e),typeof u==`function`)for(e=u.call(e),u=0;!(a=e.next()).done;)a=a.value,s=l+te(a,u++),c+=O(a,r,i,s,o);else if(s===`object`){if(typeof e.then==`function`)return O(re(e),r,i,a,o);throw r=String(e),Error(`Objects are not valid as a React child (found: `+(r===`[object Object]`?`object with keys {`+Object.keys(e).join(`, `)+`}`:r)+`). If you meant to render a collection of children, use an array instead.`)}return c}function k(e,t,n){if(e==null)return e;var r=[],i=0;return O(e,r,``,``,function(e){return t.call(n,e,i++)}),r}function A(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(t){(e._status===0||e._status===-1)&&(e._status=1,e._result=t)},function(t){(e._status===0||e._status===-1)&&(e._status=2,e._result=t)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ie=typeof reportError==`function`?reportError:function(e){if(typeof window==`object`&&typeof window.ErrorEvent==`function`){var t=new window.ErrorEvent(`error`,{bubbles:!0,cancelable:!0,message:typeof e==`object`&&e&&typeof e.message==`string`?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process==`object`&&typeof process.emit==`function`){process.emit(`uncaughtException`,e);return}console.error(e)};function ae(){}exports.Children={map:k,forEach:function(e,t,n){k(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return k(e,function(){t++}),t},toArray:function(e){return k(e,function(e){return e})||[]},only:function(e){if(!E(e))throw Error(`React.Children.only expected to receive a single React element child.`);return e}},exports.Component=_,exports.Fragment=r,exports.Profiler=a,exports.PureComponent=y,exports.StrictMode=i,exports.Suspense=l,exports.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=S,exports.__COMPILER_RUNTIME={__proto__:null,c:function(e){return S.H.useMemoCache(e)}},exports.cache=function(e){return function(){return e.apply(null,arguments)}},exports.cloneElement=function(e,t,n){if(e==null)throw Error(`The argument must be a React element, but you passed `+e+`.`);var r=h({},e.props),i=e.key,a=void 0;if(t!=null)for(o in t.ref!==void 0&&(a=void 0),t.key!==void 0&&(i=``+t.key),t)!C.call(t,o)||o===`key`||o===`__self`||o===`__source`||o===`ref`&&t.ref===void 0||(r[o]=t[o]);var o=arguments.length-2;if(o===1)r.children=n;else if(1<o){for(var s=Array(o),c=0;c<o;c++)s[c]=arguments[c+2];r.children=s}return w(e.type,i,void 0,void 0,a,r)},exports.createContext=function(e){return e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null},e.Provider=e,e.Consumer={$$typeof:o,_context:e},e},exports.createElement=function(e,t,n){var r,i={},a=null;if(t!=null)for(r in t.key!==void 0&&(a=``+t.key),t)C.call(t,r)&&r!==`key`&&r!==`__self`&&r!==`__source`&&(i[r]=t[r]);var o=arguments.length-2;if(o===1)i.children=n;else if(1<o){for(var s=Array(o),c=0;c<o;c++)s[c]=arguments[c+2];i.children=s}if(e&&e.defaultProps)for(r in o=e.defaultProps,o)i[r]===void 0&&(i[r]=o[r]);return w(e,a,void 0,void 0,null,i)},exports.createRef=function(){return{current:null}},exports.forwardRef=function(e){return{$$typeof:c,render:e}},exports.isValidElement=E,exports.lazy=function(e){return{$$typeof:d,_payload:{_status:-1,_result:e},_init:A}},exports.memo=function(e,t){return{$$typeof:u,type:e,compare:t===void 0?null:t}},exports.startTransition=function(e){var t=S.T,n={};S.T=n;try{var r=e(),i=S.S;i!==null&&i(n,r),typeof r==`object`&&r&&typeof r.then==`function`&&r.then(ae,ie)}catch(e){ie(e)}finally{S.T=t}},exports.unstable_useCacheRefresh=function(){return S.H.useCacheRefresh()},exports.use=function(e){return S.H.use(e)},exports.useActionState=function(e,t,n){return S.H.useActionState(e,t,n)},exports.useCallback=function(e,t){return S.H.useCallback(e,t)},exports.useContext=function(e){return S.H.useContext(e)},exports.useDebugValue=function(){},exports.useDeferredValue=function(e,t){return S.H.useDeferredValue(e,t)},exports.useEffect=function(e,t,n){var r=S.H;if(typeof n==`function`)throw Error(`useEffect CRUD overload is not enabled in this build of React.`);return r.useEffect(e,t)},exports.useId=function(){return S.H.useId()},exports.useImperativeHandle=function(e,t,n){return S.H.useImperativeHandle(e,t,n)},exports.useInsertionEffect=function(e,t){return S.H.useInsertionEffect(e,t)},exports.useLayoutEffect=function(e,t){return S.H.useLayoutEffect(e,t)},exports.useMemo=function(e,t){return S.H.useMemo(e,t)},exports.useOptimistic=function(e,t){return S.H.useOptimistic(e,t)},exports.useReducer=function(e,t,n){return S.H.useReducer(e,t,n)},exports.useRef=function(e){return S.H.useRef(e)},exports.useState=function(e){return S.H.useState(e)},exports.useSyncExternalStore=function(e,t,n){return S.H.useSyncExternalStore(e,t,n)},exports.useTransition=function(){return S.H.useTransition()},exports.version=`19.1.0`}),Ft=s((exports,t)=>{t.exports=Pt()}),N=u(Nt()),P=u(Ft());function It({promise:e}){let t=xt(e);if(t[bt].status===`pending`)throw t;if(t[bt].status===`error`)throw t[bt].error;return[t[bt].data,t]}function Lt(e){let t=(0,N.jsx)(Rt,{...e});return e.fallback?(0,N.jsx)(P.Suspense,{fallback:e.fallback,children:t}):t}function Rt(e){let[t]=It(e);return e.children(t)}function zt(e){let t=e.errorComponent??Vt;return(0,N.jsx)(Bt,{getResetKey:e.getResetKey,onCatch:e.onCatch,children:({error:n,reset:r})=>n?P.createElement(t,{error:n,reset:r}):e.children})}var Bt=class extends P.Component{constructor(){super(...arguments),this.state={error:null}}static getDerivedStateFromProps(e){return{resetKey:e.getResetKey()}}static getDerivedStateFromError(e){return{error:e}}reset(){this.setState({error:null})}componentDidUpdate(e,t){t.error&&t.resetKey!==this.state.resetKey&&this.reset()}componentDidCatch(e,t){this.props.onCatch&&this.props.onCatch(e,t)}render(){return this.props.children({error:this.state.resetKey===this.props.getResetKey()?this.state.error:null,reset:()=>{this.reset()}})}};function Vt({error:e}){let[t,n]=P.useState(!1);return(0,N.jsxs)(`div`,{style:{padding:`.5rem`,maxWidth:`100%`},children:[(0,N.jsxs)(`div`,{style:{display:`flex`,alignItems:`center`,gap:`.5rem`},children:[(0,N.jsx)(`strong`,{style:{fontSize:`1rem`},children:`Something went wrong!`}),(0,N.jsx)(`button`,{style:{appearance:`none`,fontSize:`.6em`,border:`1px solid currentColor`,padding:`.1rem .2rem`,fontWeight:`bold`,borderRadius:`.25rem`},onClick:()=>n(e=>!e),children:t?`Hide Error`:`Show Error`})]}),(0,N.jsx)(`div`,{style:{height:`.25rem`}}),t?(0,N.jsx)(`div`,{children:(0,N.jsx)(`pre`,{style:{fontSize:`.7em`,border:`1px solid red`,borderRadius:`.25rem`,padding:`.3rem`,color:`red`,overflow:`auto`},children:e.message?(0,N.jsx)(`code`,{children:e.message}):null})}):null]})}function Ht({children:e,fallback:t=null}){return Ut()?(0,N.jsx)(P.Fragment,{children:e}):(0,N.jsx)(P.Fragment,{children:t})}function Ut(){return P.useSyncExternalStore(Wt,()=>!0,()=>!1)}function Wt(){return()=>{}}var Gt=s(exports=>{var t=Ft();function n(e,t){return e===t&&(e!==0||1/e==1/t)||e!==e&&t!==t}var r=typeof Object.is==`function`?Object.is:n,i=t.useState,a=t.useEffect,o=t.useLayoutEffect,s=t.useDebugValue;function c(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),c=r[0].inst,u=r[1];return o(function(){c.value=n,c.getSnapshot=t,l(c)&&u({inst:c})},[e,n,t]),a(function(){return l(c)&&u({inst:c}),e(function(){l(c)&&u({inst:c})})},[e]),s(n),n}function l(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!r(e,n)}catch{return!0}}function u(e,t){return t()}var d=typeof window>`u`||window.document===void 0||window.document.createElement===void 0?u:c;exports.useSyncExternalStore=t.useSyncExternalStore===void 0?d:t.useSyncExternalStore}),Kt=s((exports,t)=>{t.exports=Gt()}),qt=s(exports=>{var t=Ft(),n=Kt();function r(e,t){return e===t&&(e!==0||1/e==1/t)||e!==e&&t!==t}var i=typeof Object.is==`function`?Object.is:r,a=n.useSyncExternalStore,o=t.useRef,s=t.useEffect,c=t.useMemo,l=t.useDebugValue;exports.useSyncExternalStoreWithSelector=function(e,t,n,r,u){var d=o(null);if(d.current===null){var f={hasValue:!1,value:null};d.current=f}else f=d.current;d=c(function(){function e(e){if(!a){if(a=!0,o=e,e=r(e),u!==void 0&&f.hasValue){var t=f.value;if(u(t,e))return s=t}return s=e}if(t=s,i(o,e))return t;var n=r(e);return u!==void 0&&u(t,n)?(o=e,t):(o=e,s=n)}var a=!1,o,s,c=n===void 0?null:n;return[function(){return e(t())},c===null?void 0:function(){return e(c())}]},[t,n,r,u]);var p=a(e,d[0],d[1]);return s(function(){f.hasValue=!0,f.value=p},[p]),l(p),p}}),Jt=s((exports,t)=>{t.exports=qt()}),Yt=u(Jt());function Xt(e,t=e=>e){let n=(0,Yt.useSyncExternalStoreWithSelector)(e.subscribe,()=>e.state,()=>e.state,t,Zt);return n}function Zt(e,t){if(Object.is(e,t))return!0;if(typeof e!=`object`||!e||typeof t!=`object`||!t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,r]of e)if(!t.has(n)||!Object.is(r,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}if(e instanceof Date&&t instanceof Date)return e.getTime()===t.getTime();let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r=0;r<n.length;r++)if(!Object.prototype.hasOwnProperty.call(t,n[r])||!Object.is(e[n[r]],t[n[r]]))return!1;return!0}const Qt=P.createContext(null);function $t(){return typeof document>`u`?Qt:window.__TSR_ROUTER_CONTEXT__?window.__TSR_ROUTER_CONTEXT__:(window.__TSR_ROUTER_CONTEXT__=Qt,Qt)}function en(e){let t=P.useContext($t());return jt(!((e?.warn??!0)&&!t),`useRouter must be used inside a <RouterProvider> component!`),t}function F(e){let t=en({warn:e?.router===void 0}),n=e?.router||t,r=(0,P.useRef)(void 0);return Xt(n.__store,t=>{if(e?.select){if(e.structuralSharing??n.options.defaultStructuralSharing){let n=A(r.current,e.select(t));return r.current=n,n}return e.select(t)}return t})}const tn=P.createContext(void 0),nn=P.createContext(void 0);function rn(e){let t=P.useContext(e.from?nn:tn),n=F({select:n=>{let r=n.matches.find(n=>e.from?e.from===n.routeId:n.id===t);if(m(!((e.shouldThrow??!0)&&!r),`Could not find ${e.from?`an active match from "${e.from}"`:`a nearest match!`}`),r!==void 0)return e.select?e.select(r):r},structuralSharing:e.structuralSharing});return n}function an(e){return rn({from:e.from,strict:e.strict,structuralSharing:e.structuralSharing,select:t=>e.select?e.select(t.loaderData):t.loaderData})}function on(e){let{select:t,...n}=e;return rn({...n,select:e=>t?t(e.loaderDeps):e.loaderDeps})}function sn(e){return rn({from:e.from,strict:e.strict,shouldThrow:e.shouldThrow,structuralSharing:e.structuralSharing,select:t=>e.select?e.select(t.params):t.params})}function cn(e){return rn({from:e.from,strict:e.strict,shouldThrow:e.shouldThrow,structuralSharing:e.structuralSharing,select:t=>e.select?e.select(t.search):t.search})}function ln(e){let{navigate:t,state:n}=en(),r=rn({strict:!1,select:e=>e.index});return P.useCallback(i=>{let a=i.from??e?.from??n.matches[r].fullPath;return t({...i,from:a})},[e?.from,t])}var un=s(exports=>{var t=Ft();function n(e){var t=`https://react.dev/errors/`+e;if(1<arguments.length){t+=`?args[]=`+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+=`&args[]=`+encodeURIComponent(arguments[n])}return`Minified React error #`+e+`; visit `+t+` for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`}function r(){}var i={d:{f:r,r:function(){throw Error(n(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},a=Symbol.for(`react.portal`);function o(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:a,key:r==null?null:``+r,children:e,containerInfo:t,implementation:n}}var s=t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function c(e,t){if(e===`font`)return``;if(typeof t==`string`)return t===`use-credentials`?t:``}exports.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=i,exports.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)throw Error(n(299));return o(e,t,null,r)},exports.flushSync=function(e){var t=s.T,n=i.p;try{if(s.T=null,i.p=2,e)return e()}finally{s.T=t,i.p=n,i.d.f()}},exports.preconnect=function(e,t){typeof e==`string`&&(t?(t=t.crossOrigin,t=typeof t==`string`?t===`use-credentials`?t:``:void 0):t=null,i.d.C(e,t))},exports.prefetchDNS=function(e){typeof e==`string`&&i.d.D(e)},exports.preinit=function(e,t){if(typeof e==`string`&&t&&typeof t.as==`string`){var n=t.as,r=c(n,t.crossOrigin),a=typeof t.integrity==`string`?t.integrity:void 0,o=typeof t.fetchPriority==`string`?t.fetchPriority:void 0;n===`style`?i.d.S(e,typeof t.precedence==`string`?t.precedence:void 0,{crossOrigin:r,integrity:a,fetchPriority:o}):n===`script`&&i.d.X(e,{crossOrigin:r,integrity:a,fetchPriority:o,nonce:typeof t.nonce==`string`?t.nonce:void 0})}},exports.preinitModule=function(e,t){if(typeof e==`string`)if(typeof t==`object`&&t){if(t.as==null||t.as===`script`){var n=c(t.as,t.crossOrigin);i.d.M(e,{crossOrigin:n,integrity:typeof t.integrity==`string`?t.integrity:void 0,nonce:typeof t.nonce==`string`?t.nonce:void 0})}}else t??i.d.M(e)},exports.preload=function(e,t){if(typeof e==`string`&&typeof t==`object`&&t&&typeof t.as==`string`){var n=t.as,r=c(n,t.crossOrigin);i.d.L(e,n,{crossOrigin:r,integrity:typeof t.integrity==`string`?t.integrity:void 0,nonce:typeof t.nonce==`string`?t.nonce:void 0,type:typeof t.type==`string`?t.type:void 0,fetchPriority:typeof t.fetchPriority==`string`?t.fetchPriority:void 0,referrerPolicy:typeof t.referrerPolicy==`string`?t.referrerPolicy:void 0,imageSrcSet:typeof t.imageSrcSet==`string`?t.imageSrcSet:void 0,imageSizes:typeof t.imageSizes==`string`?t.imageSizes:void 0,media:typeof t.media==`string`?t.media:void 0})}},exports.preloadModule=function(e,t){if(typeof e==`string`)if(t){var n=c(t.as,t.crossOrigin);i.d.m(e,{as:typeof t.as==`string`&&t.as!==`script`?t.as:void 0,crossOrigin:n,integrity:typeof t.integrity==`string`?t.integrity:void 0})}else i.d.m(e)},exports.requestFormReset=function(e){i.d.r(e)},exports.unstable_batchedUpdates=function(e,t){return e(t)},exports.useFormState=function(e,t,n){return s.H.useFormState(e,t,n)},exports.useFormStatus=function(){return s.H.useHostTransitionStatus()},exports.version=`19.1.0`}),dn=s((exports,t)=>{function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>`u`||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!=`function`))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(e){console.error(e)}}n(),t.exports=un()});const fn=typeof window<`u`?P.useLayoutEffect:P.useEffect;function pn(e){let t=P.useRef({value:e,prev:null}),n=t.current.value;return e!==n&&(t.current={value:e,prev:n}),t.current.prev}function mn(e,t,n={},r={}){P.useEffect(()=>{if(!e.current||r.disabled||typeof IntersectionObserver!=`function`)return;let i=new IntersectionObserver(([e])=>{t(e)},n);return i.observe(e.current),()=>{i.disconnect()}},[t,n,r.disabled,e])}function hn(e){let t=P.useRef(null);return P.useImperativeHandle(e,()=>t.current,[]),t}var gn=u(dn());function _n(e,t){let n=en(),[r,i]=P.useState(!1),a=P.useRef(!1),o=hn(t),{activeProps:s,inactiveProps:c,activeOptions:l,to:u,preload:d,preloadDelay:f,hashScrollIntoView:p,replace:m,startTransition:h,resetScroll:g,viewTransition:_,children:v,target:y,disabled:b,style:x,className:S,onClick:C,onFocus:w,onMouseEnter:T,onMouseLeave:E,onTouchStart:ee,ignoreBlocker:D,params:te,search:ne,hash:re,state:k,mask:A,reloadDocument:ie,unsafeRelative:ae,from:oe,_fromLocation:se,...j}=e,ce=P.useMemo(()=>{try{return new URL(u),`external`}catch{}return`internal`},[u]),le=F({select:e=>e.location.search,structuralSharing:!0}),ue=rn({strict:!1,select:t=>e.from??t.fullPath}),de=P.useMemo(()=>n.buildLocation({...e,from:ue}),[n,le,e._fromLocation,ue,e.hash,e.to,e.search,e.params,e.state,e.mask,e.unsafeRelative]),fe=ce===`external`,pe=e.reloadDocument||fe?!1:d??n.options.defaultPreload,me=f??n.options.defaultPreloadDelay??0,he=F({select:e=>{if(fe)return!1;if(l?.exact){let t=De(e.location.pathname,de.pathname,n.basepath);if(!t)return!1}else{let t=Ee(e.location.pathname,n.basepath),r=Ee(de.pathname,n.basepath),i=t.startsWith(r)&&(t.length===r.length||t[r.length]===`/`);if(!i)return!1}if(l?.includeSearch??!0){let t=M(e.location.search,de.search,{partial:!l?.exact,ignoreUndefined:!l?.explicitUndefined});if(!t)return!1}return l?.includeHash?e.location.hash===de.hash:!0}}),ge=P.useCallback(()=>{n.preloadRoute({...e,from:ue}).catch(e=>{console.warn(e),console.warn(St)})},[n,e.to,e._fromLocation,ue,e.search,e.hash,e.params,e.state,e.mask,e.unsafeRelative,e.hashScrollIntoView,e.href,e.ignoreBlocker,e.reloadDocument,e.replace,e.resetScroll,e.viewTransition]),_e=P.useCallback(e=>{e?.isIntersecting&&ge()},[ge]);if(mn(o,_e,wn,{disabled:!!b||pe!==`viewport`}),P.useEffect(()=>{a.current||!b&&pe===`render`&&(ge(),a.current=!0)},[b,ge,pe]),fe)return{...j,ref:o,type:ce,href:u,...v&&{children:v},...y&&{target:y},...b&&{disabled:b},...x&&{style:x},...S&&{className:S},...C&&{onClick:C},...w&&{onFocus:w},...T&&{onMouseEnter:T},...E&&{onMouseLeave:E},...ee&&{onTouchStart:ee}};let ve=t=>{if(!b&&!Dn(t)&&!t.defaultPrevented&&(!y||y===`_self`)&&t.button===0){t.preventDefault(),(0,gn.flushSync)(()=>{i(!0)});let r=n.subscribe(`onResolved`,()=>{r(),i(!1)});return n.navigate({...e,from:ue,replace:m,resetScroll:g,hashScrollIntoView:p,startTransition:h,viewTransition:_,ignoreBlocker:D})}},ye=e=>{b||pe&&ge()},be=ye,xe=e=>{if(!(b||!pe))if(!me)ge();else{let t=e.target;if(Cn.has(t))return;let n=setTimeout(()=>{Cn.delete(t),ge()},me);Cn.set(t,n)}},Se=e=>{if(b||!pe||!me)return;let t=e.target,n=Cn.get(t);n&&(clearTimeout(n),Cn.delete(t))},Ce=he?O(s,{})??yn:vn,we=he?vn:O(c,{})??vn,Te=[S,Ce.className,we.className].filter(Boolean).join(` `),Oe=(x||Ce.style||we.style)&&{...x,...Ce.style,...we.style};return{...j,...Ce,...we,href:b?void 0:de.maskedLocation?n.history.createHref(de.maskedLocation.href):n.history.createHref(de.href),ref:o,onClick:Tn([C,ve]),onFocus:Tn([w,ye]),onMouseEnter:Tn([T,xe]),onMouseLeave:Tn([E,Se]),onTouchStart:Tn([ee,be]),disabled:!!b,target:y,...Oe&&{style:Oe},...Te&&{className:Te},...b&&bn,...he&&xn,...r&&Sn}}const vn={},yn={className:`active`},bn={role:`link`,"aria-disabled":!0},xn={"data-status":`active`,"aria-current":`page`},Sn={"data-transitioning":`transitioning`},Cn=new WeakMap,wn={rootMargin:`100px`},Tn=e=>t=>{e.filter(Boolean).forEach(e=>{t.defaultPrevented||e(t)})},En=P.forwardRef((e,t)=>{let{_asChild:n,...r}=e,{type:i,ref:a,...o}=_n(r,t),s=typeof r.children==`function`?r.children({isActive:o[`data-status`]===`active`}):r.children;return n===void 0&&delete o.disabled,P.createElement(n||`a`,{...o,ref:a},s)});function Dn(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}var On=class extends Ct{constructor(e){super(e),this.useMatch=e=>rn({select:e?.select,from:this.id,structuralSharing:e?.structuralSharing}),this.useRouteContext=e=>rn({...e,from:this.id,select:t=>e?.select?e.select(t.context):t.context}),this.useSearch=e=>cn({select:e?.select,structuralSharing:e?.structuralSharing,from:this.id}),this.useParams=e=>sn({select:e?.select,structuralSharing:e?.structuralSharing,from:this.id}),this.useLoaderDeps=e=>on({...e,from:this.id}),this.useLoaderData=e=>an({...e,from:this.id}),this.useNavigate=()=>ln({from:this.fullPath}),this.Link=P.forwardRef((e,t)=>(0,N.jsx)(En,{ref:t,from:this.fullPath,...e})),this.$$typeof=Symbol.for(`react.memo`)}};function kn(e){return new On(e)}var An=class extends wt{constructor(e){super(e),this.useMatch=e=>rn({select:e?.select,from:this.id,structuralSharing:e?.structuralSharing}),this.useRouteContext=e=>rn({...e,from:this.id,select:t=>e?.select?e.select(t.context):t.context}),this.useSearch=e=>cn({select:e?.select,structuralSharing:e?.structuralSharing,from:this.id}),this.useParams=e=>sn({select:e?.select,structuralSharing:e?.structuralSharing,from:this.id}),this.useLoaderDeps=e=>on({...e,from:this.id}),this.useLoaderData=e=>an({...e,from:this.id}),this.useNavigate=()=>ln({from:this.fullPath}),this.Link=P.forwardRef((e,t)=>(0,N.jsx)(En,{ref:t,from:this.fullPath,...e})),this.$$typeof=Symbol.for(`react.memo`)}};function jn(e){return new An(e)}function Mn(e){return typeof e==`object`?new Nn(e,{silent:!0}).createRoute(e):new Nn(e,{silent:!0}).createRoute}var Nn=class{constructor(e,t){this.path=e,this.createRoute=e=>{jt(this.silent,`FileRoute is deprecated and will be removed in the next major version. Use the createFileRoute(path)(options) function instead.`);let t=kn(e);return t.isRoot=!1,t},this.silent=t?.silent}},Pn=class{constructor(e){this.useMatch=e=>rn({select:e?.select,from:this.options.id,structuralSharing:e?.structuralSharing}),this.useRouteContext=e=>rn({from:this.options.id,select:t=>e?.select?e.select(t.context):t.context}),this.useSearch=e=>cn({select:e?.select,structuralSharing:e?.structuralSharing,from:this.options.id}),this.useParams=e=>sn({select:e?.select,structuralSharing:e?.structuralSharing,from:this.options.id}),this.useLoaderDeps=e=>on({...e,from:this.options.id}),this.useLoaderData=e=>an({...e,from:this.options.id}),this.useNavigate=()=>{let e=en();return ln({from:e.routesById[this.options.id].fullPath})},this.options=e,this.$$typeof=Symbol.for(`react.memo`)}};function Fn(e){return typeof e==`object`?new Pn(e):t=>new Pn({id:e,...t})}function In(e,t){let n,r,i,a,o=()=>(n||=e().then(e=>{n=void 0,r=e[t??`default`]}).catch(e=>{if(i=e,le(i)&&i instanceof Error&&typeof window<`u`&&typeof sessionStorage<`u`){let e=`tanstack_router_reload:${i.message}`;sessionStorage.getItem(e)||(sessionStorage.setItem(e,`1`),a=!0)}}),n),s=function(e){if(a)throw window.location.reload(),new Promise(()=>{});if(i)throw i;if(!r)throw o();return P.createElement(r,e)};return s.preload=o,s}function Ln(){let e=en(),t=P.useRef({router:e,mounted:!1}),[n,r]=P.useState(!1),{hasPendingMatches:i,isLoading:a}=F({select:e=>({isLoading:e.isLoading,hasPendingMatches:e.matches.some(e=>e.status===`pending`)}),structuralSharing:!0}),o=pn(a),s=a||n||i,c=pn(s),l=a||i,u=pn(l);return e.startTransition=e=>{r(!0),P.startTransition(()=>{e(),r(!1)})},P.useEffect(()=>{let t=e.history.subscribe(e.load),n=e.buildLocation({to:e.latestLocation.pathname,search:!0,params:!0,hash:!0,state:!0,_includeValidateSearch:!0});return we(e.latestLocation.href)!==we(n.href)&&e.commitLocation({...n,replace:!0}),()=>{t()}},[e,e.history]),fn(()=>{if(typeof window<`u`&&e.ssr||t.current.router===e&&t.current.mounted)return;t.current={router:e,mounted:!0};let n=async()=>{try{await e.load()}catch(e){console.error(e)}};n()},[e]),fn(()=>{o&&!a&&e.emit({type:`onLoad`,...lt(e.state)})},[o,e,a]),fn(()=>{u&&!l&&e.emit({type:`onBeforeRouteMount`,...lt(e.state)})},[l,u,e]),fn(()=>{c&&!s&&(e.emit({type:`onResolved`,...lt(e.state)}),e.__store.setState(e=>({...e,status:`idle`,resolvedLocation:e.location})),Ze(e))},[s,c,e]),null}function Rn(e){let t=F({select:e=>`not-found-${e.location.pathname}-${e.status}`});return(0,N.jsx)(zt,{getResetKey:()=>t,onCatch:(t,n)=>{var r;if(Be(t))(r=e.onCatch)==null||r.call(e,t,n);else throw t},errorComponent:({error:t})=>{var n;if(Be(t))return(n=e.fallback)?.call(e,t);throw t},children:e.children})}function zn(){return(0,N.jsx)(`p`,{children:`Not Found`})}function Bn(e){return(0,N.jsx)(N.Fragment,{children:e.children})}function Vn(e,t,n){return t.options.notFoundComponent?(0,N.jsx)(t.options.notFoundComponent,{data:n}):e.options.defaultNotFoundComponent?(0,N.jsx)(e.options.defaultNotFoundComponent,{data:n}):(0,N.jsx)(zn,{})}function Hn({children:e}){return typeof document<`u`?null:(0,N.jsx)(`script`,{className:`$tsr`,dangerouslySetInnerHTML:{__html:[e].filter(Boolean).join(`
`)}})}function Un(){let e=en(),t=e.options.getScrollRestorationKey||Ke,n=t(e.latestLocation),r=n===Ke(e.latestLocation)?null:n;return!e.isScrollRestoring||!e.isServer?null:(0,N.jsx)(Hn,{children:`(${Ye.toString()})(${JSON.stringify(He)},${JSON.stringify(r)}, undefined, true)`})}const Wn=P.memo(function({matchId:e}){var t,n;let r=en(),i=F({select:t=>{let n=t.matches.find(t=>t.id===e);return m(n,`Could not find match for matchId "${e}". Please file an issue!`),k(n,[`routeId`,`ssr`,`_displayPending`])},structuralSharing:!0}),a=r.routesById[i.routeId],o=a.options.pendingComponent??r.options.defaultPendingComponent,s=o?(0,N.jsx)(o,{}):null,c=a.options.errorComponent??r.options.defaultErrorComponent,l=a.options.onCatch??r.options.defaultOnCatch,u=a.isRoot?a.options.notFoundComponent??(t=r.options.notFoundRoute)?.options.component:a.options.notFoundComponent,d=i.ssr===!1||i.ssr===`data-only`,f=(!a.isRoot||a.options.wrapInSuspense||d)&&(a.options.wrapInSuspense??o??((n=a.options.errorComponent)?.preload||d))?P.Suspense:Bn,p=c?zt:Bn,h=u?Rn:Bn,g=F({select:e=>e.loadedAt}),_=F({select:t=>{var n;let r=t.matches.findIndex(t=>t.id===e);return(n=t.matches[r-1])?.routeId}}),v=a.isRoot?a.options.shellComponent??Bn:Bn;return(0,N.jsxs)(v,{children:[(0,N.jsx)(tn.Provider,{value:e,children:(0,N.jsx)(f,{fallback:s,children:(0,N.jsx)(p,{getResetKey:()=>g,errorComponent:c||Vt,onCatch:(t,n)=>{if(Be(t))throw t;jt(!1,`Error in route match: ${e}`),l?.(t,n)},children:(0,N.jsx)(h,{fallback:e=>{if(!u||e.routeId&&e.routeId!==i.routeId||!e.routeId&&!a.isRoot)throw e;return P.createElement(u,e)},children:d||i._displayPending?(0,N.jsx)(Ht,{fallback:s,children:(0,N.jsx)(Kn,{matchId:e})}):(0,N.jsx)(Kn,{matchId:e})})})})}),_===at&&r.options.scrollRestoration?(0,N.jsxs)(N.Fragment,{children:[(0,N.jsx)(Gn,{}),(0,N.jsx)(Un,{})]}):null]})});function Gn(){let e=en(),t=P.useRef(void 0);return(0,N.jsx)(`script`,{suppressHydrationWarning:!0,ref:n=>{n&&(t.current===void 0||t.current.href!==e.latestLocation.href)&&(e.emit({type:`onRendered`,...lt(e.state)}),t.current=e.latestLocation)}},e.latestLocation.state.__TSR_key)}const Kn=P.memo(function({matchId:e}){var t,n,r,i,a;let o=en(),{match:s,key:c,routeId:l}=F({select:t=>{let n=t.matches.findIndex(t=>t.id===e),r=t.matches[n],i=r.routeId,a=o.routesById[i].options.remountDeps??o.options.defaultRemountDeps,s=a?.({routeId:i,loaderDeps:r.loaderDeps,params:r._strictParams,search:r._strictSearch}),c=s?JSON.stringify(s):void 0;return{key:c,routeId:i,match:k(r,[`id`,`status`,`error`,`_forcePending`,`_displayPending`])}},structuralSharing:!0}),u=o.routesById[l],d=P.useMemo(()=>{let e=u.options.component??o.options.defaultComponent;return e?(0,N.jsx)(e,{},c):(0,N.jsx)(qn,{})},[c,u.options.component,o.options.defaultComponent]);if(s._displayPending)throw(t=o.getMatch(s.id))?.displayPendingPromise;if(s._forcePending)throw(n=o.getMatch(s.id))?.minPendingPromise;if(s.status===`pending`){let e=u.options.pendingMinMs??o.options.defaultPendingMinMs;if(e&&!(r=o.getMatch(s.id))?.minPendingPromise&&!o.isServer){let t=ce();Promise.resolve().then(()=>{o.updateMatch(s.id,e=>({...e,minPendingPromise:t}))}),setTimeout(()=>{t.resolve(),o.updateMatch(s.id,e=>({...e,minPendingPromise:void 0}))},e)}throw(i=o.getMatch(s.id))?.loadPromise}if(s.status===`notFound`)return m(Be(s.error),`Expected a notFound error`),Vn(o,u,s.error);if(s.status===`redirected`)throw m(st(s.error),`Expected a redirect error`),(a=o.getMatch(s.id))?.loadPromise;if(s.status===`error`){if(o.isServer){let e=(u.options.errorComponent??o.options.defaultErrorComponent)||Vt;return(0,N.jsx)(e,{error:s.error,reset:void 0,info:{componentStack:``}})}throw s.error}return d}),qn=P.memo(function(){let e=en(),t=P.useContext(tn),n=F({select:e=>{var n;return(n=e.matches.find(e=>e.id===t))?.routeId}}),r=e.routesById[n],i=F({select:e=>{let n=e.matches,r=n.find(e=>e.id===t);return m(r,`Could not find parent match for matchId "${t}"`),r.globalNotFound}}),a=F({select:e=>{var n;let r=e.matches,i=r.findIndex(e=>e.id===t);return(n=r[i+1])?.id}}),o=e.options.defaultPendingComponent?(0,N.jsx)(e.options.defaultPendingComponent,{}):null;if(i)return Vn(e,r,void 0);if(!a)return null;let s=(0,N.jsx)(Wn,{matchId:a});return t===at?(0,N.jsx)(P.Suspense,{fallback:o,children:s}):s});function Jn(){let e=en(),t=e.options.defaultPendingComponent?(0,N.jsx)(e.options.defaultPendingComponent,{}):null,n=e.isServer||typeof document<`u`&&e.ssr?Bn:P.Suspense,r=(0,N.jsxs)(n,{fallback:t,children:[!e.isServer&&(0,N.jsx)(Ln,{}),(0,N.jsx)(Yn,{})]});return e.options.InnerWrap?(0,N.jsx)(e.options.InnerWrap,{children:r}):r}function Yn(){let e=F({select:e=>{var t;return(t=e.matches[0])?.id}}),t=F({select:e=>e.loadedAt});return(0,N.jsx)(tn.Provider,{value:e,children:(0,N.jsx)(zt,{getResetKey:()=>t,errorComponent:Vt,onCatch:e=>{jt(!1,`The following error wasn't caught by any route! At the very least, consider setting an 'errorComponent' in your RootRoute!`),jt(!1,e.message||e.toString())},children:e?(0,N.jsx)(Wn,{matchId:e}):null})})}const Xn=e=>new Zn(e);var Zn=class extends ut{constructor(e){super(e)}};typeof globalThis<`u`?(globalThis.createFileRoute=Mn,globalThis.createLazyFileRoute=Fn):typeof window<`u`&&(window.createFileRoute=Mn,window.createFileRoute=Fn);function Qn({router:e,children:t,...n}){Object.keys(n).length>0&&e.update({...e.options,...n,context:{...e.options.context,...n.context}});let r=$t(),i=(0,N.jsx)(r.Provider,{value:e,children:t});return e.options.Wrap?(0,N.jsx)(e.options.Wrap,{children:i}):i}function $n({router:e,...t}){return(0,N.jsx)(Qn,{router:e,...t,children:(0,N.jsx)(Jn,{})})}function er(e){return rn({...e,select:t=>e.select?e.select(t.context):t.context})}function tr(e){return F({select:t=>e?.select?e.select(t.location):t.location})}function nr({tag:e,attrs:t,children:n}){switch(e){case`title`:return(0,N.jsx)(`title`,{...t,suppressHydrationWarning:!0,children:n});case`meta`:return(0,N.jsx)(`meta`,{...t,suppressHydrationWarning:!0});case`link`:return(0,N.jsx)(`link`,{...t,suppressHydrationWarning:!0});case`style`:return(0,N.jsx)(`style`,{...t,dangerouslySetInnerHTML:{__html:n}});case`script`:return(0,N.jsx)(rr,{attrs:t,children:n});default:return null}}function rr({attrs:e,children:t}){return P.useEffect(()=>{if(e?.src){let t=document.createElement(`script`);for(let[n,r]of Object.entries(e))n!==`suppressHydrationWarning`&&r!==void 0&&r!==!1&&t.setAttribute(n,typeof r==`boolean`?``:String(r));return document.head.appendChild(t),()=>{t.parentNode&&t.parentNode.removeChild(t)}}if(typeof t==`string`){let n=document.createElement(`script`);if(n.textContent=t,e)for(let[t,r]of Object.entries(e))t!==`suppressHydrationWarning`&&r!==void 0&&r!==!1&&n.setAttribute(t,typeof r==`boolean`?``:String(r));return document.head.appendChild(n),()=>{n.parentNode&&n.parentNode.removeChild(n)}}},[e,t]),e?.src&&typeof e.src==`string`?(0,N.jsx)(`script`,{...e,suppressHydrationWarning:!0}):typeof t==`string`?(0,N.jsx)(`script`,{...e,dangerouslySetInnerHTML:{__html:t},suppressHydrationWarning:!0}):null}const ir=()=>{let e=en(),t=F({select:e=>e.matches.map(e=>e.meta).filter(Boolean)}),n=P.useMemo(()=>{let e=[],n={},r;return[...t].reverse().forEach(t=>{[...t].reverse().forEach(t=>{if(t)if(t.title)r||={tag:`title`,children:t.title};else{let r=t.name??t.property;if(r){if(n[r])return;n[r]=!0}e.push({tag:`meta`,attrs:{...t}})}})}),r&&e.push(r),e.reverse(),e},[t]),r=F({select:t=>{var n;let r=t.matches.map(e=>e.links).filter(Boolean).flat(1).map(e=>({tag:`link`,attrs:{...e}})),i=(n=e.ssr)?.manifest,a=t.matches.map(e=>{var t;return(t=i?.routes[e.routeId])?.assets??[]}).filter(Boolean).flat(1).filter(e=>e.tag===`link`).map(e=>({tag:`link`,attrs:{...e.attrs,suppressHydrationWarning:!0}}));return[...r,...a]},structuralSharing:!0}),i=F({select:t=>{let n=[];return t.matches.map(t=>e.looseRoutesById[t.routeId]).forEach(t=>{var r,i,a,o;return(o=(a=(i=(r=e.ssr)?.manifest)?.routes[t.id])?.preloads)?.filter(Boolean).forEach(e=>{n.push({tag:`link`,attrs:{rel:`modulepreload`,href:e}})})}),n},structuralSharing:!0}),a=F({select:e=>e.matches.map(e=>e.styles).flat(1).filter(Boolean).map(({children:e,...t})=>({tag:`style`,attrs:t,children:e})),structuralSharing:!0}),o=F({select:e=>e.matches.map(e=>e.headScripts).flat(1).filter(Boolean).map(({children:e,...t})=>({tag:`script`,attrs:{...t},children:e})),structuralSharing:!0});return or([...n,...i,...r,...a,...o],e=>JSON.stringify(e))};function ar(){let e=ir();return e.map(e=>(0,P.createElement)(nr,{...e,key:`tsr-meta-${JSON.stringify(e)}`}))}function or(e,t){let n=new Set;return e.filter(e=>{let r=t(e);return n.has(r)?!1:(n.add(r),!0)})}const sr=()=>{let e=en(),t=F({select:t=>{var n;let r=[],i=(n=e.ssr)?.manifest;return i?(t.matches.map(t=>e.looseRoutesById[t.routeId]).forEach(e=>{var t,n;return(n=(t=i.routes[e.id])?.assets)?.filter(e=>e.tag===`script`).forEach(e=>{r.push({tag:`script`,attrs:e.attrs,children:e.children})})}),r):[]},structuralSharing:!0}),{scripts:n}=F({select:e=>({scripts:e.matches.map(e=>e.scripts).flat(1).filter(Boolean).map(({children:e,...t})=>({tag:`script`,attrs:{...t,suppressHydrationWarning:!0},children:e}))}),structuralSharing:!0}),r=[...n,...t];return(0,N.jsx)(N.Fragment,{children:r.map((e,t)=>(0,P.createElement)(nr,{...e,key:`tsr-scripts-${e.tag}-${t}`}))})};let cr;function lr(e){return cr||=e.router.state.matches.length?Promise.resolve():de(e.router),(0,N.jsx)(Lt,{promise:cr,children:()=>(0,N.jsx)($n,{router:e.router})})}var ur=s(exports=>{function t(e,t){var n=e.length;e.push(t);a:for(;0<n;){var r=n-1>>>1,a=e[r];if(0<i(a,t))e[r]=t,e[n]=a,n=r;else break a}}function n(e){return e.length===0?null:e[0]}function r(e){if(e.length===0)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;a:for(var r=0,a=e.length,o=a>>>1;r<o;){var s=2*(r+1)-1,c=e[s],l=s+1,u=e[l];if(0>i(c,n))l<a&&0>i(u,c)?(e[r]=u,e[l]=n,r=l):(e[r]=c,e[s]=n,r=s);else if(l<a&&0>i(u,n))e[r]=u,e[l]=n,r=l;else break a}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return n===0?e.id-t.id:n}if(exports.unstable_now=void 0,typeof performance==`object`&&typeof performance.now==`function`){var a=performance;exports.unstable_now=function(){return a.now()}}else{var o=Date,s=o.now();exports.unstable_now=function(){return o.now()-s}}var c=[],l=[],u=1,d=null,f=3,p=!1,m=!1,h=!1,g=!1,_=typeof setTimeout==`function`?setTimeout:null,v=typeof clearTimeout==`function`?clearTimeout:null,y=typeof setImmediate<`u`?setImmediate:null;function b(e){for(var i=n(l);i!==null;){if(i.callback===null)r(l);else if(i.startTime<=e)r(l),i.sortIndex=i.expirationTime,t(c,i);else break;i=n(l)}}function x(e){if(h=!1,b(e),!m)if(n(c)!==null)m=!0,S||(S=!0,D());else{var t=n(l);t!==null&&re(x,t.startTime-e)}}var S=!1,C=-1,w=5,T=-1;function E(){return g?!0:exports.unstable_now()-T<w?!1:!0}function ee(){if(g=!1,S){var t=exports.unstable_now();T=t;var i=!0;try{a:{m=!1,h&&(h=!1,v(C),C=-1),p=!0;var a=f;try{b:{for(b(t),d=n(c);d!==null&&!(d.expirationTime>t&&E());){var o=d.callback;if(typeof o==`function`){d.callback=null,f=d.priorityLevel;var s=o(d.expirationTime<=t);if(t=exports.unstable_now(),typeof s==`function`){d.callback=s,b(t),i=!0;break b}d===n(c)&&r(c),b(t)}else r(c);d=n(c)}if(d!==null)i=!0;else{var u=n(l);u!==null&&re(x,u.startTime-t),i=!1}}break a}finally{d=null,f=a,p=!1}i=void 0}}finally{i?D():S=!1}}}var D;if(typeof y==`function`)D=function(){y(ee)};else if(typeof MessageChannel<`u`){var te=new MessageChannel,ne=te.port2;te.port1.onmessage=ee,D=function(){ne.postMessage(null)}}else D=function(){_(ee,0)};function re(t,n){C=_(function(){t(exports.unstable_now())},n)}exports.unstable_IdlePriority=5,exports.unstable_ImmediatePriority=1,exports.unstable_LowPriority=4,exports.unstable_NormalPriority=3,exports.unstable_Profiling=null,exports.unstable_UserBlockingPriority=2,exports.unstable_cancelCallback=function(e){e.callback=null},exports.unstable_forceFrameRate=function(e){0>e||125<e?console.error(`forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported`):w=0<e?Math.floor(1e3/e):5},exports.unstable_getCurrentPriorityLevel=function(){return f},exports.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},exports.unstable_requestPaint=function(){g=!0},exports.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},exports.unstable_scheduleCallback=function(r,i,a){var o=exports.unstable_now();switch(typeof a==`object`&&a?(a=a.delay,a=typeof a==`number`&&0<a?o+a:o):a=o,r){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return s=a+s,r={id:u++,callback:i,priorityLevel:r,startTime:a,expirationTime:s,sortIndex:-1},a>o?(r.sortIndex=a,t(l,r),n(c)===null&&r===n(l)&&(h?(v(C),C=-1):h=!0,re(x,a-o))):(r.sortIndex=s,t(c,r),m||p||(m=!0,S||(S=!0,D()))),r},exports.unstable_shouldYield=E,exports.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}}),dr=s((exports,t)=>{t.exports=ur()}),fr=s(exports=>{var t=dr(),n=Ft(),r=dn();function i(e){var t=`https://react.dev/errors/`+e;if(1<arguments.length){t+=`?args[]=`+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+=`&args[]=`+encodeURIComponent(arguments[n])}return`Minified React error #`+e+`; visit `+t+` for the full message or use the non-minified dev environment for full errors and additional helpful warnings.`}function a(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function o(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function s(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function c(e){if(o(e)!==e)throw Error(i(188))}function l(e){var t=e.alternate;if(!t){if(t=o(e),t===null)throw Error(i(188));return t===e?e:null}for(var n=e,r=t;;){var a=n.return;if(a===null)break;var s=a.alternate;if(s===null){if(r=a.return,r!==null){n=r;continue}break}if(a.child===s.child){for(s=a.child;s;){if(s===n)return c(a),e;if(s===r)return c(a),t;s=s.sibling}throw Error(i(188))}if(n.return!==r.return)n=a,r=s;else{for(var l=!1,u=a.child;u;){if(u===n){l=!0,n=a,r=s;break}if(u===r){l=!0,r=a,n=s;break}u=u.sibling}if(!l){for(u=s.child;u;){if(u===n){l=!0,n=s,r=a;break}if(u===r){l=!0,r=s,n=a;break}u=u.sibling}if(!l)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(n.tag!==3)throw Error(i(188));return n.stateNode.current===n?e:t}function u(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=u(e),t!==null)return t;e=e.sibling}return null}var d=Object.assign,f=Symbol.for(`react.element`),p=Symbol.for(`react.transitional.element`),m=Symbol.for(`react.portal`),h=Symbol.for(`react.fragment`),g=Symbol.for(`react.strict_mode`),_=Symbol.for(`react.profiler`),v=Symbol.for(`react.provider`),y=Symbol.for(`react.consumer`),b=Symbol.for(`react.context`),x=Symbol.for(`react.forward_ref`),S=Symbol.for(`react.suspense`),C=Symbol.for(`react.suspense_list`),w=Symbol.for(`react.memo`),T=Symbol.for(`react.lazy`);Symbol.for(`react.scope`);var E=Symbol.for(`react.activity`);Symbol.for(`react.legacy_hidden`),Symbol.for(`react.tracing_marker`);var ee=Symbol.for(`react.memo_cache_sentinel`);Symbol.for(`react.view_transition`);var D=Symbol.iterator;function te(e){return typeof e!=`object`||!e?null:(e=D&&e[D]||e[`@@iterator`],typeof e==`function`?e:null)}var ne=Symbol.for(`react.client.reference`);function re(e){if(e==null)return null;if(typeof e==`function`)return e.$$typeof===ne?null:e.displayName||e.name||null;if(typeof e==`string`)return e;switch(e){case h:return`Fragment`;case _:return`Profiler`;case g:return`StrictMode`;case S:return`Suspense`;case C:return`SuspenseList`;case E:return`Activity`}if(typeof e==`object`)switch(e.$$typeof){case m:return`Portal`;case b:return(e.displayName||`Context`)+`.Provider`;case y:return(e._context.displayName||`Context`)+`.Consumer`;case x:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||``,e=e===``?`ForwardRef`:`ForwardRef(`+e+`)`),e;case w:return t=e.displayName||null,t===null?re(e.type)||`Memo`:t;case T:t=e._payload,e=e._init;try{return re(e(t))}catch{}}return null}var O=Array.isArray,k=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,A=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ie={pending:!1,data:null,method:null,action:null},ae=[],oe=-1;function se(e){return{current:e}}function j(e){0>oe||(e.current=ae[oe],ae[oe]=null,oe--)}function M(e,t){oe++,ae[oe]=e.current,e.current=t}var ce=se(null),le=se(null),ue=se(null),de=se(null);function fe(e,t){switch(M(ue,t),M(le,e),M(ce,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Hd(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Hd(t),e=Ud(t,e);else switch(e){case`svg`:e=1;break;case`math`:e=2;break;default:e=0}}j(ce),M(ce,e)}function pe(){j(ce),j(le),j(ue)}function me(e){e.memoizedState!==null&&M(de,e);var t=ce.current,n=Ud(t,e.type);t!==n&&(M(le,e),M(ce,n))}function he(e){le.current===e&&(j(ce),j(le)),de.current===e&&(j(de),Zf._currentValue=ie)}var ge=Object.prototype.hasOwnProperty,_e=t.unstable_scheduleCallback,ve=t.unstable_cancelCallback,ye=t.unstable_shouldYield,be=t.unstable_requestPaint,xe=t.unstable_now,Se=t.unstable_getCurrentPriorityLevel,Ce=t.unstable_ImmediatePriority,we=t.unstable_UserBlockingPriority,Te=t.unstable_NormalPriority,Ee=t.unstable_LowPriority,De=t.unstable_IdlePriority,Oe=t.log,ke=t.unstable_setDisableYieldValue,Ae=null,je=null;function Me(e){if(typeof Oe==`function`&&ke(e),je&&typeof je.setStrictMode==`function`)try{je.setStrictMode(Ae,e)}catch{}}var Ne=Math.clz32?Math.clz32:Ie,Pe=Math.log,Fe=Math.LN2;function Ie(e){return e>>>=0,e===0?32:31-(Pe(e)/Fe|0)|0}var Le=256,Re=4194304;function ze(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Be(e,t,n){var r=e.pendingLanes;if(r===0)return 0;var i=0,a=e.suspendedLanes,o=e.pingedLanes;e=e.warmLanes;var s=r&134217727;return s===0?(s=r&~a,s===0?o===0?n||(n=r&~e,n!==0&&(i=ze(n))):i=ze(o):i=ze(s)):(r=s&~a,r===0?(o&=s,o===0?n||(n=s&~e,n!==0&&(i=ze(n))):i=ze(o)):i=ze(r)),i===0?0:t!==0&&t!==i&&(t&a)===0&&(a=i&-i,n=t&-t,a>=n||a===32&&n&4194048)?t:i}function Ve(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function He(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ue(){var e=Le;return Le<<=1,!(Le&4194048)&&(Le=256),e}function We(){var e=Re;return Re<<=1,!(Re&62914560)&&(Re=4194304),e}function Ge(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ke(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function qe(e,t,n,r,i,a){var o=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var s=e.entanglements,c=e.expirationTimes,l=e.hiddenUpdates;for(n=o&~n;0<n;){var u=31-Ne(n),d=1<<u;s[u]=0,c[u]=-1;var f=l[u];if(f!==null)for(l[u]=null,u=0;u<f.length;u++){var p=f[u];p!==null&&(p.lane&=-536870913)}n&=~d}r!==0&&Je(e,r,0),a!==0&&i===0&&e.tag!==0&&(e.suspendedLanes|=a&~(o&~t))}function Je(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-Ne(t);e.entangledLanes|=t,e.entanglements[r]=e.entanglements[r]|1073741824|n&4194090}function Ye(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ne(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}function Xe(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Ze(e){return e&=-e,2<e?8<e?e&134217727?32:268435456:8:2}function Qe(){var e=A.p;return e===0?(e=window.event,e===void 0?32:fp(e.type)):e}function $e(e,t){var n=A.p;try{return A.p=e,t()}finally{A.p=n}}var et=Math.random().toString(36).slice(2),tt=`__reactFiber$`+et,nt=`__reactProps$`+et,rt=`__reactContainer$`+et,it=`__reactEvents$`+et,at=`__reactListeners$`+et,ot=`__reactHandles$`+et,st=`__reactResources$`+et,ct=`__reactMarker$`+et;function lt(e){delete e[tt],delete e[nt],delete e[it],delete e[at],delete e[ot]}function ut(e){var t=e[tt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[rt]||n[tt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=lf(e);e!==null;){if(n=e[tt])return n;e=lf(e)}return t}e=n,n=e.parentNode}return null}function dt(e){if(e=e[tt]||e[rt]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function ft(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(i(33))}function pt(e){var t=e[st];return t||=e[st]={hoistableStyles:new Map,hoistableScripts:new Map},t}function mt(e){e[ct]=!0}var ht=new Set,gt={};function _t(e,t){vt(e,t),vt(e+`Capture`,t)}function vt(e,t){for(gt[e]=t,e=0;e<t.length;e++)ht.add(t[e])}var yt=RegExp(`^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$`),bt={},xt={};function St(e){return ge.call(xt,e)?!0:ge.call(bt,e)?!1:yt.test(e)?xt[e]=!0:(bt[e]=!0,!1)}function Ct(e,t,n){if(St(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case`undefined`:case`function`:case`symbol`:e.removeAttribute(t);return;case`boolean`:var r=t.toLowerCase().slice(0,5);if(r!==`data-`&&r!==`aria-`){e.removeAttribute(t);return}}e.setAttribute(t,``+n)}}function wt(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case`undefined`:case`function`:case`symbol`:case`boolean`:e.removeAttribute(t);return}e.setAttribute(t,``+n)}}function Tt(e,t,n,r){if(r===null)e.removeAttribute(n);else{switch(typeof r){case`undefined`:case`function`:case`symbol`:case`boolean`:e.removeAttribute(n);return}e.setAttributeNS(t,n,``+r)}}var Et,Dt;function Ot(e){if(Et===void 0)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);Et=t&&t[1]||``,Dt=-1<e.stack.indexOf(`
    at`)?` (<anonymous>)`:-1<e.stack.indexOf(`@`)?`@unknown:0:0`:``}return`
`+Et+e+Dt}var kt=!1;function At(e,t){if(!e||kt)return``;kt=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,`props`,{set:function(){throw Error()}}),typeof Reflect==`object`&&Reflect.construct){try{Reflect.construct(n,[])}catch(e){var r=e}Reflect.construct(e,[],n)}else{try{n.call()}catch(e){r=e}e.call(n.prototype)}}else{try{throw Error()}catch(e){r=e}(n=e())&&typeof n.catch==`function`&&n.catch(function(){})}}catch(e){if(e&&r&&typeof e.stack==`string`)return[e.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName=`DetermineComponentFrameRoot`;var i=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,`name`);i&&i.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,`name`,{value:`DetermineComponentFrameRoot`});var a=r.DetermineComponentFrameRoot(),o=a[0],s=a[1];if(o&&s){var c=o.split(`
`),l=s.split(`
`);for(i=r=0;r<c.length&&!c[r].includes(`DetermineComponentFrameRoot`);)r++;for(;i<l.length&&!l[i].includes(`DetermineComponentFrameRoot`);)i++;if(r===c.length||i===l.length)for(r=c.length-1,i=l.length-1;1<=r&&0<=i&&c[r]!==l[i];)i--;for(;1<=r&&0<=i;r--,i--)if(c[r]!==l[i]){if(r!==1||i!==1)do if(r--,i--,0>i||c[r]!==l[i]){var u=`
`+c[r].replace(` at new `,` at `);return e.displayName&&u.includes(`<anonymous>`)&&(u=u.replace(`<anonymous>`,e.displayName)),u}while(1<=r&&0<=i);break}}}finally{kt=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:``)?Ot(n):``}function jt(e){switch(e.tag){case 26:case 27:case 5:return Ot(e.type);case 16:return Ot(`Lazy`);case 13:return Ot(`Suspense`);case 19:return Ot(`SuspenseList`);case 0:case 15:return At(e.type,!1);case 11:return At(e.type.render,!1);case 1:return At(e.type,!0);case 31:return Ot(`Activity`);default:return``}}function Mt(e){try{var t=``;do t+=jt(e),e=e.return;while(e);return t}catch(e){return`
Error generating stack: `+e.message+`
`+e.stack}}function Nt(e){switch(typeof e){case`bigint`:case`boolean`:case`number`:case`string`:case`undefined`:return e;case`object`:return e;default:return``}}function Pt(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()===`input`&&(t===`checkbox`||t===`radio`)}function N(e){var t=Pt(e)?`checked`:`value`,n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=``+e[t];if(!e.hasOwnProperty(t)&&n!==void 0&&typeof n.get==`function`&&typeof n.set==`function`){var i=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(e){r=``+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=``+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function P(e){e._valueTracker||=N(e)}function It(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r=``;return e&&(r=Pt(e)?e.checked?`true`:`false`:e.value),e=r,e===n?!1:(t.setValue(e),!0)}function Lt(e){if(e||=typeof document<`u`?document:void 0,e===void 0)return null;try{return e.activeElement||e.body}catch{return e.body}}var Rt=/[\n"\\]/g;function zt(e){return e.replace(Rt,function(e){return`\\`+e.charCodeAt(0).toString(16)+` `})}function Bt(e,t,n,r,i,a,o,s){e.name=``,o!=null&&typeof o!=`function`&&typeof o!=`symbol`&&typeof o!=`boolean`?e.type=o:e.removeAttribute(`type`),t==null?o!==`submit`&&o!==`reset`||e.removeAttribute(`value`):o===`number`?(t===0&&e.value===``||e.value!=t)&&(e.value=``+Nt(t)):e.value!==``+Nt(t)&&(e.value=``+Nt(t)),t==null?n==null?r!=null&&e.removeAttribute(`value`):Ht(e,o,Nt(n)):Ht(e,o,Nt(t)),i==null&&a!=null&&(e.defaultChecked=!!a),i!=null&&(e.checked=i&&typeof i!=`function`&&typeof i!=`symbol`),s!=null&&typeof s!=`function`&&typeof s!=`symbol`&&typeof s!=`boolean`?e.name=``+Nt(s):e.removeAttribute(`name`)}function Vt(e,t,n,r,i,a,o,s){if(a!=null&&typeof a!=`function`&&typeof a!=`symbol`&&typeof a!=`boolean`&&(e.type=a),t!=null||n!=null){if(!(a!==`submit`&&a!==`reset`||t!=null))return;n=n==null?``:``+Nt(n),t=t==null?n:``+Nt(t),s||t===e.value||(e.value=t),e.defaultValue=t}r??=i,r=typeof r!=`function`&&typeof r!=`symbol`&&!!r,e.checked=s?e.checked:!!r,e.defaultChecked=!!r,o!=null&&typeof o!=`function`&&typeof o!=`symbol`&&typeof o!=`boolean`&&(e.name=o)}function Ht(e,t,n){t===`number`&&Lt(e.ownerDocument)===e||e.defaultValue===``+n||(e.defaultValue=``+n)}function Ut(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t[`$`+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty(`$`+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=``+Nt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Wt(e,t,n){if(t!=null&&(t=``+Nt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n==null?``:``+Nt(n)}function Gt(e,t,n,r){if(t==null){if(r!=null){if(n!=null)throw Error(i(92));if(O(r)){if(1<r.length)throw Error(i(93));r=r[0]}n=r}n??=``,t=n}n=Nt(t),e.defaultValue=n,r=e.textContent,r===n&&r!==``&&r!==null&&(e.value=r)}function Kt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var qt=new Set(`animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp`.split(` `));function Jt(e,t,n){var r=t.indexOf(`--`)===0;n==null||typeof n==`boolean`||n===``?r?e.setProperty(t,``):t===`float`?e.cssFloat=``:e[t]=``:r?e.setProperty(t,n):typeof n!=`number`||n===0||qt.has(t)?t===`float`?e.cssFloat=n:e[t]=(``+n).trim():e[t]=n+`px`}function Yt(e,t,n){if(t!=null&&typeof t!=`object`)throw Error(i(62));if(e=e.style,n!=null){for(var r in n)!n.hasOwnProperty(r)||t!=null&&t.hasOwnProperty(r)||(r.indexOf(`--`)===0?e.setProperty(r,``):r===`float`?e.cssFloat=``:e[r]=``);for(var a in t)r=t[a],t.hasOwnProperty(a)&&n[a]!==r&&Jt(e,a,r)}else for(var o in t)t.hasOwnProperty(o)&&Jt(e,o,t[o])}function Xt(e){if(e.indexOf(`-`)===-1)return!1;switch(e){case`annotation-xml`:case`color-profile`:case`font-face`:case`font-face-src`:case`font-face-uri`:case`font-face-format`:case`font-face-name`:case`missing-glyph`:return!1;default:return!0}}var Zt=new Map([[`acceptCharset`,`accept-charset`],[`htmlFor`,`for`],[`httpEquiv`,`http-equiv`],[`crossOrigin`,`crossorigin`],[`accentHeight`,`accent-height`],[`alignmentBaseline`,`alignment-baseline`],[`arabicForm`,`arabic-form`],[`baselineShift`,`baseline-shift`],[`capHeight`,`cap-height`],[`clipPath`,`clip-path`],[`clipRule`,`clip-rule`],[`colorInterpolation`,`color-interpolation`],[`colorInterpolationFilters`,`color-interpolation-filters`],[`colorProfile`,`color-profile`],[`colorRendering`,`color-rendering`],[`dominantBaseline`,`dominant-baseline`],[`enableBackground`,`enable-background`],[`fillOpacity`,`fill-opacity`],[`fillRule`,`fill-rule`],[`floodColor`,`flood-color`],[`floodOpacity`,`flood-opacity`],[`fontFamily`,`font-family`],[`fontSize`,`font-size`],[`fontSizeAdjust`,`font-size-adjust`],[`fontStretch`,`font-stretch`],[`fontStyle`,`font-style`],[`fontVariant`,`font-variant`],[`fontWeight`,`font-weight`],[`glyphName`,`glyph-name`],[`glyphOrientationHorizontal`,`glyph-orientation-horizontal`],[`glyphOrientationVertical`,`glyph-orientation-vertical`],[`horizAdvX`,`horiz-adv-x`],[`horizOriginX`,`horiz-origin-x`],[`imageRendering`,`image-rendering`],[`letterSpacing`,`letter-spacing`],[`lightingColor`,`lighting-color`],[`markerEnd`,`marker-end`],[`markerMid`,`marker-mid`],[`markerStart`,`marker-start`],[`overlinePosition`,`overline-position`],[`overlineThickness`,`overline-thickness`],[`paintOrder`,`paint-order`],[`panose-1`,`panose-1`],[`pointerEvents`,`pointer-events`],[`renderingIntent`,`rendering-intent`],[`shapeRendering`,`shape-rendering`],[`stopColor`,`stop-color`],[`stopOpacity`,`stop-opacity`],[`strikethroughPosition`,`strikethrough-position`],[`strikethroughThickness`,`strikethrough-thickness`],[`strokeDasharray`,`stroke-dasharray`],[`strokeDashoffset`,`stroke-dashoffset`],[`strokeLinecap`,`stroke-linecap`],[`strokeLinejoin`,`stroke-linejoin`],[`strokeMiterlimit`,`stroke-miterlimit`],[`strokeOpacity`,`stroke-opacity`],[`strokeWidth`,`stroke-width`],[`textAnchor`,`text-anchor`],[`textDecoration`,`text-decoration`],[`textRendering`,`text-rendering`],[`transformOrigin`,`transform-origin`],[`underlinePosition`,`underline-position`],[`underlineThickness`,`underline-thickness`],[`unicodeBidi`,`unicode-bidi`],[`unicodeRange`,`unicode-range`],[`unitsPerEm`,`units-per-em`],[`vAlphabetic`,`v-alphabetic`],[`vHanging`,`v-hanging`],[`vIdeographic`,`v-ideographic`],[`vMathematical`,`v-mathematical`],[`vectorEffect`,`vector-effect`],[`vertAdvY`,`vert-adv-y`],[`vertOriginX`,`vert-origin-x`],[`vertOriginY`,`vert-origin-y`],[`wordSpacing`,`word-spacing`],[`writingMode`,`writing-mode`],[`xmlnsXlink`,`xmlns:xlink`],[`xHeight`,`x-height`]]),Qt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function $t(e){return Qt.test(``+e)?`javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')`:e}var en=null;function F(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var tn=null,nn=null;function rn(e){var t=dt(e);if(t&&(e=t.stateNode)){var n=e[nt]||null;a:switch(e=t.stateNode,t.type){case`input`:if(Bt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type===`radio`&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll(`input[name="`+zt(``+t)+`"][type="radio"]`),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=r[nt]||null;if(!a)throw Error(i(90));Bt(r,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(t=0;t<n.length;t++)r=n[t],r.form===e.form&&It(r)}break a;case`textarea`:Wt(e,n.value,n.defaultValue);break a;case`select`:t=n.value,t!=null&&Ut(e,!!n.multiple,t,!1)}}}var an=!1;function on(e,t,n){if(an)return e(t,n);an=!0;try{var r=e(t);return r}finally{if(an=!1,(tn!==null||nn!==null)&&(Cu(),tn&&(t=tn,e=nn,nn=tn=null,rn(t),e)))for(t=0;t<e.length;t++)rn(e[t])}}function sn(e,t){var n=e.stateNode;if(n===null)return null;var r=n[nt]||null;if(r===null)return null;n=r[t];a:switch(t){case`onClick`:case`onClickCapture`:case`onDoubleClick`:case`onDoubleClickCapture`:case`onMouseDown`:case`onMouseDownCapture`:case`onMouseMove`:case`onMouseMoveCapture`:case`onMouseUp`:case`onMouseUpCapture`:case`onMouseEnter`:(r=!r.disabled)||(e=e.type,r=!(e===`button`||e===`input`||e===`select`||e===`textarea`)),e=!r;break a;default:e=!1}if(e)return null;if(n&&typeof n!=`function`)throw Error(i(231,t,typeof n));return n}var cn=!(typeof window>`u`||window.document===void 0||window.document.createElement===void 0),ln=!1;if(cn)try{var un={};Object.defineProperty(un,`passive`,{get:function(){ln=!0}}),window.addEventListener(`test`,un,un),window.removeEventListener(`test`,un,un)}catch{ln=!1}var fn=null,pn=null,mn=null;function hn(){if(mn)return mn;var e,t=pn,n=t.length,r,i=`value`in fn?fn.value:fn.textContent,a=i.length;for(e=0;e<n&&t[e]===i[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===i[a-r];r++);return mn=i.slice(e,1<r?1-r:void 0)}function gn(e){var t=e.keyCode;return`charCode`in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function _n(){return!0}function vn(){return!1}function yn(e){function t(t,n,r,i,a){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=i,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(i):i[o]);return this.isDefaultPrevented=(i.defaultPrevented==null?!1===i.returnValue:i.defaultPrevented)?_n:vn,this.isPropagationStopped=vn,this}return d(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():typeof e.returnValue!=`unknown`&&(e.returnValue=!1),this.isDefaultPrevented=_n)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():typeof e.cancelBubble!=`unknown`&&(e.cancelBubble=!0),this.isPropagationStopped=_n)},persist:function(){},isPersistent:_n}),t}var bn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},xn=yn(bn),Sn=d({},bn,{view:0,detail:0}),Cn=yn(Sn),wn,Tn,En,Dn=d({},Sn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Un,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return`movementX`in e?e.movementX:(e!==En&&(En&&e.type===`mousemove`?(wn=e.screenX-En.screenX,Tn=e.screenY-En.screenY):Tn=wn=0,En=e),wn)},movementY:function(e){return`movementY`in e?e.movementY:Tn}}),On=yn(Dn),kn=d({},Dn,{dataTransfer:0}),An=yn(kn),jn=d({},Sn,{relatedTarget:0}),Mn=yn(jn),Nn=d({},bn,{animationName:0,elapsedTime:0,pseudoElement:0}),Pn=yn(Nn),Fn=d({},bn,{clipboardData:function(e){return`clipboardData`in e?e.clipboardData:window.clipboardData}}),In=yn(Fn),Ln=d({},bn,{data:0}),Rn=yn(Ln),zn={Esc:`Escape`,Spacebar:` `,Left:`ArrowLeft`,Up:`ArrowUp`,Right:`ArrowRight`,Down:`ArrowDown`,Del:`Delete`,Win:`OS`,Menu:`ContextMenu`,Apps:`ContextMenu`,Scroll:`ScrollLock`,MozPrintableKey:`Unidentified`},Bn={8:`Backspace`,9:`Tab`,12:`Clear`,13:`Enter`,16:`Shift`,17:`Control`,18:`Alt`,19:`Pause`,20:`CapsLock`,27:`Escape`,32:` `,33:`PageUp`,34:`PageDown`,35:`End`,36:`Home`,37:`ArrowLeft`,38:`ArrowUp`,39:`ArrowRight`,40:`ArrowDown`,45:`Insert`,46:`Delete`,112:`F1`,113:`F2`,114:`F3`,115:`F4`,116:`F5`,117:`F6`,118:`F7`,119:`F8`,120:`F9`,121:`F10`,122:`F11`,123:`F12`,144:`NumLock`,145:`ScrollLock`,224:`Meta`},Vn={Alt:`altKey`,Control:`ctrlKey`,Meta:`metaKey`,Shift:`shiftKey`};function Hn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Vn[e])?!!t[e]:!1}function Un(){return Hn}var Wn=d({},Sn,{key:function(e){if(e.key){var t=zn[e.key]||e.key;if(t!==`Unidentified`)return t}return e.type===`keypress`?(e=gn(e),e===13?`Enter`:String.fromCharCode(e)):e.type===`keydown`||e.type===`keyup`?Bn[e.keyCode]||`Unidentified`:``},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Un,charCode:function(e){return e.type===`keypress`?gn(e):0},keyCode:function(e){return e.type===`keydown`||e.type===`keyup`?e.keyCode:0},which:function(e){return e.type===`keypress`?gn(e):e.type===`keydown`||e.type===`keyup`?e.keyCode:0}}),Gn=yn(Wn),Kn=d({},Dn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),qn=yn(Kn),Jn=d({},Sn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Un}),Yn=yn(Jn),Xn=d({},bn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Zn=yn(Xn),Qn=d({},Dn,{deltaX:function(e){return`deltaX`in e?e.deltaX:`wheelDeltaX`in e?-e.wheelDeltaX:0},deltaY:function(e){return`deltaY`in e?e.deltaY:`wheelDeltaY`in e?-e.wheelDeltaY:`wheelDelta`in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),$n=yn(Qn),er=d({},bn,{newState:0,oldState:0}),tr=yn(er),nr=[9,13,27,32],rr=cn&&`CompositionEvent`in window,ir=null;cn&&`documentMode`in document&&(ir=document.documentMode);var ar=cn&&`TextEvent`in window&&!ir,or=cn&&(!rr||ir&&8<ir&&11>=ir),sr=` `,cr=!1;function lr(e,t){switch(e){case`keyup`:return nr.indexOf(t.keyCode)!==-1;case`keydown`:return t.keyCode!==229;case`keypress`:case`mousedown`:case`focusout`:return!0;default:return!1}}function ur(e){return e=e.detail,typeof e==`object`&&`data`in e?e.data:null}var fr=!1;function pr(e,t){switch(e){case`compositionend`:return ur(t);case`keypress`:return t.which===32?(cr=!0,sr):null;case`textInput`:return e=t.data,e===sr&&cr?null:e;default:return null}}function mr(e,t){if(fr)return e===`compositionend`||!rr&&lr(e,t)?(e=hn(),mn=pn=fn=null,fr=!1,e):null;switch(e){case`paste`:return null;case`keypress`:if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case`compositionend`:return or&&t.locale!==`ko`?null:t.data;default:return null}}var hr={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function gr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t===`input`?!!hr[e.type]:t===`textarea`?!0:!1}function _r(e,t,n,r){tn?nn?nn.push(r):nn=[r]:tn=r,t=Od(t,`onChange`),0<t.length&&(n=new xn(`onChange`,`change`,null,n,r),e.push({event:n,listeners:t}))}var vr=null,yr=null;function br(e){xd(e,0)}function xr(e){var t=ft(e);if(It(t))return e}function Sr(e,t){if(e===`change`)return t}var Cr=!1;if(cn){var wr;if(cn){var Tr=`oninput`in document;if(!Tr){var Er=document.createElement(`div`);Er.setAttribute(`oninput`,`return;`),Tr=typeof Er.oninput==`function`}wr=Tr}else wr=!1;Cr=wr&&(!document.documentMode||9<document.documentMode)}function Dr(){vr&&(vr.detachEvent(`onpropertychange`,Or),yr=vr=null)}function Or(e){if(e.propertyName===`value`&&xr(yr)){var t=[];_r(t,yr,e,F(e)),on(br,t)}}function kr(e,t,n){e===`focusin`?(Dr(),vr=t,yr=n,vr.attachEvent(`onpropertychange`,Or)):e===`focusout`&&Dr()}function Ar(e){if(e===`selectionchange`||e===`keyup`||e===`keydown`)return xr(yr)}function jr(e,t){if(e===`click`)return xr(t)}function Mr(e,t){if(e===`input`||e===`change`)return xr(t)}function Nr(e,t){return e===t&&(e!==0||1/e==1/t)||e!==e&&t!==t}var Pr=typeof Object.is==`function`?Object.is:Nr;function Fr(e,t){if(Pr(e,t))return!0;if(typeof e!=`object`||!e||typeof t!=`object`||!t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!ge.call(t,i)||!Pr(e[i],t[i]))return!1}return!0}function Ir(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Lr(e,t){var n=Ir(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}a:{for(;n;){if(n.nextSibling){n=n.nextSibling;break a}n=n.parentNode}n=void 0}n=Ir(n)}}function Rr(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Rr(e,t.parentNode):`contains`in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function zr(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Lt(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href==`string`}catch{n=!1}if(n)e=t.contentWindow;else break;t=Lt(e.document)}return t}function Br(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t===`input`&&(e.type===`text`||e.type===`search`||e.type===`tel`||e.type===`url`||e.type===`password`)||t===`textarea`||e.contentEditable===`true`)}var Vr=cn&&`documentMode`in document&&11>=document.documentMode,Hr=null,Ur=null,Wr=null,Gr=!1;function Kr(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Gr||Hr==null||Hr!==Lt(r)||(r=Hr,`selectionStart`in r&&Br(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Wr&&Fr(Wr,r)||(Wr=r,r=Od(Ur,`onSelect`),0<r.length&&(t=new xn(`onSelect`,`select`,null,t,n),e.push({event:t,listeners:r}),t.target=Hr)))}function qr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n[`Webkit`+e]=`webkit`+t,n[`Moz`+e]=`moz`+t,n}var Jr={animationend:qr(`Animation`,`AnimationEnd`),animationiteration:qr(`Animation`,`AnimationIteration`),animationstart:qr(`Animation`,`AnimationStart`),transitionrun:qr(`Transition`,`TransitionRun`),transitionstart:qr(`Transition`,`TransitionStart`),transitioncancel:qr(`Transition`,`TransitionCancel`),transitionend:qr(`Transition`,`TransitionEnd`)},Yr={},Xr={};cn&&(Xr=document.createElement(`div`).style,`AnimationEvent`in window||(delete Jr.animationend.animation,delete Jr.animationiteration.animation,delete Jr.animationstart.animation),`TransitionEvent`in window||delete Jr.transitionend.transition);function Zr(e){if(Yr[e])return Yr[e];if(!Jr[e])return e;var t=Jr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Xr)return Yr[e]=t[n];return e}var Qr=Zr(`animationend`),$r=Zr(`animationiteration`),ei=Zr(`animationstart`),ti=Zr(`transitionrun`),ni=Zr(`transitionstart`),ri=Zr(`transitioncancel`),ii=Zr(`transitionend`),ai=new Map,oi=`abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel`.split(` `);oi.push(`scrollEnd`);function si(e,t){ai.set(e,t),_t(t,[e])}var ci=new WeakMap;function li(e,t){if(typeof e==`object`&&e){var n=ci.get(e);return n===void 0?(t={value:e,source:t,stack:Mt(t)},ci.set(e,t),t):n}return{value:e,source:t,stack:Mt(t)}}var ui=[],di=0,fi=0;function pi(){for(var e=di,t=fi=di=0;t<e;){var n=ui[t];ui[t++]=null;var r=ui[t];ui[t++]=null;var i=ui[t];ui[t++]=null;var a=ui[t];if(ui[t++]=null,r!==null&&i!==null){var o=r.pending;o===null?i.next=i:(i.next=o.next,o.next=i),r.pending=i}a!==0&&_i(n,i,a)}}function mi(e,t,n,r){ui[di++]=e,ui[di++]=t,ui[di++]=n,ui[di++]=r,fi|=r,e.lanes|=r,e=e.alternate,e!==null&&(e.lanes|=r)}function hi(e,t,n,r){return mi(e,t,n,r),vi(e)}function gi(e,t){return mi(e,null,null,t),vi(e)}function _i(e,t,n){e.lanes|=n;var r=e.alternate;r!==null&&(r.lanes|=n);for(var i=!1,a=e.return;a!==null;)a.childLanes|=n,r=a.alternate,r!==null&&(r.childLanes|=n),a.tag===22&&(e=a.stateNode,e===null||e._visibility&1||(i=!0)),e=a,a=a.return;return e.tag===3?(a=e.stateNode,i&&t!==null&&(i=31-Ne(n),e=a.hiddenUpdates,r=e[i],r===null?e[i]=[t]:r.push(t),t.lane=n|536870912),a):null}function vi(e){if(50<mu)throw mu=0,hu=null,Error(i(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var yi={};function bi(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function xi(e,t,n,r){return new bi(e,t,n,r)}function Si(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Ci(e,t){var n=e.alternate;return n===null?(n=xi(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function wi(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ti(e,t,n,r,a,o){var s=0;if(r=e,typeof e==`function`)Si(e)&&(s=1);else if(typeof e==`string`)s=Vf(e,n,ce.current)?26:e===`html`||e===`head`||e===`body`?27:5;else a:switch(e){case E:return e=xi(31,n,t,a),e.elementType=E,e.lanes=o,e;case h:return Ei(n.children,a,o,t);case g:s=8,a|=24;break;case _:return e=xi(12,n,t,a|2),e.elementType=_,e.lanes=o,e;case S:return e=xi(13,n,t,a),e.elementType=S,e.lanes=o,e;case C:return e=xi(19,n,t,a),e.elementType=C,e.lanes=o,e;default:if(typeof e==`object`&&e)switch(e.$$typeof){case v:case b:s=10;break a;case y:s=9;break a;case x:s=11;break a;case w:s=14;break a;case T:s=16,r=null;break a}s=29,n=Error(i(130,e===null?`null`:typeof e,``)),r=null}return t=xi(s,n,t,a),t.elementType=e,t.type=r,t.lanes=o,t}function Ei(e,t,n,r){return e=xi(7,e,r,t),e.lanes=n,e}function Di(e,t,n){return e=xi(6,e,null,t),e.lanes=n,e}function Oi(e,t,n){return t=xi(4,e.children===null?[]:e.children,e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var ki=[],Ai=0,ji=null,Mi=0,Ni=[],I=0,Pi=null,Fi=1,Ii=``;function Li(e,t){ki[Ai++]=Mi,ki[Ai++]=ji,ji=e,Mi=t}function Ri(e,t,n){Ni[I++]=Fi,Ni[I++]=Ii,Ni[I++]=Pi,Pi=e;var r=Fi;e=Ii;var i=32-Ne(r)-1;r&=~(1<<i),n+=1;var a=32-Ne(t)+i;if(30<a){var o=i-i%5;a=(r&(1<<o)-1).toString(32),r>>=o,i-=o,Fi=1<<32-Ne(t)+i|n<<i|r,Ii=a+e}else Fi=1<<a|n<<i|r,Ii=e}function zi(e){e.return!==null&&(Li(e,1),Ri(e,1,0))}function Bi(e){for(;e===ji;)ji=ki[--Ai],ki[Ai]=null,Mi=ki[--Ai],ki[Ai]=null;for(;e===Pi;)Pi=Ni[--I],Ni[I]=null,Ii=Ni[--I],Ni[I]=null,Fi=Ni[--I],Ni[I]=null}var Vi=null,Hi=null,L=!1,Ui=null,Wi=!1,Gi=Error(i(519));function Ki(e){var t=Error(i(418,``));throw Qi(li(t,e)),Gi}function qi(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[tt]=e,t[nt]=r,n){case`dialog`:Q(`cancel`,t),Q(`close`,t);break;case`iframe`:case`object`:case`embed`:Q(`load`,t);break;case`video`:case`audio`:for(n=0;n<yd.length;n++)Q(yd[n],t);break;case`source`:Q(`error`,t);break;case`img`:case`image`:case`link`:Q(`error`,t),Q(`load`,t);break;case`details`:Q(`toggle`,t);break;case`input`:Q(`invalid`,t),Vt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),P(t);break;case`select`:Q(`invalid`,t);break;case`textarea`:Q(`invalid`,t),Gt(t,r.value,r.defaultValue,r.children),P(t)}n=r.children,typeof n!=`string`&&typeof n!=`number`&&typeof n!=`bigint`||t.textContent===``+n||!0===r.suppressHydrationWarning||Pd(t.textContent,n)?(r.popover!=null&&(Q(`beforetoggle`,t),Q(`toggle`,t)),r.onScroll!=null&&Q(`scroll`,t),r.onScrollEnd!=null&&Q(`scrollend`,t),r.onClick!=null&&(t.onclick=Fd),t=!0):t=!1,t||Ki(e)}function Ji(e){for(Vi=e.return;Vi;)switch(Vi.tag){case 5:case 13:Wi=!1;return;case 27:case 3:Wi=!0;return;default:Vi=Vi.return}}function Yi(e){if(e!==Vi)return!1;if(!L)return Ji(e),L=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!==`form`&&n!==`button`)||Wd(e.type,e.memoizedProps)),n=!n),n&&Hi&&Ki(e),Ji(e),t===13){if(e=e.memoizedState,e=e===null?null:e.dehydrated,!e)throw Error(i(317));a:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n===`/$`){if(t===0){Hi=sf(e.nextSibling);break a}t--}else n!==`$`&&n!==`$!`&&n!==`$?`||t++;e=e.nextSibling}Hi=null}}else t===27?(t=Hi,Qd(e.type)?(e=cf,cf=null,Hi=e):Hi=t):Hi=Vi?sf(e.stateNode.nextSibling):null;return!0}function Xi(){Hi=Vi=null,L=!1}function Zi(){var e=Ui;return e!==null&&(tu===null?tu=e:tu.push.apply(tu,e),Ui=null),e}function Qi(e){Ui===null?Ui=[e]:Ui.push(e)}var $i=se(null),ea=null,ta=null;function na(e,t,n){M($i,t._currentValue),t._currentValue=n}function ra(e){e._currentValue=$i.current,j($i)}function ia(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)===t?r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t):(e.childLanes|=t,r!==null&&(r.childLanes|=t)),e===n)break;e=e.return}}function aa(e,t,n,r){var a=e.child;for(a!==null&&(a.return=e);a!==null;){var o=a.dependencies;if(o!==null){var s=a.child;o=o.firstContext;a:for(;o!==null;){var c=o;o=a;for(var l=0;l<t.length;l++)if(c.context===t[l]){o.lanes|=n,c=o.alternate,c!==null&&(c.lanes|=n),ia(o.return,n,e),r||(s=null);break a}o=c.next}}else if(a.tag===18){if(s=a.return,s===null)throw Error(i(341));s.lanes|=n,o=s.alternate,o!==null&&(o.lanes|=n),ia(s,n,e),s=null}else s=a.child;if(s!==null)s.return=a;else for(s=a;s!==null;){if(s===e){s=null;break}if(a=s.sibling,a!==null){a.return=s.return,s=a;break}s=s.return}a=s}}function oa(e,t,n,r){e=null;for(var a=t,o=!1;a!==null;){if(!o){if(a.flags&524288)o=!0;else if(a.flags&262144)break}if(a.tag===10){var s=a.alternate;if(s===null)throw Error(i(387));if(s=s.memoizedProps,s!==null){var c=a.type;Pr(a.pendingProps.value,s.value)||(e===null?e=[c]:e.push(c))}}else if(a===de.current){if(s=a.alternate,s===null)throw Error(i(387));s.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(e===null?e=[Zf]:e.push(Zf))}a=a.return}e!==null&&aa(t,e,n,r),t.flags|=262144}function sa(e){for(e=e.firstContext;e!==null;){if(!Pr(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function ca(e){ea=e,ta=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function la(e){return da(ea,e)}function ua(e,t){return ea===null&&ca(e),da(e,t)}function da(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},ta===null){if(e===null)throw Error(i(308));ta=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else ta=ta.next=t;return n}var fa=typeof AbortController<`u`?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(e){return e()})}},pa=t.unstable_scheduleCallback,ma=t.unstable_NormalPriority,ha={$$typeof:b,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function ga(){return{controller:new fa,data:new Map,refCount:0}}function _a(e){e.refCount--,e.refCount===0&&pa(ma,function(){e.controller.abort()})}var va=null,ya=0,ba=0,xa=null;function Sa(e,t){if(va===null){var n=va=[];ya=0,ba=dd(),xa={status:`pending`,value:void 0,then:function(e){n.push(e)}}}return ya++,t.then(Ca,Ca),t}function Ca(){if(--ya===0&&va!==null){xa!==null&&(xa.status=`fulfilled`);var e=va;va=null,ba=0,xa=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function wa(e,t){var n=[],r={status:`pending`,value:null,reason:null,then:function(e){n.push(e)}};return e.then(function(){r.status=`fulfilled`,r.value=t;for(var e=0;e<n.length;e++)(0,n[e])(t)},function(e){for(r.status=`rejected`,r.reason=e,e=0;e<n.length;e++)(0,n[e])(void 0)}),r}var Ta=k.S;k.S=function(e,t){typeof t==`object`&&t&&typeof t.then==`function`&&Sa(e,t),Ta!==null&&Ta(e,t)};var Ea=se(null);function Da(){var e=Ea.current;return e===null?G.pooledCache:e}function Oa(e,t){t===null?M(Ea,Ea.current):M(Ea,t.pool)}function ka(){var e=Da();return e===null?null:{parent:ha._currentValue,pool:e}}var Aa=Error(i(460)),ja=Error(i(474)),Ma=Error(i(542)),Na={then:function(){}};function Pa(e){return e=e.status,e===`fulfilled`||e===`rejected`}function Fa(){}function Ia(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(Fa,Fa),t=n),t.status){case`fulfilled`:return t.value;case`rejected`:throw e=t.reason,za(e),e;default:if(typeof t.status==`string`)t.then(Fa,Fa);else{if(e=G,e!==null&&100<e.shellSuspendCounter)throw Error(i(482));e=t,e.status=`pending`,e.then(function(e){if(t.status===`pending`){var n=t;n.status=`fulfilled`,n.value=e}},function(e){if(t.status===`pending`){var n=t;n.status=`rejected`,n.reason=e}})}switch(t.status){case`fulfilled`:return t.value;case`rejected`:throw e=t.reason,za(e),e}throw La=t,Aa}}var La=null;function Ra(){if(La===null)throw Error(i(459));var e=La;return La=null,e}function za(e){if(e===Aa||e===Ma)throw Error(i(483))}var Ba=!1;function Va(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Ha(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Ua(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Wa(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,W&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,t=vi(e),_i(e,null,n),t}return mi(e,r,t,n),vi(e)}function Ga(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,n&4194048)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ye(e,n)}}function Ka(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,a=null;if(n=n.firstBaseUpdate,n!==null){do{var o={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};a===null?i=a=o:a=a.next=o,n=n.next}while(n!==null);a===null?i=a=t:a=a.next=t}else i=a=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:a,shared:r.shared,callbacks:r.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var qa=!1;function Ja(){if(qa){var e=xa;if(e!==null)throw e}}function Ya(e,t,n,r){qa=!1;var i=e.updateQueue;Ba=!1;var a=i.firstBaseUpdate,o=i.lastBaseUpdate,s=i.shared.pending;if(s!==null){i.shared.pending=null;var c=s,l=c.next;c.next=null,o===null?a=l:o.next=l,o=c;var u=e.alternate;u!==null&&(u=u.updateQueue,s=u.lastBaseUpdate,s!==o&&(s===null?u.firstBaseUpdate=l:s.next=l,u.lastBaseUpdate=c))}if(a!==null){var f=i.baseState;o=0,u=l=c=null,s=a;do{var p=s.lane&-536870913,m=p!==s.lane;if(m?(q&p)===p:(r&p)===p){p!==0&&p===ba&&(qa=!0),u!==null&&(u=u.next={lane:0,tag:s.tag,payload:s.payload,callback:null,next:null});a:{var h=e,g=s;p=t;var _=n;switch(g.tag){case 1:if(h=g.payload,typeof h==`function`){f=h.call(_,f,p);break a}f=h;break a;case 3:h.flags=h.flags&-65537|128;case 0:if(h=g.payload,p=typeof h==`function`?h.call(_,f,p):h,p==null)break a;f=d({},f,p);break a;case 2:Ba=!0}}p=s.callback,p!==null&&(e.flags|=64,m&&(e.flags|=8192),m=i.callbacks,m===null?i.callbacks=[p]:m.push(p))}else m={lane:p,tag:s.tag,payload:s.payload,callback:s.callback,next:null},u===null?(l=u=m,c=f):u=u.next=m,o|=p;if(s=s.next,s===null){if(s=i.shared.pending,s===null)break;m=s,s=m.next,m.next=null,i.lastBaseUpdate=m,i.shared.pending=null}}while(1);u===null&&(c=f),i.baseState=c,i.firstBaseUpdate=l,i.lastBaseUpdate=u,a===null&&(i.shared.lanes=0),Yl|=o,e.lanes=o,e.memoizedState=f}}function Xa(e,t){if(typeof e!=`function`)throw Error(i(191,e));e.call(t)}function Za(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)Xa(n[e],t)}var Qa=se(null),$a=se(0);function eo(e,t){e=ql,M($a,e),M(Qa,t),ql=e|t.baseLanes}function to(){M($a,ql),M(Qa,Qa.current)}function no(){ql=$a.current,j(Qa),j($a)}var ro=0,R=null,z=null,io=null,ao=!1,oo=!1,so=!1,co=0,lo=0,uo=null,fo=0;function po(){throw Error(i(321))}function mo(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Pr(e[n],t[n]))return!1;return!0}function ho(e,t,n,r,i,a){return ro=a,R=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,k.H=e===null||e.memoizedState===null?Ns:Ps,so=!1,a=n(r,i),so=!1,oo&&(a=_o(t,n,r,i)),go(e),a}function go(e){k.H=Ms;var t=z!==null&&z.next!==null;if(ro=0,io=z=R=null,ao=!1,lo=0,uo=null,t)throw Error(i(300));e===null||pc||(e=e.dependencies,e!==null&&sa(e)&&(pc=!0))}function _o(e,t,n,r){R=e;var a=0;do{if(oo&&(uo=null),lo=0,oo=!1,25<=a)throw Error(i(301));if(a+=1,io=z=null,e.updateQueue!=null){var o=e.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,o.memoCache!=null&&(o.memoCache.index=0)}k.H=Fs,o=t(n,r)}while(oo);return o}function vo(){var e=k.H,t=e.useState()[0];return t=typeof t.then==`function`?To(t):t,e=e.useState()[0],(z===null?null:z.memoizedState)!==e&&(R.flags|=1024),t}function yo(){var e=co!==0;return co=0,e}function bo(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function xo(e){if(ao){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}ao=!1}ro=0,io=z=R=null,oo=!1,lo=co=0,uo=null}function So(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return io===null?R.memoizedState=io=e:io=io.next=e,io}function Co(){if(z===null){var e=R.alternate;e=e===null?null:e.memoizedState}else e=z.next;var t=io===null?R.memoizedState:io.next;if(t!==null)io=t,z=e;else{if(e===null)throw R.alternate===null?Error(i(467)):Error(i(310));z=e,e={memoizedState:z.memoizedState,baseState:z.baseState,baseQueue:z.baseQueue,queue:z.queue,next:null},io===null?R.memoizedState=io=e:io=io.next=e}return io}function wo(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function To(e){var t=lo;return lo+=1,uo===null&&(uo=[]),e=Ia(uo,e,t),t=R,(io===null?t.memoizedState:io.next)===null&&(t=t.alternate,k.H=t===null||t.memoizedState===null?Ns:Ps),e}function Eo(e){if(typeof e==`object`&&e){if(typeof e.then==`function`)return To(e);if(e.$$typeof===b)return la(e)}throw Error(i(438,String(e)))}function Do(e){var t=null,n=R.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var r=R.alternate;r!==null&&(r=r.updateQueue,r!==null&&(r=r.memoCache,r!=null&&(t={data:r.data.map(function(e){return e.slice()}),index:0})))}if(t??={data:[],index:0},n===null&&(n=wo(),R.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=ee;return t.index++,n}function Oo(e,t){return typeof t==`function`?t(e):t}function ko(e){var t=Co();return Ao(t,z,e)}function Ao(e,t,n){var r=e.queue;if(r===null)throw Error(i(311));r.lastRenderedReducer=n;var a=e.baseQueue,o=r.pending;if(o!==null){if(a!==null){var s=a.next;a.next=o.next,o.next=s}t.baseQueue=a=o,r.pending=null}if(o=e.baseState,a===null)e.memoizedState=o;else{t=a.next;var c=s=null,l=null,u=t,d=!1;do{var f=u.lane&-536870913;if(f===u.lane?(ro&f)===f:(q&f)===f){var p=u.revertLane;if(p===0)l!==null&&(l=l.next={lane:0,revertLane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),f===ba&&(d=!0);else if((ro&p)===p){u=u.next,p===ba&&(d=!0);continue}else f={lane:0,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},l===null?(c=l=f,s=o):l=l.next=f,R.lanes|=p,Yl|=p;f=u.action,so&&n(o,f),o=u.hasEagerState?u.eagerState:n(o,f)}else p={lane:f,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},l===null?(c=l=p,s=o):l=l.next=p,R.lanes|=f,Yl|=f;u=u.next}while(u!==null&&u!==t);if(l===null?s=o:l.next=c,!Pr(o,e.memoizedState)&&(pc=!0,d&&(n=xa,n!==null)))throw n;e.memoizedState=o,e.baseState=s,e.baseQueue=l,r.lastRenderedState=o}return a===null&&(r.lanes=0),[e.memoizedState,r.dispatch]}function jo(e){var t=Co(),n=t.queue;if(n===null)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(a!==null){n.pending=null;var s=a=a.next;do o=e(o,s.action),s=s.next;while(s!==a);Pr(o,t.memoizedState)||(pc=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Mo(e,t,n){var r=R,a=Co(),o=L;if(o){if(n===void 0)throw Error(i(407));n=n()}else n=t();var s=!Pr((z||a).memoizedState,n);s&&(a.memoizedState=n,pc=!0),a=a.queue;var c=Fo.bind(null,r,a,e);if(ns(2048,8,c,[e]),a.getSnapshot!==t||s||io!==null&&io.memoizedState.tag&1){if(r.flags|=2048,Qo(9,$o(),Po.bind(null,r,a,n,t),null),G===null)throw Error(i(349));o||ro&124||No(r,t,n)}return n}function No(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=R.updateQueue,t===null?(t=wo(),R.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Po(e,t,n,r){t.value=n,t.getSnapshot=r,Io(t)&&Lo(e)}function Fo(e,t,n){return n(function(){Io(t)&&Lo(e)})}function Io(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Pr(e,n)}catch{return!0}}function Lo(e){var t=gi(e,2);t!==null&&vu(t,e,2)}function Ro(e){var t=So();if(typeof e==`function`){var n=e;if(e=n(),so){Me(!0);try{n()}finally{Me(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Oo,lastRenderedState:e},t}function zo(e,t,n,r){return e.baseState=n,Ao(e,z,typeof r==`function`?r:Oo)}function Bo(e,t,n,r,a){if(ks(e))throw Error(i(485));if(e=t.action,e!==null){var o={payload:a,action:e,next:null,isTransition:!0,status:`pending`,value:null,reason:null,listeners:[],then:function(e){o.listeners.push(e)}};k.T===null?o.isTransition=!1:n(!0),r(o),n=t.pending,n===null?(o.next=t.pending=o,Vo(t,o)):(o.next=n.next,t.pending=n.next=o)}}function Vo(e,t){var n=t.action,r=t.payload,i=e.state;if(t.isTransition){var a=k.T,o={};k.T=o;try{var s=n(i,r),c=k.S;c!==null&&c(o,s),Ho(e,t,s)}catch(n){Wo(e,t,n)}finally{k.T=a}}else try{a=n(i,r),Ho(e,t,a)}catch(n){Wo(e,t,n)}}function Ho(e,t,n){typeof n==`object`&&n&&typeof n.then==`function`?n.then(function(n){Uo(e,t,n)},function(n){return Wo(e,t,n)}):Uo(e,t,n)}function Uo(e,t,n){t.status=`fulfilled`,t.value=n,Go(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,Vo(e,n)))}function Wo(e,t,n){var r=e.pending;if(e.pending=null,r!==null){r=r.next;do t.status=`rejected`,t.reason=n,Go(t),t=t.next;while(t!==r)}e.action=null}function Go(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Ko(e,t){return t}function qo(e,t){if(L){var n=G.formState;if(n!==null){a:{var r=R;if(L){if(Hi){b:{for(var i=Hi,a=Wi;i.nodeType!==8;){if(!a){i=null;break b}if(i=sf(i.nextSibling),i===null){i=null;break b}}a=i.data,i=a===`F!`||a===`F`?i:null}if(i){Hi=sf(i.nextSibling),r=i.data===`F!`;break a}}Ki(r)}r=!1}r&&(t=n[0])}}return n=So(),n.memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ko,lastRenderedState:t},n.queue=r,n=Es.bind(null,R,r),r.dispatch=n,r=Ro(!1),a=Os.bind(null,R,!1,r.queue),r=So(),i={state:t,dispatch:null,action:e,pending:null},r.queue=i,n=Bo.bind(null,R,i,a,n),i.dispatch=n,r.memoizedState=e,[t,n,!1]}function Jo(e){var t=Co();return Yo(t,z,e)}function Yo(e,t,n){if(t=Ao(e,t,Ko)[0],e=ko(Oo)[0],typeof t==`object`&&t&&typeof t.then==`function`)try{var r=To(t)}catch(e){throw e===Aa?Ma:e}else r=t;t=Co();var i=t.queue,a=i.dispatch;return n!==t.memoizedState&&(R.flags|=2048,Qo(9,$o(),Xo.bind(null,i,n),null)),[r,a,e]}function Xo(e,t){e.action=t}function Zo(e){var t=Co(),n=z;if(n!==null)return Yo(t,n,e);Co(),t=t.memoizedState,n=Co();var r=n.queue.dispatch;return n.memoizedState=e,[t,r,!1]}function Qo(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},t=R.updateQueue,t===null&&(t=wo(),R.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function $o(){return{destroy:void 0,resource:void 0}}function es(){return Co().memoizedState}function ts(e,t,n,r){var i=So();r=r===void 0?null:r,R.flags|=e,i.memoizedState=Qo(1|t,$o(),n,r)}function ns(e,t,n,r){var i=Co();r=r===void 0?null:r;var a=i.memoizedState.inst;z!==null&&r!==null&&mo(r,z.memoizedState.deps)?i.memoizedState=Qo(t,a,n,r):(R.flags|=e,i.memoizedState=Qo(1|t,a,n,r))}function rs(e,t){ts(8390656,8,e,t)}function os(e,t){ns(2048,8,e,t)}function ss(e,t){return ns(4,2,e,t)}function cs(e,t){return ns(4,4,e,t)}function ls(e,t){if(typeof t==`function`){e=e();var n=t(e);return function(){typeof n==`function`?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function us(e,t,n){n=n==null?null:n.concat([e]),ns(4,4,ls.bind(null,t,e),n)}function ds(){}function fs(e,t){var n=Co();t=t===void 0?null:t;var r=n.memoizedState;return t!==null&&mo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function ps(e,t){var n=Co();t=t===void 0?null:t;var r=n.memoizedState;if(t!==null&&mo(t,r[1]))return r[0];if(r=e(),so){Me(!0);try{e()}finally{Me(!1)}}return n.memoizedState=[r,t],r}function ms(e,t,n){return n===void 0||ro&1073741824?e.memoizedState=t:(e.memoizedState=n,e=_u(),R.lanes|=e,Yl|=e,n)}function hs(e,t,n,r){return Pr(n,t)?n:Qa.current===null?ro&42?(e=_u(),R.lanes|=e,Yl|=e,t):(pc=!0,e.memoizedState=n):(e=ms(e,n,r),Pr(e,t)||(pc=!0),e)}function gs(e,t,n,r,i){var a=A.p;A.p=a!==0&&8>a?a:8;var o=k.T,s={};k.T=s,Os(e,!1,t,n);try{var c=i(),l=k.S;if(l!==null&&l(s,c),typeof c==`object`&&c&&typeof c.then==`function`){var u=wa(c,r);Ds(e,t,u,gu(e))}else Ds(e,t,r,gu(e))}catch(n){Ds(e,t,{then:function(){},status:`rejected`,reason:n},gu())}finally{A.p=a,k.T=o}}function _s(){}function vs(e,t,n,r){if(e.tag!==5)throw Error(i(476));var a=ys(e).queue;gs(e,a,t,ie,n===null?_s:function(){return bs(e),n(r)})}function ys(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:ie,baseState:ie,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Oo,lastRenderedState:ie},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Oo,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function bs(e){var t=ys(e).next.queue;Ds(e,t,{},gu())}function xs(){return la(Zf)}function Ss(){return Co().memoizedState}function Cs(){return Co().memoizedState}function ws(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=gu();e=Ua(n);var r=Wa(t,e,n);r!==null&&(vu(r,t,n),Ga(r,t,n)),t={cache:ga()},e.payload=t;return}t=t.return}}function Ts(e,t,n){var r=gu();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},ks(e)?As(t,n):(n=hi(e,t,n,r),n!==null&&(vu(n,e,r),js(n,t,r)))}function Es(e,t,n){var r=gu();Ds(e,t,n,r)}function Ds(e,t,n,r){var i={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(ks(e))As(t,i);else{var a=e.alternate;if(e.lanes===0&&(a===null||a.lanes===0)&&(a=t.lastRenderedReducer,a!==null))try{var o=t.lastRenderedState,s=a(o,n);if(i.hasEagerState=!0,i.eagerState=s,Pr(s,o))return mi(e,t,i,0),G===null&&pi(),!1}catch{}if(n=hi(e,t,i,r),n!==null)return vu(n,e,r),js(n,t,r),!0}return!1}function Os(e,t,n,r){if(r={lane:2,revertLane:dd(),action:r,hasEagerState:!1,eagerState:null,next:null},ks(e)){if(t)throw Error(i(479))}else t=hi(e,n,r,2),t!==null&&vu(t,e,2)}function ks(e){var t=e.alternate;return e===R||t!==null&&t===R}function As(e,t){oo=ao=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function js(e,t,n){if(n&4194048){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ye(e,n)}}var Ms={readContext:la,use:Eo,useCallback:po,useContext:po,useEffect:po,useImperativeHandle:po,useLayoutEffect:po,useInsertionEffect:po,useMemo:po,useReducer:po,useRef:po,useState:po,useDebugValue:po,useDeferredValue:po,useTransition:po,useSyncExternalStore:po,useId:po,useHostTransitionStatus:po,useFormState:po,useActionState:po,useOptimistic:po,useMemoCache:po,useCacheRefresh:po},Ns={readContext:la,use:Eo,useCallback:function(e,t){return So().memoizedState=[e,t===void 0?null:t],e},useContext:la,useEffect:rs,useImperativeHandle:function(e,t,n){n=n==null?null:n.concat([e]),ts(4194308,4,ls.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ts(4194308,4,e,t)},useInsertionEffect:function(e,t){ts(4,2,e,t)},useMemo:function(e,t){var n=So();t=t===void 0?null:t;var r=e();if(so){Me(!0);try{e()}finally{Me(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=So();if(n!==void 0){var i=n(t);if(so){Me(!0);try{n(t)}finally{Me(!1)}}}else i=t;return r.memoizedState=r.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},r.queue=e,e=e.dispatch=Ts.bind(null,R,e),[r.memoizedState,e]},useRef:function(e){var t=So();return e={current:e},t.memoizedState=e},useState:function(e){e=Ro(e);var t=e.queue,n=Es.bind(null,R,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:ds,useDeferredValue:function(e,t){var n=So();return ms(n,e,t)},useTransition:function(){var e=Ro(!1);return e=gs.bind(null,R,e.queue,!0,!1),So().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=R,a=So();if(L){if(n===void 0)throw Error(i(407));n=n()}else{if(n=t(),G===null)throw Error(i(349));q&124||No(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,rs(Fo.bind(null,r,o,e),[e]),r.flags|=2048,Qo(9,$o(),Po.bind(null,r,o,n,t),null),n},useId:function(){var e=So(),t=G.identifierPrefix;if(L){var n=Ii,r=Fi;n=(r&~(1<<32-Ne(r)-1)).toString(32)+n,t=`«`+t+`R`+n,n=co++,0<n&&(t+=`H`+n.toString(32)),t+=`»`}else n=fo++,t=`«`+t+`r`+n.toString(32)+`»`;return e.memoizedState=t},useHostTransitionStatus:xs,useFormState:qo,useActionState:qo,useOptimistic:function(e){var t=So();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Os.bind(null,R,!0,n),n.dispatch=t,[e,t]},useMemoCache:Do,useCacheRefresh:function(){return So().memoizedState=ws.bind(null,R)}},Ps={readContext:la,use:Eo,useCallback:fs,useContext:la,useEffect:os,useImperativeHandle:us,useInsertionEffect:ss,useLayoutEffect:cs,useMemo:ps,useReducer:ko,useRef:es,useState:function(){return ko(Oo)},useDebugValue:ds,useDeferredValue:function(e,t){var n=Co();return hs(n,z.memoizedState,e,t)},useTransition:function(){var e=ko(Oo)[0],t=Co().memoizedState;return[typeof e==`boolean`?e:To(e),t]},useSyncExternalStore:Mo,useId:Ss,useHostTransitionStatus:xs,useFormState:Jo,useActionState:Jo,useOptimistic:function(e,t){var n=Co();return zo(n,z,e,t)},useMemoCache:Do,useCacheRefresh:Cs},Fs={readContext:la,use:Eo,useCallback:fs,useContext:la,useEffect:os,useImperativeHandle:us,useInsertionEffect:ss,useLayoutEffect:cs,useMemo:ps,useReducer:jo,useRef:es,useState:function(){return jo(Oo)},useDebugValue:ds,useDeferredValue:function(e,t){var n=Co();return z===null?ms(n,e,t):hs(n,z.memoizedState,e,t)},useTransition:function(){var e=jo(Oo)[0],t=Co().memoizedState;return[typeof e==`boolean`?e:To(e),t]},useSyncExternalStore:Mo,useId:Ss,useHostTransitionStatus:xs,useFormState:Zo,useActionState:Zo,useOptimistic:function(e,t){var n=Co();return z===null?(n.baseState=e,[e,n.queue.dispatch]):zo(n,z,e,t)},useMemoCache:Do,useCacheRefresh:Cs},Is=null,Ls=0;function Rs(e){var t=Ls;return Ls+=1,Is===null&&(Is=[]),Ia(Is,e,t)}function zs(e,t){t=t.props.ref,e.ref=t===void 0?null:t}function Bs(e,t){throw t.$$typeof===f?Error(i(525)):(e=Object.prototype.toString.call(t),Error(i(31,e===`[object Object]`?`object with keys {`+Object.keys(t).join(`, `)+`}`:e)))}function Vs(e){var t=e._init;return t(e._payload)}function Hs(e){function t(t,n){if(e){var r=t.deletions;r===null?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;r!==null;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;e!==null;)e.key===null?t.set(e.index,e):t.set(e.key,e),e=e.sibling;return t}function a(e,t){return e=Ci(e,t),e.index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?(r=t.alternate,r===null?(t.flags|=67108866,n):(r=r.index,r<n?(t.flags|=67108866,n):r)):(t.flags|=1048576,n)}function s(t){return e&&t.alternate===null&&(t.flags|=67108866),t}function c(e,t,n,r){return t===null||t.tag!==6?(t=Di(n,e.mode,r),t.return=e,t):(t=a(t,n),t.return=e,t)}function l(e,t,n,r){var i=n.type;return i===h?d(e,t,n.props.children,r,n.key):t!==null&&(t.elementType===i||typeof i==`object`&&i&&i.$$typeof===T&&Vs(i)===t.type)?(t=a(t,n.props),zs(t,n),t.return=e,t):(t=Ti(n.type,n.key,n.props,null,e.mode,r),zs(t,n),t.return=e,t)}function u(e,t,n,r){return t===null||t.tag!==4||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?(t=Oi(n,e.mode,r),t.return=e,t):(t=a(t,n.children||[]),t.return=e,t)}function d(e,t,n,r,i){return t===null||t.tag!==7?(t=Ei(n,e.mode,r,i),t.return=e,t):(t=a(t,n),t.return=e,t)}function f(e,t,n){if(typeof t==`string`&&t!==``||typeof t==`number`||typeof t==`bigint`)return t=Di(``+t,e.mode,n),t.return=e,t;if(typeof t==`object`&&t){switch(t.$$typeof){case p:return n=Ti(t.type,t.key,t.props,null,e.mode,n),zs(n,t),n.return=e,n;case m:return t=Oi(t,e.mode,n),t.return=e,t;case T:var r=t._init;return t=r(t._payload),f(e,t,n)}if(O(t)||te(t))return t=Ei(t,e.mode,n,null),t.return=e,t;if(typeof t.then==`function`)return f(e,Rs(t),n);if(t.$$typeof===b)return f(e,ua(e,t),n);Bs(e,t)}return null}function g(e,t,n,r){var i=t===null?null:t.key;if(typeof n==`string`&&n!==``||typeof n==`number`||typeof n==`bigint`)return i===null?c(e,t,``+n,r):null;if(typeof n==`object`&&n){switch(n.$$typeof){case p:return n.key===i?l(e,t,n,r):null;case m:return n.key===i?u(e,t,n,r):null;case T:return i=n._init,n=i(n._payload),g(e,t,n,r)}if(O(n)||te(n))return i===null?d(e,t,n,r,null):null;if(typeof n.then==`function`)return g(e,t,Rs(n),r);if(n.$$typeof===b)return g(e,t,ua(e,n),r);Bs(e,n)}return null}function _(e,t,n,r,i){if(typeof r==`string`&&r!==``||typeof r==`number`||typeof r==`bigint`)return e=e.get(n)||null,c(t,e,``+r,i);if(typeof r==`object`&&r){switch(r.$$typeof){case p:return e=e.get(r.key===null?n:r.key)||null,l(t,e,r,i);case m:return e=e.get(r.key===null?n:r.key)||null,u(t,e,r,i);case T:var a=r._init;return r=a(r._payload),_(e,t,n,r,i)}if(O(r)||te(r))return e=e.get(n)||null,d(t,e,r,i,null);if(typeof r.then==`function`)return _(e,t,n,Rs(r),i);if(r.$$typeof===b)return _(e,t,n,ua(t,r),i);Bs(t,r)}return null}function v(i,a,s,c){for(var l=null,u=null,d=a,p=a=0,m=null;d!==null&&p<s.length;p++){d.index>p?(m=d,d=null):m=d.sibling;var h=g(i,d,s[p],c);if(h===null){d===null&&(d=m);break}e&&d&&h.alternate===null&&t(i,d),a=o(h,a,p),u===null?l=h:u.sibling=h,u=h,d=m}if(p===s.length)return n(i,d),L&&Li(i,p),l;if(d===null){for(;p<s.length;p++)d=f(i,s[p],c),d!==null&&(a=o(d,a,p),u===null?l=d:u.sibling=d,u=d);return L&&Li(i,p),l}for(d=r(d);p<s.length;p++)m=_(d,i,p,s[p],c),m!==null&&(e&&m.alternate!==null&&d.delete(m.key===null?p:m.key),a=o(m,a,p),u===null?l=m:u.sibling=m,u=m);return e&&d.forEach(function(e){return t(i,e)}),L&&Li(i,p),l}function y(a,s,c,l){if(c==null)throw Error(i(151));for(var u=null,d=null,p=s,m=s=0,h=null,v=c.next();p!==null&&!v.done;m++,v=c.next()){p.index>m?(h=p,p=null):h=p.sibling;var y=g(a,p,v.value,l);if(y===null){p===null&&(p=h);break}e&&p&&y.alternate===null&&t(a,p),s=o(y,s,m),d===null?u=y:d.sibling=y,d=y,p=h}if(v.done)return n(a,p),L&&Li(a,m),u;if(p===null){for(;!v.done;m++,v=c.next())v=f(a,v.value,l),v!==null&&(s=o(v,s,m),d===null?u=v:d.sibling=v,d=v);return L&&Li(a,m),u}for(p=r(p);!v.done;m++,v=c.next())v=_(p,a,m,v.value,l),v!==null&&(e&&v.alternate!==null&&p.delete(v.key===null?m:v.key),s=o(v,s,m),d===null?u=v:d.sibling=v,d=v);return e&&p.forEach(function(e){return t(a,e)}),L&&Li(a,m),u}function x(e,r,o,c){if(typeof o==`object`&&o&&o.type===h&&o.key===null&&(o=o.props.children),typeof o==`object`&&o){switch(o.$$typeof){case p:a:{for(var l=o.key;r!==null;){if(r.key===l){if(l=o.type,l===h){if(r.tag===7){n(e,r.sibling),c=a(r,o.props.children),c.return=e,e=c;break a}}else if(r.elementType===l||typeof l==`object`&&l&&l.$$typeof===T&&Vs(l)===r.type){n(e,r.sibling),c=a(r,o.props),zs(c,o),c.return=e,e=c;break a}n(e,r);break}else t(e,r);r=r.sibling}o.type===h?(c=Ei(o.props.children,e.mode,c,o.key),c.return=e,e=c):(c=Ti(o.type,o.key,o.props,null,e.mode,c),zs(c,o),c.return=e,e=c)}return s(e);case m:a:{for(l=o.key;r!==null;){if(r.key===l)if(r.tag===4&&r.stateNode.containerInfo===o.containerInfo&&r.stateNode.implementation===o.implementation){n(e,r.sibling),c=a(r,o.children||[]),c.return=e,e=c;break a}else{n(e,r);break}else t(e,r);r=r.sibling}c=Oi(o,e.mode,c),c.return=e,e=c}return s(e);case T:return l=o._init,o=l(o._payload),x(e,r,o,c)}if(O(o))return v(e,r,o,c);if(te(o)){if(l=te(o),typeof l!=`function`)throw Error(i(150));return o=l.call(o),y(e,r,o,c)}if(typeof o.then==`function`)return x(e,r,Rs(o),c);if(o.$$typeof===b)return x(e,r,ua(e,o),c);Bs(e,o)}return typeof o==`string`&&o!==``||typeof o==`number`||typeof o==`bigint`?(o=``+o,r!==null&&r.tag===6?(n(e,r.sibling),c=a(r,o),c.return=e,e=c):(n(e,r),c=Di(o,e.mode,c),c.return=e,e=c),s(e)):n(e,r)}return function(e,t,n,r){try{Ls=0;var i=x(e,t,n,r);return Is=null,i}catch(t){if(t===Aa||t===Ma)throw t;var a=xi(29,t,null,e.mode);return a.lanes=r,a.return=e,a}}}var Us=Hs(!0),Ws=Hs(!1),Gs=se(null),Ks=null;function qs(e){var t=e.alternate;M(Zs,Zs.current&1),M(Gs,e),Ks===null&&(t===null||Qa.current!==null||t.memoizedState!==null)&&(Ks=e)}function Js(e){if(e.tag===22){if(M(Zs,Zs.current),M(Gs,e),Ks===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Ks=e)}}else Ys(e)}function Ys(){M(Zs,Zs.current),M(Gs,Gs.current)}function Xs(e){j(Gs),Ks===e&&(Ks=null),j(Zs)}var Zs=se(0);function Qs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data===`$?`||rf(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function B(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:d({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var $s={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=gu(),i=Ua(r);i.payload=t,n!=null&&(i.callback=n),t=Wa(e,i,r),t!==null&&(vu(t,e,r),Ga(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=gu(),i=Ua(r);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Wa(e,i,r),t!==null&&(vu(t,e,r),Ga(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=gu(),r=Ua(n);r.tag=2,t!=null&&(r.callback=t),t=Wa(e,r,n),t!==null&&(vu(t,e,n),Ga(t,e,n))}};function ec(e,t,n,r,i,a,o){return e=e.stateNode,typeof e.shouldComponentUpdate==`function`?e.shouldComponentUpdate(r,a,o):t.prototype&&t.prototype.isPureReactComponent?!Fr(n,r)||!Fr(i,a):!0}function tc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps==`function`&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps==`function`&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&$s.enqueueReplaceState(t,t.state,null)}function nc(e,t){var n=t;if(`ref`in t)for(var r in n={},t)r!==`ref`&&(n[r]=t[r]);if(e=e.defaultProps)for(var i in n===t&&(n=d({},n)),e)n[i]===void 0&&(n[i]=e[i]);return n}var rc=typeof reportError==`function`?reportError:function(e){if(typeof window==`object`&&typeof window.ErrorEvent==`function`){var t=new window.ErrorEvent(`error`,{bubbles:!0,cancelable:!0,message:typeof e==`object`&&e&&typeof e.message==`string`?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process==`object`&&typeof process.emit==`function`){process.emit(`uncaughtException`,e);return}console.error(e)};function ic(e){rc(e)}function ac(e){console.error(e)}function oc(e){rc(e)}function sc(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(e){setTimeout(function(){throw e})}}function cc(e,t,n){try{var r=e.onCaughtError;r(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(e){setTimeout(function(){throw e})}}function V(e,t,n){return n=Ua(n),n.tag=3,n.payload={element:null},n.callback=function(){sc(e,t)},n}function lc(e){return e=Ua(e),e.tag=3,e}function uc(e,t,n,r){var i=n.type.getDerivedStateFromError;if(typeof i==`function`){var a=r.value;e.payload=function(){return i(a)},e.callback=function(){cc(t,n,r)}}var o=n.stateNode;o!==null&&typeof o.componentDidCatch==`function`&&(e.callback=function(){cc(t,n,r),typeof i!=`function`&&(ou===null?ou=new Set([this]):ou.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:e===null?``:e})})}function dc(e,t,n,r,a){if(n.flags|=32768,typeof r==`object`&&r&&typeof r.then==`function`){if(t=n.alternate,t!==null&&oa(t,n,a,!0),n=Gs.current,n!==null){switch(n.tag){case 13:return Ks===null?ku():n.alternate===null&&Jl===0&&(Jl=3),n.flags&=-257,n.flags|=65536,n.lanes=a,r===Na?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([r]):t.add(r),qu(e,r,a)),!1;case 22:return n.flags|=65536,r===Na?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([r]):n.add(r)),qu(e,r,a)),!1}throw Error(i(435,n.tag))}return qu(e,r,a),ku(),!1}if(L)return t=Gs.current,t===null?(r!==Gi&&(t=Error(i(423),{cause:r}),Qi(li(t,n))),e=e.current.alternate,e.flags|=65536,a&=-a,e.lanes|=a,r=li(r,n),a=V(e.stateNode,r,a),Ka(e,a),Jl!==4&&(Jl=2)):(!(t.flags&65536)&&(t.flags|=256),t.flags|=65536,t.lanes=a,r!==Gi&&(e=Error(i(422),{cause:r}),Qi(li(e,n)))),!1;var o=Error(i(520),{cause:r});if(o=li(o,n),eu===null?eu=[o]:eu.push(o),Jl!==4&&(Jl=2),t===null)return!0;r=li(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=a&-a,n.lanes|=e,e=V(n.stateNode,r,e),Ka(n,e),!1;case 1:if(t=n.type,o=n.stateNode,!(n.flags&128)&&(typeof t.getDerivedStateFromError==`function`||o!==null&&typeof o.componentDidCatch==`function`&&(ou===null||!ou.has(o))))return n.flags|=65536,a&=-a,n.lanes|=a,a=lc(a),uc(a,e,n,r),Ka(n,a),!1}n=n.return}while(n!==null);return!1}var fc=Error(i(461)),pc=!1;function mc(e,t,n,r){t.child=e===null?Ws(t,null,n,r):Us(t,e.child,n,r)}function hc(e,t,n,r,i){n=n.render;var a=t.ref;if(`ref`in r){var o={};for(var s in r)s!==`ref`&&(o[s]=r[s])}else o=r;return ca(t),r=ho(e,t,n,o,a,i),s=yo(),e!==null&&!pc?(bo(e,t,i),Fc(e,t,i)):(L&&s&&zi(t),t.flags|=1,mc(e,t,r,i),t.child)}function gc(e,t,n,r,i){if(e===null){var a=n.type;return typeof a==`function`&&!Si(a)&&a.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=a,_c(e,t,a,r,i)):(e=Ti(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(a=e.child,!Ic(e,i)){var o=a.memoizedProps;if(n=n.compare,n=n===null?Fr:n,n(o,r)&&e.ref===t.ref)return Fc(e,t,i)}return t.flags|=1,e=Ci(a,r),e.ref=t.ref,e.return=t,t.child=e}function _c(e,t,n,r,i){if(e!==null){var a=e.memoizedProps;if(Fr(a,r)&&e.ref===t.ref)if(pc=!1,t.pendingProps=r=a,Ic(e,i))e.flags&131072&&(pc=!0);else return t.lanes=e.lanes,Fc(e,t,i)}return xc(e,t,n,r,i)}function vc(e,t,n){var r=t.pendingProps,i=r.children,a=e===null?null:e.memoizedState;if(r.mode===`hidden`){if(t.flags&128){if(r=a===null?n:a.baseLanes|n,e!==null){for(i=t.child=e.child,a=0;i!==null;)a=a|i.lanes|i.childLanes,i=i.sibling;t.childLanes=a&~r}else t.childLanes=0,t.child=null;return yc(e,t,r,n)}if(n&536870912)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Oa(t,a===null?null:a.cachePool),a===null?to():eo(t,a),Js(t);else return t.lanes=t.childLanes=536870912,yc(e,t,a===null?n:a.baseLanes|n,n)}else a===null?(e!==null&&Oa(t,null),to(),Ys(t)):(Oa(t,a.cachePool),eo(t,a),Ys(t),t.memoizedState=null);return mc(e,t,i,n),t.child}function yc(e,t,n,r){var i=Da();return i=i===null?null:{parent:ha._currentValue,pool:i},t.memoizedState={baseLanes:n,cachePool:i},e!==null&&Oa(t,null),to(),Js(t),e!==null&&oa(e,t,r,!0),null}function bc(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!=`function`&&typeof n!=`object`)throw Error(i(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function xc(e,t,n,r,i){return ca(t),n=ho(e,t,n,r,void 0,i),r=yo(),e!==null&&!pc?(bo(e,t,i),Fc(e,t,i)):(L&&r&&zi(t),t.flags|=1,mc(e,t,n,i),t.child)}function Sc(e,t,n,r,i,a){return ca(t),t.updateQueue=null,n=_o(t,r,n,i),go(e),r=yo(),e!==null&&!pc?(bo(e,t,a),Fc(e,t,a)):(L&&r&&zi(t),t.flags|=1,mc(e,t,n,a),t.child)}function Cc(e,t,n,r,i){if(ca(t),t.stateNode===null){var a=yi,o=n.contextType;typeof o==`object`&&o&&(a=la(o)),a=new n(r,a),t.memoizedState=a.state!==null&&a.state!==void 0?a.state:null,a.updater=$s,t.stateNode=a,a._reactInternals=t,a=t.stateNode,a.props=r,a.state=t.memoizedState,a.refs={},Va(t),o=n.contextType,a.context=typeof o==`object`&&o?la(o):yi,a.state=t.memoizedState,o=n.getDerivedStateFromProps,typeof o==`function`&&(B(t,n,o,r),a.state=t.memoizedState),typeof n.getDerivedStateFromProps==`function`||typeof a.getSnapshotBeforeUpdate==`function`||typeof a.UNSAFE_componentWillMount!=`function`&&typeof a.componentWillMount!=`function`||(o=a.state,typeof a.componentWillMount==`function`&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount==`function`&&a.UNSAFE_componentWillMount(),o!==a.state&&$s.enqueueReplaceState(a,a.state,null),Ya(t,r,a,i),Ja(),a.state=t.memoizedState),typeof a.componentDidMount==`function`&&(t.flags|=4194308),r=!0}else if(e===null){a=t.stateNode;var s=t.memoizedProps,c=nc(n,s);a.props=c;var l=a.context,u=n.contextType;o=yi,typeof u==`object`&&u&&(o=la(u));var d=n.getDerivedStateFromProps;u=typeof d==`function`||typeof a.getSnapshotBeforeUpdate==`function`,s=t.pendingProps!==s,u||typeof a.UNSAFE_componentWillReceiveProps!=`function`&&typeof a.componentWillReceiveProps!=`function`||(s||l!==o)&&tc(t,a,r,o),Ba=!1;var f=t.memoizedState;a.state=f,Ya(t,r,a,i),Ja(),l=t.memoizedState,s||f!==l||Ba?(typeof d==`function`&&(B(t,n,d,r),l=t.memoizedState),(c=Ba||ec(t,n,c,r,f,l,o))?(u||typeof a.UNSAFE_componentWillMount!=`function`&&typeof a.componentWillMount!=`function`||(typeof a.componentWillMount==`function`&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount==`function`&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount==`function`&&(t.flags|=4194308)):(typeof a.componentDidMount==`function`&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),a.props=r,a.state=l,a.context=o,r=c):(typeof a.componentDidMount==`function`&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Ha(e,t),o=t.memoizedProps,u=nc(n,o),a.props=u,d=t.pendingProps,f=a.context,l=n.contextType,c=yi,typeof l==`object`&&l&&(c=la(l)),s=n.getDerivedStateFromProps,(l=typeof s==`function`||typeof a.getSnapshotBeforeUpdate==`function`)||typeof a.UNSAFE_componentWillReceiveProps!=`function`&&typeof a.componentWillReceiveProps!=`function`||(o!==d||f!==c)&&tc(t,a,r,c),Ba=!1,f=t.memoizedState,a.state=f,Ya(t,r,a,i),Ja();var p=t.memoizedState;o!==d||f!==p||Ba||e!==null&&e.dependencies!==null&&sa(e.dependencies)?(typeof s==`function`&&(B(t,n,s,r),p=t.memoizedState),(u=Ba||ec(t,n,u,r,f,p,c)||e!==null&&e.dependencies!==null&&sa(e.dependencies))?(l||typeof a.UNSAFE_componentWillUpdate!=`function`&&typeof a.componentWillUpdate!=`function`||(typeof a.componentWillUpdate==`function`&&a.componentWillUpdate(r,p,c),typeof a.UNSAFE_componentWillUpdate==`function`&&a.UNSAFE_componentWillUpdate(r,p,c)),typeof a.componentDidUpdate==`function`&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate==`function`&&(t.flags|=1024)):(typeof a.componentDidUpdate!=`function`||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!=`function`||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),a.props=r,a.state=p,a.context=c,r=u):(typeof a.componentDidUpdate!=`function`||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!=`function`||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return a=r,bc(e,t),r=(t.flags&128)!=0,a||r?(a=t.stateNode,n=r&&typeof n.getDerivedStateFromError!=`function`?null:a.render(),t.flags|=1,e!==null&&r?(t.child=Us(t,e.child,null,i),t.child=Us(t,null,n,i)):mc(e,t,n,i),t.memoizedState=a.state,e=t.child):e=Fc(e,t,i),e}function wc(e,t,n,r){return Xi(),t.flags|=256,mc(e,t,n,r),t.child}var Tc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ec(e){return{baseLanes:e,cachePool:ka()}}function Dc(e,t,n){return e=e===null?0:e.childLanes&~n,t&&(e|=Ql),e}function Oc(e,t,n){var r=t.pendingProps,a=!1,o=(t.flags&128)!=0,s;if((s=o)||(s=e!==null&&e.memoizedState===null?!1:(Zs.current&2)!=0),s&&(a=!0,t.flags&=-129),s=(t.flags&32)!=0,t.flags&=-33,e===null){if(L){if(a?qs(t):Ys(t),L){var c=Hi,l;if(l=c){c:{for(l=c,c=Wi;l.nodeType!==8;){if(!c){c=null;break c}if(l=sf(l.nextSibling),l===null){c=null;break c}}c=l}c===null?l=!1:(t.memoizedState={dehydrated:c,treeContext:Pi===null?null:{id:Fi,overflow:Ii},retryLane:536870912,hydrationErrors:null},l=xi(18,null,null,0),l.stateNode=c,l.return=t,t.child=l,Vi=t,Hi=null,l=!0)}l||Ki(t)}if(c=t.memoizedState,c!==null&&(c=c.dehydrated,c!==null))return rf(c)?t.lanes=32:t.lanes=536870912,null;Xs(t)}return c=r.children,r=r.fallback,a?(Ys(t),a=t.mode,c=Ac({mode:`hidden`,children:c},a),r=Ei(r,a,n,null),c.return=t,r.return=t,c.sibling=r,t.child=c,a=t.child,a.memoizedState=Ec(n),a.childLanes=Dc(e,s,n),t.memoizedState=Tc,r):(qs(t),kc(t,c))}if(l=e.memoizedState,l!==null&&(c=l.dehydrated,c!==null)){if(o)t.flags&256?(qs(t),t.flags&=-257,t=jc(e,t,n)):t.memoizedState===null?(Ys(t),a=r.fallback,c=t.mode,r=Ac({mode:`visible`,children:r.children},c),a=Ei(a,c,n,null),a.flags|=2,r.return=t,a.return=t,r.sibling=a,t.child=r,Us(t,e.child,null,n),r=t.child,r.memoizedState=Ec(n),r.childLanes=Dc(e,s,n),t.memoizedState=Tc,t=a):(Ys(t),t.child=e.child,t.flags|=128,t=null);else if(qs(t),rf(c)){if(s=c.nextSibling&&c.nextSibling.dataset,s)var u=s.dgst;s=u,r=Error(i(419)),r.stack=``,r.digest=s,Qi({value:r,source:null,stack:null}),t=jc(e,t,n)}else if(pc||oa(e,t,n,!1),s=(n&e.childLanes)!==0,pc||s){if(s=G,s!==null&&(r=n&-n,r=r&42?1:Xe(r),r=(r&(s.suspendedLanes|n))===0?r:0,r!==0&&r!==l.retryLane))throw l.retryLane=r,gi(e,r),vu(s,e,r),fc;c.data===`$?`||ku(),t=jc(e,t,n)}else c.data===`$?`?(t.flags|=192,t.child=e.child,t=null):(e=l.treeContext,Hi=sf(c.nextSibling),Vi=t,L=!0,Ui=null,Wi=!1,e!==null&&(Ni[I++]=Fi,Ni[I++]=Ii,Ni[I++]=Pi,Fi=e.id,Ii=e.overflow,Pi=t),t=kc(t,r.children),t.flags|=4096);return t}return a?(Ys(t),a=r.fallback,c=t.mode,l=e.child,u=l.sibling,r=Ci(l,{mode:`hidden`,children:r.children}),r.subtreeFlags=l.subtreeFlags&65011712,u===null?(a=Ei(a,c,n,null),a.flags|=2):a=Ci(u,a),a.return=t,r.return=t,r.sibling=a,t.child=r,r=a,a=t.child,c=e.child.memoizedState,c===null?c=Ec(n):(l=c.cachePool,l===null?l=ka():(u=ha._currentValue,l=l.parent===u?l:{parent:u,pool:u}),c={baseLanes:c.baseLanes|n,cachePool:l}),a.memoizedState=c,a.childLanes=Dc(e,s,n),t.memoizedState=Tc,r):(qs(t),n=e.child,e=n.sibling,n=Ci(n,{mode:`visible`,children:r.children}),n.return=t,n.sibling=null,e!==null&&(s=t.deletions,s===null?(t.deletions=[e],t.flags|=16):s.push(e)),t.child=n,t.memoizedState=null,n)}function kc(e,t){return t=Ac({mode:`visible`,children:t},e.mode),t.return=e,e.child=t}function Ac(e,t){return e=xi(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function jc(e,t,n){return Us(t,e.child,null,n),e=kc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Mc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ia(e.return,t,n)}function Nc(e,t,n,r,i){var a=e.memoizedState;a===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=i)}function Pc(e,t,n){var r=t.pendingProps,i=r.revealOrder,a=r.tail;if(mc(e,t,r.children,n),r=Zs.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)a:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Mc(e,n,t);else if(e.tag===19)Mc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break a;for(;e.sibling===null;){if(e.return===null||e.return===t)break a;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(M(Zs,r),i){case`forwards`:for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Qs(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Nc(t,!1,i,n,a);break;case`backwards`:for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Qs(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Nc(t,!0,n,null,a);break;case`together`:Nc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Fc(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Yl|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(oa(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(i(153));if(t.child!==null){for(e=t.child,n=Ci(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ci(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Ic(e,t){return(e.lanes&t)===0?(e=e.dependencies,e!==null&&sa(e)?!0:!1):!0}function Lc(e,t,n){switch(t.tag){case 3:fe(t,t.stateNode.containerInfo),na(t,ha,e.memoizedState.cache),Xi();break;case 27:case 5:me(t);break;case 4:fe(t,t.stateNode.containerInfo);break;case 10:na(t,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(r!==null)return r.dehydrated===null?(n&t.child.childLanes)===0?(qs(t),e=Fc(e,t,n),e===null?null:e.sibling):Oc(e,t,n):(qs(t),t.flags|=128,null);qs(t);break;case 19:var i=(e.flags&128)!=0;if(r=(n&t.childLanes)!==0,r||(oa(e,t,n,!1),r=(n&t.childLanes)!==0),i){if(r)return Pc(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),M(Zs,Zs.current),r)break;return null;case 22:case 23:return t.lanes=0,vc(e,t,n);case 24:na(t,ha,e.memoizedState.cache)}return Fc(e,t,n)}function Rc(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)pc=!0;else{if(!Ic(e,n)&&!(t.flags&128))return pc=!1,Lc(e,t,n);pc=e.flags&131072?!0:!1}else pc=!1,L&&t.flags&1048576&&Ri(t,Mi,t.index);switch(t.lanes=0,t.tag){case 16:a:{e=t.pendingProps;var r=t.elementType,a=r._init;if(r=a(r._payload),t.type=r,typeof r==`function`)Si(r)?(e=nc(r,e),t.tag=1,t=Cc(null,t,r,e,n)):(t.tag=0,t=xc(null,t,r,e,n));else{if(r!=null){if(a=r.$$typeof,a===x){t.tag=11,t=hc(null,t,r,e,n);break a}else if(a===w){t.tag=14,t=gc(null,t,r,e,n);break a}}throw t=re(r)||r,Error(i(306,t,``))}}return t;case 0:return xc(e,t,t.type,t.pendingProps,n);case 1:return r=t.type,a=nc(r,t.pendingProps),Cc(e,t,r,a,n);case 3:a:{if(fe(t,t.stateNode.containerInfo),e===null)throw Error(i(387));r=t.pendingProps;var o=t.memoizedState;a=o.element,Ha(e,t),Ya(t,r,null,n);var s=t.memoizedState;if(r=s.cache,na(t,ha,r),r!==o.cache&&aa(t,[ha],n,!0),Ja(),r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){t=wc(e,t,r,n);break a}else if(r!==a){a=li(Error(i(424)),t),Qi(a),t=wc(e,t,r,n);break a}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName===`HTML`?e.ownerDocument.body:e}for(Hi=sf(e.firstChild),Vi=t,L=!0,Ui=null,Wi=!0,n=Ws(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Xi(),r===a){t=Fc(e,t,n);break a}mc(e,t,r,n)}t=t.child}return t;case 26:return bc(e,t),e===null?(n=Df(t.type,null,t.pendingProps,null))?t.memoizedState=n:L||(n=t.type,e=t.pendingProps,r=Vd(ue.current).createElement(n),r[tt]=t,r[nt]=e,Ld(r,n,e),mt(r),t.stateNode=r):t.memoizedState=Df(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return me(t),e===null&&L&&(r=t.stateNode=uf(t.type,t.pendingProps,ue.current),Vi=t,Wi=!0,a=Hi,Qd(t.type)?(cf=a,Hi=sf(r.firstChild)):Hi=a),mc(e,t,t.pendingProps.children,n),bc(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&L&&((a=r=Hi)&&(r=tf(r,t.type,t.pendingProps,Wi),r===null?a=!1:(t.stateNode=r,Vi=t,Hi=sf(r.firstChild),Wi=!1,a=!0)),a||Ki(t)),me(t),a=t.type,o=t.pendingProps,s=e===null?null:e.memoizedProps,r=o.children,Wd(a,o)?r=null:s!==null&&Wd(a,s)&&(t.flags|=32),t.memoizedState!==null&&(a=ho(e,t,vo,null,null,n),Zf._currentValue=a),bc(e,t),mc(e,t,r,n),t.child;case 6:return e===null&&L&&((e=n=Hi)&&(n=nf(n,t.pendingProps,Wi),n===null?e=!1:(t.stateNode=n,Vi=t,Hi=null,e=!0)),e||Ki(t)),null;case 13:return Oc(e,t,n);case 4:return fe(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Us(t,null,r,n):mc(e,t,r,n),t.child;case 11:return hc(e,t,t.type,t.pendingProps,n);case 7:return mc(e,t,t.pendingProps,n),t.child;case 8:return mc(e,t,t.pendingProps.children,n),t.child;case 12:return mc(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,na(t,t.type,r.value),mc(e,t,r.children,n),t.child;case 9:return a=t.type._context,r=t.pendingProps.children,ca(t),a=la(a),r=r(a),t.flags|=1,mc(e,t,r,n),t.child;case 14:return gc(e,t,t.type,t.pendingProps,n);case 15:return _c(e,t,t.type,t.pendingProps,n);case 19:return Pc(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},e===null?(n=Ac(r,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=Ci(e.child,r),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return vc(e,t,n);case 24:return ca(t),r=la(ha),e===null?(a=Da(),a===null&&(a=G,o=ga(),a.pooledCache=o,o.refCount++,o!==null&&(a.pooledCacheLanes|=n),a=o),t.memoizedState={parent:r,cache:a},Va(t),na(t,ha,a)):((e.lanes&n)!==0&&(Ha(e,t),Ya(t,null,null,n),Ja()),a=e.memoizedState,o=t.memoizedState,a.parent===r?(r=o.cache,na(t,ha,r),r!==a.cache&&aa(t,[ha],n,!0)):(a={parent:r,cache:r},t.memoizedState=a,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=a),na(t,ha,r))),mc(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(i(156,t.tag))}function zc(e){e.flags|=4}function Bc(e,t){if(t.type!==`stylesheet`||t.state.loading&4)e.flags&=-16777217;else if(e.flags|=16777216,!Hf(t)){if(t=Gs.current,t!==null&&((q&4194048)===q?Ks!==null:(q&62914560)!==q&&!(q&536870912)||t!==Ks))throw La=Na,ja;e.flags|=8192}}function Vc(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag===22?536870912:We(),e.lanes|=t,$l|=t)}function Hc(e,t){if(!L)switch(e.tailMode){case`hidden`:t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case`collapsed`:n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Uc(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&65011712,r|=i.flags&65011712,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Wc(e,t,n){var r=t.pendingProps;switch(Bi(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Uc(t),null;case 1:return Uc(t),null;case 3:return n=t.stateNode,r=null,e!==null&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),ra(ha),pe(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Yi(t)?zc(t):e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Zi())),Uc(t),null;case 26:return n=t.memoizedState,e===null?(zc(t),n===null?(Uc(t),t.flags&=-16777217):(Uc(t),Bc(t,n))):n?n===e.memoizedState?(Uc(t),t.flags&=-16777217):(zc(t),Uc(t),Bc(t,n)):(e.memoizedProps!==r&&zc(t),Uc(t),t.flags&=-16777217),null;case 27:he(t),n=ue.current;var a=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==r&&zc(t);else{if(!r){if(t.stateNode===null)throw Error(i(166));return Uc(t),null}e=ce.current,Yi(t)?qi(t,e):(e=uf(a,r,n),t.stateNode=e,zc(t))}return Uc(t),null;case 5:if(he(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==r&&zc(t);else{if(!r){if(t.stateNode===null)throw Error(i(166));return Uc(t),null}if(e=ce.current,Yi(t))qi(t,e);else{switch(a=Vd(ue.current),e){case 1:e=a.createElementNS(`http://www.w3.org/2000/svg`,n);break;case 2:e=a.createElementNS(`http://www.w3.org/1998/Math/MathML`,n);break;default:switch(n){case`svg`:e=a.createElementNS(`http://www.w3.org/2000/svg`,n);break;case`math`:e=a.createElementNS(`http://www.w3.org/1998/Math/MathML`,n);break;case`script`:e=a.createElement(`div`),e.innerHTML=`<script></script>`,e=e.removeChild(e.firstChild);break;case`select`:e=typeof r.is==`string`?a.createElement(`select`,{is:r.is}):a.createElement(`select`),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e=typeof r.is==`string`?a.createElement(n,{is:r.is}):a.createElement(n)}}e[tt]=t,e[nt]=r;a:for(a=t.child;a!==null;){if(a.tag===5||a.tag===6)e.appendChild(a.stateNode);else if(a.tag!==4&&a.tag!==27&&a.child!==null){a.child.return=a,a=a.child;continue}if(a===t)break a;for(;a.sibling===null;){if(a.return===null||a.return===t)break a;a=a.return}a.sibling.return=a.return,a=a.sibling}t.stateNode=e;a:switch(Ld(e,n,r),n){case`button`:case`input`:case`select`:case`textarea`:e=!!r.autoFocus;break a;case`img`:e=!0;break a;default:e=!1}e&&zc(t)}}return Uc(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==r&&zc(t);else{if(typeof r!=`string`&&t.stateNode===null)throw Error(i(166));if(e=ue.current,Yi(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,a=Vi,a!==null)switch(a.tag){case 27:case 5:r=a.memoizedProps}e[tt]=t,e=e.nodeValue===n||r!==null&&!0===r.suppressHydrationWarning||Pd(e.nodeValue,n)?!0:!1,e||Ki(t)}else e=Vd(e).createTextNode(r),e[tt]=t,t.stateNode=e}return Uc(t),null;case 13:if(r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(a=Yi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!a)throw Error(i(318));if(a=t.memoizedState,a=a===null?null:a.dehydrated,!a)throw Error(i(317));a[tt]=t}else Xi(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Uc(t),a=!1}else a=Zi(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=a),a=!0;if(!a)return t.flags&256?(Xs(t),t):(Xs(t),null)}if(Xs(t),t.flags&128)return t.lanes=n,t;if(n=r!==null,e=e!==null&&e.memoizedState!==null,n){r=t.child,a=null,r.alternate!==null&&r.alternate.memoizedState!==null&&r.alternate.memoizedState.cachePool!==null&&(a=r.alternate.memoizedState.cachePool.pool);var o=null;r.memoizedState!==null&&r.memoizedState.cachePool!==null&&(o=r.memoizedState.cachePool.pool),o!==a&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),Vc(t,t.updateQueue),Uc(t),null;case 4:return pe(),e===null&&wd(t.stateNode.containerInfo),Uc(t),null;case 10:return ra(t.type),Uc(t),null;case 19:if(j(Zs),a=t.memoizedState,a===null)return Uc(t),null;if(r=(t.flags&128)!=0,o=a.rendering,o===null)if(r)Hc(a,!1);else{if(Jl!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Qs(e),o!==null){for(t.flags|=128,Hc(a,!1),e=o.updateQueue,t.updateQueue=e,Vc(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)wi(n,e),n=n.sibling;return M(Zs,Zs.current&1|2),t.child}e=e.sibling}a.tail!==null&&xe()>iu&&(t.flags|=128,r=!0,Hc(a,!1),t.lanes=4194304)}else{if(!r)if(e=Qs(o),e!==null){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,Vc(t,e),Hc(a,!0),a.tail===null&&a.tailMode===`hidden`&&!o.alternate&&!L)return Uc(t),null}else 2*xe()-a.renderingStartTime>iu&&n!==536870912&&(t.flags|=128,r=!0,Hc(a,!1),t.lanes=4194304);a.isBackwards?(o.sibling=t.child,t.child=o):(e=a.last,e===null?t.child=o:e.sibling=o,a.last=o)}return a.tail===null?(Uc(t),null):(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=xe(),t.sibling=null,e=Zs.current,M(Zs,r?e&1|2:e&1),t);case 22:case 23:return Xs(t),no(),r=t.memoizedState!==null,e===null?r&&(t.flags|=8192):e.memoizedState!==null!==r&&(t.flags|=8192),r?n&536870912&&!(t.flags&128)&&(Uc(t),t.subtreeFlags&6&&(t.flags|=8192)):Uc(t),n=t.updateQueue,n!==null&&Vc(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),r=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),e!==null&&j(Ea),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),ra(ha),Uc(t),null;case 25:return null;case 30:return null}throw Error(i(156,t.tag))}function Gc(e,t){switch(Bi(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ra(ha),pe(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return he(t),null;case 13:if(Xs(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(i(340));Xi()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return j(Zs),null;case 4:return pe(),null;case 10:return ra(t.type),null;case 22:case 23:return Xs(t),no(),e!==null&&j(Ea),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return ra(ha),null;case 25:return null;default:return null}}function Kc(e,t){switch(Bi(t),t.tag){case 3:ra(ha),pe();break;case 26:case 27:case 5:he(t);break;case 4:pe();break;case 13:Xs(t);break;case 19:j(Zs);break;case 10:ra(t.type);break;case 22:case 23:Xs(t),no(),e!==null&&j(Ea);break;case 24:ra(ha)}}function qc(e,t){try{var n=t.updateQueue,r=n===null?null:n.lastEffect;if(r!==null){var i=r.next;n=i;do{if((n.tag&e)===e){r=void 0;var a=n.create,o=n.inst;r=a(),o.destroy=r}n=n.next}while(n!==i)}}catch(e){Y(t,t.return,e)}}function Jc(e,t,n){try{var r=t.updateQueue,i=r===null?null:r.lastEffect;if(i!==null){var a=i.next;r=a;do{if((r.tag&e)===e){var o=r.inst,s=o.destroy;if(s!==void 0){o.destroy=void 0,i=t;var c=n,l=s;try{l()}catch(e){Y(i,c,e)}}}r=r.next}while(r!==a)}}catch(e){Y(t,t.return,e)}}function Yc(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{Za(t,n)}catch(t){Y(e,e.return,t)}}}function Xc(e,t,n){n.props=nc(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(n){Y(e,t,n)}}function Zc(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;case 30:r=e.stateNode;break;default:r=e.stateNode}typeof n==`function`?e.refCleanup=n(r):n.current=r}}catch(n){Y(e,t,n)}}function Qc(e,t){var n=e.ref,r=e.refCleanup;if(n!==null)if(typeof r==`function`)try{r()}catch(n){Y(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n==`function`)try{n(null)}catch(n){Y(e,t,n)}else n.current=null}function $c(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{a:switch(t){case`button`:case`input`:case`select`:case`textarea`:n.autoFocus&&r.focus();break a;case`img`:n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(t){Y(e,e.return,t)}}function el(e,t,n){try{var r=e.stateNode;Rd(r,e.type,n,t),r[nt]=t}catch(t){Y(e,e.return,t)}}function tl(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Qd(e.type)||e.tag===4}function nl(e){a:for(;;){for(;e.sibling===null;){if(e.return===null||tl(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Qd(e.type)||e.flags&2||e.child===null||e.tag===4)continue a;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function rl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName===`HTML`?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName===`HTML`?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Fd));else if(r!==4&&(r===27&&Qd(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(rl(e,t,n),e=e.sibling;e!==null;)rl(e,t,n),e=e.sibling}function il(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(r===27&&Qd(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(il(e,t,n),e=e.sibling;e!==null;)il(e,t,n),e=e.sibling}function al(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);Ld(t,r,n),t[tt]=e,t[nt]=n}catch(t){Y(e,e.return,t)}}var ol=!1,H=!1,sl=!1,cl=typeof WeakSet==`function`?WeakSet:Set,ll=null;function ul(e,t){if(e=e.containerInfo,zd=ap,e=zr(e),Br(e)){if(`selectionStart`in e)var n={start:e.selectionStart,end:e.selectionEnd};else a:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break a}var s=0,c=-1,l=-1,u=0,d=0,f=e,p=null;b:for(;;){for(var m;f!==n||a!==0&&f.nodeType!==3||(c=s+a),f!==o||r!==0&&f.nodeType!==3||(l=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(m=f.firstChild)!==null;)p=f,f=m;for(;;){if(f===e)break b;if(p===n&&++u===a&&(c=s),p===o&&++d===r&&(l=s),(m=f.nextSibling)!==null)break;f=p,p=f.parentNode}f=m}n=c===-1||l===-1?null:{start:c,end:l}}else n=null}n||={start:0,end:0}}else n=null;for(Bd={focusedElem:e,selectionRange:n},ap=!1,ll=t;ll!==null;)if(t=ll,e=t.child,t.subtreeFlags&1024&&e!==null)e.return=t,ll=e;else for(;ll!==null;){switch(t=ll,o=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if(e&1024&&o!==null){e=void 0,n=t,a=o.memoizedProps,o=o.memoizedState,r=n.stateNode;try{var h=nc(n.type,a,n.elementType===n.type);e=r.getSnapshotBeforeUpdate(h,o),r.__reactInternalSnapshotBeforeUpdate=e}catch(e){Y(n,n.return,e)}}break;case 3:if(e&1024){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)ef(e);else if(n===1)switch(e.nodeName){case`HEAD`:case`HTML`:case`BODY`:ef(e);break;default:e.textContent=``}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if(e&1024)throw Error(i(163))}if(e=t.sibling,e!==null){e.return=t.return,ll=e;break}ll=t.return}}function dl(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:wl(e,n),r&4&&qc(5,n);break;case 1:if(wl(e,n),r&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(e){Y(n,n.return,e)}else{var i=nc(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(e){Y(n,n.return,e)}}r&64&&Yc(n),r&512&&Zc(n,n.return);break;case 3:if(wl(e,n),r&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{Za(e,t)}catch(e){Y(n,n.return,e)}}break;case 27:t===null&&r&4&&al(n);case 26:case 5:wl(e,n),t===null&&r&4&&$c(n),r&512&&Zc(n,n.return);break;case 12:wl(e,n);break;case 13:wl(e,n),r&4&&gl(e,n),r&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=Yu.bind(null,n),af(e,n))));break;case 22:if(r=n.memoizedState!==null||ol,!r){t=t!==null&&t.memoizedState!==null||H,i=ol;var a=H;ol=r,(H=t)&&!a?El(e,n,(n.subtreeFlags&8772)!=0):wl(e,n),ol=i,H=a}break;case 30:break;default:wl(e,n)}}function fl(e){var t=e.alternate;t!==null&&(e.alternate=null,fl(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&lt(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var U=null,pl=!1;function ml(e,t,n){for(n=n.child;n!==null;)hl(e,t,n),n=n.sibling}function hl(e,t,n){if(je&&typeof je.onCommitFiberUnmount==`function`)try{je.onCommitFiberUnmount(Ae,n)}catch{}switch(n.tag){case 26:H||Qc(n,t),ml(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:H||Qc(n,t);var r=U,i=pl;Qd(n.type)&&(U=n.stateNode,pl=!1),ml(e,t,n),df(n.stateNode),U=r,pl=i;break;case 5:H||Qc(n,t);case 6:if(r=U,i=pl,U=null,ml(e,t,n),U=r,pl=i,U!==null)if(pl)try{(U.nodeType===9?U.body:U.nodeName===`HTML`?U.ownerDocument.body:U).removeChild(n.stateNode)}catch(e){Y(n,t,e)}else try{U.removeChild(n.stateNode)}catch(e){Y(n,t,e)}break;case 18:U!==null&&(pl?(e=U,$d(e.nodeType===9?e.body:e.nodeName===`HTML`?e.ownerDocument.body:e,n.stateNode),jp(e)):$d(U,n.stateNode));break;case 4:r=U,i=pl,U=n.stateNode.containerInfo,pl=!0,ml(e,t,n),U=r,pl=i;break;case 0:case 11:case 14:case 15:H||Jc(2,n,t),H||Jc(4,n,t),ml(e,t,n);break;case 1:H||(Qc(n,t),r=n.stateNode,typeof r.componentWillUnmount==`function`&&Xc(n,t,r)),ml(e,t,n);break;case 21:ml(e,t,n);break;case 22:H=(r=H)||n.memoizedState!==null,ml(e,t,n),H=r;break;default:ml(e,t,n)}}function gl(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{jp(e)}catch(e){Y(t,t.return,e)}}function _l(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new cl),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new cl),t;default:throw Error(i(435,e.tag))}}function vl(e,t){var n=_l(e);t.forEach(function(t){var r=Xu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}function yl(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var a=n[r],o=e,s=t,c=s;a:for(;c!==null;){switch(c.tag){case 27:if(Qd(c.type)){U=c.stateNode,pl=!1;break a}break;case 5:U=c.stateNode,pl=!1;break a;case 3:case 4:U=c.stateNode.containerInfo,pl=!0;break a}c=c.return}if(U===null)throw Error(i(160));hl(o,s,a),U=null,pl=!1,o=a.alternate,o!==null&&(o.return=null),a.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)xl(t,e),t=t.sibling}var bl=null;function xl(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:yl(t,e),Sl(e),r&4&&(Jc(3,e,e.return),qc(3,e),Jc(5,e,e.return));break;case 1:yl(t,e),Sl(e),r&512&&(H||n===null||Qc(n,n.return)),r&64&&ol&&(e=e.updateQueue,e!==null&&(r=e.callbacks,r!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?r:n.concat(r))));break;case 26:var a=bl;if(yl(t,e),Sl(e),r&512&&(H||n===null||Qc(n,n.return)),r&4){var o=n===null?null:n.memoizedState;if(r=e.memoizedState,n===null)if(r===null)if(e.stateNode===null){a:{r=e.type,n=e.memoizedProps,a=a.ownerDocument||a;b:switch(r){case`title`:o=a.getElementsByTagName(`title`)[0],(!o||o[ct]||o[tt]||o.namespaceURI===`http://www.w3.org/2000/svg`||o.hasAttribute(`itemprop`))&&(o=a.createElement(r),a.head.insertBefore(o,a.querySelector(`head > title`))),Ld(o,r,n),o[tt]=e,mt(o),r=o;break a;case`link`:var s=zf(`link`,`href`,a).get(r+(n.href||``));if(s){for(var c=0;c<s.length;c++)if(o=s[c],o.getAttribute(`href`)===(n.href==null||n.href===``?null:n.href)&&o.getAttribute(`rel`)===(n.rel==null?null:n.rel)&&o.getAttribute(`title`)===(n.title==null?null:n.title)&&o.getAttribute(`crossorigin`)===(n.crossOrigin==null?null:n.crossOrigin)){s.splice(c,1);break b}}o=a.createElement(r),Ld(o,r,n),a.head.appendChild(o);break;case`meta`:if(s=zf(`meta`,`content`,a).get(r+(n.content||``))){for(c=0;c<s.length;c++)if(o=s[c],o.getAttribute(`content`)===(n.content==null?null:``+n.content)&&o.getAttribute(`name`)===(n.name==null?null:n.name)&&o.getAttribute(`property`)===(n.property==null?null:n.property)&&o.getAttribute(`http-equiv`)===(n.httpEquiv==null?null:n.httpEquiv)&&o.getAttribute(`charset`)===(n.charSet==null?null:n.charSet)){s.splice(c,1);break b}}o=a.createElement(r),Ld(o,r,n),a.head.appendChild(o);break;default:throw Error(i(468,r))}o[tt]=e,mt(o),r=o}e.stateNode=r}else Bf(a,e.type,e.stateNode);else e.stateNode=Pf(a,r,e.memoizedProps);else o===r?r===null&&e.stateNode!==null&&el(e,e.memoizedProps,n.memoizedProps):(o===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):o.count--,r===null?Bf(a,e.type,e.stateNode):Pf(a,r,e.memoizedProps))}break;case 27:yl(t,e),Sl(e),r&512&&(H||n===null||Qc(n,n.return)),n!==null&&r&4&&el(e,e.memoizedProps,n.memoizedProps);break;case 5:if(yl(t,e),Sl(e),r&512&&(H||n===null||Qc(n,n.return)),e.flags&32){a=e.stateNode;try{Kt(a,``)}catch(t){Y(e,e.return,t)}}r&4&&e.stateNode!=null&&(a=e.memoizedProps,el(e,a,n===null?a:n.memoizedProps)),r&1024&&(sl=!0);break;case 6:if(yl(t,e),Sl(e),r&4){if(e.stateNode===null)throw Error(i(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(t){Y(e,e.return,t)}}break;case 3:if(Rf=null,a=bl,bl=mf(t.containerInfo),yl(t,e),bl=a,Sl(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{jp(t.containerInfo)}catch(t){Y(e,e.return,t)}sl&&(sl=!1,Cl(e));break;case 4:r=bl,bl=mf(e.stateNode.containerInfo),yl(t,e),Sl(e),bl=r;break;case 12:yl(t,e),Sl(e);break;case 13:yl(t,e),Sl(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(ru=xe()),r&4&&(r=e.updateQueue,r!==null&&(e.updateQueue=null,vl(e,r)));break;case 22:a=e.memoizedState!==null;var l=n!==null&&n.memoizedState!==null,u=ol,d=H;if(ol=u||a,H=d||l,yl(t,e),H=d,ol=u,Sl(e),r&8192)a:for(t=e.stateNode,t._visibility=a?t._visibility&-2:t._visibility|1,a&&(n===null||l||ol||H||Tl(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){l=n=t;try{if(o=l.stateNode,a)s=o.style,typeof s.setProperty==`function`?s.setProperty(`display`,`none`,`important`):s.display=`none`;else{c=l.stateNode;var f=l.memoizedProps.style,p=f!=null&&f.hasOwnProperty(`display`)?f.display:null;c.style.display=p==null||typeof p==`boolean`?``:(``+p).trim()}}catch(e){Y(l,l.return,e)}}}else if(t.tag===6){if(n===null){l=t;try{l.stateNode.nodeValue=a?``:l.memoizedProps}catch(e){Y(l,l.return,e)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break a;for(;t.sibling===null;){if(t.return===null||t.return===e)break a;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}r&4&&(r=e.updateQueue,r!==null&&(n=r.retryQueue,n!==null&&(r.retryQueue=null,vl(e,n))));break;case 19:yl(t,e),Sl(e),r&4&&(r=e.updateQueue,r!==null&&(e.updateQueue=null,vl(e,r)));break;case 30:break;case 21:break;default:yl(t,e),Sl(e)}}function Sl(e){var t=e.flags;if(t&2){try{for(var n,r=e.return;r!==null;){if(tl(r)){n=r;break}r=r.return}if(n==null)throw Error(i(160));switch(n.tag){case 27:var a=n.stateNode,o=nl(e);il(e,o,a);break;case 5:var s=n.stateNode;n.flags&32&&(Kt(s,``),n.flags&=-33);var c=nl(e);il(e,c,s);break;case 3:case 4:var l=n.stateNode.containerInfo,u=nl(e);rl(e,u,l);break;default:throw Error(i(161))}}catch(t){Y(e,e.return,t)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Cl(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Cl(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function wl(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)dl(e,t.alternate,t),t=t.sibling}function Tl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Jc(4,t,t.return),Tl(t);break;case 1:Qc(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount==`function`&&Xc(t,t.return,n),Tl(t);break;case 27:df(t.stateNode);case 26:case 5:Qc(t,t.return),Tl(t);break;case 22:t.memoizedState===null&&Tl(t);break;case 30:Tl(t);break;default:Tl(t)}e=e.sibling}}function El(e,t,n){for(n&&=(t.subtreeFlags&8772)!=0,t=t.child;t!==null;){var r=t.alternate,i=e,a=t,o=a.flags;switch(a.tag){case 0:case 11:case 15:El(i,a,n),qc(4,a);break;case 1:if(El(i,a,n),r=a,i=r.stateNode,typeof i.componentDidMount==`function`)try{i.componentDidMount()}catch(e){Y(r,r.return,e)}if(r=a,i=r.updateQueue,i!==null){var s=r.stateNode;try{var c=i.shared.hiddenCallbacks;if(c!==null)for(i.shared.hiddenCallbacks=null,i=0;i<c.length;i++)Xa(c[i],s)}catch(e){Y(r,r.return,e)}}n&&o&64&&Yc(a),Zc(a,a.return);break;case 27:al(a);case 26:case 5:El(i,a,n),n&&r===null&&o&4&&$c(a),Zc(a,a.return);break;case 12:El(i,a,n);break;case 13:El(i,a,n),n&&o&4&&gl(i,a);break;case 22:a.memoizedState===null&&El(i,a,n),Zc(a,a.return);break;case 30:break;default:El(i,a,n)}t=t.sibling}}function Dl(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&_a(n))}function Ol(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&_a(e))}function kl(e,t,n,r){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Al(e,t,n,r),t=t.sibling}function Al(e,t,n,r){var i=t.flags;switch(t.tag){case 0:case 11:case 15:kl(e,t,n,r),i&2048&&qc(9,t);break;case 1:kl(e,t,n,r);break;case 3:kl(e,t,n,r),i&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&_a(e)));break;case 12:if(i&2048){kl(e,t,n,r),e=t.stateNode;try{var a=t.memoizedProps,o=a.id,s=a.onPostCommit;typeof s==`function`&&s(o,t.alternate===null?`mount`:`update`,e.passiveEffectDuration,-0)}catch(e){Y(t,t.return,e)}}else kl(e,t,n,r);break;case 13:kl(e,t,n,r);break;case 23:break;case 22:a=t.stateNode,o=t.alternate,t.memoizedState===null?a._visibility&2?kl(e,t,n,r):(a._visibility|=2,jl(e,t,n,r,(t.subtreeFlags&10256)!=0)):a._visibility&2?kl(e,t,n,r):Ml(e,t),i&2048&&Dl(o,t);break;case 24:kl(e,t,n,r),i&2048&&Ol(t.alternate,t);break;default:kl(e,t,n,r)}}function jl(e,t,n,r,i){for(i&&=(t.subtreeFlags&10256)!=0,t=t.child;t!==null;){var a=e,o=t,s=n,c=r,l=o.flags;switch(o.tag){case 0:case 11:case 15:jl(a,o,s,c,i),qc(8,o);break;case 23:break;case 22:var u=o.stateNode;o.memoizedState===null?(u._visibility|=2,jl(a,o,s,c,i)):u._visibility&2?jl(a,o,s,c,i):Ml(a,o),i&&l&2048&&Dl(o.alternate,o);break;case 24:jl(a,o,s,c,i),i&&l&2048&&Ol(o.alternate,o);break;default:jl(a,o,s,c,i)}t=t.sibling}}function Ml(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,r=t,i=r.flags;switch(r.tag){case 22:Ml(n,r),i&2048&&Dl(r.alternate,r);break;case 24:Ml(n,r),i&2048&&Ol(r.alternate,r);break;default:Ml(n,r)}t=t.sibling}}var Nl=8192;function Pl(e){if(e.subtreeFlags&Nl)for(e=e.child;e!==null;)Fl(e),e=e.sibling}function Fl(e){switch(e.tag){case 26:Pl(e),e.flags&Nl&&e.memoizedState!==null&&Gf(bl,e.memoizedState,e.memoizedProps);break;case 5:Pl(e);break;case 3:case 4:var t=bl;bl=mf(e.stateNode.containerInfo),Pl(e),bl=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Nl,Nl=16777216,Pl(e),Nl=t):Pl(e));break;default:Pl(e)}}function Il(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Ll(e){var t=e.deletions;if(e.flags&16){if(t!==null)for(var n=0;n<t.length;n++){var r=t[n];ll=r,Bl(r,e)}Il(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Rl(e),e=e.sibling}function Rl(e){switch(e.tag){case 0:case 11:case 15:Ll(e),e.flags&2048&&Jc(9,e,e.return);break;case 3:Ll(e);break;case 12:Ll(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,zl(e)):Ll(e);break;default:Ll(e)}}function zl(e){var t=e.deletions;if(e.flags&16){if(t!==null)for(var n=0;n<t.length;n++){var r=t[n];ll=r,Bl(r,e)}Il(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Jc(8,t,t.return),zl(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,zl(t));break;default:zl(t)}e=e.sibling}}function Bl(e,t){for(;ll!==null;){var n=ll;switch(n.tag){case 0:case 11:case 15:Jc(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var r=n.memoizedState.cachePool.pool;r!=null&&r.refCount++}break;case 24:_a(n.memoizedState.cache)}if(r=n.child,r!==null)r.return=n,ll=r;else a:for(n=e;ll!==null;){r=ll;var i=r.sibling,a=r.return;if(fl(r),r===n){ll=null;break a}if(i!==null){i.return=a,ll=i;break a}ll=a}}}var Vl={getCacheForType:function(e){var t=la(ha),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},Hl=typeof WeakMap==`function`?WeakMap:Map,W=0,G=null,K=null,q=0,J=0,Ul=null,Wl=!1,Gl=!1,Kl=!1,ql=0,Jl=0,Yl=0,Xl=0,Zl=0,Ql=0,$l=0,eu=null,tu=null,nu=!1,ru=0,iu=1/0,au=null,ou=null,su=0,cu=null,lu=null,uu=0,du=0,fu=null,pu=null,mu=0,hu=null;function gu(){if(W&2&&q!==0)return q&-q;if(k.T!==null){var e=ba;return e===0?dd():e}return Qe()}function _u(){Ql===0&&(Ql=!(q&536870912)||L?Ue():536870912);var e=Gs.current;return e!==null&&(e.flags|=32),Ql}function vu(e,t,n){(e===G&&(J===2||J===9)||e.cancelPendingCommit!==null)&&(Tu(e,0),Su(e,q,Ql,!1)),Ke(e,n),(!(W&2)||e!==G)&&(e===G&&(!(W&2)&&(Xl|=n),Jl===4&&Su(e,q,Ql,!1)),id(e))}function yu(e,t,n){if(W&6)throw Error(i(327));var r=!n&&(t&124)==0&&(t&e.expiredLanes)===0||Ve(e,t),a=r?Mu(e,t):Au(e,t,!0),o=r;do{if(a===0){Gl&&!r&&Su(e,t,0,!1);break}else{if(n=e.current.alternate,o&&!xu(n)){a=Au(e,t,!1),o=!1;continue}if(a===2){if(o=t,e.errorRecoveryDisabledLanes&o)var s=0;else s=e.pendingLanes&-536870913,s=s===0?s&536870912?536870912:0:s;if(s!==0){t=s;a:{var c=e;a=eu;var l=c.current.memoizedState.isDehydrated;if(l&&(Tu(c,s).flags|=256),s=Au(c,s,!1),s!==2){if(Kl&&!l){c.errorRecoveryDisabledLanes|=o,Xl|=o,a=4;break a}o=tu,tu=a,o!==null&&(tu===null?tu=o:tu.push.apply(tu,o))}a=s}if(o=!1,a!==2)continue}}if(a===1){Tu(e,0),Su(e,t,0,!0);break}a:{switch(r=e,o=a,o){case 0:case 1:throw Error(i(345));case 4:if((t&4194048)!==t)break;case 6:Su(r,t,Ql,!Wl);break a;case 2:tu=null;break;case 3:case 5:break;default:throw Error(i(329))}if((t&62914560)===t&&(a=ru+300-xe(),10<a)){if(Su(r,t,Ql,!Wl),Be(r,0,!0)!==0)break a;r.timeoutHandle=qd(bu.bind(null,r,n,tu,au,nu,t,Ql,Xl,$l,Wl,o,2,-0,0),a);break a}bu(r,n,tu,au,nu,t,Ql,Xl,$l,Wl,o,0,-0,0)}}break}while(1);id(e)}function bu(e,t,n,r,i,a,o,s,c,l,u,d,f,p){if(e.timeoutHandle=-1,d=t.subtreeFlags,(d&8192||(d&16785408)==16785408)&&(Uf={stylesheets:null,count:0,unsuspend:Wf},Fl(t),d=Kf(),d!==null)){e.cancelPendingCommit=d(zu.bind(null,e,t,a,n,r,i,o,s,c,u,1,f,p)),Su(e,a,o,!l);return}zu(e,t,a,n,r,i,o,s,c)}function xu(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var r=0;r<n.length;r++){var i=n[r],a=i.getSnapshot;i=i.value;try{if(!Pr(a(),i))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Su(e,t,n,r){t&=~Zl,t&=~Xl,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var i=t;0<i;){var a=31-Ne(i),o=1<<a;r[a]=-1,i&=~o}n!==0&&Je(e,n,t)}function Cu(){return W&6?!0:(ad(0,!1),!1)}function wu(){if(K!==null){if(J===0)var e=K.return;else e=K,ta=ea=null,xo(e),Is=null,Ls=0,e=K;for(;e!==null;)Kc(e.alternate,e),e=e.return;K=null}}function Tu(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,Jd(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),wu(),G=e,K=n=Ci(e.current,null),q=t,J=0,Ul=null,Wl=!1,Gl=Ve(e,t),Kl=!1,$l=Ql=Zl=Xl=Yl=Jl=0,tu=eu=null,nu=!1,t&8&&(t|=t&32);var r=e.entangledLanes;if(r!==0)for(e=e.entanglements,r&=t;0<r;){var i=31-Ne(r),a=1<<i;t|=e[i],r&=~a}return ql=t,pi(),n}function Eu(e,t){R=null,k.H=Ms,t===Aa||t===Ma?(t=Ra(),J=3):t===ja?(t=Ra(),J=4):J=t===fc?8:typeof t==`object`&&t&&typeof t.then==`function`?6:1,Ul=t,K===null&&(Jl=1,sc(e,li(t,e.current)))}function Du(){var e=k.H;return k.H=Ms,e===null?Ms:e}function Ou(){var e=k.A;return k.A=Vl,e}function ku(){Jl=4,Wl||(q&4194048)!==q&&Gs.current!==null||(Gl=!0),!(Yl&134217727)&&!(Xl&134217727)||G===null||Su(G,q,Ql,!1)}function Au(e,t,n){var r=W;W|=2;var i=Du(),a=Ou();(G!==e||q!==t)&&(au=null,Tu(e,t)),t=!1;var o=Jl;a:do try{if(J!==0&&K!==null){var s=K,c=Ul;switch(J){case 8:wu(),o=6;break a;case 3:case 2:case 9:case 6:Gs.current===null&&(t=!0);var l=J;if(J=0,Ul=null,Iu(e,s,c,l),n&&Gl){o=0;break a}break;default:l=J,J=0,Ul=null,Iu(e,s,c,l)}}ju(),o=Jl;break}catch(t){Eu(e,t)}while(1);return t&&e.shellSuspendCounter++,ta=ea=null,W=r,k.H=i,k.A=a,K===null&&(G=null,q=0,pi()),o}function ju(){for(;K!==null;)Pu(K)}function Mu(e,t){var n=W;W|=2;var r=Du(),a=Ou();G!==e||q!==t?(au=null,iu=xe()+500,Tu(e,t)):Gl=Ve(e,t);a:do try{if(J!==0&&K!==null){t=K;var o=Ul;b:switch(J){case 1:J=0,Ul=null,Iu(e,t,o,1);break;case 2:case 9:if(Pa(o)){J=0,Ul=null,Fu(t);break}t=function(){J!==2&&J!==9||G!==e||(J=7),id(e)},o.then(t,t);break a;case 3:J=7;break a;case 4:J=5;break a;case 7:Pa(o)?(J=0,Ul=null,Fu(t)):(J=0,Ul=null,Iu(e,t,o,7));break;case 5:var s=null;switch(K.tag){case 26:s=K.memoizedState;case 5:case 27:var c=K;if(!s||Hf(s)){J=0,Ul=null;var l=c.sibling;if(l!==null)K=l;else{var u=c.return;u===null?K=null:(K=u,Lu(u))}break b}}J=0,Ul=null,Iu(e,t,o,5);break;case 6:J=0,Ul=null,Iu(e,t,o,6);break;case 8:wu(),Jl=6;break a;default:throw Error(i(462))}}Nu();break}catch(t){Eu(e,t)}while(1);return ta=ea=null,k.H=r,k.A=a,W=n,K===null?(G=null,q=0,pi(),Jl):0}function Nu(){for(;K!==null&&!ye();)Pu(K)}function Pu(e){var t=Rc(e.alternate,e,ql);e.memoizedProps=e.pendingProps,t===null?Lu(e):K=t}function Fu(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Sc(n,t,t.pendingProps,t.type,void 0,q);break;case 11:t=Sc(n,t,t.pendingProps,t.type.render,t.ref,q);break;case 5:xo(t);default:Kc(n,t),t=K=wi(t,ql),t=Rc(n,t,ql)}e.memoizedProps=e.pendingProps,t===null?Lu(e):K=t}function Iu(e,t,n,r){ta=ea=null,xo(t),Is=null,Ls=0;var i=t.return;try{if(dc(e,i,t,n,q)){Jl=1,sc(e,li(n,e.current)),K=null;return}}catch(t){if(i!==null)throw K=i,t;Jl=1,sc(e,li(n,e.current)),K=null;return}t.flags&32768?(L||r===1?e=!0:Gl||q&536870912?e=!1:(Wl=e=!0,(r===2||r===9||r===3||r===6)&&(r=Gs.current,r!==null&&r.tag===13&&(r.flags|=16384))),Ru(t,e)):Lu(t)}function Lu(e){var t=e;do{if(t.flags&32768){Ru(t,Wl);return}e=t.return;var n=Wc(t.alternate,t,ql);if(n!==null){K=n;return}if(t=t.sibling,t!==null){K=t;return}K=t=e}while(t!==null);Jl===0&&(Jl=5)}function Ru(e,t){do{var n=Gc(e.alternate,e);if(n!==null){n.flags&=32767,K=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){K=e;return}K=e=n}while(e!==null);Jl=6,K=null}function zu(e,t,n,r,a,o,s,c,l){e.cancelPendingCommit=null;do Wu();while(su!==0);if(W&6)throw Error(i(327));if(t!==null){if(t===e.current)throw Error(i(177));if(o=t.lanes|t.childLanes,o|=fi,qe(e,n,o,s,c,l),e===G&&(K=G=null,q=0),lu=t,cu=e,uu=n,du=o,fu=a,pu=r,t.subtreeFlags&10256||t.flags&10256?(e.callbackNode=null,e.callbackPriority=0,Zu(Te,function(){return Gu(!0),null})):(e.callbackNode=null,e.callbackPriority=0),r=(t.flags&13878)!=0,t.subtreeFlags&13878||r){r=k.T,k.T=null,a=A.p,A.p=2,s=W,W|=4;try{ul(e,t,n)}finally{W=s,A.p=a,k.T=r}}su=1,Bu(),Vu(),Hu()}}function Bu(){if(su===1){su=0;var e=cu,t=lu,n=(t.flags&13878)!=0;if(t.subtreeFlags&13878||n){n=k.T,k.T=null;var r=A.p;A.p=2;var i=W;W|=4;try{xl(t,e);var a=Bd,o=zr(e.containerInfo),s=a.focusedElem,c=a.selectionRange;if(o!==s&&s&&s.ownerDocument&&Rr(s.ownerDocument.documentElement,s)){if(c!==null&&Br(s)){var l=c.start,u=c.end;if(u===void 0&&(u=l),`selectionStart`in s)s.selectionStart=l,s.selectionEnd=Math.min(u,s.value.length);else{var d=s.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var p=f.getSelection(),m=s.textContent.length,h=Math.min(c.start,m),g=c.end===void 0?h:Math.min(c.end,m);!p.extend&&h>g&&(o=g,g=h,h=o);var _=Lr(s,h),v=Lr(s,g);if(_&&v&&(p.rangeCount!==1||p.anchorNode!==_.node||p.anchorOffset!==_.offset||p.focusNode!==v.node||p.focusOffset!==v.offset)){var y=d.createRange();y.setStart(_.node,_.offset),p.removeAllRanges(),h>g?(p.addRange(y),p.extend(v.node,v.offset)):(y.setEnd(v.node,v.offset),p.addRange(y))}}}}for(d=[],p=s;p=p.parentNode;)p.nodeType===1&&d.push({element:p,left:p.scrollLeft,top:p.scrollTop});for(typeof s.focus==`function`&&s.focus(),s=0;s<d.length;s++){var b=d[s];b.element.scrollLeft=b.left,b.element.scrollTop=b.top}}ap=!!zd,Bd=zd=null}finally{W=i,A.p=r,k.T=n}}e.current=t,su=2}}function Vu(){if(su===2){su=0;var e=cu,t=lu,n=(t.flags&8772)!=0;if(t.subtreeFlags&8772||n){n=k.T,k.T=null;var r=A.p;A.p=2;var i=W;W|=4;try{dl(e,t.alternate,t)}finally{W=i,A.p=r,k.T=n}}su=3}}function Hu(){if(su===4||su===3){su=0,be();var e=cu,t=lu,n=uu,r=pu;t.subtreeFlags&10256||t.flags&10256?su=5:(su=0,lu=cu=null,Uu(e,e.pendingLanes));var i=e.pendingLanes;if(i===0&&(ou=null),Ze(n),t=t.stateNode,je&&typeof je.onCommitFiberRoot==`function`)try{je.onCommitFiberRoot(Ae,t,void 0,(t.current.flags&128)==128)}catch{}if(r!==null){t=k.T,i=A.p,A.p=2,k.T=null;try{for(var a=e.onRecoverableError,o=0;o<r.length;o++){var s=r[o];a(s.value,{componentStack:s.stack})}}finally{k.T=t,A.p=i}}uu&3&&Wu(),id(e),i=e.pendingLanes,n&4194090&&i&42?e===hu?mu++:(mu=0,hu=e):mu=0,ad(0,!1)}}function Uu(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,_a(t)))}function Wu(e){return Bu(),Vu(),Hu(),Gu(e)}function Gu(){if(su!==5)return!1;var e=cu,t=du;du=0;var n=Ze(uu),r=k.T,a=A.p;try{A.p=32>n?32:n,k.T=null,n=fu,fu=null;var o=cu,s=uu;if(su=0,lu=cu=null,uu=0,W&6)throw Error(i(331));var c=W;if(W|=4,Rl(o.current),Al(o,o.current,s,n),W=c,ad(0,!1),je&&typeof je.onPostCommitFiberRoot==`function`)try{je.onPostCommitFiberRoot(Ae,o)}catch{}return!0}finally{A.p=a,k.T=r,Uu(e,t)}}function Ku(e,t,n){t=li(n,t),t=V(e.stateNode,t,2),e=Wa(e,t,2),e!==null&&(Ke(e,2),id(e))}function Y(e,t,n){if(e.tag===3)Ku(e,e,n);else for(;t!==null;){if(t.tag===3){Ku(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError==`function`||typeof r.componentDidCatch==`function`&&(ou===null||!ou.has(r))){e=li(n,e),n=lc(2),r=Wa(t,n,2),r!==null&&(uc(n,r,t,e),Ke(r,2),id(r));break}}t=t.return}}function qu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Hl;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(Kl=!0,i.add(n),e=X.bind(null,e,t,n),t.then(e,e))}function X(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,G===e&&(q&n)===n&&(Jl===4||Jl===3&&(q&62914560)===q&&300>xe()-ru?!(W&2)&&Tu(e,0):Zl|=n,$l===q&&($l=0)),id(e)}function Ju(e,t){t===0&&(t=We()),e=gi(e,t),e!==null&&(Ke(e,t),id(e))}function Yu(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Ju(e,n)}function Xu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;a!==null&&(n=a.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(i(314))}r!==null&&r.delete(t),Ju(e,n)}function Zu(e,t){return _e(e,t)}var Qu=null,$u=null,ed=!1,td=!1,nd=!1,rd=0;function id(e){e!==$u&&e.next===null&&($u===null?Qu=$u=e:$u=$u.next=e),td=!0,ed||(ed=!0,ud())}function ad(e,t){if(!nd&&td){nd=!0;do for(var n=!1,r=Qu;r!==null;){if(!t)if(e!==0){var i=r.pendingLanes;if(i===0)var a=0;else{var o=r.suspendedLanes,s=r.pingedLanes;a=(1<<31-Ne(42|e)+1)-1,a&=i&~(o&~s),a=a&201326741?a&201326741|1:a?a|2:0}a!==0&&(n=!0,ld(r,a))}else a=q,a=Be(r,r===G?a:0,r.cancelPendingCommit!==null||r.timeoutHandle!==-1),!(a&3)||Ve(r,a)||(n=!0,ld(r,a));r=r.next}while(n);nd=!1}}function od(){sd()}function sd(){td=ed=!1;var e=0;rd!==0&&(Kd()&&(e=rd),rd=0);for(var t=xe(),n=null,r=Qu;r!==null;){var i=r.next,a=Z(r,t);a===0?(r.next=null,n===null?Qu=i:n.next=i,i===null&&($u=n)):(n=r,(e!==0||a&3)&&(td=!0)),r=i}ad(e,!1)}function Z(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,a=e.pendingLanes&-62914561;0<a;){var o=31-Ne(a),s=1<<o,c=i[o];c===-1?((s&n)===0||(s&r)!==0)&&(i[o]=He(s,t)):c<=t&&(e.expiredLanes|=s),a&=~s}if(t=G,n=q,n=Be(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),r=e.callbackNode,n===0||e===t&&(J===2||J===9)||e.cancelPendingCommit!==null)return r!==null&&r!==null&&ve(r),e.callbackNode=null,e.callbackPriority=0;if(!(n&3)||Ve(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(r!==null&&ve(r),Ze(n)){case 2:case 8:n=we;break;case 32:n=Te;break;case 268435456:n=De;break;default:n=Te}return r=cd.bind(null,e),n=_e(n,r),e.callbackPriority=t,e.callbackNode=n,t}return r!==null&&r!==null&&ve(r),e.callbackPriority=2,e.callbackNode=null,2}function cd(e,t){if(su!==0&&su!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(Wu(!0)&&e.callbackNode!==n)return null;var r=q;return r=Be(e,e===G?r:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),r===0?null:(yu(e,r,t),Z(e,xe()),e.callbackNode!=null&&e.callbackNode===n?cd.bind(null,e):null)}function ld(e,t){if(Wu())return null;yu(e,t,!0)}function ud(){Xd(function(){W&6?_e(Ce,od):sd()})}function dd(){return rd===0&&(rd=Ue()),rd}function fd(e){return e==null||typeof e==`symbol`||typeof e==`boolean`?null:typeof e==`function`?e:$t(``+e)}function pd(e,t){var n=t.ownerDocument.createElement(`input`);return n.name=t.name,n.value=t.value,e.id&&n.setAttribute(`form`,e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function md(e,t,n,r,i){if(t===`submit`&&n&&n.stateNode===i){var a=fd((i[nt]||null).action),o=r.submitter;o&&(t=(t=o[nt]||null)?fd(t.formAction):o.getAttribute(`formAction`),t!==null&&(a=t,o=null));var s=new xn(`action`,`action`,null,r,i);e.push({event:s,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(rd!==0){var e=o?pd(i,o):new FormData(i);vs(n,{pending:!0,data:e,method:i.method,action:a},null,e)}}else typeof a==`function`&&(s.preventDefault(),e=o?pd(i,o):new FormData(i),vs(n,{pending:!0,data:e,method:i.method,action:a},a,e))},currentTarget:i}]})}}for(var hd=0;hd<oi.length;hd++){var gd=oi[hd],_d=gd.toLowerCase(),vd=gd[0].toUpperCase()+gd.slice(1);si(_d,`on`+vd)}si(Qr,`onAnimationEnd`),si($r,`onAnimationIteration`),si(ei,`onAnimationStart`),si(`dblclick`,`onDoubleClick`),si(`focusin`,`onFocus`),si(`focusout`,`onBlur`),si(ti,`onTransitionRun`),si(ni,`onTransitionStart`),si(ri,`onTransitionCancel`),si(ii,`onTransitionEnd`),vt(`onMouseEnter`,[`mouseout`,`mouseover`]),vt(`onMouseLeave`,[`mouseout`,`mouseover`]),vt(`onPointerEnter`,[`pointerout`,`pointerover`]),vt(`onPointerLeave`,[`pointerout`,`pointerover`]),_t(`onChange`,`change click focusin focusout input keydown keyup selectionchange`.split(` `)),_t(`onSelect`,`focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange`.split(` `)),_t(`onBeforeInput`,[`compositionend`,`keypress`,`textInput`,`paste`]),_t(`onCompositionEnd`,`compositionend focusout keydown keypress keyup mousedown`.split(` `)),_t(`onCompositionStart`,`compositionstart focusout keydown keypress keyup mousedown`.split(` `)),_t(`onCompositionUpdate`,`compositionupdate focusout keydown keypress keyup mousedown`.split(` `));var yd=`abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting`.split(` `),bd=new Set(`beforetoggle cancel close invalid load scroll scrollend toggle`.split(` `).concat(yd));function xd(e,t){t=(t&4)!=0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;a:{var a=void 0;if(t)for(var o=r.length-1;0<=o;o--){var s=r[o],c=s.instance,l=s.currentTarget;if(s=s.listener,c!==a&&i.isPropagationStopped())break a;a=s,i.currentTarget=l;try{a(i)}catch(e){rc(e)}i.currentTarget=null,a=c}else for(o=0;o<r.length;o++){if(s=r[o],c=s.instance,l=s.currentTarget,s=s.listener,c!==a&&i.isPropagationStopped())break a;a=s,i.currentTarget=l;try{a(i)}catch(e){rc(e)}i.currentTarget=null,a=c}}}}function Q(e,t){var n=t[it];n===void 0&&(n=t[it]=new Set);var r=e+`__bubble`;n.has(r)||(Td(t,e,2,!1),n.add(r))}function Sd(e,t,n){var r=0;t&&(r|=4),Td(n,e,r,t)}var Cd=`_reactListening`+Math.random().toString(36).slice(2);function wd(e){if(!e[Cd]){e[Cd]=!0,ht.forEach(function(t){t!==`selectionchange`&&(bd.has(t)||Sd(t,!1,e),Sd(t,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Cd]||(t[Cd]=!0,Sd(`selectionchange`,!1,t))}}function Td(e,t,n,r){switch(fp(t)){case 2:var i=op;break;case 8:i=sp;break;default:i=cp}n=i.bind(null,t,n,e),i=void 0,!ln||t!==`touchstart`&&t!==`touchmove`&&t!==`wheel`||(i=!0),r?i===void 0?e.addEventListener(t,n,!0):e.addEventListener(t,n,{capture:!0,passive:i}):i===void 0?e.addEventListener(t,n,!1):e.addEventListener(t,n,{passive:i})}function Ed(e,t,n,r,i){var a=r;if(!(t&1)&&!(t&2)&&r!==null)a:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var c=r.stateNode.containerInfo;if(c===i)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&s.stateNode.containerInfo===i)return;s=s.return}for(;c!==null;){if(s=ut(c),s===null)return;if(l=s.tag,l===5||l===6||l===26||l===27){r=a=s;continue a}c=c.parentNode}}r=r.return}on(function(){var r=a,i=F(n),s=[];a:{var c=ai.get(e);if(c!==void 0){var l=xn,u=e;switch(e){case`keypress`:if(gn(n)===0)break a;case`keydown`:case`keyup`:l=Gn;break;case`focusin`:u=`focus`,l=Mn;break;case`focusout`:u=`blur`,l=Mn;break;case`beforeblur`:case`afterblur`:l=Mn;break;case`click`:if(n.button===2)break a;case`auxclick`:case`dblclick`:case`mousedown`:case`mousemove`:case`mouseup`:case`mouseout`:case`mouseover`:case`contextmenu`:l=On;break;case`drag`:case`dragend`:case`dragenter`:case`dragexit`:case`dragleave`:case`dragover`:case`dragstart`:case`drop`:l=An;break;case`touchcancel`:case`touchend`:case`touchmove`:case`touchstart`:l=Yn;break;case Qr:case $r:case ei:l=Pn;break;case ii:l=Zn;break;case`scroll`:case`scrollend`:l=Cn;break;case`wheel`:l=$n;break;case`copy`:case`cut`:case`paste`:l=In;break;case`gotpointercapture`:case`lostpointercapture`:case`pointercancel`:case`pointerdown`:case`pointermove`:case`pointerout`:case`pointerover`:case`pointerup`:l=qn;break;case`toggle`:case`beforetoggle`:l=tr}var d=(t&4)!=0,f=!d&&(e===`scroll`||e===`scrollend`),p=d?c===null?null:c+`Capture`:c;d=[];for(var m=r,h;m!==null;){var g=m;if(h=g.stateNode,g=g.tag,g!==5&&g!==26&&g!==27||h===null||p===null||(g=sn(m,p),g!=null&&d.push(Dd(m,g,h))),f)break;m=m.return}0<d.length&&(c=new l(c,u,null,n,i),s.push({event:c,listeners:d}))}}if(!(t&7)){a:{if(c=e===`mouseover`||e===`pointerover`,l=e===`mouseout`||e===`pointerout`,c&&n!==en&&(u=n.relatedTarget||n.fromElement)&&(ut(u)||u[rt]))break a;if((l||c)&&(c=i.window===i?i:(c=i.ownerDocument)?c.defaultView||c.parentWindow:window,l?(u=n.relatedTarget||n.toElement,l=r,u=u?ut(u):null,u!==null&&(f=o(u),d=u.tag,u!==f||d!==5&&d!==27&&d!==6)&&(u=null)):(l=null,u=r),l!==u)){if(d=On,g=`onMouseLeave`,p=`onMouseEnter`,m=`mouse`,(e===`pointerout`||e===`pointerover`)&&(d=qn,g=`onPointerLeave`,p=`onPointerEnter`,m=`pointer`),f=l==null?c:ft(l),h=u==null?c:ft(u),c=new d(g,m+`leave`,l,n,i),c.target=f,c.relatedTarget=h,g=null,ut(i)===r&&(d=new d(p,m+`enter`,u,n,i),d.target=h,d.relatedTarget=f,g=d),f=g,l&&u)b:{for(d=l,p=u,m=0,h=d;h;h=kd(h))m++;for(h=0,g=p;g;g=kd(g))h++;for(;0<m-h;)d=kd(d),m--;for(;0<h-m;)p=kd(p),h--;for(;m--;){if(d===p||p!==null&&d===p.alternate)break b;d=kd(d),p=kd(p)}d=null}else d=null;l!==null&&Ad(s,c,l,d,!1),u!==null&&f!==null&&Ad(s,f,u,d,!0)}}a:{if(c=r?ft(r):window,l=c.nodeName&&c.nodeName.toLowerCase(),l===`select`||l===`input`&&c.type===`file`)var _=Sr;else if(gr(c))if(Cr)_=Mr;else{_=Ar;var v=kr}else l=c.nodeName,!l||l.toLowerCase()!==`input`||c.type!==`checkbox`&&c.type!==`radio`?r&&Xt(r.elementType)&&(_=Sr):_=jr;if(_&&=_(e,r)){_r(s,_,n,i);break a}v&&v(e,c,r),e===`focusout`&&r&&c.type===`number`&&r.memoizedProps.value!=null&&Ht(c,`number`,c.value)}switch(v=r?ft(r):window,e){case`focusin`:(gr(v)||v.contentEditable===`true`)&&(Hr=v,Ur=r,Wr=null);break;case`focusout`:Wr=Ur=Hr=null;break;case`mousedown`:Gr=!0;break;case`contextmenu`:case`mouseup`:case`dragend`:Gr=!1,Kr(s,n,i);break;case`selectionchange`:if(Vr)break;case`keydown`:case`keyup`:Kr(s,n,i)}var y;if(rr)b:{switch(e){case`compositionstart`:var b=`onCompositionStart`;break b;case`compositionend`:b=`onCompositionEnd`;break b;case`compositionupdate`:b=`onCompositionUpdate`;break b}b=void 0}else fr?lr(e,n)&&(b=`onCompositionEnd`):e===`keydown`&&n.keyCode===229&&(b=`onCompositionStart`);b&&(or&&n.locale!==`ko`&&(fr||b!==`onCompositionStart`?b===`onCompositionEnd`&&fr&&(y=hn()):(fn=i,pn=`value`in fn?fn.value:fn.textContent,fr=!0)),v=Od(r,b),0<v.length&&(b=new Rn(b,e,null,n,i),s.push({event:b,listeners:v}),y?b.data=y:(y=ur(n),y!==null&&(b.data=y)))),(y=ar?pr(e,n):mr(e,n))&&(b=Od(r,`onBeforeInput`),0<b.length&&(v=new Rn(`onBeforeInput`,`beforeinput`,null,n,i),s.push({event:v,listeners:b}),v.data=y)),md(s,e,r,n,i)}xd(s,t)})}function Dd(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Od(e,t){for(var n=t+`Capture`,r=[];e!==null;){var i=e,a=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||a===null||(i=sn(e,n),i!=null&&r.unshift(Dd(e,i,a)),i=sn(e,t),i!=null&&r.push(Dd(e,i,a))),e.tag===3)return r;e=e.return}return[]}function kd(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Ad(e,t,n,r,i){for(var a=t._reactName,o=[];n!==null&&n!==r;){var s=n,c=s.alternate,l=s.stateNode;if(s=s.tag,c!==null&&c===r)break;s!==5&&s!==26&&s!==27||l===null||(c=l,i?(l=sn(n,a),l!=null&&o.unshift(Dd(n,l,c))):i||(l=sn(n,a),l!=null&&o.push(Dd(n,l,c)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var jd=/\r\n?/g,Md=/\u0000|\uFFFD/g;function Nd(e){return(typeof e==`string`?e:``+e).replace(jd,`
`).replace(Md,``)}function Pd(e,t){return t=Nd(t),Nd(e)===t?!0:!1}function Fd(){}function $(e,t,n,r,a,o){switch(n){case`children`:typeof r==`string`?t===`body`||t===`textarea`&&r===``||Kt(e,r):(typeof r==`number`||typeof r==`bigint`)&&t!==`body`&&Kt(e,``+r);break;case`className`:wt(e,`class`,r);break;case`tabIndex`:wt(e,`tabindex`,r);break;case`dir`:case`role`:case`viewBox`:case`width`:case`height`:wt(e,n,r);break;case`style`:Yt(e,r,o);break;case`data`:if(t!==`object`){wt(e,`data`,r);break}case`src`:case`href`:if(r===``&&(t!==`a`||n!==`href`)){e.removeAttribute(n);break}if(r==null||typeof r==`function`||typeof r==`symbol`||typeof r==`boolean`){e.removeAttribute(n);break}r=$t(``+r),e.setAttribute(n,r);break;case`action`:case`formAction`:if(typeof r==`function`){e.setAttribute(n,`javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')`);break}else typeof o==`function`&&(n===`formAction`?(t!==`input`&&$(e,t,`name`,a.name,a,null),$(e,t,`formEncType`,a.formEncType,a,null),$(e,t,`formMethod`,a.formMethod,a,null),$(e,t,`formTarget`,a.formTarget,a,null)):($(e,t,`encType`,a.encType,a,null),$(e,t,`method`,a.method,a,null),$(e,t,`target`,a.target,a,null)));if(r==null||typeof r==`symbol`||typeof r==`boolean`){e.removeAttribute(n);break}r=$t(``+r),e.setAttribute(n,r);break;case`onClick`:r!=null&&(e.onclick=Fd);break;case`onScroll`:r!=null&&Q(`scroll`,e);break;case`onScrollEnd`:r!=null&&Q(`scrollend`,e);break;case`dangerouslySetInnerHTML`:if(r!=null){if(typeof r!=`object`||!(`__html`in r))throw Error(i(61));if(n=r.__html,n!=null){if(a.children!=null)throw Error(i(60));e.innerHTML=n}}break;case`multiple`:e.multiple=r&&typeof r!=`function`&&typeof r!=`symbol`;break;case`muted`:e.muted=r&&typeof r!=`function`&&typeof r!=`symbol`;break;case`suppressContentEditableWarning`:case`suppressHydrationWarning`:case`defaultValue`:case`defaultChecked`:case`innerHTML`:case`ref`:break;case`autoFocus`:break;case`xlinkHref`:if(r==null||typeof r==`function`||typeof r==`boolean`||typeof r==`symbol`){e.removeAttribute(`xlink:href`);break}n=$t(``+r),e.setAttributeNS(`http://www.w3.org/1999/xlink`,`xlink:href`,n);break;case`contentEditable`:case`spellCheck`:case`draggable`:case`value`:case`autoReverse`:case`externalResourcesRequired`:case`focusable`:case`preserveAlpha`:r!=null&&typeof r!=`function`&&typeof r!=`symbol`?e.setAttribute(n,``+r):e.removeAttribute(n);break;case`inert`:case`allowFullScreen`:case`async`:case`autoPlay`:case`controls`:case`default`:case`defer`:case`disabled`:case`disablePictureInPicture`:case`disableRemotePlayback`:case`formNoValidate`:case`hidden`:case`loop`:case`noModule`:case`noValidate`:case`open`:case`playsInline`:case`readOnly`:case`required`:case`reversed`:case`scoped`:case`seamless`:case`itemScope`:r&&typeof r!=`function`&&typeof r!=`symbol`?e.setAttribute(n,``):e.removeAttribute(n);break;case`capture`:case`download`:!0===r?e.setAttribute(n,``):!1!==r&&r!=null&&typeof r!=`function`&&typeof r!=`symbol`?e.setAttribute(n,r):e.removeAttribute(n);break;case`cols`:case`rows`:case`size`:case`span`:r!=null&&typeof r!=`function`&&typeof r!=`symbol`&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case`rowSpan`:case`start`:r==null||typeof r==`function`||typeof r==`symbol`||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case`popover`:Q(`beforetoggle`,e),Q(`toggle`,e),Ct(e,`popover`,r);break;case`xlinkActuate`:Tt(e,`http://www.w3.org/1999/xlink`,`xlink:actuate`,r);break;case`xlinkArcrole`:Tt(e,`http://www.w3.org/1999/xlink`,`xlink:arcrole`,r);break;case`xlinkRole`:Tt(e,`http://www.w3.org/1999/xlink`,`xlink:role`,r);break;case`xlinkShow`:Tt(e,`http://www.w3.org/1999/xlink`,`xlink:show`,r);break;case`xlinkTitle`:Tt(e,`http://www.w3.org/1999/xlink`,`xlink:title`,r);break;case`xlinkType`:Tt(e,`http://www.w3.org/1999/xlink`,`xlink:type`,r);break;case`xmlBase`:Tt(e,`http://www.w3.org/XML/1998/namespace`,`xml:base`,r);break;case`xmlLang`:Tt(e,`http://www.w3.org/XML/1998/namespace`,`xml:lang`,r);break;case`xmlSpace`:Tt(e,`http://www.w3.org/XML/1998/namespace`,`xml:space`,r);break;case`is`:Ct(e,`is`,r);break;case`innerText`:case`textContent`:break;default:(!(2<n.length)||n[0]!==`o`&&n[0]!==`O`||n[1]!==`n`&&n[1]!==`N`)&&(n=Zt.get(n)||n,Ct(e,n,r))}}function Id(e,t,n,r,a,o){switch(n){case`style`:Yt(e,r,o);break;case`dangerouslySetInnerHTML`:if(r!=null){if(typeof r!=`object`||!(`__html`in r))throw Error(i(61));if(n=r.__html,n!=null){if(a.children!=null)throw Error(i(60));e.innerHTML=n}}break;case`children`:typeof r==`string`?Kt(e,r):(typeof r==`number`||typeof r==`bigint`)&&Kt(e,``+r);break;case`onScroll`:r!=null&&Q(`scroll`,e);break;case`onScrollEnd`:r!=null&&Q(`scrollend`,e);break;case`onClick`:r!=null&&(e.onclick=Fd);break;case`suppressContentEditableWarning`:case`suppressHydrationWarning`:case`innerHTML`:case`ref`:break;case`innerText`:case`textContent`:break;default:if(!gt.hasOwnProperty(n))a:{if(n[0]===`o`&&n[1]===`n`&&(a=n.endsWith(`Capture`),t=n.slice(2,a?n.length-7:void 0),o=e[nt]||null,o=o==null?null:o[n],typeof o==`function`&&e.removeEventListener(t,o,a),typeof r==`function`)){typeof o!=`function`&&o!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,a);break a}n in e?e[n]=r:!0===r?e.setAttribute(n,``):Ct(e,n,r)}}}function Ld(e,t,n){switch(t){case`div`:case`span`:case`svg`:case`path`:case`a`:case`g`:case`p`:case`li`:break;case`img`:Q(`error`,e),Q(`load`,e);var r=!1,a=!1,o;for(o in n)if(n.hasOwnProperty(o)){var s=n[o];if(s!=null)switch(o){case`src`:r=!0;break;case`srcSet`:a=!0;break;case`children`:case`dangerouslySetInnerHTML`:throw Error(i(137,t));default:$(e,t,o,s,n,null)}}a&&$(e,t,`srcSet`,n.srcSet,n,null),r&&$(e,t,`src`,n.src,n,null);return;case`input`:Q(`invalid`,e);var c=o=s=a=null,l=null,u=null;for(r in n)if(n.hasOwnProperty(r)){var d=n[r];if(d!=null)switch(r){case`name`:a=d;break;case`type`:s=d;break;case`checked`:l=d;break;case`defaultChecked`:u=d;break;case`value`:o=d;break;case`defaultValue`:c=d;break;case`children`:case`dangerouslySetInnerHTML`:if(d!=null)throw Error(i(137,t));break;default:$(e,t,r,d,n,null)}}Vt(e,o,c,l,u,s,a,!1),P(e);return;case`select`:for(a in Q(`invalid`,e),r=s=o=null,n)if(n.hasOwnProperty(a)&&(c=n[a],c!=null))switch(a){case`value`:o=c;break;case`defaultValue`:s=c;break;case`multiple`:r=c;default:$(e,t,a,c,n,null)}t=o,n=s,e.multiple=!!r,t==null?n!=null&&Ut(e,!!r,n,!0):Ut(e,!!r,t,!1);return;case`textarea`:for(s in Q(`invalid`,e),o=a=r=null,n)if(n.hasOwnProperty(s)&&(c=n[s],c!=null))switch(s){case`value`:r=c;break;case`defaultValue`:a=c;break;case`children`:o=c;break;case`dangerouslySetInnerHTML`:if(c!=null)throw Error(i(91));break;default:$(e,t,s,c,n,null)}Gt(e,r,a,o),P(e);return;case`option`:for(l in n)if(n.hasOwnProperty(l)&&(r=n[l],r!=null))switch(l){case`selected`:e.selected=r&&typeof r!=`function`&&typeof r!=`symbol`;break;default:$(e,t,l,r,n,null)}return;case`dialog`:Q(`beforetoggle`,e),Q(`toggle`,e),Q(`cancel`,e),Q(`close`,e);break;case`iframe`:case`object`:Q(`load`,e);break;case`video`:case`audio`:for(r=0;r<yd.length;r++)Q(yd[r],e);break;case`image`:Q(`error`,e),Q(`load`,e);break;case`details`:Q(`toggle`,e);break;case`embed`:case`source`:case`link`:Q(`error`,e),Q(`load`,e);case`area`:case`base`:case`br`:case`col`:case`hr`:case`keygen`:case`meta`:case`param`:case`track`:case`wbr`:case`menuitem`:for(u in n)if(n.hasOwnProperty(u)&&(r=n[u],r!=null))switch(u){case`children`:case`dangerouslySetInnerHTML`:throw Error(i(137,t));default:$(e,t,u,r,n,null)}return;default:if(Xt(t)){for(d in n)n.hasOwnProperty(d)&&(r=n[d],r!==void 0&&Id(e,t,d,r,n,void 0));return}}for(c in n)n.hasOwnProperty(c)&&(r=n[c],r!=null&&$(e,t,c,r,n,null))}function Rd(e,t,n,r){switch(t){case`div`:case`span`:case`svg`:case`path`:case`a`:case`g`:case`p`:case`li`:break;case`input`:var a=null,o=null,s=null,c=null,l=null,u=null,d=null;for(m in n){var f=n[m];if(n.hasOwnProperty(m)&&f!=null)switch(m){case`checked`:break;case`value`:break;case`defaultValue`:l=f;default:r.hasOwnProperty(m)||$(e,t,m,null,r,f)}}for(var p in r){var m=r[p];if(f=n[p],r.hasOwnProperty(p)&&(m!=null||f!=null))switch(p){case`type`:o=m;break;case`name`:a=m;break;case`checked`:u=m;break;case`defaultChecked`:d=m;break;case`value`:s=m;break;case`defaultValue`:c=m;break;case`children`:case`dangerouslySetInnerHTML`:if(m!=null)throw Error(i(137,t));break;default:m!==f&&$(e,t,p,m,r,f)}}Bt(e,s,c,l,u,d,o,a);return;case`select`:for(o in m=s=c=p=null,n)if(l=n[o],n.hasOwnProperty(o)&&l!=null)switch(o){case`value`:break;case`multiple`:m=l;default:r.hasOwnProperty(o)||$(e,t,o,null,r,l)}for(a in r)if(o=r[a],l=n[a],r.hasOwnProperty(a)&&(o!=null||l!=null))switch(a){case`value`:p=o;break;case`defaultValue`:c=o;break;case`multiple`:s=o;default:o!==l&&$(e,t,a,o,r,l)}t=c,n=s,r=m,p==null?!!r!=!!n&&(t==null?Ut(e,!!n,n?[]:``,!1):Ut(e,!!n,t,!0)):Ut(e,!!n,p,!1);return;case`textarea`:for(c in m=p=null,n)if(a=n[c],n.hasOwnProperty(c)&&a!=null&&!r.hasOwnProperty(c))switch(c){case`value`:break;case`children`:break;default:$(e,t,c,null,r,a)}for(s in r)if(a=r[s],o=n[s],r.hasOwnProperty(s)&&(a!=null||o!=null))switch(s){case`value`:p=a;break;case`defaultValue`:m=a;break;case`children`:break;case`dangerouslySetInnerHTML`:if(a!=null)throw Error(i(91));break;default:a!==o&&$(e,t,s,a,r,o)}Wt(e,p,m);return;case`option`:for(var h in n)if(p=n[h],n.hasOwnProperty(h)&&p!=null&&!r.hasOwnProperty(h))switch(h){case`selected`:e.selected=!1;break;default:$(e,t,h,null,r,p)}for(l in r)if(p=r[l],m=n[l],r.hasOwnProperty(l)&&p!==m&&(p!=null||m!=null))switch(l){case`selected`:e.selected=p&&typeof p!=`function`&&typeof p!=`symbol`;break;default:$(e,t,l,p,r,m)}return;case`img`:case`link`:case`area`:case`base`:case`br`:case`col`:case`embed`:case`hr`:case`keygen`:case`meta`:case`param`:case`source`:case`track`:case`wbr`:case`menuitem`:for(var g in n)p=n[g],n.hasOwnProperty(g)&&p!=null&&!r.hasOwnProperty(g)&&$(e,t,g,null,r,p);for(u in r)if(p=r[u],m=n[u],r.hasOwnProperty(u)&&p!==m&&(p!=null||m!=null))switch(u){case`children`:case`dangerouslySetInnerHTML`:if(p!=null)throw Error(i(137,t));break;default:$(e,t,u,p,r,m)}return;default:if(Xt(t)){for(var _ in n)p=n[_],n.hasOwnProperty(_)&&p!==void 0&&!r.hasOwnProperty(_)&&Id(e,t,_,void 0,r,p);for(d in r)p=r[d],m=n[d],!r.hasOwnProperty(d)||p===m||p===void 0&&m===void 0||Id(e,t,d,p,r,m);return}}for(var v in n)p=n[v],n.hasOwnProperty(v)&&p!=null&&!r.hasOwnProperty(v)&&$(e,t,v,null,r,p);for(f in r)p=r[f],m=n[f],!r.hasOwnProperty(f)||p===m||p==null&&m==null||$(e,t,f,p,r,m)}var zd=null,Bd=null;function Vd(e){return e.nodeType===9?e:e.ownerDocument}function Hd(e){switch(e){case`http://www.w3.org/2000/svg`:return 1;case`http://www.w3.org/1998/Math/MathML`:return 2;default:return 0}}function Ud(e,t){if(e===0)switch(t){case`svg`:return 1;case`math`:return 2;default:return 0}return e===1&&t===`foreignObject`?0:e}function Wd(e,t){return e===`textarea`||e===`noscript`||typeof t.children==`string`||typeof t.children==`number`||typeof t.children==`bigint`||typeof t.dangerouslySetInnerHTML==`object`&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Gd=null;function Kd(){var e=window.event;return e&&e.type===`popstate`?e===Gd?!1:(Gd=e,!0):(Gd=null,!1)}var qd=typeof setTimeout==`function`?setTimeout:void 0,Jd=typeof clearTimeout==`function`?clearTimeout:void 0,Yd=typeof Promise==`function`?Promise:void 0,Xd=typeof queueMicrotask==`function`?queueMicrotask:Yd===void 0?qd:function(e){return Yd.resolve(null).then(e).catch(Zd)};function Zd(e){setTimeout(function(){throw e})}function Qd(e){return e===`head`}function $d(e,t){var n=t,r=0,i=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&a.nodeType===8)if(n=a.data,n===`/$`){if(0<r&&8>r){n=r;var o=e.ownerDocument;if(n&1&&df(o.documentElement),n&2&&df(o.body),n&4)for(n=o.head,df(n),o=n.firstChild;o;){var s=o.nextSibling,c=o.nodeName;o[ct]||c===`SCRIPT`||c===`STYLE`||c===`LINK`&&o.rel.toLowerCase()===`stylesheet`||n.removeChild(o),o=s}}if(i===0){e.removeChild(a),jp(t);return}i--}else n===`$`||n===`$?`||n===`$!`?i++:r=n.charCodeAt(0)-48;else r=0;n=a}while(n);jp(t)}function ef(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case`HTML`:case`HEAD`:case`BODY`:ef(n),lt(n);continue;case`SCRIPT`:case`STYLE`:continue;case`LINK`:if(n.rel.toLowerCase()===`stylesheet`)continue}e.removeChild(n)}}function tf(e,t,n,r){for(;e.nodeType===1;){var i=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&(e.nodeName!==`INPUT`||e.type!==`hidden`))break}else if(r){if(!e[ct])switch(t){case`meta`:if(!e.hasAttribute(`itemprop`))break;return e;case`link`:if(a=e.getAttribute(`rel`),a===`stylesheet`&&e.hasAttribute(`data-precedence`)||a!==i.rel||e.getAttribute(`href`)!==(i.href==null||i.href===``?null:i.href)||e.getAttribute(`crossorigin`)!==(i.crossOrigin==null?null:i.crossOrigin)||e.getAttribute(`title`)!==(i.title==null?null:i.title))break;return e;case`style`:if(e.hasAttribute(`data-precedence`))break;return e;case`script`:if(a=e.getAttribute(`src`),(a!==(i.src==null?null:i.src)||e.getAttribute(`type`)!==(i.type==null?null:i.type)||e.getAttribute(`crossorigin`)!==(i.crossOrigin==null?null:i.crossOrigin))&&a&&e.hasAttribute(`async`)&&!e.hasAttribute(`itemprop`))break;return e;default:return e}}else if(t===`input`&&e.type===`hidden`){var a=i.name==null?null:``+i.name;if(i.type===`hidden`&&e.getAttribute(`name`)===a)return e}else return e;if(e=sf(e.nextSibling),e===null)break}return null}function nf(e,t,n){if(t===``)return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!==`INPUT`||e.type!==`hidden`)&&!n||(e=sf(e.nextSibling),e===null))return null;return e}function rf(e){return e.data===`$!`||e.data===`$?`&&e.ownerDocument.readyState===`complete`}function af(e,t){var n=e.ownerDocument;if(e.data!==`$?`||n.readyState===`complete`)t();else{var r=function(){t(),n.removeEventListener(`DOMContentLoaded`,r)};n.addEventListener(`DOMContentLoaded`,r),e._reactRetry=r}}function sf(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t===`$`||t===`$!`||t===`$?`||t===`F!`||t===`F`)break;if(t===`/$`)return null}}return e}var cf=null;function lf(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n===`$`||n===`$!`||n===`$?`){if(t===0)return e;t--}else n===`/$`&&t++}e=e.previousSibling}return null}function uf(e,t,n){switch(t=Vd(n),e){case`html`:if(e=t.documentElement,!e)throw Error(i(452));return e;case`head`:if(e=t.head,!e)throw Error(i(453));return e;case`body`:if(e=t.body,!e)throw Error(i(454));return e;default:throw Error(i(451))}}function df(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);lt(e)}var ff=new Map,pf=new Set;function mf(e){return typeof e.getRootNode==`function`?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var hf=A.d;A.d={f:gf,r:_f,D:bf,C:xf,L:Sf,m:Cf,X:Tf,S:wf,M:Ef};function gf(){var e=hf.f(),t=Cu();return e||t}function _f(e){var t=dt(e);t!==null&&t.tag===5&&t.type===`form`?bs(t):hf.r(e)}var vf=typeof document>`u`?null:document;function yf(e,t,n){var r=vf;if(r&&typeof t==`string`&&t){var i=zt(t);i=`link[rel="`+e+`"][href="`+i+`"]`,typeof n==`string`&&(i+=`[crossorigin="`+n+`"]`),pf.has(i)||(pf.add(i),e={rel:e,crossOrigin:n,href:t},r.querySelector(i)===null&&(t=r.createElement(`link`),Ld(t,`link`,e),mt(t),r.head.appendChild(t)))}}function bf(e){hf.D(e),yf(`dns-prefetch`,e,null)}function xf(e,t){hf.C(e,t),yf(`preconnect`,e,t)}function Sf(e,t,n){hf.L(e,t,n);var r=vf;if(r&&e&&t){var i=`link[rel="preload"][as="`+zt(t)+`"]`;t===`image`&&n&&n.imageSrcSet?(i+=`[imagesrcset="`+zt(n.imageSrcSet)+`"]`,typeof n.imageSizes==`string`&&(i+=`[imagesizes="`+zt(n.imageSizes)+`"]`)):i+=`[href="`+zt(e)+`"]`;var a=i;switch(t){case`style`:a=Of(e);break;case`script`:a=Mf(e)}ff.has(a)||(e=d({rel:`preload`,href:t===`image`&&n&&n.imageSrcSet?void 0:e,as:t},n),ff.set(a,e),r.querySelector(i)!==null||t===`style`&&r.querySelector(kf(a))||t===`script`&&r.querySelector(Nf(a))||(t=r.createElement(`link`),Ld(t,`link`,e),mt(t),r.head.appendChild(t)))}}function Cf(e,t){hf.m(e,t);var n=vf;if(n&&e){var r=t&&typeof t.as==`string`?t.as:`script`,i=`link[rel="modulepreload"][as="`+zt(r)+`"][href="`+zt(e)+`"]`,a=i;switch(r){case`audioworklet`:case`paintworklet`:case`serviceworker`:case`sharedworker`:case`worker`:case`script`:a=Mf(e)}if(!ff.has(a)&&(e=d({rel:`modulepreload`,href:e},t),ff.set(a,e),n.querySelector(i)===null)){switch(r){case`audioworklet`:case`paintworklet`:case`serviceworker`:case`sharedworker`:case`worker`:case`script`:if(n.querySelector(Nf(a)))return}r=n.createElement(`link`),Ld(r,`link`,e),mt(r),n.head.appendChild(r)}}}function wf(e,t,n){hf.S(e,t,n);var r=vf;if(r&&e){var i=pt(r).hoistableStyles,a=Of(e);t||=`default`;var o=i.get(a);if(!o){var s={loading:0,preload:null};if(o=r.querySelector(kf(a)))s.loading=5;else{e=d({rel:`stylesheet`,href:e,"data-precedence":t},n),(n=ff.get(a))&&If(e,n);var c=o=r.createElement(`link`);mt(c),Ld(c,`link`,e),c._p=new Promise(function(e,t){c.onload=e,c.onerror=t}),c.addEventListener(`load`,function(){s.loading|=1}),c.addEventListener(`error`,function(){s.loading|=2}),s.loading|=4,Ff(o,t,r)}o={type:`stylesheet`,instance:o,count:1,state:s},i.set(a,o)}}}function Tf(e,t){hf.X(e,t);var n=vf;if(n&&e){var r=pt(n).hoistableScripts,i=Mf(e),a=r.get(i);a||(a=n.querySelector(Nf(i)),a||(e=d({src:e,async:!0},t),(t=ff.get(i))&&Lf(e,t),a=n.createElement(`script`),mt(a),Ld(a,`link`,e),n.head.appendChild(a)),a={type:`script`,instance:a,count:1,state:null},r.set(i,a))}}function Ef(e,t){hf.M(e,t);var n=vf;if(n&&e){var r=pt(n).hoistableScripts,i=Mf(e),a=r.get(i);a||(a=n.querySelector(Nf(i)),a||(e=d({src:e,async:!0,type:`module`},t),(t=ff.get(i))&&Lf(e,t),a=n.createElement(`script`),mt(a),Ld(a,`link`,e),n.head.appendChild(a)),a={type:`script`,instance:a,count:1,state:null},r.set(i,a))}}function Df(e,t,n,r){var a=(a=ue.current)?mf(a):null;if(!a)throw Error(i(446));switch(e){case`meta`:case`title`:return null;case`style`:return typeof n.precedence==`string`&&typeof n.href==`string`?(t=Of(n.href),n=pt(a).hoistableStyles,r=n.get(t),r||(r={type:`style`,instance:null,count:0,state:null},n.set(t,r)),r):{type:`void`,instance:null,count:0,state:null};case`link`:if(n.rel===`stylesheet`&&typeof n.href==`string`&&typeof n.precedence==`string`){e=Of(n.href);var o=pt(a).hoistableStyles,s=o.get(e);if(s||(a=a.ownerDocument||a,s={type:`stylesheet`,instance:null,count:0,state:{loading:0,preload:null}},o.set(e,s),(o=a.querySelector(kf(e)))&&!o._p&&(s.instance=o,s.state.loading=5),ff.has(e)||(n={rel:`preload`,as:`style`,href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},ff.set(e,n),o||jf(a,e,n,s.state))),t&&r===null)throw Error(i(528,``));return s}if(t&&r!==null)throw Error(i(529,``));return null;case`script`:return t=n.async,n=n.src,typeof n==`string`&&t&&typeof t!=`function`&&typeof t!=`symbol`?(t=Mf(n),n=pt(a).hoistableScripts,r=n.get(t),r||(r={type:`script`,instance:null,count:0,state:null},n.set(t,r)),r):{type:`void`,instance:null,count:0,state:null};default:throw Error(i(444,e))}}function Of(e){return`href="`+zt(e)+`"`}function kf(e){return`link[rel="stylesheet"][`+e+`]`}function Af(e){return d({},e,{"data-precedence":e.precedence,precedence:null})}function jf(e,t,n,r){e.querySelector(`link[rel="preload"][as="style"][`+t+`]`)?r.loading=1:(t=e.createElement(`link`),r.preload=t,t.addEventListener(`load`,function(){return r.loading|=1}),t.addEventListener(`error`,function(){return r.loading|=2}),Ld(t,`link`,n),mt(t),e.head.appendChild(t))}function Mf(e){return`[src="`+zt(e)+`"]`}function Nf(e){return`script[async]`+e}function Pf(e,t,n){if(t.count++,t.instance===null)switch(t.type){case`style`:var r=e.querySelector(`style[data-href~="`+zt(n.href)+`"]`);if(r)return t.instance=r,mt(r),r;var a=d({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return r=(e.ownerDocument||e).createElement(`style`),mt(r),Ld(r,`style`,a),Ff(r,n.precedence,e),t.instance=r;case`stylesheet`:a=Of(n.href);var o=e.querySelector(kf(a));if(o)return t.state.loading|=4,t.instance=o,mt(o),o;r=Af(n),(a=ff.get(a))&&If(r,a),o=(e.ownerDocument||e).createElement(`link`),mt(o);var s=o;return s._p=new Promise(function(e,t){s.onload=e,s.onerror=t}),Ld(o,`link`,r),t.state.loading|=4,Ff(o,n.precedence,e),t.instance=o;case`script`:return o=Mf(n.src),(a=e.querySelector(Nf(o)))?(t.instance=a,mt(a),a):(r=n,(a=ff.get(o))&&(r=d({},n),Lf(r,a)),e=e.ownerDocument||e,a=e.createElement(`script`),mt(a),Ld(a,`link`,r),e.head.appendChild(a),t.instance=a);case`void`:return null;default:throw Error(i(443,t.type))}else t.type===`stylesheet`&&!(t.state.loading&4)&&(r=t.instance,t.state.loading|=4,Ff(r,n.precedence,e));return t.instance}function Ff(e,t,n){for(var r=n.querySelectorAll(`link[rel="stylesheet"][data-precedence],style[data-precedence]`),i=r.length?r[r.length-1]:null,a=i,o=0;o<r.length;o++){var s=r[o];if(s.dataset.precedence===t)a=s;else if(a!==i)break}a?a.parentNode.insertBefore(e,a.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function If(e,t){e.crossOrigin??=t.crossOrigin,e.referrerPolicy??=t.referrerPolicy,e.title??=t.title}function Lf(e,t){e.crossOrigin??=t.crossOrigin,e.referrerPolicy??=t.referrerPolicy,e.integrity??=t.integrity}var Rf=null;function zf(e,t,n){if(Rf===null){var r=new Map,i=Rf=new Map;i.set(n,r)}else i=Rf,r=i.get(n),r||(r=new Map,i.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),i=0;i<n.length;i++){var a=n[i];if(!(a[ct]||a[tt]||e===`link`&&a.getAttribute(`rel`)===`stylesheet`)&&a.namespaceURI!==`http://www.w3.org/2000/svg`){var o=a.getAttribute(t)||``;o=e+o;var s=r.get(o);s?s.push(a):r.set(o,[a])}}return r}function Bf(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t===`title`?e.querySelector(`head > title`):null)}function Vf(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case`meta`:case`title`:return!0;case`style`:if(typeof t.precedence!=`string`||typeof t.href!=`string`||t.href===``)break;return!0;case`link`:if(typeof t.rel!=`string`||typeof t.href!=`string`||t.href===``||t.onLoad||t.onError)break;switch(t.rel){case`stylesheet`:return e=t.disabled,typeof t.precedence==`string`&&e==null;default:return!0}case`script`:if(t.async&&typeof t.async!=`function`&&typeof t.async!=`symbol`&&!t.onLoad&&!t.onError&&t.src&&typeof t.src==`string`)return!0}return!1}function Hf(e){return e.type===`stylesheet`&&!(e.state.loading&3)?!1:!0}var Uf=null;function Wf(){}function Gf(e,t,n){if(Uf===null)throw Error(i(475));var r=Uf;if(t.type===`stylesheet`&&(typeof n.media!=`string`||!1!==matchMedia(n.media).matches)&&!(t.state.loading&4)){if(t.instance===null){var a=Of(n.href),o=e.querySelector(kf(a));if(o){e=o._p,typeof e==`object`&&e&&typeof e.then==`function`&&(r.count++,r=qf.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=o,mt(o);return}o=e.ownerDocument||e,n=Af(n),(a=ff.get(a))&&If(n,a),o=o.createElement(`link`),mt(o);var s=o;s._p=new Promise(function(e,t){s.onload=e,s.onerror=t}),Ld(o,`link`,n),t.instance=o}r.stylesheets===null&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&!(t.state.loading&3)&&(r.count++,t=qf.bind(r),e.addEventListener(`load`,t),e.addEventListener(`error`,t))}}function Kf(){if(Uf===null)throw Error(i(475));var e=Uf;return e.stylesheets&&e.count===0&&Yf(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Yf(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function qf(){if(this.count--,this.count===0){if(this.stylesheets)Yf(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Jf=null;function Yf(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Jf=new Map,t.forEach(Xf,e),Jf=null,qf.call(e))}function Xf(e,t){if(!(t.state.loading&4)){var n=Jf.get(e);if(n)var r=n.get(null);else{n=new Map,Jf.set(e,n);for(var i=e.querySelectorAll(`link[data-precedence],style[data-precedence]`),a=0;a<i.length;a++){var o=i[a];(o.nodeName===`LINK`||o.getAttribute(`media`)!==`not all`)&&(n.set(o.dataset.precedence,o),r=o)}r&&n.set(null,r)}i=t.instance,o=i.getAttribute(`data-precedence`),a=n.get(o)||r,a===r&&n.set(null,i),n.set(o,i),this.count++,r=qf.bind(this),i.addEventListener(`load`,r),i.addEventListener(`error`,r),a?a.parentNode.insertBefore(i,a.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(i,e.firstChild)),t.state.loading|=4}}var Zf={$$typeof:b,Provider:null,Consumer:null,_currentValue:ie,_currentValue2:ie,_threadCount:0};function Qf(e,t,n,r,i,a,o,s){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Ge(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ge(0),this.hiddenUpdates=Ge(null),this.identifierPrefix=r,this.onUncaughtError=i,this.onCaughtError=a,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=s,this.incompleteTransitions=new Map}function $f(e,t,n,r,i,a,o,s,c,l,u,d){return e=new Qf(e,t,n,o,s,c,l,d),t=1,!0===a&&(t|=24),a=xi(3,null,null,t),e.current=a,a.stateNode=e,t=ga(),t.refCount++,e.pooledCache=t,t.refCount++,a.memoizedState={element:r,isDehydrated:n,cache:t},Va(a),e}function ep(e){return e?(e=yi,e):yi}function tp(e,t,n,r,i,a){i=ep(i),r.context===null?r.context=i:r.pendingContext=i,r=Ua(t),r.payload={element:n},a=a===void 0?null:a,a!==null&&(r.callback=a),n=Wa(e,r,t),n!==null&&(vu(n,e,t),Ga(n,e,t))}function np(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function rp(e,t){np(e,t),(e=e.alternate)&&np(e,t)}function ip(e){if(e.tag===13){var t=gi(e,67108864);t!==null&&vu(t,e,67108864),rp(e,67108864)}}var ap=!0;function op(e,t,n,r){var i=k.T;k.T=null;var a=A.p;try{A.p=2,cp(e,t,n,r)}finally{A.p=a,k.T=i}}function sp(e,t,n,r){var i=k.T;k.T=null;var a=A.p;try{A.p=8,cp(e,t,n,r)}finally{A.p=a,k.T=i}}function cp(e,t,n,r){if(ap){var i=lp(r);if(i===null)Ed(e,t,r,up,n),xp(e,r);else if(Cp(i,e,t,n,r))r.stopPropagation();else if(xp(e,r),t&4&&-1<bp.indexOf(e)){for(;i!==null;){var a=dt(i);if(a!==null)switch(a.tag){case 3:if(a=a.stateNode,a.current.memoizedState.isDehydrated){var o=ze(a.pendingLanes);if(o!==0){var s=a;for(s.pendingLanes|=2,s.entangledLanes|=2;o;){var c=1<<31-Ne(o);s.entanglements[1]|=c,o&=~c}id(a),!(W&6)&&(iu=xe()+500,ad(0,!1))}}break;case 13:s=gi(a,2),s!==null&&vu(s,a,2),Cu(),rp(a,2)}if(a=lp(r),a===null&&Ed(e,t,r,up,n),a===i)break;i=a}i!==null&&r.stopPropagation()}else Ed(e,t,r,null,n)}}function lp(e){return e=F(e),dp(e)}var up=null;function dp(e){if(up=null,e=ut(e),e!==null){var t=o(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=s(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return up=e,null}function fp(e){switch(e){case`beforetoggle`:case`cancel`:case`click`:case`close`:case`contextmenu`:case`copy`:case`cut`:case`auxclick`:case`dblclick`:case`dragend`:case`dragstart`:case`drop`:case`focusin`:case`focusout`:case`input`:case`invalid`:case`keydown`:case`keypress`:case`keyup`:case`mousedown`:case`mouseup`:case`paste`:case`pause`:case`play`:case`pointercancel`:case`pointerdown`:case`pointerup`:case`ratechange`:case`reset`:case`resize`:case`seeked`:case`submit`:case`toggle`:case`touchcancel`:case`touchend`:case`touchstart`:case`volumechange`:case`change`:case`selectionchange`:case`textInput`:case`compositionstart`:case`compositionend`:case`compositionupdate`:case`beforeblur`:case`afterblur`:case`beforeinput`:case`blur`:case`fullscreenchange`:case`focus`:case`hashchange`:case`popstate`:case`select`:case`selectstart`:return 2;case`drag`:case`dragenter`:case`dragexit`:case`dragleave`:case`dragover`:case`mousemove`:case`mouseout`:case`mouseover`:case`pointermove`:case`pointerout`:case`pointerover`:case`scroll`:case`touchmove`:case`wheel`:case`mouseenter`:case`mouseleave`:case`pointerenter`:case`pointerleave`:return 8;case`message`:switch(Se()){case Ce:return 2;case we:return 8;case Te:case Ee:return 32;case De:return 268435456;default:return 32}default:return 32}}var pp=!1,mp=null,hp=null,gp=null,_p=new Map,vp=new Map,yp=[],bp=`mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset`.split(` `);function xp(e,t){switch(e){case`focusin`:case`focusout`:mp=null;break;case`dragenter`:case`dragleave`:hp=null;break;case`mouseover`:case`mouseout`:gp=null;break;case`pointerover`:case`pointerout`:_p.delete(t.pointerId);break;case`gotpointercapture`:case`lostpointercapture`:vp.delete(t.pointerId)}}function Sp(e,t,n,r,i,a){return e===null||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[i]},t!==null&&(t=dt(t),t!==null&&ip(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Cp(e,t,n,r,i){switch(t){case`focusin`:return mp=Sp(mp,e,t,n,r,i),!0;case`dragenter`:return hp=Sp(hp,e,t,n,r,i),!0;case`mouseover`:return gp=Sp(gp,e,t,n,r,i),!0;case`pointerover`:var a=i.pointerId;return _p.set(a,Sp(_p.get(a)||null,e,t,n,r,i)),!0;case`gotpointercapture`:return a=i.pointerId,vp.set(a,Sp(vp.get(a)||null,e,t,n,r,i)),!0}return!1}function wp(e){var t=ut(e.target);if(t!==null){var n=o(t);if(n!==null){if(t=n.tag,t===13){if(t=s(n),t!==null){e.blockedOn=t,$e(e.priority,function(){if(n.tag===13){var e=gu();e=Xe(e);var t=gi(n,e);t!==null&&vu(t,n,e),rp(n,e)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Tp(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=lp(e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);en=r,n.target.dispatchEvent(r),en=null}else return t=dt(n),t!==null&&ip(t),e.blockedOn=n,!1;t.shift()}return!0}function Ep(e,t,n){Tp(e)&&n.delete(t)}function Dp(){pp=!1,mp!==null&&Tp(mp)&&(mp=null),hp!==null&&Tp(hp)&&(hp=null),gp!==null&&Tp(gp)&&(gp=null),_p.forEach(Ep),vp.forEach(Ep)}function Op(e,n){e.blockedOn===n&&(e.blockedOn=null,pp||(pp=!0,t.unstable_scheduleCallback(t.unstable_NormalPriority,Dp)))}var kp=null;function Ap(e){kp!==e&&(kp=e,t.unstable_scheduleCallback(t.unstable_NormalPriority,function(){kp===e&&(kp=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],i=e[t+2];if(typeof r!=`function`){if(dp(r||n)===null)continue;break}var a=dt(n);a!==null&&(e.splice(t,3),t-=3,vs(a,{pending:!0,data:i,method:n.method,action:r},r,i))}}))}function jp(e){function t(t){return Op(t,e)}mp!==null&&Op(mp,e),hp!==null&&Op(hp,e),gp!==null&&Op(gp,e),_p.forEach(t),vp.forEach(t);for(var n=0;n<yp.length;n++){var r=yp[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<yp.length&&(n=yp[0],n.blockedOn===null);)wp(n),n.blockedOn===null&&yp.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(r=0;r<n.length;r+=3){var i=n[r],a=n[r+1],o=i[nt]||null;if(typeof a==`function`)o||Ap(n);else if(o){var s=null;if(a&&a.hasAttribute(`formAction`)){if(i=a,o=a[nt]||null)s=o.formAction;else if(dp(i)!==null)continue}else s=o.action;typeof s==`function`?n[r+1]=s:(n.splice(r,3),r-=3),Ap(n)}}}function Mp(e){this._internalRoot=e}Np.prototype.render=Mp.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(i(409));var n=t.current,r=gu();tp(n,r,e,t,null,null)},Np.prototype.unmount=Mp.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;tp(e.current,2,null,e,null,null),Cu(),t[rt]=null}};function Np(e){this._internalRoot=e}Np.prototype.unstable_scheduleHydration=function(e){if(e){var t=Qe();e={blockedOn:null,target:e,priority:t};for(var n=0;n<yp.length&&t!==0&&t<yp[n].priority;n++);yp.splice(n,0,e),n===0&&wp(e)}};var Pp=n.version;if(Pp!==`19.1.0`)throw Error(i(527,Pp,`19.1.0`));A.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render==`function`?Error(i(188)):(e=Object.keys(e).join(`,`),Error(i(268,e)));return e=l(t),e=e===null?null:u(e),e=e===null?null:e.stateNode,e};var Fp={bundleType:0,version:`19.1.0`,rendererPackageName:`react-dom`,currentDispatcherRef:k,reconcilerVersion:`19.1.0`};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<`u`){var Ip=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ip.isDisabled&&Ip.supportsFiber)try{Ae=Ip.inject(Fp),je=Ip}catch{}}exports.createRoot=function(e,t){if(!a(e))throw Error(i(299));var n=!1,r=``,o=ic,s=ac,c=oc,l=null;return t!=null&&(!0===t.unstable_strictMode&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onUncaughtError!==void 0&&(o=t.onUncaughtError),t.onCaughtError!==void 0&&(s=t.onCaughtError),t.onRecoverableError!==void 0&&(c=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(l=t.unstable_transitionCallbacks)),t=$f(e,1,!1,null,null,n,r,o,s,c,l,null),e[rt]=t.current,wd(e),new Mp(t)},exports.hydrateRoot=function(e,t,n){if(!a(e))throw Error(i(299));var r=!1,o=``,s=ic,c=ac,l=oc,u=null,d=null;return n!=null&&(!0===n.unstable_strictMode&&(r=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onUncaughtError!==void 0&&(s=n.onUncaughtError),n.onCaughtError!==void 0&&(c=n.onCaughtError),n.onRecoverableError!==void 0&&(l=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(u=n.unstable_transitionCallbacks),n.formState!==void 0&&(d=n.formState)),t=$f(e,1,!0,t,n??null,r,o,s,c,l,u,d),t.context=ep(null),n=t.current,r=gu(),r=Xe(r),o=Ua(r),o.callback=null,Wa(n,o,r),n=r,t.current.lanes=n,Ke(t,n),id(t),e[rt]=t.current,wd(e),new Np(t)},exports.version=`19.1.0`}),pr=s((exports,t)=>{function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>`u`||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!=`function`))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(e){console.error(e)}}n(),t.exports=fr()}),mr=Object.freeze({InvalidProxyUrlErrorMessage:`The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})`,InvalidPublishableKeyErrorMessage:`The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,MissingPublishableKeyErrorMessage:`Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,MissingSecretKeyErrorMessage:`Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,MissingClerkProvider:`{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider`});function hr({packageName:e,customMessages:t}){let n=e;function r(e,t){if(!t)return`${n}: ${e}`;let r=e,i=e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);for(let e of i){let n=(t[e[1]]||``).toString();r=r.replace(`{{${e[1]}}}`,n)}return`${n}: ${r}`}let i={...mr,...t};return{setPackageName({packageName:e}){return typeof e==`string`&&(n=e),this},setMessages({customMessages:e}){return Object.assign(i,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(r(i.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(r(i.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(r(i.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(r(i.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(r(i.MissingClerkProvider,e))},throw(e){throw Error(r(e))}}}var gr=Object.defineProperty,_r=Object.getOwnPropertyDescriptor,vr=Object.getOwnPropertyNames,yr=Object.prototype.hasOwnProperty,br=e=>{throw TypeError(e)},xr=(e,t)=>{for(var n in t)gr(e,n,{get:t[n],enumerable:!0})},Sr=(e,t,n,r)=>{if(t&&typeof t==`object`||typeof t==`function`)for(let i of vr(t))!yr.call(e,i)&&i!==n&&gr(e,i,{get:()=>t[i],enumerable:!(r=_r(t,i))||r.enumerable});return e},Cr=(e,t,n)=>(Sr(e,t,`default`),n&&Sr(n,t,`default`)),wr=(e,t,n)=>t.has(e)||br(`Cannot `+n),Tr=(e,t,n)=>(wr(e,t,`read from private field`),n?n.call(e):t.get(e)),Er=(e,t,n,r)=>(wr(e,t,`write to private field`),r?r.call(e,n):t.set(e,n),n),Dr=(e,t,n)=>(wr(e,t,`access private method`),n),Or={strict_mfa:{afterMinutes:10,level:`multi_factor`},strict:{afterMinutes:10,level:`second_factor`},moderate:{afterMinutes:60,level:`second_factor`},lax:{afterMinutes:1440,level:`second_factor`}},kr=new Set([`first_factor`,`second_factor`,`multi_factor`]),Ar=new Set([`strict_mfa`,`strict`,`moderate`,`lax`]),jr=e=>typeof e==`number`&&e>0,Mr=e=>kr.has(e),Nr=e=>Ar.has(e),Pr=e=>e.replace(/^(org:)*/,`org:`),Fr=(e,t)=>{let{orgId:n,orgRole:r,orgPermissions:i}=t;return!e.role&&!e.permission||!n||!r||!i?null:e.permission?i.includes(Pr(e.permission)):e.role?Pr(r)===Pr(e.role):null},Ir=(e,t)=>{let{org:n,user:r}=Rr(e),[i,a]=t.split(`:`),o=a||i;return i===`org`?n.includes(o):i===`user`?r.includes(o):[...n,...r].includes(o)},Lr=(e,t)=>{let{features:n,plans:r}=t;return e.feature&&n?Ir(n,e.feature):e.plan&&r?Ir(r,e.plan):null},Rr=e=>{let t=e?e.split(`,`).map(e=>e.trim()):[];return{org:t.filter(e=>e.split(`:`)[0].includes(`o`)).map(e=>e.split(`:`)[1]),user:t.filter(e=>e.split(`:`)[0].includes(`u`)).map(e=>e.split(`:`)[1])}},zr=e=>{if(!e)return!1;let t=e=>typeof e==`string`?Or[e]:e,n=typeof e==`string`&&Nr(e),r=typeof e==`object`&&Mr(e.level)&&jr(e.afterMinutes);return n||r?t.bind(null,e):!1},Br=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let n=zr(e.reverification);if(!n)return null;let{level:r,afterMinutes:i}=n(),[a,o]=t,s=a===-1?null:i>a,c=o===-1?null:i>o;switch(r){case`first_factor`:return s;case`second_factor`:return o===-1?s:c;case`multi_factor`:return o===-1?s:s&&c}},Vr=e=>t=>{if(!e.userId)return!1;let n=Lr(t,e),r=Fr(t,e),i=Br(t,e);return[n||r,i].some(e=>e===null)?[n||r,i].some(e=>e===!0):[n||r,i].every(e=>e===!0)},Hr=({authObject:{sessionId:e,sessionStatus:t,userId:n,actor:r,orgId:i,orgRole:a,orgSlug:o,signOut:s,getToken:c,has:l,sessionClaims:u},options:{treatPendingAsSignedOut:d=!0}})=>{if(e===void 0&&n===void 0)return{isLoaded:!1,isSignedIn:void 0,sessionId:e,sessionClaims:void 0,userId:n,actor:void 0,orgId:void 0,orgRole:void 0,orgSlug:void 0,has:void 0,signOut:s,getToken:c};if(e===null&&n===null)return{isLoaded:!0,isSignedIn:!1,sessionId:e,userId:n,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:s,getToken:c};if(d&&t===`pending`)return{isLoaded:!0,isSignedIn:!1,sessionId:null,userId:null,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:s,getToken:c};if(e&&u&&n&&i&&a)return{isLoaded:!0,isSignedIn:!0,sessionId:e,sessionClaims:u,userId:n,actor:r||null,orgId:i,orgRole:a,orgSlug:o||null,has:l,signOut:s,getToken:c};if(e&&u&&n&&!i)return{isLoaded:!0,isSignedIn:!0,sessionId:e,sessionClaims:u,userId:n,actor:r||null,orgId:null,orgRole:null,orgSlug:null,has:l,signOut:s,getToken:c}};function Ur(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,``)):``}function Wr(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):``}var Gr=e=>{let t=n=>{if(!n)return n;if(Array.isArray(n))return n.map(e=>typeof e==`object`||Array.isArray(e)?t(e):e);let r={...n},i=Object.keys(r);for(let n of i){let i=e(n.toString());i!==n&&(r[i]=r[n],delete r[n]),typeof r[i]==`object`&&(r[i]=t(r[i]))}return r};return t},Kr=Gr(Wr),qr=Gr(Ur);function Jr(e){if(typeof e==`boolean`)return e;if(e==null)return!1;if(typeof e==`string`){if(e.toLowerCase()===`true`)return!0;if(e.toLowerCase()===`false`)return!1}let t=parseInt(e,10);return isNaN(t)?!1:t>0}var Yr=e=>typeof atob<`u`&&typeof atob==`function`?atob(e):typeof global<`u`&&global.Buffer?new global.Buffer(e,`base64`).toString():e,Xr=[`.lcl.dev`,`.stg.dev`,`.lclstage.dev`,`.stgstage.dev`,`.dev.lclclerk.com`,`.stg.lclclerk.com`,`.accounts.lclclerk.com`,`accountsstage.dev`,`accounts.dev`],Zr=`pk_live_`,Qr=`pk_test_`;function $r(e){if(!e.endsWith(`$`))return!1;let t=e.slice(0,-1);return t.includes(`$`)?!1:t.includes(`.`)}function ei(e,t={}){if(e||=``,!e||!ti(e)){if(t.fatal&&!e)throw Error(`Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys`);if(t.fatal&&!ti(e))throw Error(`Publishable key not valid.`);return null}let n=e.startsWith(Zr)?`production`:`development`,r;try{r=Yr(e.split(`_`)[2])}catch{if(t.fatal)throw Error(`Publishable key not valid: Failed to decode key.`);return null}if(!$r(r)){if(t.fatal)throw Error(`Publishable key not valid: Decoded key has invalid format.`);return null}let i=r.slice(0,-1);return t.proxyUrl?i=t.proxyUrl:n!==`development`&&t.domain&&t.isSatellite&&(i=`clerk.${t.domain}`),{instanceType:n,frontendApi:i}}function ti(e=``){try{let t=e.startsWith(Zr)||e.startsWith(Qr);if(!t)return!1;let n=e.split(`_`);if(n.length!==3)return!1;let r=n[2];if(!r)return!1;let i=Yr(r);return $r(i)}catch{return!1}}function ni(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let n=typeof t==`string`?t:t.hostname,r=e.get(n);return r===void 0&&(r=Xr.some(e=>n.endsWith(e)),e.set(n,r)),r}}}var ri,ii,ai,oi,si,ci;ri=new WeakMap,ii=new WeakMap,ai=new WeakSet,oi=function(e){let{sk:t,pk:n,payload:r,...i}=e,a={...r,...i};return JSON.stringify(Object.keys({...r,...i}).sort().map(e=>a[e]))},si=function(){let e=localStorage.getItem(Tr(this,ri));return e?JSON.parse(e):{}},ci=function(){if(typeof window>`u`)return!1;let e=window.localStorage;if(!e)return!1;try{let t=`test`;return e.setItem(t,t),e.removeItem(t),!0}catch(t){let n=t instanceof DOMException&&(t.name===`QuotaExceededError`||t.name===`NS_ERROR_DOM_QUOTA_REACHED`);return n&&e.length>0&&e.removeItem(Tr(this,ri)),!1}};var li,ui,di,fi,pi,mi,hi,gi,_i,vi,yi,bi,xi;li=new WeakMap,ui=new WeakMap,di=new WeakMap,fi=new WeakMap,pi=new WeakMap,mi=new WeakSet,hi=function(e,t){return this.isEnabled&&!this.isDebug&&Dr(this,mi,gi).call(this,e,t)},gi=function(e,t){let n=Math.random(),r=n<=Tr(this,li).samplingRate&&(t===void 0||n<=t);return r?!Tr(this,ui).isEventThrottled(e):!1},_i=function(){if(typeof window>`u`){Dr(this,mi,vi).call(this);return}let e=Tr(this,fi).length>=Tr(this,li).maxBufferSize;if(e){if(Tr(this,pi)){let e=typeof cancelIdleCallback<`u`?cancelIdleCallback:clearTimeout;e(Tr(this,pi))}Dr(this,mi,vi).call(this);return}Tr(this,pi)||(`requestIdleCallback`in window?Er(this,pi,requestIdleCallback(()=>{Dr(this,mi,vi).call(this)})):Er(this,pi,setTimeout(()=>{Dr(this,mi,vi).call(this)},0)))},vi=function(){fetch(new URL(`/v1/event`,Tr(this,li).endpoint),{method:`POST`,body:JSON.stringify({events:Tr(this,fi)}),headers:{"Content-Type":`application/json`}}).catch(()=>void 0).then(()=>{Er(this,fi,[])}).catch(()=>void 0)},yi=function(e,t){this.isDebug&&(console.groupCollapsed===void 0?console.log(`[clerk/telemetry]`,e,t):(console.groupCollapsed(`[clerk/telemetry]`,e),console.log(t),console.groupEnd()))},bi=function(){let e={name:Tr(this,di).sdk,version:Tr(this,di).sdkVersion};return typeof window<`u`&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},xi=function(e,t){let n=Dr(this,mi,bi).call(this);return{event:e,cv:Tr(this,di).clerkVersion??``,it:Tr(this,di).instanceType??``,sdk:n.name,sdkv:n.version,...Tr(this,di).publishableKey?{pk:Tr(this,di).publishableKey}:{},...Tr(this,di).secretKey?{sk:Tr(this,di).secretKey}:{},payload:t}};var Si=`METHOD_CALLED`;function Ci(e,t){return{event:Si,payload:{method:e,...t}}}const wi=0,Ti=1,Ei=2,Di=3;var Oi=Object.prototype.hasOwnProperty;function ki(e,t){var n,r;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&ki(e[r],t[r]););return r===-1}if(!n||typeof e==`object`){for(n in r=0,e)if(Oi.call(e,n)&&++r&&!Oi.call(t,n)||!(n in t)||!ki(e[n],t[n]))return!1;return Object.keys(t).length===r}}return e!==e&&t!==t}const Ai=new WeakMap,ji=()=>{},Mi=ji(),Ni=Object,I=e=>e===Mi,Pi=e=>typeof e==`function`,Fi=(e,t)=>({...e,...t}),Ii=e=>Pi(e.then),Li={},Ri={},zi=`undefined`,Bi=typeof window!=zi,Vi=typeof document!=zi,Hi=Bi&&`Deno`in window,L=()=>Bi&&typeof window.requestAnimationFrame!=zi,Ui=(e,t)=>{let n=Ai.get(e);return[()=>!I(t)&&e.get(t)||Li,r=>{if(!I(t)){let i=e.get(t);t in Ri||(Ri[t]=i),n[5](t,Fi(i,r),i||Li)}},n[6],()=>!I(t)&&t in Ri?Ri[t]:!I(t)&&e.get(t)||Li]};let Wi=!0;const Gi=()=>Wi,[Ki,qi]=Bi&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[ji,ji],Ji=()=>{let e=Vi&&document.visibilityState;return I(e)||e!==`hidden`},Yi=e=>(Vi&&document.addEventListener(`visibilitychange`,e),Ki(`focus`,e),()=>{Vi&&document.removeEventListener(`visibilitychange`,e),qi(`focus`,e)}),Xi=e=>{let t=()=>{Wi=!0,e()},n=()=>{Wi=!1};return Ki(`online`,t),Ki(`offline`,n),()=>{qi(`online`,t),qi(`offline`,n)}},Zi={isOnline:Gi,isVisible:Ji},Qi={initFocus:Yi,initReconnect:Xi},$i=!P.useId,ea=!Bi||Hi,ta=e=>L()?window.requestAnimationFrame(e):setTimeout(e,1),na=ea?P.useEffect:P.useLayoutEffect,ra=typeof navigator<`u`&&navigator.connection,ia=!ea&&ra&&([`slow-2g`,`2g`].includes(ra.effectiveType)||ra.saveData),aa=new WeakMap,oa=(e,t)=>Ni.prototype.toString.call(e)===`[object ${t}]`;let sa=0;const ca=e=>{let t=typeof e,n=oa(e,`Date`),r=oa(e,`RegExp`),i=oa(e,`Object`),a,o;if(Ni(e)===e&&!n&&!r){if(a=aa.get(e),a)return a;if(a=++sa+`~`,aa.set(e,a),Array.isArray(e)){for(a=`@`,o=0;o<e.length;o++)a+=ca(e[o])+`,`;aa.set(e,a)}if(i){a=`#`;let t=Ni.keys(e).sort();for(;!I(o=t.pop());)I(e[o])||(a+=o+`:`+ca(e[o])+`,`);aa.set(e,a)}}else a=n?e.toJSON():t==`symbol`?e.toString():t==`string`?JSON.stringify(e):``+e;return a},la=e=>{if(Pi(e))try{e=e()}catch{e=``}let t=e;return e=typeof e==`string`?e:(Array.isArray(e)?e.length:e)?ca(e):``,[e,t]};let ua=0;const da=()=>++ua;async function fa(...e){let[t,n,r,i]=e,a=Fi({populateCache:!0,throwOnError:!0},typeof i==`boolean`?{revalidate:i}:i||{}),o=a.populateCache,s=a.rollbackOnError,c=a.optimisticData,l=e=>typeof s==`function`?s(e):s!==!1,u=a.throwOnError;if(Pi(n)){let e=n,r=[],i=t.keys();for(let n of i)!/^\$(inf|sub)\$/.test(n)&&e(t.get(n)._k)&&r.push(n);return Promise.all(r.map(d))}return d(n);async function d(n){let[i]=la(n);if(!i)return;let[s,d]=Ui(t,i),[f,p,m,h]=Ai.get(t),g=()=>{let e=f[i],t=Pi(a.revalidate)?a.revalidate(s().data,n):a.revalidate!==!1;return t&&(delete m[i],delete h[i],e&&e[0])?e[0](Ei).then(()=>s().data):s().data};if(e.length<3)return g();let _=r,v,y=da();p[i]=[y,0];let b=!I(c),x=s(),S=x.data,C=x._c,w=I(C)?S:C;if(b&&(c=Pi(c)?c(w,S):c,d({data:c,_c:w})),Pi(_))try{_=_(w)}catch(e){v=e}if(_&&Ii(_))if(_=await _.catch(e=>{v=e}),y!==p[i][0]){if(v)throw v;return _}else v&&b&&l(v)&&(o=!0,d({data:w,_c:Mi}));if(o&&!v)if(Pi(o)){let e=o(_,w);d({data:e,error:Mi,_c:Mi})}else d({data:_,error:Mi,_c:Mi});if(p[i][1]=da(),Promise.resolve(g()).then(()=>{d({_c:Mi})}),v){if(u)throw v;return}return _}}const pa=(e,t)=>{for(let n in e)e[n][0]&&e[n][0](t)},ma=(e,t)=>{if(!Ai.has(e)){let n=Fi(Qi,t),r=Object.create(null),i=fa.bind(Mi,e),a=ji,o=Object.create(null),s=(e,t)=>{let n=o[e]||[];return o[e]=n,n.push(t),()=>n.splice(n.indexOf(t),1)},c=(t,n,r)=>{e.set(t,n);let i=o[t];if(i)for(let e of i)e(n,r)},l=()=>{if(!Ai.has(e)&&(Ai.set(e,[r,Object.create(null),Object.create(null),Object.create(null),i,c,s]),!ea)){let t=n.initFocus(setTimeout.bind(Mi,pa.bind(Mi,r,wi))),i=n.initReconnect(setTimeout.bind(Mi,pa.bind(Mi,r,Ti)));a=()=>{t&&t(),i&&i(),Ai.delete(e)}}};return l(),[e,i,l,a]}return[e,Ai.get(e)[4]]},ha=(e,t,n,r,i)=>{let a=n.errorRetryCount,o=i.retryCount,s=~~((Math.random()+.5)*(1<<(o<8?o:8)))*n.errorRetryInterval;!I(a)&&o>a||setTimeout(r,s,i)},ga=ki,[_a,va]=ma(new Map),ya=Fi({onLoadingSlow:ji,onSuccess:ji,onError:ji,onErrorRetry:ha,onDiscarded:ji,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:ia?1e4:5e3,focusThrottleInterval:5*1e3,dedupingInterval:2*1e3,loadingTimeout:ia?5e3:3e3,compare:ga,isPaused:()=>!1,cache:_a,mutate:va,fallback:{}},Zi),ba=(e,t)=>{let n=Fi(e,t);if(t){let{use:r,fallback:i}=e,{use:a,fallback:o}=t;r&&a&&(n.use=r.concat(a)),i&&o&&(n.fallback=Fi(i,o))}return n},xa=(0,P.createContext)({}),Sa=e=>{let{value:t}=e,n=(0,P.useContext)(xa),r=Pi(t),i=(0,P.useMemo)(()=>r?t(n):t,[r,n,t]),a=(0,P.useMemo)(()=>r?i:ba(n,i),[r,n,i]),o=i&&i.provider,s=(0,P.useRef)(Mi);o&&!s.current&&(s.current=ma(o(a.cache||_a),i));let c=s.current;return c&&(a.cache=c[0],a.mutate=c[1]),na(()=>{if(c)return c[2]&&c[2](),c[3]},[]),(0,P.createElement)(xa.Provider,Fi(e,{value:a}))},Ca=`$inf$`,wa=Bi&&window.__SWR_DEVTOOLS_USE__,Ta=wa?window.__SWR_DEVTOOLS_USE__:[],Ea=()=>{wa&&(window.__SWR_DEVTOOLS_REACT__=P.default)},Da=e=>Pi(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(e[1]===null?e[2]:e[1])||{}],Oa=()=>Fi(ya,(0,P.useContext)(xa)),ka=(e,t)=>{let[n,r]=la(e),[,,,i]=Ai.get(_a);if(i[n])return i[n];let a=t(r);return i[n]=a,a},Aa=e=>(t,n,r)=>{let i=n&&((...e)=>{let[r]=la(t),[,,,i]=Ai.get(_a);if(r.startsWith(Ca))return n(...e);let a=i[r];return I(a)?n(...e):(delete i[r],a)});return e(t,i,r)},ja=Ta.concat(Aa),Ma=e=>function(...t){let n=Oa(),[r,i,a]=Da(t),o=ba(n,a),s=e,{use:c}=o,l=(c||[]).concat(ja);for(let e=l.length;e--;)s=l[e](s);return s(r,i||o.fetcher||null,o)},Na=(e,t,n)=>{let r=t[e]||(t[e]=[]);return r.push(n),()=>{let e=r.indexOf(n);e>=0&&(r[e]=r[r.length-1],r.pop())}},Pa=(e,t)=>(...n)=>{let[r,i,a]=Da(n),o=(a.use||[]).concat(t);return e(r,i,{...a,use:o})};Ea();var Fa={};c(Fa,{SWRConfig:()=>Za,default:()=>Qa,mutate:()=>va,preload:()=>ka,unstable_serialize:()=>qa,useSWRConfig:()=>Oa});var Ia=u(Kt(),1);const La=()=>{},Ra=La(),za=Object,Ba=e=>e===Ra,Va=e=>typeof e==`function`,Ha=new WeakMap,Ua=(e,t)=>za.prototype.toString.call(e)===`[object ${t}]`;let Wa=0;const Ga=e=>{let t=typeof e,n=Ua(e,`Date`),r=Ua(e,`RegExp`),i=Ua(e,`Object`),a,o;if(za(e)===e&&!n&&!r){if(a=Ha.get(e),a)return a;if(a=++Wa+`~`,Ha.set(e,a),Array.isArray(e)){for(a=`@`,o=0;o<e.length;o++)a+=Ga(e[o])+`,`;Ha.set(e,a)}if(i){a=`#`;let t=za.keys(e).sort();for(;!Ba(o=t.pop());)Ba(e[o])||(a+=o+`:`+Ga(e[o])+`,`);Ha.set(e,a)}}else a=n?e.toJSON():t==`symbol`?e.toString():t==`string`?JSON.stringify(e):``+e;return a},Ka=e=>{if(Va(e))try{e=e()}catch{e=``}let t=e;return e=typeof e==`string`?e:(Array.isArray(e)?e.length:e)?Ga(e):``,[e,t]},qa=e=>Ka(e)[0],Ja=P.use||(e=>{switch(e.status){case`pending`:throw e;case`fulfilled`:return e.value;case`rejected`:throw e.reason;default:throw e.status=`pending`,e.then(t=>{e.status=`fulfilled`,e.value=t},t=>{e.status=`rejected`,e.reason=t}),e}}),Ya={dedupe:!0},Xa=(e,t,n)=>{let{cache:r,compare:i,suspense:a,fallbackData:o,revalidateOnMount:s,revalidateIfStale:c,refreshInterval:l,refreshWhenHidden:u,refreshWhenOffline:d,keepPreviousData:f}=n,[p,m,h,g]=Ai.get(r),[_,v]=la(e),y=(0,P.useRef)(!1),b=(0,P.useRef)(!1),x=(0,P.useRef)(_),S=(0,P.useRef)(t),C=(0,P.useRef)(n),w=()=>C.current,T=()=>w().isVisible()&&w().isOnline(),[E,ee,D,te]=Ui(r,_),ne=(0,P.useRef)({}).current,re=I(o)?I(n.fallback)?Mi:n.fallback[_]:o,O=(e,t)=>{for(let n in ne){let r=n;if(r===`data`){if(!i(e[r],t[r])&&(!I(e[r])||!i(ce,t[r])))return!1}else if(t[r]!==e[r])return!1}return!0},k=(0,P.useMemo)(()=>{let e=(()=>!_||!t?!1:I(s)?w().isPaused()||a?!1:c!==!1:s)(),n=t=>{let n=Fi(t);return delete n._k,e?{isValidating:!0,isLoading:!0,...n}:n},r=E(),i=te(),o=n(r),l=r===i?o:n(i),u=o;return[()=>{let e=n(E()),t=O(e,u);return t?(u.data=e.data,u.isLoading=e.isLoading,u.isValidating=e.isValidating,u.error=e.error,u):(u=e,e)},()=>l]},[r,_]),A=(0,Ia.useSyncExternalStore)((0,P.useCallback)(e=>D(_,(t,n)=>{O(n,t)||e()}),[r,_]),k[0],k[1]),ie=!y.current,ae=p[_]&&p[_].length>0,oe=A.data,se=I(oe)?re&&Ii(re)?Ja(re):re:oe,j=A.error,M=(0,P.useRef)(se),ce=f?I(oe)?I(M.current)?se:M.current:oe:se,le=(()=>ae&&!I(j)?!1:ie&&!I(s)?s:w().isPaused()?!1:a?I(se)?!1:c:I(se)||c)(),ue=!!(_&&t&&ie&&le),de=I(A.isValidating)?ue:A.isValidating,fe=I(A.isLoading)?ue:A.isLoading,pe=(0,P.useCallback)(async e=>{let t=S.current;if(!_||!t||b.current||w().isPaused())return!1;let r,a,o=!0,s=e||{},c=!h[_]||!s.dedupe,l=()=>$i?!b.current&&_===x.current&&y.current:_===x.current,u={isValidating:!1,isLoading:!1},d=()=>{ee(u)},f=()=>{let e=h[_];e&&e[1]===a&&delete h[_]},g={isValidating:!0};I(E().data)&&(g.isLoading=!0);try{if(c&&(ee(g),n.loadingTimeout&&I(E().data)&&setTimeout(()=>{o&&l()&&w().onLoadingSlow(_,n)},n.loadingTimeout),h[_]=[t(v),da()]),[r,a]=h[_],r=await r,c&&setTimeout(f,n.dedupingInterval),!h[_]||h[_][1]!==a)return c&&l()&&w().onDiscarded(_),!1;u.error=Mi;let e=m[_];if(!I(e)&&(a<=e[0]||a<=e[1]||e[1]===0))return d(),c&&l()&&w().onDiscarded(_),!1;let s=E().data;u.data=i(s,r)?s:r,c&&l()&&w().onSuccess(r,_,n)}catch(e){f();let t=w(),{shouldRetryOnError:n}=t;t.isPaused()||(u.error=e,c&&l()&&(t.onError(e,_,t),(n===!0||Pi(n)&&n(e))&&(!w().revalidateOnFocus||!w().revalidateOnReconnect||T())&&t.onErrorRetry(e,_,t,e=>{let t=p[_];t&&t[0]&&t[0](Di,e)},{retryCount:(s.retryCount||0)+1,dedupe:!0})))}return o=!1,d(),!0},[_,r]),me=(0,P.useCallback)((...e)=>fa(r,x.current,...e),[]);if(na(()=>{S.current=t,C.current=n,I(oe)||(M.current=oe)}),na(()=>{if(!_)return;let e=pe.bind(Mi,Ya),t=0;if(w().revalidateOnFocus){let e=Date.now();t=e+w().focusThrottleInterval}let n=(n,r={})=>{if(n==wi){let n=Date.now();w().revalidateOnFocus&&n>t&&T()&&(t=n+w().focusThrottleInterval,e())}else if(n==Ti)w().revalidateOnReconnect&&T()&&e();else if(n==Ei)return pe();else if(n==Di)return pe(r)},r=Na(_,p,n);return b.current=!1,x.current=_,y.current=!0,ee({_k:v}),le&&(I(se)||ea?e():ta(e)),()=>{b.current=!0,r()}},[_]),na(()=>{let e;function t(){let t=Pi(l)?l(E().data):l;t&&e!==-1&&(e=setTimeout(n,t))}function n(){!E().error&&(u||w().isVisible())&&(d||w().isOnline())?pe(Ya).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[l,u,d,_]),(0,P.useDebugValue)(ce),a&&I(se)&&_){if(!$i&&ea)throw Error(`Fallback data is required when using Suspense in SSR.`);S.current=t,C.current=n,b.current=!1;let e=g[_];if(!I(e)){let t=me(e);Ja(t)}if(I(j)){let e=pe(Ya);I(ce)||(e.status=`fulfilled`,e.value=!0),Ja(e)}else throw j}let he={mutate:me,get data(){return ne.data=!0,ce},get error(){return ne.error=!0,j},get isValidating(){return ne.isValidating=!0,de},get isLoading(){return ne.isLoading=!0,fe}};return he},Za=Ni.defineProperty(Sa,`defaultValue`,{value:ya}),Qa=Ma(Xa);var $a=u(Kt(),1);const eo=()=>{},to=eo(),no=Object,ro=e=>e===to,R=e=>typeof e==`function`,z=new WeakMap,io=(e,t)=>no.prototype.toString.call(e)===`[object ${t}]`;let ao=0;const oo=e=>{let t=typeof e,n=io(e,`Date`),r=io(e,`RegExp`),i=io(e,`Object`),a,o;if(no(e)===e&&!n&&!r){if(a=z.get(e),a)return a;if(a=++ao+`~`,z.set(e,a),Array.isArray(e)){for(a=`@`,o=0;o<e.length;o++)a+=oo(e[o])+`,`;z.set(e,a)}if(i){a=`#`;let t=no.keys(e).sort();for(;!ro(o=t.pop());)ro(e[o])||(a+=o+`:`+oo(e[o])+`,`);z.set(e,a)}}else a=n?e.toJSON():t==`symbol`?e.toString():t==`string`?JSON.stringify(e):``+e;return a},so=e=>{if(R(e))try{e=e()}catch{e=``}let t=e;return e=typeof e==`string`?e:(Array.isArray(e)?e.length:e)?oo(e):``,[e,t]},co=e=>so(e?e(0,null):null)[0],lo=Promise.resolve(),uo=e=>(t,n,r)=>{let i=(0,P.useRef)(!1),{cache:a,initialSize:o=1,revalidateAll:s=!1,persistSize:c=!1,revalidateFirstPage:l=!0,revalidateOnMount:u=!1,parallel:d=!1}=r,[,,,f]=Ai.get(_a),p;try{p=co(t),p&&=Ca+p}catch{}let[m,h,g]=Ui(a,p),_=(0,P.useCallback)(()=>{let e=I(m()._l)?o:m()._l;return e},[a,p,o]);(0,$a.useSyncExternalStore)((0,P.useCallback)(e=>p?g(p,()=>{e()}):()=>{},[a,p]),_,_);let v=(0,P.useCallback)(()=>{let e=m()._l;return I(e)?o:e},[p,o]),y=(0,P.useRef)(v());na(()=>{if(!i.current){i.current=!0;return}p&&h({_l:c?y.current:v()})},[p,a]);let b=u&&!i.current,x=e(p,async e=>{let i=m()._i,o=m()._r;h({_r:Mi});let c=[],u=v(),[p]=Ui(a,e),g=p().data,_=[],y=null;for(let e=0;e<u;++e){let[u,p]=la(t(e,d?null:y));if(!u)break;let[m,h]=Ui(a,u),v=m().data,x=s||i||I(v)||l&&!e&&!I(g)||b||g&&!I(g[e])&&!r.compare(g[e],v);if(n&&(typeof o==`function`?o(v,p):x)){let t=async()=>{let t=u in f;if(!t)v=await n(p);else{let e=f[u];delete f[u],v=await e}h({data:v,_k:p}),c[e]=v};d?_.push(t):await t()}else c[e]=v;d||(y=v)}return d&&await Promise.all(_.map(e=>e())),h({_i:Mi}),c},r),S=(0,P.useCallback)(function(e,t){let n=typeof t==`boolean`?{revalidate:t}:t||{},r=n.revalidate!==!1;return p?(r&&(I(e)?h({_i:!0,_r:n.revalidate}):h({_i:!1,_r:n.revalidate})),arguments.length?x.mutate(e,{...n,revalidate:r}):x.mutate()):lo},[p,a]),C=(0,P.useCallback)(e=>{if(!p)return lo;let[,n]=Ui(a,p),r;if(Pi(e)?r=e(v()):typeof e==`number`&&(r=e),typeof r!=`number`)return lo;n({_l:r}),y.current=r;let i=[],[o]=Ui(a,p),s=null;for(let e=0;e<r;++e){let[n]=la(t(e,s)),[r]=Ui(a,n),c=n?r().data:Mi;if(I(c))return S(o().data);i.push(c),s=c}return S(i)},[p,a,S,v]);return{size:v(),setSize:C,mutate:S,get data(){return x.data},get error(){return x.error},get isValidating(){return x.isValidating},get isLoading(){return x.isLoading}}},fo=Pa(Qa,uo);var po=Object.prototype.hasOwnProperty;function mo(e,t,n){for(n of e.keys())if(ho(n,t))return n}function ho(e,t){var n,r,i;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&ho(e[r],t[r]););return r===-1}if(n===Set){if(e.size!==t.size)return!1;for(r of e)if(i=r,i&&typeof i==`object`&&(i=mo(t,i),!i)||!t.has(i))return!1;return!0}if(n===Map){if(e.size!==t.size)return!1;for(r of e)if(i=r[0],i&&typeof i==`object`&&(i=mo(t,i),!i)||!ho(r[1],t.get(i)))return!1;return!0}if(n===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(n===DataView){if((r=e.byteLength)===t.byteLength)for(;r--&&e.getInt8(r)===t.getInt8(r););return r===-1}if(ArrayBuffer.isView(e)){if((r=e.byteLength)===t.byteLength)for(;r--&&e[r]===t[r];);return r===-1}if(!n||typeof e==`object`){for(n in r=0,e)if(po.call(e,n)&&++r&&!po.call(t,n)||!(n in t)||!ho(e[n],t[n]))return!1;return Object.keys(t).length===r}}return e!==e&&t!==t}function go(e,t){if(!e)throw typeof t==`string`?Error(t):Error(`${t.displayName} not found`)}var _o=(e,t)=>{let{assertCtxFn:n=go}=t||{},r=P.createContext(void 0);r.displayName=e;let i=()=>{let t=P.useContext(r);return n(t,`${e} not found`),t.value},a=()=>{let e=P.useContext(r);return e?e.value:{}};return[r,i,a]},vo={};xr(vo,{useSWR:()=>Qa,useSWRInfinite:()=>fo}),Cr(vo,Fa);var[yo,bo]=_o(`ClerkInstanceContext`),[xo,So]=_o(`UserContext`),[Co,wo]=_o(`ClientContext`),[To,Eo]=_o(`SessionContext`),Do=P.createContext({}),[Oo,ko]=_o(`CheckoutContext`),Ao=({children:e,...t})=>P.createElement(Oo.Provider,{value:{value:t}},e),[jo,Mo]=_o(`OrganizationContext`),No=({children:e,organization:t,swrConfig:n})=>P.createElement(vo.SWRConfig,{value:n},P.createElement(jo.Provider,{value:{value:{organization:t}}},e));function Po(e){let t=P.useContext(yo);if(!t){if(typeof e==`function`){e();return}throw Error(`${e} can only be used within the <ClerkProvider /> component.

Possible fixes:
1. Ensure that the <ClerkProvider /> is correctly wrapping your application where this component is used.
2. Check for multiple versions of the \`@clerk/shared\` package in your project. Use a tool like \`npm ls @clerk/shared\` to identify multiple versions, and update your dependencies to only rely on one.

Learn more: https://clerk.com/docs/components/clerk-provider`.trim())}}function Fo(e,t){let n=new Set(Object.keys(t)),r={};for(let t of Object.keys(e))n.has(t)||(r[t]=e[t]);return r}var Io=(e,t)=>{let n=typeof e==`boolean`&&e,r=(0,P.useRef)(n?t.initialPage:e?.initialPage??t.initialPage),i=(0,P.useRef)(n?t.pageSize:e?.pageSize??t.pageSize),a={};for(let r of Object.keys(t))a[r]=n?t[r]:e?.[r]??t[r];return{...a,initialPage:r.current,pageSize:i.current}},Lo={dedupingInterval:1e3*60,focusThrottleInterval:1e3*60*2},Ro=(e,t,n,r)=>{let[i,a]=(0,P.useState)(e.initialPage??1),o=(0,P.useRef)(e.initialPage??1),s=(0,P.useRef)(e.pageSize??10),c=n.enabled??!0,l=n.__experimental_mode===`cache`,u=n.infinite??!1,d=n.keepPreviousData??!1,f={...r,...e,initialPage:i,pageSize:s.current},p=!u&&c&&(l?!0:!!t),m=p?f:null,h=!l&&t?n=>{let i=Fo(n,r);return t({...e,...i})}:null,{data:g,isValidating:_,isLoading:v,error:y,mutate:b}=Qa(m,h,{keepPreviousData:d,...Lo}),{data:x,isLoading:S,isValidating:C,error:w,size:T,setSize:E,mutate:ee}=fo(t=>!u||!c?null:{...e,...r,initialPage:o.current+t,pageSize:s.current},e=>{let n=Fo(e,r);return t?.(n)},Lo),D=(0,P.useMemo)(()=>u?T:i,[u,T,i]),te=(0,P.useCallback)(e=>{if(u){E(e);return}return a(e)},[E]),ne=(0,P.useMemo)(()=>u?x?.map(e=>e?.data).flat()??[]:g?.data??[],[u,g,x]),re=(0,P.useMemo)(()=>u?x?.[x?.length-1]?.total_count||0:g?.total_count??0,[u,g,x]),O=u?S:v,k=u?C:_,A=(u?w:y)??null,ie=!!A,ae=(0,P.useCallback)(()=>{te(e=>Math.max(0,e+1))},[te]),oe=(0,P.useCallback)(()=>{te(e=>Math.max(0,e-1))},[te]),se=(o.current-1)*s.current,j=Math.ceil((re-se)/s.current),M=re-se*s.current>D*s.current,ce=(D-1)*s.current>se*s.current,le=u?e=>ee(e,{revalidate:!1}):e=>b(e,{revalidate:!1}),ue=u?()=>ee():()=>b();return{data:ne,count:re,error:A,isLoading:O,isFetching:k,isError:ie,page:D,pageCount:j,fetchPage:te,fetchNext:ae,fetchPrevious:oe,hasNextPage:M,hasPreviousPage:ce,revalidate:ue,setData:le}},zo=typeof window<`u`?P.useLayoutEffect:P.useEffect,Bo=`useUser`;function Vo(){Po(Bo);let e=So(),t=bo();return t.telemetry?.record(Ci(Bo)),e===void 0?{isLoaded:!1,isSignedIn:void 0,user:void 0}:e===null?{isLoaded:!0,isSignedIn:!1,user:null}:{isLoaded:!0,isSignedIn:!0,user:e}}var Ho=ho;function Uo({hookName:e,resourceType:t,useFetcher:n,options:r}){return function(i){let{for:a,...o}=i;Po(e);let s=n(a),c=Io(o,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1,__experimental_mode:void 0}),l=bo(),u=So(),{organization:d}=Mo();l.telemetry?.record(Ci(e));let f=o===void 0?void 0:{initialPage:c.initialPage,pageSize:c.pageSize,...a===`organization`?{orgId:d?.id}:{}},p=!!(l.loaded&&(r?.unauthenticated||u)),m=!!f&&p,h=Ro(f||{},s,{keepPreviousData:c.keepPreviousData,infinite:c.infinite,enabled:m,__experimental_mode:c.__experimental_mode},{type:t,userId:u?.id,...a===`organization`?{orgId:d?.id}:{}});return h}}var Wo=Uo({hookName:`useStatements`,resourceType:`commerce-statements`,useFetcher:()=>{let e=bo();return e.billing.getStatements}}),Go=Uo({hookName:`usePaymentAttempts`,resourceType:`commerce-payment-attempts`,useFetcher:()=>{let e=bo();return e.billing.getPaymentAttempts}}),Ko=Uo({hookName:`usePaymentMethods`,resourceType:`commerce-payment-methods`,useFetcher:e=>{let{organization:t}=Mo(),n=So();return e===`organization`?t?.getPaymentSources:n?.getPaymentSources}}),qo=Uo({hookName:`useSubscriptionItems`,resourceType:`commerce-subscription-items`,useFetcher:()=>{let e=bo();return e.billing.getSubscriptions}}),Jo=Uo({hookName:`usePlans`,resourceType:`commerce-plans`,useFetcher:e=>{let t=bo();return n=>t.billing.getPlans({...n,for:e===`organization`?`org`:`user`})},options:{unauthenticated:!0}}),Yo=e=>{let t=(0,P.useRef)(e);return(0,P.useEffect)(()=>{t.current=e},[e]),t.current},Xo=(e,t,n)=>{let r=!!n,i=(0,P.useRef)(n);(0,P.useEffect)(()=>{i.current=n},[n]),(0,P.useEffect)(()=>{if(!r||!e)return()=>{};let n=(...e)=>{i.current&&i.current(...e)};return e.on(t,n),()=>{e.off(t,n)}},[r,t,e,i])},Zo=P.createContext(null);Zo.displayName=`ElementsContext`;var Qo=(e,t)=>{if(!e)throw Error(`Could not find Elements context; You need to wrap the part of your app that ${t} in an <Elements> provider.`);return e},$o=e=>typeof e==`object`&&!!e,es=(e,t,n)=>$o(e)?Object.keys(e).reduce((r,i)=>{let a=!$o(t)||!ns(e[i],t[i]);return n.includes(i)?(a&&console.warn(`Unsupported prop change: options.${i} is not a mutable property.`),r):a?{...r||{},[i]:e[i]}:r},null):null,ts=`[object Object]`,ns=(e,t)=>{if(!$o(e)||!$o(t))return e===t;let n=Array.isArray(e),r=Array.isArray(t);if(n!==r)return!1;let i=Object.prototype.toString.call(e)===ts,a=Object.prototype.toString.call(t)===ts;if(i!==a)return!1;if(!i&&!n)return e===t;let o=Object.keys(e),s=Object.keys(t);if(o.length!==s.length)return!1;let c={};for(let e=0;e<o.length;e+=1)c[o[e]]=!0;for(let e=0;e<s.length;e+=1)c[s[e]]=!0;let l=Object.keys(c);if(l.length!==o.length)return!1;let u=e,d=t,f=e=>ns(u[e],d[e]);return l.every(f)},rs=e=>{let t=P.useContext(Zo);return Qo(t,e)},os=e=>e.charAt(0).toUpperCase()+e.slice(1),ss=(e,t)=>{let n=`${os(e)}Element`,r=({id:t,className:r,fallback:i,options:a={},onBlur:o,onFocus:s,onReady:c,onChange:l,onEscape:u,onClick:d,onLoadError:f,onLoaderStart:p,onNetworksChange:m,onConfirm:h,onCancel:g,onShippingAddressChange:_,onShippingRateChange:v})=>{let y=rs(`mounts <${n}>`),b=`elements`in y?y.elements:null,[x,S]=P.useState(null),C=P.useRef(null),w=P.useRef(null),[T,E]=(0,P.useState)(!1);Xo(x,`blur`,o),Xo(x,`focus`,s),Xo(x,`escape`,u),Xo(x,`click`,d),Xo(x,`loaderror`,f),Xo(x,`loaderstart`,p),Xo(x,`networkschange`,m),Xo(x,`confirm`,h),Xo(x,`cancel`,g),Xo(x,`shippingaddresschange`,_),Xo(x,`shippingratechange`,v),Xo(x,`change`,l);let ee;c&&(ee=()=>{E(!0),c(x)}),Xo(x,`ready`,ee),P.useLayoutEffect(()=>{if(C.current===null&&w.current!==null&&b){let t=null;b&&(t=b.create(e,a)),C.current=t,S(t),t&&t.mount(w.current)}},[b,a]);let D=Yo(a);return P.useEffect(()=>{if(!C.current)return;let e=es(a,D,[`paymentRequest`]);e&&`update`in C.current&&C.current.update(e)},[a,D]),P.useLayoutEffect(()=>()=>{if(C.current&&typeof C.current.destroy==`function`)try{C.current.destroy(),C.current=null}catch{}},[]),P.createElement(P.Fragment,null,!T&&i,P.createElement(`div`,{id:t,className:r,ref:w}))},i=e=>{rs(`mounts <${n}>`);let{id:t,className:r}=e;return P.createElement(`div`,{id:t,className:r})},a=t?i:r;return a.displayName=n,a.__elementType=e,a},cs=typeof window>`u`,ls=ss(`payment`,cs),[us,ds]=_o(`StripeLibsContext`),[fs,ps]=_o(`PaymentElementContext`),[ms,hs]=_o(`StripeUtilsContext`),gs=()=>{try{return!1}catch{}return!1},_s=()=>{try{return!1}catch{}return!1},vs=()=>{try{return!0}catch{}return!1},ys=new Set,bs=(e,t,n)=>{let r=_s()||vs(),i=n??e;ys.has(i)||r||(ys.add(i),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))},xs=hr({packageName:`@clerk/clerk-react`});function Ss(e){xs.setMessages(e).setPackageName(e)}var[Cs,ws]=_o(`AuthContext`),Ts=yo,Es=bo,Ds=`You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.`,Os=e=>`You've passed multiple children components to <${e}/>. You can only pass a single child component or text.`,ks=`Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support`,As=`Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.`,js="<UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",Ms="<UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",Ns="<OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",Ps="<OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",Fs=e=>`<${e} /> can only accept <${e}.Page /> and <${e}.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`,Is=e=>`Missing props. <${e}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`,Ls=e=>`Missing props. <${e}.Link /> component requires the following props: url, label and labelIcon.`,Rs=e=>`The <${e}/> component uses path-based routing by default unless a different routing strategy is provided using the \`routing\` prop. When path-based routing is used, you need to provide the path where the component is mounted on by using the \`path\` prop. Example: <${e} path={'/my-path'} />`,zs=e=>`The \`path\` prop will only be respected when the Clerk component uses path-based routing. To resolve this error, pass \`routing='path'\` to the <${e}/> component, or drop the \`path\` prop to switch to hash-based routing. For more details please refer to our docs: https://clerk.com/docs`,Bs=`<UserButton /> can only accept <UserButton.UserProfilePage />, <UserButton.UserProfileLink /> and <UserButton.MenuItems /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`,Vs=`<UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`,Hs="<UserButton.MenuItems /> component needs to be a direct child of `<UserButton />`.",Us="<UserButton.Action /> component needs to be a direct child of `<UserButton.MenuItems />`.",Ws="<UserButton.Link /> component needs to be a direct child of `<UserButton.MenuItems />`.",Gs=`Missing props. <UserButton.Link /> component requires the following props: href, label and labelIcon.`,Ks=`Missing props. <UserButton.Action /> component requires the following props: label.`,qs=e=>{Po(()=>{xs.throwMissingClerkProviderError({source:e})})},Js=e=>new Promise(t=>{let n=r=>{[`ready`,`degraded`].includes(r)&&(t(),e.off(`status`,n))};e.on(`status`,n,{notify:!0})}),Ys=e=>async t=>(await Js(e),e.session?e.session.getToken(t):null),Xs=e=>async(...t)=>(await Js(e),e.signOut(...t)),Zs=(e={})=>{var t,n;qs(`useAuth`);let{treatPendingAsSignedOut:r,...i}=e??{},a=i,o=ws(),s=o;s.sessionId===void 0&&s.userId===void 0&&(s=a??{});let c=Es(),l=(0,P.useCallback)(Ys(c),[c]),u=(0,P.useCallback)(Xs(c),[c]);return(t=c.telemetry)==null||t.record(Ci(`useAuth`,{treatPendingAsSignedOut:r})),Qs({...s,getToken:l,signOut:u},{treatPendingAsSignedOut:r??(n=c.__internal_getOption)?.call(c,`treatPendingAsSignedOut`)})};function Qs(e,{treatPendingAsSignedOut:t=!0}={}){let{userId:n,orgId:r,orgRole:i,has:a,signOut:o,getToken:s,orgPermissions:c,factorVerificationAge:l,sessionClaims:u}=e??{},d=(0,P.useCallback)(e=>a?a(e):Vr({userId:n,orgId:r,orgRole:i,orgPermissions:c,factorVerificationAge:l,features:u?.fea||``,plans:u?.pla||``})(e),[a,n,r,i,c,l]),f=Hr({authObject:{...e,getToken:s,signOut:o,has:d},options:{treatPendingAsSignedOut:t}});return f||xs.throw(ks)}var B=(e,t)=>{let n=typeof t==`string`?t:t?.component,r=n||e.displayName||e.name||`Component`;e.displayName=r;let i=typeof t==`string`?void 0:t,a=t=>{qs(r||`withClerk`);let n=Es();return!n.loaded&&!i?.renderWhileLoading?null:P.createElement(e,{...t,component:r,clerk:n})};return a.displayName=`withClerk(${r})`,a},$s=({children:e,treatPendingAsSignedOut:t})=>{qs(`SignedIn`);let{userId:n}=Zs({treatPendingAsSignedOut:t});return n?e:null},ec=({children:e,treatPendingAsSignedOut:t})=>{qs(`SignedOut`);let{userId:n}=Zs({treatPendingAsSignedOut:t});return n===null?e:null},tc=B(({clerk:e,...t})=>{let{client:n,session:r}=e,i=n.signedInSessions?n.signedInSessions.length>0:n.activeSessions&&n.activeSessions.length>0;return P.useEffect(()=>{r===null&&i?e.redirectToAfterSignOut():e.redirectToSignIn(t)},[]),null},`RedirectToSignIn`),nc=B(({clerk:e,...t})=>(P.useEffect(()=>{e.redirectToSignUp(t)},[]),null),`RedirectToSignUp`),rc=B(({clerk:e})=>(P.useEffect(()=>{bs(`RedirectToUserProfile`,"Use the `redirectToUserProfile()` method instead."),e.redirectToUserProfile()},[]),null),`RedirectToUserProfile`),ic=B(({clerk:e})=>(P.useEffect(()=>{bs(`RedirectToOrganizationProfile`,"Use the `redirectToOrganizationProfile()` method instead."),e.redirectToOrganizationProfile()},[]),null),`RedirectToOrganizationProfile`),ac=B(({clerk:e})=>(P.useEffect(()=>{bs(`RedirectToCreateOrganization`,"Use the `redirectToCreateOrganization()` method instead."),e.redirectToCreateOrganization()},[]),null),`RedirectToCreateOrganization`),oc=B(({clerk:e,...t})=>(P.useEffect(()=>{e.handleRedirectCallback(t)},[]),null),`AuthenticateWithRedirectCallback`),sc=e=>{throw TypeError(e)},cc=(e,t,n)=>t.has(e)||sc(`Cannot `+n),V=(e,t,n)=>(cc(e,t,`read from private field`),n?n.call(e):t.get(e)),lc=(e,t,n)=>t.has(e)?sc(`Cannot add the same private member more than once`):t instanceof WeakSet?t.add(e):t.set(e,n),uc=(e,t,n,r)=>(cc(e,t,`write to private field`),r?r.call(e,n):t.set(e,n),n),dc=(e,t,n)=>(cc(e,t,`access private method`),n),fc=(e,t=`5.74.0`)=>{if(e)return e;let n=pc(t);return n?n===`snapshot`?`5.74.0`:n:mc(t)},pc=e=>e.trim().replace(/^v/,``).match(/-(.+?)(\.|$)/)?.[1],mc=e=>e.trim().replace(/^v/,``).split(`.`)[0];function hc(e){return e?gc(e)||_c(e):!0}function gc(e){return/^http(s)?:\/\//.test(e||``)}function _c(e){return e.startsWith(`/`)}function vc(e){return e?_c(e)?new URL(e,window.location.origin).toString():e:``}function yc(e){if(!e)return``;let t;if(e.match(/^(clerk\.)+\w*$/))t=/(clerk\.)*(?=clerk\.)/;else if(e.match(/\.clerk.accounts/))return e;else t=/^(clerk\.)*/gi;let n=e.replace(t,``);return`clerk.${n}`}var bc={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!1,jitter:!0},xc=100,Sc=async e=>new Promise(t=>setTimeout(t,e)),Cc=(e,t)=>t?e*(1+Math.random()):e,wc=e=>{let t=0,n=()=>{let n=e.initialDelay,r=e.factor,i=n*r**+t;return i=Cc(i,e.jitter),Math.min(e.maxDelayBetweenRetries||i,i)};return async()=>{await Sc(n()),t++}},Tc=async(e,t={})=>{let n=0,{shouldRetry:r,initialDelay:i,maxDelayBetweenRetries:a,factor:o,retryImmediately:s,jitter:c}={...bc,...t},l=wc({initialDelay:i,maxDelayBetweenRetries:a,factor:o,jitter:c});for(;;)try{return await e()}catch(e){if(n++,!r(e,n))throw e;s&&n===1?await Sc(Cc(xc,c)):await l()}},Ec=`loadScript cannot be called when document does not exist`,Dc=`loadScript cannot be called without a src`;async function Oc(e=``,t){let{async:n,defer:r,beforeLoad:i,crossOrigin:a,nonce:o}=t||{},s=()=>new Promise((t,s)=>{e||s(Error(Dc)),(!document||!document.body)&&s(Ec);let c=document.createElement(`script`);a&&c.setAttribute(`crossorigin`,a),c.async=n||!1,c.defer=r||!1,c.addEventListener(`load`,()=>{c.remove(),t(c)}),c.addEventListener(`error`,()=>{c.remove(),s()}),c.src=e,c.nonce=o,i?.(c),document.body.appendChild(c)});return Tc(s,{shouldRetry:(e,t)=>t<=5})}var kc=`Clerk: Failed to load Clerk`,{isDevOrStagingUrl:Ac}=ni(),jc=hr({packageName:`@clerk/shared`});function Mc(e){jc.setPackageName({packageName:e})}var Nc=async e=>{let t=document.querySelector(`script[data-clerk-js-script]`);if(t)return new Promise((e,n)=>{t.addEventListener(`load`,()=>{e(t)}),t.addEventListener(`error`,()=>{n(kc)})});if(!e?.publishableKey){jc.throwMissingPublishableKeyError();return}return Oc(Pc(e),{async:!0,crossOrigin:`anonymous`,nonce:e.nonce,beforeLoad:Ic(e)}).catch(()=>{throw Error(kc)})},Pc=e=>{let{clerkJSUrl:t,clerkJSVariant:n,clerkJSVersion:r,proxyUrl:i,domain:a,publishableKey:o}=e;if(t)return t;let s=``;s=i&&hc(i)?vc(i).replace(/http(s)?:\/\//,``):a&&!Ac(ei(o)?.frontendApi||``)?yc(a):ei(o)?.frontendApi||``;let c=n?`${n.replace(/\.+$/,``)}.`:``,l=fc(r);return`https://${s}/npm/@clerk/clerk-js@${l}/dist/clerk.${c}browser.js`},Fc=e=>{let t={};return e.publishableKey&&(t[`data-clerk-publishable-key`]=e.publishableKey),e.proxyUrl&&(t[`data-clerk-proxy-url`]=e.proxyUrl),e.domain&&(t[`data-clerk-domain`]=e.domain),e.nonce&&(t.nonce=e.nonce),t},Ic=e=>t=>{let n=Fc(e);for(let e in n)t.setAttribute(e,n[e])},Lc=e=>{gs()&&console.error(`Clerk: ${e}`)};function Rc(e,t,n){if(typeof e==`function`)return e(t);if(e!==void 0)return e;if(n!==void 0)return n}var zc=(e,...t)=>{let n={...e};for(let e of t)delete n[e];return n},Bc=(e,t,n)=>!e&&n?Vc(n):Hc(t),Vc=e=>{let t=e.userId,n=e.user,r=e.sessionId,i=e.sessionStatus,a=e.sessionClaims,o=e.session,s=e.organization,c=e.orgId,l=e.orgRole,u=e.orgPermissions,d=e.orgSlug,f=e.actor,p=e.factorVerificationAge;return{userId:t,user:n,sessionId:r,session:o,sessionStatus:i,sessionClaims:a,organization:s,orgId:c,orgRole:l,orgPermissions:u,orgSlug:d,actor:f,factorVerificationAge:p}},Hc=e=>{let t=e.user?e.user.id:e.user,n=e.user,r=e.session?e.session.id:e.session,i=e.session,a=e.session?.status,o=e.session?e.session.lastActiveToken?.jwt?.claims:null,s=e.session?e.session.factorVerificationAge:null,c=i?.actor,l=e.organization,u=e.organization?e.organization.id:e.organization,d=l?.slug,f=l&&n?.organizationMemberships?.find(e=>e.organization.id===u),p=f&&f.permissions,m=f&&f.role;return{userId:t,user:n,sessionId:r,session:i,sessionStatus:a,sessionClaims:o,organization:l,orgId:u,orgRole:m,orgSlug:d,orgPermissions:p,actor:c,factorVerificationAge:s}};function Uc(){return typeof window<`u`}var Wc=`bot.spider.crawl.APIs-Google.AdsBot.Googlebot.mediapartners.Google Favicon.FeedFetcher.Google-Read-Aloud.DuplexWeb-Google.googleweblight.bing.yandex.baidu.duckduck.yahoo.ecosia.ia_archiver.facebook.instagram.pinterest.reddit.slack.twitter.whatsapp.youtube.semrush`.split(`.`),Gc=new RegExp(Wc.join(`|`),`i`),Kc=(e,t,n,r,i)=>{let{notify:a}=i||{},o=e.get(n);o||(o=[],e.set(n,o)),o.push(r),a&&t.has(n)&&r(t.get(n))},qc=(e,t,n)=>(e.get(t)||[]).map(e=>e(n)),Jc=(e,t,n)=>{let r=e.get(t);r&&(n?r.splice(r.indexOf(n)>>>0,1):e.set(t,[]))},Yc=()=>{let e=new Map,t=new Map,n=new Map,r=(r,i)=>{t.set(r,i),qc(n,r,i),qc(e,r,i)};return{on:(...n)=>Kc(e,t,...n),prioritizedOn:(...e)=>Kc(n,t,...e),emit:r,off:(...t)=>Jc(e,...t),prioritizedOff:(...e)=>Jc(n,...e),internal:{retrieveListeners:t=>e.get(t)||[]}}},Xc={Status:`status`},Zc=()=>Yc(),Qc=u(dn(),1);typeof window<`u`&&!window.global&&(window.global=typeof global>`u`?window:global);var $c=e=>t=>{try{return P.Children.only(e)}catch{return xs.throw(Os(t))}},el=(e,t)=>(e||=t,typeof e==`string`&&(e=P.createElement(`button`,null,e)),e),tl=e=>(...t)=>{if(e&&typeof e==`function`)return e(...t)};function nl(e){return typeof e==`function`}var rl=new Map;function il(e,t,n=1){P.useEffect(()=>{let r=rl.get(e)||0;return r==n?xs.throw(t):(rl.set(e,r+1),()=>{rl.set(e,(rl.get(e)||1)-1)})},[])}function al(e,t,n){let r=e.displayName||e.name||t||`Component`,i=r=>(il(t,n),P.createElement(e,{...r}));return i.displayName=`withMaxAllowedInstancesGuard(${r})`,i}var ol=e=>{let t=Array(e.length).fill(null),[n,r]=(0,P.useState)(t);return e.map((e,t)=>({id:e.id,mount:e=>r(n=>n.map((n,r)=>r===t?e:n)),unmount:()=>r(e=>e.map((e,n)=>n===t?null:e)),portal:()=>P.createElement(P.Fragment,null,n[t]?(0,Qc.createPortal)(e.component,n[t]):null)}))},H=(e,t)=>!!e&&P.isValidElement(e)&&e?.type===t,sl=(e,t)=>{let n=[`account`,`security`];return ul({children:e,reorderItemsLabels:n,LinkComponent:Ol,PageComponent:Dl,MenuItemsComponent:Nl,componentName:`UserProfile`},t)},cl=(e,t)=>{let n=[`general`,`members`];return ul({children:e,reorderItemsLabels:n,LinkComponent:zl,PageComponent:Rl,componentName:`OrganizationProfile`},t)},ll=e=>{let t=[],n=[zl,Rl,Nl,Dl,Ol];return P.Children.forEach(e,e=>{n.some(t=>H(e,t))||t.push(e)}),t},ul=(e,t)=>{let{children:n,LinkComponent:r,PageComponent:i,MenuItemsComponent:a,reorderItemsLabels:o,componentName:s}=e,{allowForAnyChildren:c=!1}=t||{},l=[];P.Children.forEach(n,e=>{if(!H(e,i)&&!H(e,r)&&!H(e,a)){e&&!c&&Lc(Fs(s));return}let{props:t}=e,{children:n,label:u,url:d,labelIcon:f}=t;if(H(e,i))if(dl(t,o))l.push({label:u});else if(fl(t))l.push({label:u,labelIcon:f,children:n,url:d});else{Lc(Is(s));return}if(H(e,r))if(U(t))l.push({label:u,labelIcon:f,url:d});else{Lc(Ls(s));return}});let u=[],d=[],f=[];l.forEach((e,t)=>{if(fl(e)){u.push({component:e.children,id:t}),d.push({component:e.labelIcon,id:t});return}U(e)&&f.push({component:e.labelIcon,id:t})});let p=ol(u),m=ol(d),h=ol(f),g=[],_=[];return l.forEach((e,t)=>{if(dl(e,o)){g.push({label:e.label});return}if(fl(e)){let{portal:n,mount:r,unmount:i}=p.find(e=>e.id===t),{portal:a,mount:o,unmount:s}=m.find(e=>e.id===t);g.push({label:e.label,url:e.url,mount:r,unmount:i,mountIcon:o,unmountIcon:s}),_.push(n),_.push(a);return}if(U(e)){let{portal:n,mount:r,unmount:i}=h.find(e=>e.id===t);g.push({label:e.label,url:e.url,mountIcon:r,unmountIcon:i}),_.push(n);return}}),{customPages:g,customPagesPortals:_}},dl=(e,t)=>{let{children:n,label:r,url:i,labelIcon:a}=e;return!n&&!i&&!a&&t.some(e=>e===r)},fl=e=>{let{children:t,label:n,url:r,labelIcon:i}=e;return!!t&&!!r&&!!i&&!!n},U=e=>{let{children:t,label:n,url:r,labelIcon:i}=e;return!t&&!!r&&!!i&&!!n},pl=e=>{let t=[`manageAccount`,`signOut`];return ml({children:e,reorderItemsLabels:t,MenuItemsComponent:Nl,MenuActionComponent:Pl,MenuLinkComponent:Fl,UserProfileLinkComponent:Ol,UserProfilePageComponent:Dl})},ml=({children:e,MenuItemsComponent:t,MenuActionComponent:n,MenuLinkComponent:r,UserProfileLinkComponent:i,UserProfilePageComponent:a,reorderItemsLabels:o})=>{let s=[],c=[],l=[];P.Children.forEach(e,e=>{if(!H(e,t)&&!H(e,i)&&!H(e,a)){e&&Lc(Bs);return}if(H(e,i)||H(e,a))return;let{props:c}=e;P.Children.forEach(c.children,e=>{if(!H(e,n)&&!H(e,r)){e&&Lc(Vs);return}let{props:t}=e,{label:i,labelIcon:a,href:c,onClick:l,open:u}=t;if(H(e,n))if(hl(t,o))s.push({label:i});else if(gl(t)){let e={label:i,labelIcon:a};if(l!==void 0)s.push({...e,onClick:l});else if(u!==void 0)s.push({...e,open:u.startsWith(`/`)?u:`/${u}`});else{Lc(`Custom menu item must have either onClick or open property`);return}}else{Lc(Ks);return}if(H(e,r))if(_l(t))s.push({label:i,labelIcon:a,href:c});else{Lc(Gs);return}})});let u=[],d=[];s.forEach((e,t)=>{gl(e)&&u.push({component:e.labelIcon,id:t}),_l(e)&&d.push({component:e.labelIcon,id:t})});let f=ol(u),p=ol(d);return s.forEach((e,t)=>{if(hl(e,o)&&c.push({label:e.label}),gl(e)){let{portal:n,mount:r,unmount:i}=f.find(e=>e.id===t),a={label:e.label,mountIcon:r,unmountIcon:i};`onClick`in e?a.onClick=e.onClick:`open`in e&&(a.open=e.open),c.push(a),l.push(n)}if(_l(e)){let{portal:n,mount:r,unmount:i}=p.find(e=>e.id===t);c.push({label:e.label,href:e.href,mountIcon:r,unmountIcon:i}),l.push(n)}}),{customMenuItems:c,customMenuItemsPortals:l}},hl=(e,t)=>{let{children:n,label:r,onClick:i,labelIcon:a}=e;return!n&&!i&&!a&&t.some(e=>e===r)},gl=e=>{let{label:t,labelIcon:n,onClick:r,open:i}=e;return!!n&&!!t&&(typeof r==`function`||typeof i==`string`)},_l=e=>{let{label:t,href:n,labelIcon:r}=e;return!!n&&!!r&&!!t};function vl(e){let{root:t=document==null?void 0:document.body,selector:n,timeout:r=0}=e;return new Promise((e,i)=>{if(!t){i(Error(`No root element provided`));return}let a=t;n&&(a=t?.querySelector(n));let o=a?.childElementCount&&a.childElementCount>0;if(o){e();return}let s=new MutationObserver(r=>{for(let i of r)if(i.type===`childList`&&(!a&&n&&(a=t?.querySelector(n)),a?.childElementCount&&a.childElementCount>0)){s.disconnect(),e();return}});s.observe(t,{childList:!0,subtree:!0}),r>0&&setTimeout(()=>{s.disconnect(),i(Error(`Timeout waiting for element children`))},r)})}function yl(e){let t=(0,P.useRef)(),[n,r]=(0,P.useState)(`rendering`);return(0,P.useEffect)(()=>{if(!e)throw Error(`Clerk: no component name provided, unable to detect mount.`);typeof window<`u`&&!t.current&&(t.current=vl({selector:`[data-clerk-component="${e}"]`}).then(()=>{r(`rendered`)}).catch(()=>{r(`error`)}))},[e]),n}var bl=e=>`mount`in e,xl=e=>`open`in e,Sl=e=>e?.map(({mountIcon:e,unmountIcon:t,...n})=>n),Cl=class extends P.PureComponent{constructor(){super(...arguments),this.rootRef=P.createRef()}componentDidUpdate(e){var t,n,r,i;if(!bl(e)||!bl(this.props))return;let a=zc(e.props,`customPages`,`customMenuItems`,`children`),o=zc(this.props.props,`customPages`,`customMenuItems`,`children`),s=(t=a.customPages)?.length!==(n=o.customPages)?.length,c=(r=a.customMenuItems)?.length!==(i=o.customMenuItems)?.length,l=Sl(e.props.customMenuItems),u=Sl(this.props.props.customMenuItems);(!Ho(a,o)||!Ho(l,u)||s||c)&&this.rootRef.current&&this.props.updateProps({node:this.rootRef.current,props:this.props.props})}componentDidMount(){this.rootRef.current&&(bl(this.props)&&this.props.mount(this.rootRef.current,this.props.props),xl(this.props)&&this.props.open(this.props.props))}componentWillUnmount(){this.rootRef.current&&(bl(this.props)&&this.props.unmount(this.rootRef.current),xl(this.props)&&this.props.close())}render(){let{hideRootHtmlElement:e=!1}=this.props,t={ref:this.rootRef,...this.props.rootProps,...this.props.component&&{"data-clerk-component":this.props.component}};return P.createElement(P.Fragment,null,!e&&P.createElement(`div`,{...t}),this.props.children)}},wl=e=>{var t,n;return P.createElement(P.Fragment,null,(t=e?.customPagesPortals)?.map((e,t)=>(0,P.createElement)(e,{key:t})),(n=e?.customMenuItemsPortals)?.map((e,t)=>(0,P.createElement)(e,{key:t})))},Tl=B(({clerk:e,component:t,fallback:n,...r})=>{let i=yl(t),a=i===`rendering`||!e.loaded,o={...a&&n&&{style:{display:`none`}}};return P.createElement(P.Fragment,null,a&&n,e.loaded&&P.createElement(Cl,{component:t,mount:e.mountSignIn,unmount:e.unmountSignIn,updateProps:e.__unstable__updateProps,props:r,rootProps:o}))},{component:`SignIn`,renderWhileLoading:!0}),El=B(({clerk:e,component:t,fallback:n,...r})=>{let i=yl(t),a=i===`rendering`||!e.loaded,o={...a&&n&&{style:{display:`none`}}};return P.createElement(P.Fragment,null,a&&n,e.loaded&&P.createElement(Cl,{component:t,mount:e.mountSignUp,unmount:e.unmountSignUp,updateProps:e.__unstable__updateProps,props:r,rootProps:o}))},{component:`SignUp`,renderWhileLoading:!0});function Dl({children:e}){return Lc(js),P.createElement(P.Fragment,null,e)}function Ol({children:e}){return Lc(Ms),P.createElement(P.Fragment,null,e)}var kl=B(({clerk:e,component:t,fallback:n,...r})=>{let i=yl(t),a=i===`rendering`||!e.loaded,o={...a&&n&&{style:{display:`none`}}},{customPages:s,customPagesPortals:c}=sl(r.children);return P.createElement(P.Fragment,null,a&&n,P.createElement(Cl,{component:t,mount:e.mountUserProfile,unmount:e.unmountUserProfile,updateProps:e.__unstable__updateProps,props:{...r,customPages:s},rootProps:o},P.createElement(wl,{customPagesPortals:c})))},{component:`UserProfile`,renderWhileLoading:!0}),Al=Object.assign(kl,{Page:Dl,Link:Ol}),jl=(0,P.createContext)({mount:()=>{},unmount:()=>{},updateProps:()=>{}}),Ml=B(({clerk:e,component:t,fallback:n,...r})=>{let i=yl(t),a=i===`rendering`||!e.loaded,o={...a&&n&&{style:{display:`none`}}},{customPages:s,customPagesPortals:c}=sl(r.children,{allowForAnyChildren:!!r.__experimental_asProvider}),l=Object.assign(r.userProfileProps||{},{customPages:s}),{customMenuItems:u,customMenuItemsPortals:d}=pl(r.children),f=ll(r.children),p={mount:e.mountUserButton,unmount:e.unmountUserButton,updateProps:e.__unstable__updateProps,props:{...r,userProfileProps:l,customMenuItems:u}},m={customPagesPortals:c,customMenuItemsPortals:d};return P.createElement(jl.Provider,{value:p},a&&n,e.loaded&&P.createElement(Cl,{component:t,...p,hideRootHtmlElement:!!r.__experimental_asProvider,rootProps:o},r.__experimental_asProvider?f:null,P.createElement(wl,{...m})))},{component:`UserButton`,renderWhileLoading:!0});function Nl({children:e}){return Lc(Hs),P.createElement(P.Fragment,null,e)}function Pl({children:e}){return Lc(Us),P.createElement(P.Fragment,null,e)}function Fl({children:e}){return Lc(Ws),P.createElement(P.Fragment,null,e)}function Il(e){let t=(0,P.useContext)(jl),n={...t,props:{...t.props,...e}};return P.createElement(Cl,{...n})}var Ll=Object.assign(Ml,{UserProfilePage:Dl,UserProfileLink:Ol,MenuItems:Nl,Action:Pl,Link:Fl,__experimental_Outlet:Il});function Rl({children:e}){return Lc(Ns),P.createElement(P.Fragment,null,e)}function zl({children:e}){return Lc(Ps),P.createElement(P.Fragment,null,e)}var Bl=B(({clerk:e,component:t,fallback:n,...r})=>{let i=yl(t),a=i===`rendering`||!e.loaded,o={...a&&n&&{style:{display:`none`}}},{customPages:s,customPagesPortals:c}=cl(r.children);return P.createElement(P.Fragment,null,a&&n,e.loaded&&P.createElement(Cl,{component:t,mount:e.mountOrganizationProfile,unmount:e.unmountOrganizationProfile,updateProps:e.__unstable__updateProps,props:{...r,customPages:s},rootProps:o},P.createElement(wl,{customPagesPortals:c})))},{component:`OrganizationProfile`,renderWhileLoading:!0}),Vl=Object.assign(Bl,{Page:Rl,Link:zl}),Hl=B(({clerk:e,component:t,fallback:n,...r})=>{let i=yl(t),a=i===`rendering`||!e.loaded,o={...a&&n&&{style:{display:`none`}}};return P.createElement(P.Fragment,null,a&&n,e.loaded&&P.createElement(Cl,{component:t,mount:e.mountCreateOrganization,unmount:e.unmountCreateOrganization,updateProps:e.__unstable__updateProps,props:r,rootProps:o}))},{component:`CreateOrganization`,renderWhileLoading:!0}),W=(0,P.createContext)({mount:()=>{},unmount:()=>{},updateProps:()=>{}}),G=B(({clerk:e,component:t,fallback:n,...r})=>{let i=yl(t),a=i===`rendering`||!e.loaded,o={...a&&n&&{style:{display:`none`}}},{customPages:s,customPagesPortals:c}=cl(r.children,{allowForAnyChildren:!!r.__experimental_asProvider}),l=Object.assign(r.organizationProfileProps||{},{customPages:s}),u=ll(r.children),d={mount:e.mountOrganizationSwitcher,unmount:e.unmountOrganizationSwitcher,updateProps:e.__unstable__updateProps,props:{...r,organizationProfileProps:l},rootProps:o,component:t};return e.__experimental_prefetchOrganizationSwitcher(),P.createElement(W.Provider,{value:d},P.createElement(P.Fragment,null,a&&n,e.loaded&&P.createElement(Cl,{...d,hideRootHtmlElement:!!r.__experimental_asProvider},r.__experimental_asProvider?u:null,P.createElement(wl,{customPagesPortals:c}))))},{component:`OrganizationSwitcher`,renderWhileLoading:!0});function K(e){let t=(0,P.useContext)(W),n={...t,props:{...t.props,...e}};return P.createElement(Cl,{...n})}var q=Object.assign(G,{OrganizationProfilePage:Rl,OrganizationProfileLink:zl,__experimental_Outlet:K}),J=B(({clerk:e,component:t,fallback:n,...r})=>{let i=yl(t),a=i===`rendering`||!e.loaded,o={...a&&n&&{style:{display:`none`}}};return P.createElement(P.Fragment,null,a&&n,e.loaded&&P.createElement(Cl,{component:t,mount:e.mountOrganizationList,unmount:e.unmountOrganizationList,updateProps:e.__unstable__updateProps,props:r,rootProps:o}))},{component:`OrganizationList`,renderWhileLoading:!0}),Ul=B(({clerk:e,component:t,fallback:n,...r})=>{let i=yl(t),a=i===`rendering`||!e.loaded,o={...a&&n&&{style:{display:`none`}}};return P.createElement(P.Fragment,null,a&&n,e.loaded&&P.createElement(Cl,{component:t,open:e.openGoogleOneTap,close:e.closeGoogleOneTap,updateProps:e.__unstable__updateProps,props:r,rootProps:o}))},{component:`GoogleOneTap`,renderWhileLoading:!0}),Wl=B(({clerk:e,component:t,fallback:n,...r})=>{let i=yl(t),a=i===`rendering`||!e.loaded,o={...a&&n&&{style:{display:`none`}}};return P.createElement(P.Fragment,null,a&&n,e.loaded&&P.createElement(Cl,{component:t,mount:e.mountWaitlist,unmount:e.unmountWaitlist,updateProps:e.__unstable__updateProps,props:r,rootProps:o}))},{component:`Waitlist`,renderWhileLoading:!0}),Gl=B(({clerk:e,component:t,fallback:n,...r})=>{let i=yl(t),a=i===`rendering`||!e.loaded,o={...a&&n&&{style:{display:`none`}}};return P.createElement(P.Fragment,null,a&&n,e.loaded&&P.createElement(Cl,{component:t,mount:e.mountPricingTable,unmount:e.unmountPricingTable,updateProps:e.__unstable__updateProps,props:r,rootProps:o}))},{component:`PricingTable`,renderWhileLoading:!0}),Kl=B(({clerk:e,component:t,fallback:n,...r})=>{let i=yl(t),a=i===`rendering`||!e.loaded,o={...a&&n&&{style:{display:`none`}}};return P.createElement(P.Fragment,null,a&&n,e.loaded&&P.createElement(Cl,{component:t,mount:e.mountApiKeys,unmount:e.unmountApiKeys,updateProps:e.__unstable__updateProps,props:r,rootProps:o}))},{component:`ApiKeys`,renderWhileLoading:!0}),ql=B(({clerk:e,children:t,...n})=>{let{signUpFallbackRedirectUrl:r,forceRedirectUrl:i,fallbackRedirectUrl:a,signUpForceRedirectUrl:o,mode:s,initialValues:c,withSignUp:l,oauthFlow:u,...d}=n;t=el(t,`Sign in`);let f=$c(t)(`SignInButton`),p=()=>{let t={forceRedirectUrl:i,fallbackRedirectUrl:a,signUpFallbackRedirectUrl:r,signUpForceRedirectUrl:o,initialValues:c,withSignUp:l,oauthFlow:u};return s===`modal`?e.openSignIn({...t,appearance:n.appearance}):e.redirectToSignIn({...t,signInFallbackRedirectUrl:a,signInForceRedirectUrl:i})},m=async e=>(f&&typeof f==`object`&&`props`in f&&await tl(f.props.onClick)(e),p()),h={...d,onClick:m};return P.cloneElement(f,h)},{component:`SignInButton`,renderWhileLoading:!0}),Jl=B(({clerk:e,children:t,...n})=>{let{fallbackRedirectUrl:r,forceRedirectUrl:i,signInFallbackRedirectUrl:a,signInForceRedirectUrl:o,mode:s,initialValues:c,oauthFlow:l,...u}=n;t=el(t,`Sign up`);let d=$c(t)(`SignUpButton`),f=()=>{let t={fallbackRedirectUrl:r,forceRedirectUrl:i,signInFallbackRedirectUrl:a,signInForceRedirectUrl:o,initialValues:c,oauthFlow:l};return s===`modal`?e.openSignUp({...t,appearance:n.appearance,unsafeMetadata:n.unsafeMetadata}):e.redirectToSignUp({...t,signUpFallbackRedirectUrl:r,signUpForceRedirectUrl:i})},p=async e=>(d&&typeof d==`object`&&`props`in d&&await tl(d.props.onClick)(e),f()),m={...u,onClick:p};return P.cloneElement(d,m)},{component:`SignUpButton`,renderWhileLoading:!0}),Yl=B(({clerk:e,children:t,...n})=>{let{redirectUrl:r=`/`,signOutOptions:i,...a}=n;t=el(t,`Sign out`);let o=$c(t)(`SignOutButton`),s=()=>e.signOut({redirectUrl:r,...i}),c=async e=>(await tl(o.props.onClick)(e),s()),l={...a,onClick:c};return P.cloneElement(o,l)},{component:`SignOutButton`,renderWhileLoading:!0}),Xl=B(({clerk:e,children:t,...n})=>{let{redirectUrl:r,...i}=n;t=el(t,`Sign in with Metamask`);let a=$c(t)(`SignInWithMetamaskButton`),o=async()=>{async function t(){await e.authenticateWithMetamask({redirectUrl:r||void 0})}t()},s=async e=>(await tl(a.props.onClick)(e),o()),c={...i,onClick:s};return P.cloneElement(a,c)},{component:`SignInWithMetamask`,renderWhileLoading:!0});globalThis.__BUILD_DISABLE_RHC__===void 0&&(globalThis.__BUILD_DISABLE_RHC__=!1);var Zl={name:`@clerk/clerk-react`,version:`5.35.3`,environment:`production`},Ql,$l,eu,tu,nu,ru,iu,au,ou=class e{constructor(e){lc(this,iu),this.clerkjs=null,this.preopenOneTap=null,this.preopenUserVerification=null,this.preopenSignIn=null,this.preopenCheckout=null,this.preopenPlanDetails=null,this.preopenSubscriptionDetails=null,this.preopenSignUp=null,this.preopenUserProfile=null,this.preopenOrganizationProfile=null,this.preopenCreateOrganization=null,this.preOpenWaitlist=null,this.premountSignInNodes=new Map,this.premountSignUpNodes=new Map,this.premountUserProfileNodes=new Map,this.premountUserButtonNodes=new Map,this.premountOrganizationProfileNodes=new Map,this.premountCreateOrganizationNodes=new Map,this.premountOrganizationSwitcherNodes=new Map,this.premountOrganizationListNodes=new Map,this.premountMethodCalls=new Map,this.premountWaitlistNodes=new Map,this.premountPricingTableNodes=new Map,this.premountApiKeysNodes=new Map,this.premountOAuthConsentNodes=new Map,this.premountAddListenerCalls=new Map,this.loadedListeners=[],lc(this,Ql,`loading`),lc(this,$l),lc(this,eu),lc(this,tu),lc(this,nu,Zc()),this.buildSignInUrl=e=>{let t=()=>{var t;return(t=this.clerkjs)?.buildSignInUrl(e)||``};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set(`buildSignInUrl`,t)},this.buildSignUpUrl=e=>{let t=()=>{var t;return(t=this.clerkjs)?.buildSignUpUrl(e)||``};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set(`buildSignUpUrl`,t)},this.buildAfterSignInUrl=(...e)=>{let t=()=>{var t;return(t=this.clerkjs)?.buildAfterSignInUrl(...e)||``};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set(`buildAfterSignInUrl`,t)},this.buildAfterSignUpUrl=(...e)=>{let t=()=>{var t;return(t=this.clerkjs)?.buildAfterSignUpUrl(...e)||``};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set(`buildAfterSignUpUrl`,t)},this.buildAfterSignOutUrl=()=>{let e=()=>{var e;return(e=this.clerkjs)?.buildAfterSignOutUrl()||``};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set(`buildAfterSignOutUrl`,e)},this.buildNewSubscriptionRedirectUrl=()=>{let e=()=>{var e;return(e=this.clerkjs)?.buildNewSubscriptionRedirectUrl()||``};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set(`buildNewSubscriptionRedirectUrl`,e)},this.buildAfterMultiSessionSingleSignOutUrl=()=>{let e=()=>{var e;return(e=this.clerkjs)?.buildAfterMultiSessionSingleSignOutUrl()||``};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set(`buildAfterMultiSessionSingleSignOutUrl`,e)},this.buildUserProfileUrl=()=>{let e=()=>{var e;return(e=this.clerkjs)?.buildUserProfileUrl()||``};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set(`buildUserProfileUrl`,e)},this.buildCreateOrganizationUrl=()=>{let e=()=>{var e;return(e=this.clerkjs)?.buildCreateOrganizationUrl()||``};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set(`buildCreateOrganizationUrl`,e)},this.buildOrganizationProfileUrl=()=>{let e=()=>{var e;return(e=this.clerkjs)?.buildOrganizationProfileUrl()||``};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set(`buildOrganizationProfileUrl`,e)},this.buildWaitlistUrl=()=>{let e=()=>{var e;return(e=this.clerkjs)?.buildWaitlistUrl()||``};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set(`buildWaitlistUrl`,e)},this.buildUrlWithAuth=e=>{let t=()=>{var t;return(t=this.clerkjs)?.buildUrlWithAuth(e)||``};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set(`buildUrlWithAuth`,t)},this.handleUnauthenticated=async()=>{let e=()=>{var e;return(e=this.clerkjs)?.handleUnauthenticated()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set(`handleUnauthenticated`,e)},this.on=(...e)=>{var t;if((t=this.clerkjs)?.on)return this.clerkjs.on(...e);V(this,nu).on(...e)},this.off=(...e)=>{var t;if((t=this.clerkjs)?.off)return this.clerkjs.off(...e);V(this,nu).off(...e)},this.addOnLoaded=e=>{this.loadedListeners.push(e),this.loaded&&this.emitLoaded()},this.emitLoaded=()=>{this.loadedListeners.forEach(e=>e()),this.loadedListeners=[]},this.beforeLoad=e=>{if(!e)throw Error(`Failed to hydrate latest Clerk JS`)},this.hydrateClerkJS=e=>{var t;if(!e)throw Error(`Failed to hydrate latest Clerk JS`);return this.clerkjs=e,this.premountMethodCalls.forEach(e=>e()),this.premountAddListenerCalls.forEach((t,n)=>{t.nativeUnsubscribe=e.addListener(n)}),(t=V(this,nu).internal.retrieveListeners(`status`))==null||t.forEach(e=>{this.on(`status`,e,{notify:!0})}),this.preopenSignIn!==null&&e.openSignIn(this.preopenSignIn),this.preopenCheckout!==null&&e.__internal_openCheckout(this.preopenCheckout),this.preopenPlanDetails!==null&&e.__internal_openPlanDetails(this.preopenPlanDetails),this.preopenSubscriptionDetails!==null&&e.__internal_openSubscriptionDetails(this.preopenSubscriptionDetails),this.preopenSignUp!==null&&e.openSignUp(this.preopenSignUp),this.preopenUserProfile!==null&&e.openUserProfile(this.preopenUserProfile),this.preopenUserVerification!==null&&e.__internal_openReverification(this.preopenUserVerification),this.preopenOneTap!==null&&e.openGoogleOneTap(this.preopenOneTap),this.preopenOrganizationProfile!==null&&e.openOrganizationProfile(this.preopenOrganizationProfile),this.preopenCreateOrganization!==null&&e.openCreateOrganization(this.preopenCreateOrganization),this.preOpenWaitlist!==null&&e.openWaitlist(this.preOpenWaitlist),this.premountSignInNodes.forEach((t,n)=>{e.mountSignIn(n,t)}),this.premountSignUpNodes.forEach((t,n)=>{e.mountSignUp(n,t)}),this.premountUserProfileNodes.forEach((t,n)=>{e.mountUserProfile(n,t)}),this.premountUserButtonNodes.forEach((t,n)=>{e.mountUserButton(n,t)}),this.premountOrganizationListNodes.forEach((t,n)=>{e.mountOrganizationList(n,t)}),this.premountWaitlistNodes.forEach((t,n)=>{e.mountWaitlist(n,t)}),this.premountPricingTableNodes.forEach((t,n)=>{e.mountPricingTable(n,t)}),this.premountApiKeysNodes.forEach((t,n)=>{e.mountApiKeys(n,t)}),this.premountOAuthConsentNodes.forEach((t,n)=>{e.__internal_mountOAuthConsent(n,t)}),this.clerkjs.status===void 0&&V(this,nu).emit(Xc.Status,`ready`),this.emitLoaded(),this.clerkjs},this.__experimental_checkout=(...e)=>{var t;return(t=this.clerkjs)?.__experimental_checkout(...e)},this.__unstable__updateProps=async e=>{let t=await dc(this,iu,au).call(this);if(t&&`__unstable__updateProps`in t)return t.__unstable__updateProps(e)},this.__internal_navigateToTaskIfAvailable=async e=>this.clerkjs?this.clerkjs.__internal_navigateToTaskIfAvailable(e):Promise.reject(),this.setActive=e=>this.clerkjs?this.clerkjs.setActive(e):Promise.reject(),this.openSignIn=e=>{this.clerkjs&&this.loaded?this.clerkjs.openSignIn(e):this.preopenSignIn=e},this.closeSignIn=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignIn():this.preopenSignIn=null},this.__internal_openCheckout=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openCheckout(e):this.preopenCheckout=e},this.__internal_closeCheckout=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeCheckout():this.preopenCheckout=null},this.__internal_openPlanDetails=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openPlanDetails(e):this.preopenPlanDetails=e},this.__internal_closePlanDetails=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closePlanDetails():this.preopenPlanDetails=null},this.__internal_openSubscriptionDetails=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openSubscriptionDetails(e):this.preopenSubscriptionDetails=e??null},this.__internal_closeSubscriptionDetails=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeSubscriptionDetails():this.preopenSubscriptionDetails=null},this.__internal_openReverification=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openReverification(e):this.preopenUserVerification=e},this.__internal_closeReverification=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeReverification():this.preopenUserVerification=null},this.openGoogleOneTap=e=>{this.clerkjs&&this.loaded?this.clerkjs.openGoogleOneTap(e):this.preopenOneTap=e},this.closeGoogleOneTap=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeGoogleOneTap():this.preopenOneTap=null},this.openUserProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.openUserProfile(e):this.preopenUserProfile=e},this.closeUserProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeUserProfile():this.preopenUserProfile=null},this.openOrganizationProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.openOrganizationProfile(e):this.preopenOrganizationProfile=e},this.closeOrganizationProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeOrganizationProfile():this.preopenOrganizationProfile=null},this.openCreateOrganization=e=>{this.clerkjs&&this.loaded?this.clerkjs.openCreateOrganization(e):this.preopenCreateOrganization=e},this.closeCreateOrganization=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeCreateOrganization():this.preopenCreateOrganization=null},this.openWaitlist=e=>{this.clerkjs&&this.loaded?this.clerkjs.openWaitlist(e):this.preOpenWaitlist=e},this.closeWaitlist=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeWaitlist():this.preOpenWaitlist=null},this.openSignUp=e=>{this.clerkjs&&this.loaded?this.clerkjs.openSignUp(e):this.preopenSignUp=e},this.closeSignUp=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignUp():this.preopenSignUp=null},this.mountSignIn=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignIn(e,t):this.premountSignInNodes.set(e,t)},this.unmountSignIn=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignIn(e):this.premountSignInNodes.delete(e)},this.mountSignUp=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignUp(e,t):this.premountSignUpNodes.set(e,t)},this.unmountSignUp=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignUp(e):this.premountSignUpNodes.delete(e)},this.mountUserProfile=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserProfile(e,t):this.premountUserProfileNodes.set(e,t)},this.unmountUserProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserProfile(e):this.premountUserProfileNodes.delete(e)},this.mountOrganizationProfile=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationProfile(e,t):this.premountOrganizationProfileNodes.set(e,t)},this.unmountOrganizationProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationProfile(e):this.premountOrganizationProfileNodes.delete(e)},this.mountCreateOrganization=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountCreateOrganization(e,t):this.premountCreateOrganizationNodes.set(e,t)},this.unmountCreateOrganization=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountCreateOrganization(e):this.premountCreateOrganizationNodes.delete(e)},this.mountOrganizationSwitcher=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationSwitcher(e,t):this.premountOrganizationSwitcherNodes.set(e,t)},this.unmountOrganizationSwitcher=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationSwitcher(e):this.premountOrganizationSwitcherNodes.delete(e)},this.__experimental_prefetchOrganizationSwitcher=()=>{let e=()=>{var e;return(e=this.clerkjs)?.__experimental_prefetchOrganizationSwitcher()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set(`__experimental_prefetchOrganizationSwitcher`,e)},this.mountOrganizationList=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationList(e,t):this.premountOrganizationListNodes.set(e,t)},this.unmountOrganizationList=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationList(e):this.premountOrganizationListNodes.delete(e)},this.mountUserButton=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserButton(e,t):this.premountUserButtonNodes.set(e,t)},this.unmountUserButton=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserButton(e):this.premountUserButtonNodes.delete(e)},this.mountWaitlist=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountWaitlist(e,t):this.premountWaitlistNodes.set(e,t)},this.unmountWaitlist=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountWaitlist(e):this.premountWaitlistNodes.delete(e)},this.mountPricingTable=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountPricingTable(e,t):this.premountPricingTableNodes.set(e,t)},this.unmountPricingTable=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountPricingTable(e):this.premountPricingTableNodes.delete(e)},this.mountApiKeys=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountApiKeys(e,t):this.premountApiKeysNodes.set(e,t)},this.unmountApiKeys=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountApiKeys(e):this.premountApiKeysNodes.delete(e)},this.__internal_mountOAuthConsent=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_mountOAuthConsent(e,t):this.premountOAuthConsentNodes.set(e,t)},this.__internal_unmountOAuthConsent=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_unmountOAuthConsent(e):this.premountOAuthConsentNodes.delete(e)},this.addListener=e=>{if(this.clerkjs)return this.clerkjs.addListener(e);{let t=()=>{var t;let n=this.premountAddListenerCalls.get(e);n&&((t=n.nativeUnsubscribe)==null||t.call(n),this.premountAddListenerCalls.delete(e))};return this.premountAddListenerCalls.set(e,{unsubscribe:t,nativeUnsubscribe:void 0}),t}},this.navigate=e=>{let t=()=>{var t;return(t=this.clerkjs)?.navigate(e)};this.clerkjs&&this.loaded?t():this.premountMethodCalls.set(`navigate`,t)},this.redirectWithAuth=async(...e)=>{let t=()=>{var t;return(t=this.clerkjs)?.redirectWithAuth(...e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set(`redirectWithAuth`,t)},this.redirectToSignIn=async e=>{let t=()=>{var t;return(t=this.clerkjs)?.redirectToSignIn(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set(`redirectToSignIn`,t)},this.redirectToSignUp=async e=>{let t=()=>{var t;return(t=this.clerkjs)?.redirectToSignUp(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set(`redirectToSignUp`,t)},this.redirectToUserProfile=async()=>{let e=()=>{var e;return(e=this.clerkjs)?.redirectToUserProfile()};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set(`redirectToUserProfile`,e)},this.redirectToAfterSignUp=()=>{let e=()=>{var e;return(e=this.clerkjs)?.redirectToAfterSignUp()};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set(`redirectToAfterSignUp`,e)},this.redirectToAfterSignIn=()=>{let e=()=>{var e;return(e=this.clerkjs)?.redirectToAfterSignIn()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set(`redirectToAfterSignIn`,e)},this.redirectToAfterSignOut=()=>{let e=()=>{var e;return(e=this.clerkjs)?.redirectToAfterSignOut()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set(`redirectToAfterSignOut`,e)},this.redirectToOrganizationProfile=async()=>{let e=()=>{var e;return(e=this.clerkjs)?.redirectToOrganizationProfile()};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set(`redirectToOrganizationProfile`,e)},this.redirectToCreateOrganization=async()=>{let e=()=>{var e;return(e=this.clerkjs)?.redirectToCreateOrganization()};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set(`redirectToCreateOrganization`,e)},this.redirectToWaitlist=async()=>{let e=()=>{var e;return(e=this.clerkjs)?.redirectToWaitlist()};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set(`redirectToWaitlist`,e)},this.handleRedirectCallback=async e=>{var t;let n=()=>{var t;return(t=this.clerkjs)?.handleRedirectCallback(e)};this.clerkjs&&this.loaded?(t=n())==null||t.catch(()=>{}):this.premountMethodCalls.set(`handleRedirectCallback`,n)},this.handleGoogleOneTapCallback=async(e,t)=>{var n;let r=()=>{var n;return(n=this.clerkjs)?.handleGoogleOneTapCallback(e,t)};this.clerkjs&&this.loaded?(n=r())==null||n.catch(()=>{}):this.premountMethodCalls.set(`handleGoogleOneTapCallback`,r)},this.handleEmailLinkVerification=async e=>{let t=()=>{var t;return(t=this.clerkjs)?.handleEmailLinkVerification(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set(`handleEmailLinkVerification`,t)},this.authenticateWithMetamask=async e=>{let t=()=>{var t;return(t=this.clerkjs)?.authenticateWithMetamask(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set(`authenticateWithMetamask`,t)},this.authenticateWithCoinbaseWallet=async e=>{let t=()=>{var t;return(t=this.clerkjs)?.authenticateWithCoinbaseWallet(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set(`authenticateWithCoinbaseWallet`,t)},this.authenticateWithOKXWallet=async e=>{let t=()=>{var t;return(t=this.clerkjs)?.authenticateWithOKXWallet(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set(`authenticateWithOKXWallet`,t)},this.authenticateWithWeb3=async e=>{let t=()=>{var t;return(t=this.clerkjs)?.authenticateWithWeb3(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set(`authenticateWithWeb3`,t)},this.authenticateWithGoogleOneTap=async e=>{let t=await dc(this,iu,au).call(this);return t.authenticateWithGoogleOneTap(e)},this.__internal_loadStripeJs=async()=>{let e=await dc(this,iu,au).call(this);return e.__internal_loadStripeJs()},this.createOrganization=async e=>{let t=()=>{var t;return(t=this.clerkjs)?.createOrganization(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set(`createOrganization`,t)},this.getOrganization=async e=>{let t=()=>{var t;return(t=this.clerkjs)?.getOrganization(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set(`getOrganization`,t)},this.joinWaitlist=async e=>{let t=()=>{var t;return(t=this.clerkjs)?.joinWaitlist(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set(`joinWaitlist`,t)},this.signOut=async(...e)=>{let t=()=>{var t;return(t=this.clerkjs)?.signOut(...e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set(`signOut`,t)};let{Clerk:t=null,publishableKey:n}=e||{};uc(this,tu,n),uc(this,eu,e?.proxyUrl),uc(this,$l,e?.domain),this.options=e,this.Clerk=t,this.mode=Uc()?`browser`:`server`,this.options.sdkMetadata||(this.options.sdkMetadata=Zl),V(this,nu).emit(Xc.Status,`loading`),V(this,nu).prioritizedOn(Xc.Status,e=>uc(this,Ql,e)),V(this,tu)&&this.loadClerkJS()}get publishableKey(){return V(this,tu)}get loaded(){var e;return(e=this.clerkjs)?.loaded||!1}get status(){var e;return this.clerkjs?(e=this.clerkjs)?.status||(this.clerkjs.loaded?`ready`:`loading`):V(this,Ql)}static getOrCreateInstance(t){return(!Uc()||!V(this,ru)||t.Clerk&&V(this,ru).Clerk!==t.Clerk||V(this,ru).publishableKey!==t.publishableKey)&&uc(this,ru,new e(t)),V(this,ru)}static clearInstance(){uc(this,ru,null)}get domain(){return typeof window<`u`&&window.location?Rc(V(this,$l),new URL(window.location.href),``):typeof V(this,$l)==`function`?xs.throw(As):V(this,$l)||``}get proxyUrl(){return typeof window<`u`&&window.location?Rc(V(this,eu),new URL(window.location.href),``):typeof V(this,eu)==`function`?xs.throw(As):V(this,eu)||``}__internal_getOption(e){var t,n;return(t=this.clerkjs)?.__internal_getOption?(n=this.clerkjs)?.__internal_getOption(e):this.options[e]}get sdkMetadata(){var e;return(e=this.clerkjs)?.sdkMetadata||this.options.sdkMetadata||void 0}get instanceType(){var e;return(e=this.clerkjs)?.instanceType}get frontendApi(){var e;return(e=this.clerkjs)?.frontendApi||``}get isStandardBrowser(){var e;return(e=this.clerkjs)?.isStandardBrowser||this.options.standardBrowser||!1}get isSatellite(){return typeof window<`u`&&window.location?Rc(this.options.isSatellite,new URL(window.location.href),!1):typeof this.options.isSatellite==`function`?xs.throw(As):!1}async loadClerkJS(){var e;if(!(this.mode!==`browser`||this.loaded)){typeof window<`u`&&(window.__clerk_publishable_key=V(this,tu),window.__clerk_proxy_url=this.proxyUrl,window.__clerk_domain=this.domain);try{if(this.Clerk){let e;nl(this.Clerk)?(e=new this.Clerk(V(this,tu),{proxyUrl:this.proxyUrl,domain:this.domain}),this.beforeLoad(e),await e.load(this.options)):(e=this.Clerk,e.loaded||(this.beforeLoad(e),await e.load(this.options))),global.Clerk=e}else if(!__BUILD_DISABLE_RHC__){if(global.Clerk||await Nc({...this.options,publishableKey:V(this,tu),proxyUrl:this.proxyUrl,domain:this.domain,nonce:this.options.nonce}),!global.Clerk)throw Error(`Failed to download latest ClerkJS. Contact <EMAIL>.`);this.beforeLoad(global.Clerk),await global.Clerk.load(this.options)}return(e=global.Clerk)?.loaded?this.hydrateClerkJS(global.Clerk):void 0}catch(e){let t=e;V(this,nu).emit(Xc.Status,`error`),console.error(t.stack||t.message||t);return}}}get version(){var e;return(e=this.clerkjs)?.version}get client(){if(this.clerkjs)return this.clerkjs.client}get session(){if(this.clerkjs)return this.clerkjs.session}get user(){if(this.clerkjs)return this.clerkjs.user}get organization(){if(this.clerkjs)return this.clerkjs.organization}get telemetry(){if(this.clerkjs)return this.clerkjs.telemetry}get __unstable__environment(){if(this.clerkjs)return this.clerkjs.__unstable__environment}get isSignedIn(){return this.clerkjs?this.clerkjs.isSignedIn:!1}get billing(){var e;return(e=this.clerkjs)?.billing}get apiKeys(){var e;return(e=this.clerkjs)?.apiKeys}__unstable__setEnvironment(...e){if(this.clerkjs&&`__unstable__setEnvironment`in this.clerkjs)this.clerkjs.__unstable__setEnvironment(e);else return}};Ql=new WeakMap,$l=new WeakMap,eu=new WeakMap,tu=new WeakMap,nu=new WeakMap,ru=new WeakMap,iu=new WeakSet,au=function(){return new Promise(e=>{this.addOnLoaded(()=>e(this.clerkjs))})},lc(ou,ru);var su=ou;function cu(e){let{isomorphicClerkOptions:t,initialState:n,children:r}=e,{isomorphicClerk:i,clerkStatus:a}=lu(t),[o,s]=P.useState({client:i.client,session:i.session,user:i.user,organization:i.organization});P.useEffect(()=>i.addListener(e=>s({...e})),[]);let c=Bc(i.loaded,o,n),l=P.useMemo(()=>({value:i}),[a]),u=P.useMemo(()=>({value:o.client}),[o.client]),{sessionId:d,sessionStatus:f,sessionClaims:p,session:m,userId:h,user:g,orgId:_,actor:v,organization:y,orgRole:b,orgSlug:x,orgPermissions:S,factorVerificationAge:C}=c,w=P.useMemo(()=>{let e={sessionId:d,sessionStatus:f,sessionClaims:p,userId:h,actor:v,orgId:_,orgRole:b,orgSlug:x,orgPermissions:S,factorVerificationAge:C};return{value:e}},[d,f,h,v,_,b,x,C,p?.__raw]),T=P.useMemo(()=>({value:m}),[d,m]),E=P.useMemo(()=>({value:g}),[h,g]),ee=P.useMemo(()=>{let e={organization:y};return{value:e}},[_,y]);return P.createElement(Ts.Provider,{value:l},P.createElement(Co.Provider,{value:u},P.createElement(To.Provider,{value:T},P.createElement(No,{...ee.value},P.createElement(Cs.Provider,{value:w},P.createElement(xo.Provider,{value:E},P.createElement(Ao,{value:void 0},r)))))))}var lu=e=>{let t=P.useRef(su.getOrCreateInstance(e)),[n,r]=P.useState(t.current.status);return P.useEffect(()=>{t.current.__unstable__updateProps({appearance:e.appearance})},[e.appearance]),P.useEffect(()=>{t.current.__unstable__updateProps({options:e})},[e.localization]),P.useEffect(()=>(t.current.on(`status`,r),()=>{t.current&&t.current.off(`status`,r),su.clearInstance()}),[]),{isomorphicClerk:t.current,clerkStatus:n}};function uu(e){let{initialState:t,children:n,__internal_bypassMissingPublishableKey:r,...i}=e,{publishableKey:a=``,Clerk:o}=i;return!o&&!r&&(a?a&&!ti(a)&&xs.throwInvalidPublishableKeyError({key:a}):xs.throwMissingPublishableKeyError()),P.createElement(cu,{initialState:t,isomorphicClerkOptions:i},n)}var du=al(uu,`ClerkProvider`,Ds);du.displayName=`ClerkProvider`,Ss({packageName:`@clerk/clerk-react`}),Mc(`@clerk/clerk-react`);var fu=()=>typeof window<`u`,pu=hr({packageName:`@clerk/tanstack-react-start`}),mu=P.createContext(void 0);mu.displayName=`ClerkOptionsCtx`;var hu=e=>{let{children:t,options:n}=e;return(0,N.jsx)(mu.Provider,{value:{value:n},children:t})},gu=()=>{let e=ln(),t=tr(),n=P.useRef([]),r=()=>{n.current.forEach(e=>e()),n.current.splice(0,n.current.length)},[i,a]=(0,P.useTransition)();return P.useEffect(()=>{r()},[t]),t=>new Promise(r=>{a(()=>{n.current.push(r),r(e(t))})})};const _u={BASE_URL:`/`,DEV:!1,MODE:`production`,PROD:!0,SSR:!1,TSS_APP_BASE:`/`,TSS_OUTPUT_PUBLIC_DIR:`/Users/<USER>/Downloads/Coding/Personal/TinderOP/web/.output/public`,TSS_SERVER_FN_BASE:`/_serverFn`,TSS_SPA_MODE:`false`,VITE_CLERK_PUBLISHABLE_KEY:`pk_test_bW92aW5nLWFsYmFjb3JlLTU4LmNsZXJrLmFjY291bnRzLmRldiQ`,VITE_CLERK_SECRET_KEY:`sk_test_ccNlGaVflH5VSqng9wPGqDQTdNwzpUAxA2duR9UyJT`,VITE_OPENAI_API_KEY:`********************************************************************************************************************************************************************`,VITE_OPENROUTER_API_KEY:`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`,VITE_USER_NODE_ENV:`production`};var vu=e=>!!e?.cloudflare?.env,yu=e=>!!e?.env,bu=(e,t)=>{if(typeof process<`u`&&typeof{}[e]==`string`)return{}[e];if(_u&&typeof _u[e]==`string`)return _u[e];if(vu(t))return t.cloudflare.env[e]||``;if(yu(t))return t.env[e]||``;if(t&&typeof t[e]==`string`)return t[e];try{return globalThis[e]}catch{}return``},xu=e=>{let t=t=>bu(`VITE_${t}`,e)||bu(t,e);return{publishableKey:t(`CLERK_PUBLISHABLE_KEY`),domain:t(`CLERK_DOMAIN`),isSatellite:Jr(t(`CLERK_IS_SATELLITE`)),proxyUrl:t(`CLERK_PROXY_URL`),signInUrl:t(`CLERK_SIGN_IN_URL`),signUpUrl:t(`CLERK_SIGN_UP_URL`),clerkJsUrl:t(`CLERK_JS_URL`)||t(`CLERK_JS`),clerkJsVariant:t(`CLERK_JS_VARIANT`),clerkJsVersion:t(`CLERK_JS_VERSION`),telemetryDisabled:Jr(t(`CLERK_TELEMETRY_DISABLED`)),telemetryDebug:Jr(t(`CLERK_TELEMETRY_DEBUG`)),afterSignInUrl:t(`CLERK_AFTER_SIGN_IN_URL`),afterSignUpUrl:t(`CLERK_AFTER_SIGN_UP_URL`),newSubscriptionRedirectUrl:t(`CLERK_CHECKOUT_CONTINUE_URL`)}},Su=e=>{let{__clerk_ssr_state:t,__publishableKey:n,__proxyUrl:r,__domain:i,__isSatellite:a,__signInUrl:o,__signUpUrl:s,__afterSignInUrl:c,__afterSignUpUrl:l,__clerkJSUrl:u,__clerkJSVersion:d,__telemetryDisabled:f,__telemetryDebug:p,__signInForceRedirectUrl:m,__signUpForceRedirectUrl:h,__signInFallbackRedirectUrl:g,__signUpFallbackRedirectUrl:_}=e||{};return{clerkSsrState:t,publishableKey:n,proxyUrl:r,domain:i,isSatellite:!!a,signInUrl:o,signUpUrl:s,afterSignInUrl:c,afterSignUpUrl:l,clerkJSUrl:u,clerkJSVersion:d,telemetry:{disabled:f,debug:p},signInForceRedirectUrl:m,signUpForceRedirectUrl:h,signInFallbackRedirectUrl:g,signUpFallbackRedirectUrl:_}},Cu=e=>({...e,publishableKey:e.publishableKey||xu().publishableKey,domain:e.domain||xu().domain,isSatellite:e.isSatellite||xu().isSatellite,signInUrl:e.signInUrl||xu().signInUrl,signUpUrl:e.signUpUrl||xu().signUpUrl,afterSignInUrl:e.afterSignInUrl||xu().afterSignInUrl,afterSignUpUrl:e.afterSignUpUrl||xu().afterSignUpUrl,clerkJSUrl:e.clerkJSUrl||xu().clerkJsUrl,clerkJSVersion:e.clerkJSVersion||xu().clerkJsVersion,signInForceRedirectUrl:e.signInForceRedirectUrl,clerkJSVariant:e.clerkJSVariant||xu().clerkJsVariant}),wu={name:`@clerk/tanstack-react-start`,version:`0.20.4`},Tu={current:void 0};function Eu({children:e,...t}){let n=gu(),r=er({strict:!1});(0,P.useEffect)(()=>{Tu.current=n},[n]);let i=fu()?window.__clerk_init_state:r?.clerkInitialState,{clerkSsrState:a,...o}=Su(i?.__internal_clerk_state),s={...Cu(o),...t};return(0,N.jsxs)(N.Fragment,{children:[(0,N.jsx)(Hn,{children:`window.__clerk_init_state = ${JSON.stringify(r?.clerkInitialState)};`}),(0,N.jsx)(hu,{options:s,children:(0,N.jsx)(du,{initialState:a,sdkMetadata:wu,routerPush:e=>Tu.current?.({to:e,replace:!1}),routerReplace:e=>Tu.current?.({to:e,replace:!0}),...s,children:e})})]})}Eu.displayName=`ClerkProvider`;function Du(e){var t,n,r=``;if(typeof e==`string`||typeof e==`number`)r+=e;else if(typeof e==`object`)if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(n=Du(e[t]))&&(r&&(r+=` `),r+=n)}else for(n in e)e[n]&&(r&&(r+=` `),r+=n);return r}function Ou(){for(var e,t,n=0,r=``,i=arguments.length;n<i;n++)(e=arguments[n])&&(t=Du(e))&&(r&&(r+=` `),r+=t);return r}const ku=`-`,Au=e=>{let t=Pu(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e,i=e=>{let n=e.split(ku);return n[0]===``&&n.length!==1&&n.shift(),ju(n,t)||Nu(e)},a=(e,t)=>{let i=n[e]||[];return t&&r[e]?[...i,...r[e]]:i};return{getClassGroupId:i,getConflictingClassGroupIds:a}},ju=(e,t)=>{if(e.length===0)return t.classGroupId;let n=e[0],r=t.nextPart.get(n),i=r?ju(e.slice(1),r):void 0;if(i)return i;if(t.validators.length===0)return;let a=e.join(ku);return t.validators.find(({validator:e})=>e(a))?.classGroupId},Mu=/^\[(.+)\]$/,Nu=e=>{if(Mu.test(e)){let t=Mu.exec(e)[1],n=t?.substring(0,t.indexOf(`:`));if(n)return`arbitrary..`+n}},Pu=e=>{let{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]},i=Ru(Object.entries(e.classGroups),n);return i.forEach(([e,n])=>{Fu(n,r,e,t)}),r},Fu=(e,t,n,r)=>{e.forEach(e=>{if(typeof e==`string`){let r=e===``?t:Iu(t,e);r.classGroupId=n;return}if(typeof e==`function`){if(Lu(e)){Fu(e(r),t,n,r);return}t.validators.push({validator:e,classGroupId:n});return}Object.entries(e).forEach(([e,i])=>{Fu(i,Iu(t,e),n,r)})})},Iu=(e,t)=>{let n=e;return t.split(ku).forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},Lu=e=>e.isThemeGetter,Ru=(e,t)=>t?e.map(([e,n])=>{let r=n.map(e=>typeof e==`string`?t+e:typeof e==`object`?Object.fromEntries(Object.entries(e).map(([e,n])=>[t+e,n])):e);return[e,r]}):e,zu=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,n=new Map,r=new Map,i=(i,a)=>{n.set(i,a),t++,t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);if(t!==void 0)return t;if((t=r.get(e))!==void 0)return i(e,t),t},set(e,t){n.has(e)?n.set(e,t):i(e,t)}}},Bu=`!`,Vu=e=>{let{separator:t,experimentalParseClassName:n}=e,r=t.length===1,i=t[0],a=t.length,o=e=>{let n=[],o=0,s=0,c;for(let l=0;l<e.length;l++){let u=e[l];if(o===0){if(u===i&&(r||e.slice(l,l+a)===t)){n.push(e.slice(s,l)),s=l+a;continue}if(u===`/`){c=l;continue}}u===`[`?o++:u===`]`&&o--}let l=n.length===0?e:e.substring(s),u=l.startsWith(Bu),d=u?l.substring(1):l,f=c&&c>s?c-s:void 0;return{modifiers:n,hasImportantModifier:u,baseClassName:d,maybePostfixModifierPosition:f}};return n?e=>n({className:e,parseClassName:o}):o},Hu=e=>{if(e.length<=1)return e;let t=[],n=[];return e.forEach(e=>{let r=e[0]===`[`;r?(t.push(...n.sort(),e),n=[]):n.push(e)}),t.push(...n.sort()),t},Uu=e=>({cache:zu(e.cacheSize),parseClassName:Vu(e),...Au(e)}),Wu=/\s+/,Gu=(e,t)=>{let{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:i}=t,a=[],o=e.trim().split(Wu),s=``;for(let e=o.length-1;e>=0;--e){let t=o[e],{modifiers:c,hasImportantModifier:l,baseClassName:u,maybePostfixModifierPosition:d}=n(t),f=!!d,p=r(f?u.substring(0,d):u);if(!p){if(!f){s=t+(s.length>0?` `+s:s);continue}if(p=r(u),!p){s=t+(s.length>0?` `+s:s);continue}f=!1}let m=Hu(c).join(`:`),h=l?m+Bu:m,g=h+p;if(a.includes(g))continue;a.push(g);let _=i(p,f);for(let e=0;e<_.length;++e){let t=_[e];a.push(h+t)}s=t+(s.length>0?` `+s:s)}return s};function Ku(){let e=0,t,n,r=``;for(;e<arguments.length;)(t=arguments[e++])&&(n=Y(t))&&(r&&(r+=` `),r+=n);return r}const Y=e=>{if(typeof e==`string`)return e;let t,n=``;for(let r=0;r<e.length;r++)e[r]&&(t=Y(e[r]))&&(n&&(n+=` `),n+=t);return n};function qu(e,...t){let n,r,i,a=o;function o(o){let c=t.reduce((e,t)=>t(e),e());return n=Uu(c),r=n.cache.get,i=n.cache.set,a=s,s(o)}function s(e){let t=r(e);if(t)return t;let a=Gu(e,n);return i(e,a),a}return function(){return a(Ku.apply(null,arguments))}}const X=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},Ju=/^\[(?:([a-z-]+):)?(.+)\]$/i,Yu=/^\d+\/\d+$/,Xu=new Set([`px`,`full`,`screen`]),Zu=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Qu=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,$u=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,ed=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,td=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,nd=e=>id(e)||Xu.has(e)||Yu.test(e),rd=e=>gd(e,`length`,_d),id=e=>!!e&&!Number.isNaN(Number(e)),ad=e=>gd(e,`number`,id),od=e=>!!e&&Number.isInteger(Number(e)),sd=e=>e.endsWith(`%`)&&id(e.slice(0,-1)),Z=e=>Ju.test(e),cd=e=>Zu.test(e),ld=new Set([`length`,`size`,`percentage`]),ud=e=>gd(e,ld,vd),dd=e=>gd(e,`position`,vd),fd=new Set([`image`,`url`]),pd=e=>gd(e,fd,bd),md=e=>gd(e,``,yd),hd=()=>!0,gd=(e,t,n)=>{let r=Ju.exec(e);return r?r[1]?typeof t==`string`?r[1]===t:t.has(r[1]):n(r[2]):!1},_d=e=>Qu.test(e)&&!$u.test(e),vd=()=>!1,yd=e=>ed.test(e),bd=e=>td.test(e),xd=()=>{let e=X(`colors`),t=X(`spacing`),n=X(`blur`),r=X(`brightness`),i=X(`borderColor`),a=X(`borderRadius`),o=X(`borderSpacing`),s=X(`borderWidth`),c=X(`contrast`),l=X(`grayscale`),u=X(`hueRotate`),d=X(`invert`),f=X(`gap`),p=X(`gradientColorStops`),m=X(`gradientColorStopPositions`),h=X(`inset`),g=X(`margin`),_=X(`opacity`),v=X(`padding`),y=X(`saturate`),b=X(`scale`),x=X(`sepia`),S=X(`skew`),C=X(`space`),w=X(`translate`),T=()=>[`auto`,`contain`,`none`],E=()=>[`auto`,`hidden`,`clip`,`visible`,`scroll`],ee=()=>[`auto`,Z,t],D=()=>[Z,t],te=()=>[``,nd,rd],ne=()=>[`auto`,id,Z],re=()=>[`bottom`,`center`,`left`,`left-bottom`,`left-top`,`right`,`right-bottom`,`right-top`,`top`],O=()=>[`solid`,`dashed`,`dotted`,`double`,`none`],k=()=>[`normal`,`multiply`,`screen`,`overlay`,`darken`,`lighten`,`color-dodge`,`color-burn`,`hard-light`,`soft-light`,`difference`,`exclusion`,`hue`,`saturation`,`color`,`luminosity`],A=()=>[`start`,`end`,`center`,`between`,`around`,`evenly`,`stretch`],ie=()=>[``,`0`,Z],ae=()=>[`auto`,`avoid`,`all`,`avoid-page`,`page`,`left`,`right`,`column`],oe=()=>[id,Z];return{cacheSize:500,separator:`:`,theme:{colors:[hd],spacing:[nd,rd],blur:[`none`,``,cd,Z],brightness:oe(),borderColor:[e],borderRadius:[`none`,``,`full`,cd,Z],borderSpacing:D(),borderWidth:te(),contrast:oe(),grayscale:ie(),hueRotate:oe(),invert:ie(),gap:D(),gradientColorStops:[e],gradientColorStopPositions:[sd,rd],inset:ee(),margin:ee(),opacity:oe(),padding:D(),saturate:oe(),scale:oe(),sepia:ie(),skew:oe(),space:D(),translate:D()},classGroups:{aspect:[{aspect:[`auto`,`square`,`video`,Z]}],container:[`container`],columns:[{columns:[cd]}],"break-after":[{"break-after":ae()}],"break-before":[{"break-before":ae()}],"break-inside":[{"break-inside":[`auto`,`avoid`,`avoid-page`,`avoid-column`]}],"box-decoration":[{"box-decoration":[`slice`,`clone`]}],box:[{box:[`border`,`content`]}],display:[`block`,`inline-block`,`inline`,`flex`,`inline-flex`,`table`,`inline-table`,`table-caption`,`table-cell`,`table-column`,`table-column-group`,`table-footer-group`,`table-header-group`,`table-row-group`,`table-row`,`flow-root`,`grid`,`inline-grid`,`contents`,`list-item`,`hidden`],float:[{float:[`right`,`left`,`none`,`start`,`end`]}],clear:[{clear:[`left`,`right`,`both`,`none`,`start`,`end`]}],isolation:[`isolate`,`isolation-auto`],"object-fit":[{object:[`contain`,`cover`,`fill`,`none`,`scale-down`]}],"object-position":[{object:[...re(),Z]}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:T()}],"overscroll-x":[{"overscroll-x":T()}],"overscroll-y":[{"overscroll-y":T()}],position:[`static`,`fixed`,`absolute`,`relative`,`sticky`],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:[`visible`,`invisible`,`collapse`],z:[{z:[`auto`,od,Z]}],basis:[{basis:ee()}],"flex-direction":[{flex:[`row`,`row-reverse`,`col`,`col-reverse`]}],"flex-wrap":[{flex:[`wrap`,`wrap-reverse`,`nowrap`]}],flex:[{flex:[`1`,`auto`,`initial`,`none`,Z]}],grow:[{grow:ie()}],shrink:[{shrink:ie()}],order:[{order:[`first`,`last`,`none`,od,Z]}],"grid-cols":[{"grid-cols":[hd]}],"col-start-end":[{col:[`auto`,{span:[`full`,od,Z]},Z]}],"col-start":[{"col-start":ne()}],"col-end":[{"col-end":ne()}],"grid-rows":[{"grid-rows":[hd]}],"row-start-end":[{row:[`auto`,{span:[od,Z]},Z]}],"row-start":[{"row-start":ne()}],"row-end":[{"row-end":ne()}],"grid-flow":[{"grid-flow":[`row`,`col`,`dense`,`row-dense`,`col-dense`]}],"auto-cols":[{"auto-cols":[`auto`,`min`,`max`,`fr`,Z]}],"auto-rows":[{"auto-rows":[`auto`,`min`,`max`,`fr`,Z]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:[`normal`,...A()]}],"justify-items":[{"justify-items":[`start`,`end`,`center`,`stretch`]}],"justify-self":[{"justify-self":[`auto`,`start`,`end`,`center`,`stretch`]}],"align-content":[{content:[`normal`,...A(),`baseline`]}],"align-items":[{items:[`start`,`end`,`center`,`baseline`,`stretch`]}],"align-self":[{self:[`auto`,`start`,`end`,`center`,`stretch`,`baseline`]}],"place-content":[{"place-content":[...A(),`baseline`]}],"place-items":[{"place-items":[`start`,`end`,`center`,`baseline`,`stretch`]}],"place-self":[{"place-self":[`auto`,`start`,`end`,`center`,`stretch`]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[C]}],"space-x-reverse":[`space-x-reverse`],"space-y":[{"space-y":[C]}],"space-y-reverse":[`space-y-reverse`],w:[{w:[`auto`,`min`,`max`,`fit`,`svw`,`lvw`,`dvw`,Z,t]}],"min-w":[{"min-w":[Z,t,`min`,`max`,`fit`]}],"max-w":[{"max-w":[Z,t,`none`,`full`,`min`,`max`,`fit`,`prose`,{screen:[cd]},cd]}],h:[{h:[Z,t,`auto`,`min`,`max`,`fit`,`svh`,`lvh`,`dvh`]}],"min-h":[{"min-h":[Z,t,`min`,`max`,`fit`,`svh`,`lvh`,`dvh`]}],"max-h":[{"max-h":[Z,t,`min`,`max`,`fit`,`svh`,`lvh`,`dvh`]}],size:[{size:[Z,t,`auto`,`min`,`max`,`fit`]}],"font-size":[{text:[`base`,cd,rd]}],"font-smoothing":[`antialiased`,`subpixel-antialiased`],"font-style":[`italic`,`not-italic`],"font-weight":[{font:[`thin`,`extralight`,`light`,`normal`,`medium`,`semibold`,`bold`,`extrabold`,`black`,ad]}],"font-family":[{font:[hd]}],"fvn-normal":[`normal-nums`],"fvn-ordinal":[`ordinal`],"fvn-slashed-zero":[`slashed-zero`],"fvn-figure":[`lining-nums`,`oldstyle-nums`],"fvn-spacing":[`proportional-nums`,`tabular-nums`],"fvn-fraction":[`diagonal-fractions`,`stacked-fractions`],tracking:[{tracking:[`tighter`,`tight`,`normal`,`wide`,`wider`,`widest`,Z]}],"line-clamp":[{"line-clamp":[`none`,id,ad]}],leading:[{leading:[`none`,`tight`,`snug`,`normal`,`relaxed`,`loose`,nd,Z]}],"list-image":[{"list-image":[`none`,Z]}],"list-style-type":[{list:[`none`,`disc`,`decimal`,Z]}],"list-style-position":[{list:[`inside`,`outside`]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[_]}],"text-alignment":[{text:[`left`,`center`,`right`,`justify`,`start`,`end`]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[_]}],"text-decoration":[`underline`,`overline`,`line-through`,`no-underline`],"text-decoration-style":[{decoration:[...O(),`wavy`]}],"text-decoration-thickness":[{decoration:[`auto`,`from-font`,nd,rd]}],"underline-offset":[{"underline-offset":[`auto`,nd,Z]}],"text-decoration-color":[{decoration:[e]}],"text-transform":[`uppercase`,`lowercase`,`capitalize`,`normal-case`],"text-overflow":[`truncate`,`text-ellipsis`,`text-clip`],"text-wrap":[{text:[`wrap`,`nowrap`,`balance`,`pretty`]}],indent:[{indent:D()}],"vertical-align":[{align:[`baseline`,`top`,`middle`,`bottom`,`text-top`,`text-bottom`,`sub`,`super`,Z]}],whitespace:[{whitespace:[`normal`,`nowrap`,`pre`,`pre-line`,`pre-wrap`,`break-spaces`]}],break:[{break:[`normal`,`words`,`all`,`keep`]}],hyphens:[{hyphens:[`none`,`manual`,`auto`]}],content:[{content:[`none`,Z]}],"bg-attachment":[{bg:[`fixed`,`local`,`scroll`]}],"bg-clip":[{"bg-clip":[`border`,`padding`,`content`,`text`]}],"bg-opacity":[{"bg-opacity":[_]}],"bg-origin":[{"bg-origin":[`border`,`padding`,`content`]}],"bg-position":[{bg:[...re(),dd]}],"bg-repeat":[{bg:[`no-repeat`,{repeat:[``,`x`,`y`,`round`,`space`]}]}],"bg-size":[{bg:[`auto`,`cover`,`contain`,ud]}],"bg-image":[{bg:[`none`,{"gradient-to":[`t`,`tr`,`r`,`br`,`b`,`bl`,`l`,`tl`]},pd]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[_]}],"border-style":[{border:[...O(),`hidden`]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":[`divide-x-reverse`],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":[`divide-y-reverse`],"divide-opacity":[{"divide-opacity":[_]}],"divide-style":[{divide:O()}],"border-color":[{border:[i]}],"border-color-x":[{"border-x":[i]}],"border-color-y":[{"border-y":[i]}],"border-color-s":[{"border-s":[i]}],"border-color-e":[{"border-e":[i]}],"border-color-t":[{"border-t":[i]}],"border-color-r":[{"border-r":[i]}],"border-color-b":[{"border-b":[i]}],"border-color-l":[{"border-l":[i]}],"divide-color":[{divide:[i]}],"outline-style":[{outline:[``,...O()]}],"outline-offset":[{"outline-offset":[nd,Z]}],"outline-w":[{outline:[nd,rd]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:te()}],"ring-w-inset":[`ring-inset`],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[_]}],"ring-offset-w":[{"ring-offset":[nd,rd]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:[``,`inner`,`none`,cd,md]}],"shadow-color":[{shadow:[hd]}],opacity:[{opacity:[_]}],"mix-blend":[{"mix-blend":[...k(),`plus-lighter`,`plus-darker`]}],"bg-blend":[{"bg-blend":k()}],filter:[{filter:[``,`none`]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[c]}],"drop-shadow":[{"drop-shadow":[``,`none`,cd,Z]}],grayscale:[{grayscale:[l]}],"hue-rotate":[{"hue-rotate":[u]}],invert:[{invert:[d]}],saturate:[{saturate:[y]}],sepia:[{sepia:[x]}],"backdrop-filter":[{"backdrop-filter":[``,`none`]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[c]}],"backdrop-grayscale":[{"backdrop-grayscale":[l]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[_]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[x]}],"border-collapse":[{border:[`collapse`,`separate`]}],"border-spacing":[{"border-spacing":[o]}],"border-spacing-x":[{"border-spacing-x":[o]}],"border-spacing-y":[{"border-spacing-y":[o]}],"table-layout":[{table:[`auto`,`fixed`]}],caption:[{caption:[`top`,`bottom`]}],transition:[{transition:[`none`,`all`,``,`colors`,`opacity`,`shadow`,`transform`,Z]}],duration:[{duration:oe()}],ease:[{ease:[`linear`,`in`,`out`,`in-out`,Z]}],delay:[{delay:oe()}],animate:[{animate:[`none`,`spin`,`ping`,`pulse`,`bounce`,Z]}],transform:[{transform:[``,`gpu`,`none`]}],scale:[{scale:[b]}],"scale-x":[{"scale-x":[b]}],"scale-y":[{"scale-y":[b]}],rotate:[{rotate:[od,Z]}],"translate-x":[{"translate-x":[w]}],"translate-y":[{"translate-y":[w]}],"skew-x":[{"skew-x":[S]}],"skew-y":[{"skew-y":[S]}],"transform-origin":[{origin:[`center`,`top`,`top-right`,`right`,`bottom-right`,`bottom`,`bottom-left`,`left`,`top-left`,Z]}],accent:[{accent:[`auto`,e]}],appearance:[{appearance:[`none`,`auto`]}],cursor:[{cursor:[`auto`,`default`,`pointer`,`wait`,`text`,`move`,`help`,`not-allowed`,`none`,`context-menu`,`progress`,`cell`,`crosshair`,`vertical-text`,`alias`,`copy`,`no-drop`,`grab`,`grabbing`,`all-scroll`,`col-resize`,`row-resize`,`n-resize`,`e-resize`,`s-resize`,`w-resize`,`ne-resize`,`nw-resize`,`se-resize`,`sw-resize`,`ew-resize`,`ns-resize`,`nesw-resize`,`nwse-resize`,`zoom-in`,`zoom-out`,Z]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":[`none`,`auto`]}],resize:[{resize:[`none`,`y`,`x`,``]}],"scroll-behavior":[{scroll:[`auto`,`smooth`]}],"scroll-m":[{"scroll-m":D()}],"scroll-mx":[{"scroll-mx":D()}],"scroll-my":[{"scroll-my":D()}],"scroll-ms":[{"scroll-ms":D()}],"scroll-me":[{"scroll-me":D()}],"scroll-mt":[{"scroll-mt":D()}],"scroll-mr":[{"scroll-mr":D()}],"scroll-mb":[{"scroll-mb":D()}],"scroll-ml":[{"scroll-ml":D()}],"scroll-p":[{"scroll-p":D()}],"scroll-px":[{"scroll-px":D()}],"scroll-py":[{"scroll-py":D()}],"scroll-ps":[{"scroll-ps":D()}],"scroll-pe":[{"scroll-pe":D()}],"scroll-pt":[{"scroll-pt":D()}],"scroll-pr":[{"scroll-pr":D()}],"scroll-pb":[{"scroll-pb":D()}],"scroll-pl":[{"scroll-pl":D()}],"snap-align":[{snap:[`start`,`end`,`center`,`align-none`]}],"snap-stop":[{snap:[`normal`,`always`]}],"snap-type":[{snap:[`none`,`x`,`y`,`both`]}],"snap-strictness":[{snap:[`mandatory`,`proximity`]}],touch:[{touch:[`auto`,`none`,`manipulation`]}],"touch-x":[{"touch-pan":[`x`,`left`,`right`]}],"touch-y":[{"touch-pan":[`y`,`up`,`down`]}],"touch-pz":[`touch-pinch-zoom`],select:[{select:[`none`,`text`,`all`,`auto`]}],"will-change":[{"will-change":[`auto`,`scroll`,`contents`,`transform`,Z]}],fill:[{fill:[e,`none`]}],"stroke-w":[{stroke:[nd,rd,ad]}],stroke:[{stroke:[e,`none`]}],sr:[`sr-only`,`not-sr-only`],"forced-color-adjust":[{"forced-color-adjust":[`auto`,`none`]}]},conflictingClassGroups:{overflow:[`overflow-x`,`overflow-y`],overscroll:[`overscroll-x`,`overscroll-y`],inset:[`inset-x`,`inset-y`,`start`,`end`,`top`,`right`,`bottom`,`left`],"inset-x":[`right`,`left`],"inset-y":[`top`,`bottom`],flex:[`basis`,`grow`,`shrink`],gap:[`gap-x`,`gap-y`],p:[`px`,`py`,`ps`,`pe`,`pt`,`pr`,`pb`,`pl`],px:[`pr`,`pl`],py:[`pt`,`pb`],m:[`mx`,`my`,`ms`,`me`,`mt`,`mr`,`mb`,`ml`],mx:[`mr`,`ml`],my:[`mt`,`mb`],size:[`w`,`h`],"font-size":[`leading`],"fvn-normal":[`fvn-ordinal`,`fvn-slashed-zero`,`fvn-figure`,`fvn-spacing`,`fvn-fraction`],"fvn-ordinal":[`fvn-normal`],"fvn-slashed-zero":[`fvn-normal`],"fvn-figure":[`fvn-normal`],"fvn-spacing":[`fvn-normal`],"fvn-fraction":[`fvn-normal`],"line-clamp":[`display`,`overflow`],rounded:[`rounded-s`,`rounded-e`,`rounded-t`,`rounded-r`,`rounded-b`,`rounded-l`,`rounded-ss`,`rounded-se`,`rounded-ee`,`rounded-es`,`rounded-tl`,`rounded-tr`,`rounded-br`,`rounded-bl`],"rounded-s":[`rounded-ss`,`rounded-es`],"rounded-e":[`rounded-se`,`rounded-ee`],"rounded-t":[`rounded-tl`,`rounded-tr`],"rounded-r":[`rounded-tr`,`rounded-br`],"rounded-b":[`rounded-br`,`rounded-bl`],"rounded-l":[`rounded-tl`,`rounded-bl`],"border-spacing":[`border-spacing-x`,`border-spacing-y`],"border-w":[`border-w-s`,`border-w-e`,`border-w-t`,`border-w-r`,`border-w-b`,`border-w-l`],"border-w-x":[`border-w-r`,`border-w-l`],"border-w-y":[`border-w-t`,`border-w-b`],"border-color":[`border-color-s`,`border-color-e`,`border-color-t`,`border-color-r`,`border-color-b`,`border-color-l`],"border-color-x":[`border-color-r`,`border-color-l`],"border-color-y":[`border-color-t`,`border-color-b`],"scroll-m":[`scroll-mx`,`scroll-my`,`scroll-ms`,`scroll-me`,`scroll-mt`,`scroll-mr`,`scroll-mb`,`scroll-ml`],"scroll-mx":[`scroll-mr`,`scroll-ml`],"scroll-my":[`scroll-mt`,`scroll-mb`],"scroll-p":[`scroll-px`,`scroll-py`,`scroll-ps`,`scroll-pe`,`scroll-pt`,`scroll-pr`,`scroll-pb`,`scroll-pl`],"scroll-px":[`scroll-pr`,`scroll-pl`],"scroll-py":[`scroll-pt`,`scroll-pb`],touch:[`touch-x`,`touch-y`,`touch-pz`],"touch-x":[`touch`],"touch-y":[`touch`],"touch-pz":[`touch`]},conflictingClassGroupModifiers:{"font-size":[`leading`]}}},Q=qu(xd);function Sd(...e){return Q(Ou(e))}const Cd=jn({head:()=>({meta:[{charSet:`utf-8`},{name:`viewport`,content:`width=device-width, initial-scale=1`},{title:`Tinder AI Profile Analyzer`}]}),component:wd});function wd(){return(0,N.jsx)(Td,{children:(0,N.jsx)(qn,{})})}function Td({children:e}){return(0,N.jsx)(Eu,{children:(0,N.jsxs)(`html`,{lang:`en`,children:[(0,N.jsx)(`head`,{children:(0,N.jsx)(ar,{})}),(0,N.jsxs)(`body`,{className:Sd(`min-h-screen bg-background font-sans antialiased`),children:[e,(0,N.jsx)(sr,{})]})]})})}const Ed=()=>Ot(()=>import(`./welcome-DaUTS0x0.js`),__vite__mapDeps([0,1,2,3,4,5,6])),Dd=Mn(`/welcome`)({component:In(Ed,`component`)}),Od=()=>Ot(()=>import(`./routes-BLsNgnxf.js`),__vite__mapDeps([7,2,4,5,8])),kd=Mn(`/`)({component:In(Od,`component`)}),Ad=()=>Ot(()=>import(`./image-editor-test-BwhwjrcH.js`),__vite__mapDeps([9,10,11,12,13,14,2,15,16,17,18,19,20,6])),jd=Mn(`/_authed/image-editor-test`)({component:In(Ad,`component`)}),Md=()=>Ot(()=>import(`./image-analyzer-pro-BSNjoE5E.js`),__vite__mapDeps([21,11,22,2,13,14,23,24,25,20,15,17,3,26,27,28,5,8,6,29])),Nd=Mn(`/_authed/image-analyzer-pro`)({component:In(Md,`component`)}),Pd=()=>Ot(()=>import(`./image-analyzer-mcjslnNz.js`),__vite__mapDeps([30,11,22,2,13,14,23,24,25,20,15,3,28,5,31,12])),Fd=Mn(`/_authed/image-analyzer`)({component:In(Pd,`component`)}),$=()=>Ot(()=>import(`./dashboard-BBZPk5g4.js`),__vite__mapDeps([32,2,27,4,19,8,33,20,6])),Id=Mn(`/_authed/dashboard`)({component:In($,`component`)}),Ld=()=>Ot(()=>import(`./bio-analyzer-pro-DWKz7lQ3.js`),__vite__mapDeps([34,11,22,2,15,17,3,26,35,28,5,8,36,20,6,29])),Rd=Mn(`/_authed/bio-analyzer-pro`)({component:In(Ld,`component`)}),zd=()=>Ot(()=>import(`./bio-analyzer-DgsoUF1K.js`),__vite__mapDeps([37,11,22,2,15,16,17,3,35,28,5,25,20,31,12])),Bd=Mn(`/_authed/bio-analyzer`)({component:In(zd,`component`)}),Vd=()=>Ot(()=>import(`./analysis-comparison-Cnr7fkj1.js`),__vite__mapDeps([38,2,26,27,28,5,8,36,20,6])),Hd=Mn(`/_authed/analysis-comparison`)({component:In(Vd,`component`)}),Ud=()=>Ot(()=>import(`./account-settings-C00NADvv.js`),__vite__mapDeps([39,1,14,2,3,18,24,33,20,6])),Wd=Mn(`/_authed/account-settings`)({component:In(Ud,`component`)}),Gd=()=>Ot(()=>import(`./404-JXD3VwG4.js`),[]),Kd=Mn(`/_authed/404`)({component:In(Gd,`component`)}),qd=Dd.update({id:`/welcome`,path:`/welcome`,getParentRoute:()=>Cd}),Jd=kd.update({id:`/`,path:`/`,getParentRoute:()=>Cd}),Yd=jd.update({id:`/_authed/image-editor-test`,path:`/image-editor-test`,getParentRoute:()=>Cd}),Xd=Nd.update({id:`/_authed/image-analyzer-pro`,path:`/image-analyzer-pro`,getParentRoute:()=>Cd}),Zd=Fd.update({id:`/_authed/image-analyzer`,path:`/image-analyzer`,getParentRoute:()=>Cd}),Qd=Id.update({id:`/_authed/dashboard`,path:`/dashboard`,getParentRoute:()=>Cd}),$d=Rd.update({id:`/_authed/bio-analyzer-pro`,path:`/bio-analyzer-pro`,getParentRoute:()=>Cd}),ef=Bd.update({id:`/_authed/bio-analyzer`,path:`/bio-analyzer`,getParentRoute:()=>Cd}),tf=Hd.update({id:`/_authed/analysis-comparison`,path:`/analysis-comparison`,getParentRoute:()=>Cd}),nf=Wd.update({id:`/_authed/account-settings`,path:`/account-settings`,getParentRoute:()=>Cd}),rf=Kd.update({id:`/_authed/404`,path:`/404`,getParentRoute:()=>Cd}),af={IndexRoute:Jd,WelcomeRoute:qd,Authed404Route:rf,AuthedAccountSettingsRoute:nf,AuthedAnalysisComparisonRoute:tf,AuthedBioAnalyzerRoute:ef,AuthedBioAnalyzerProRoute:$d,AuthedDashboardRoute:Qd,AuthedImageAnalyzerRoute:Zd,AuthedImageAnalyzerProRoute:Xd,AuthedImageEditorTestRoute:Yd},sf=Cd._addFileChildren(af)._addFileTypes();function cf({error:e}){let t=en(),n=rn({strict:!1,select:e=>e.id===at});return console.error(`DefaultCatchBoundary Error:`,e),(0,N.jsxs)(`div`,{className:`min-w-0 flex-1 p-4 flex flex-col items-center justify-center gap-6`,children:[(0,N.jsx)(Vt,{error:e}),(0,N.jsxs)(`div`,{className:`flex gap-2 items-center flex-wrap`,children:[(0,N.jsx)(`button`,{onClick:()=>{t.invalidate()},className:`px-2 py-1 bg-gray-600 dark:bg-gray-700 rounded text-white uppercase font-extrabold`,children:`Try Again`}),n?(0,N.jsx)(En,{to:`/`,className:`px-2 py-1 bg-gray-600 dark:bg-gray-700 rounded text-white uppercase font-extrabold`,children:`Home`}):(0,N.jsx)(En,{to:`/`,className:`px-2 py-1 bg-gray-600 dark:bg-gray-700 rounded text-white uppercase font-extrabold`,onClick:e=>{e.preventDefault(),window.history.back()},children:`Go Back`})]})]})}function lf({children:e}){return(0,N.jsxs)(`div`,{className:`space-y-2 p-2`,children:[(0,N.jsx)(`div`,{className:`text-gray-600 dark:text-gray-400`,children:e||(0,N.jsx)(`p`,{children:`The page you are looking for does not exist.`})}),(0,N.jsxs)(`p`,{className:`flex items-center gap-2 flex-wrap`,children:[(0,N.jsx)(`button`,{onClick:()=>window.history.back(),className:`bg-emerald-500 text-white px-2 py-1 rounded uppercase font-black text-sm`,children:`Go back`}),(0,N.jsx)(En,{to:`/`,className:`bg-cyan-600 text-white px-2 py-1 rounded uppercase font-black text-sm`,children:`Start Over`})]})]})}function uf(){let e=Xn({routeTree:sf,defaultPreload:`intent`,defaultErrorComponent:cf,defaultNotFoundComponent:()=>(0,N.jsx)(lf,{}),scrollRestoration:!0});return e}var df=u(pr());const ff=uf();(0,df.hydrateRoot)(document,(0,N.jsx)(P.StrictMode,{children:(0,N.jsx)(lr,{router:ff})}));export{En as Link,J as OrganizationList,Vl as OrganizationProfile,Tl as SignIn,El as SignUp,$s as SignedIn,ec as SignedOut,Ll as UserButton,Al as UserProfile,s as __commonJSMin,o as __esmMin,c as __export,d as __toCommonJS,u as __toESM,Ot as __vitePreload,Ou as clsx,Sd as cn,xs as errorThrower,zs as incompatibleRoutingWithPathProvidedError,Rs as noPathProvidedError,Nt as require_jsx_runtime,Ft as require_react,dn as require_react_dom,tr as useLocation,sn as useParams,Vo as useUser};