import{__toESM as e,cn as t,require_jsx_runtime as n,require_react as r,require_react_dom as i}from"./main-B4G73TvM.js";import{createLucideIcon as a,createSlot as o}from"./createLucideIcon-JB7IMeGf.js";const s=a(`RefreshCw`,[[`path`,{d:`M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8`,key:`v9h5vc`}],[`path`,{d:`M21 3v5h-5`,key:`1q7to0`}],[`path`,{d:`M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16`,key:`3uifl3`}],[`path`,{d:`M8 16H3v5`,key:`1cv678`}]]);function c(e,[t,n]){return Math.min(n,Math.max(t,e))}function l(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),n===!1||!r.defaultPrevented)return t?.(r)}}var u=e(r(),1);function d(e,t){if(typeof e==`function`)return e(t);e!=null&&(e.current=t)}function f(...e){return t=>{let n=!1,r=e.map(e=>{let r=d(e,t);return!n&&typeof r==`function`&&(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];typeof n==`function`?n():d(e[t],null)}}}}function p(...e){return u.useCallback(f(...e),e)}var m=e(n(),1);function h(e,t=[]){let n=[];function r(t,r){let i=u.createContext(r),a=n.length;n=[...n,r];let o=t=>{let{scope:n,children:r,...o}=t,s=n?.[e]?.[a]||i,c=u.useMemo(()=>o,Object.values(o));return(0,m.jsx)(s.Provider,{value:c,children:r})};o.displayName=t+`Provider`;function s(n,o){let s=o?.[e]?.[a]||i,c=u.useContext(s);if(c)return c;if(r!==void 0)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}return[o,s]}let i=()=>{let t=n.map(e=>u.createContext(e));return function(n){let r=n?.[e]||t;return u.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return i.scopeName=e,[r,g(i,...t)]}function g(...e){let t=e[0];if(e.length===1)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e),a=i[`__scope${r}`];return{...t,...a}},{});return u.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}var _=globalThis?.document?u.useLayoutEffect:()=>{},v=u.useInsertionEffect||_;function y({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,o]=b({defaultProp:t,onChange:n}),s=e!==void 0,c=s?e:i;{let t=u.useRef(e!==void 0);u.useEffect(()=>{let e=t.current;if(e!==s){let t=e?`controlled`:`uncontrolled`,n=s?`controlled`:`uncontrolled`;console.warn(`${r} is changing from ${t} to ${n}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,r])}let l=u.useCallback(t=>{if(s){let n=x(t)?t(e):t;n!==e&&o.current?.(n)}else a(t)},[s,e,a,o]);return[c,l]}function b({defaultProp:e,onChange:t}){let[n,r]=u.useState(e),i=u.useRef(n),a=u.useRef(t);return v(()=>{a.current=t},[t]),u.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}function x(e){return typeof e==`function`}var S=Symbol(`RADIX:SYNC_STATE`),C=u.createContext(void 0);function w(e){let t=u.useContext(C);return e||t||`ltr`}function T(e){let t=u.useRef({value:e,previous:e});return u.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}function E(e){let[t,n]=u.useState(void 0);return _(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{if(!Array.isArray(t)||!t.length)return;let r=t[0],i,a;if(`borderBoxSize`in r){let e=r.borderBoxSize,t=Array.isArray(e)?e[0]:e;i=t.inlineSize,a=t.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return t.observe(e,{box:`border-box`}),()=>t.unobserve(e)}else n(void 0)},[e]),t}var D=e(i(),1),O=[`a`,`button`,`div`,`form`,`h2`,`h3`,`img`,`input`,`label`,`li`,`nav`,`ol`,`p`,`select`,`span`,`svg`,`ul`],k=O.reduce((e,t)=>{let n=o(`Primitive.${t}`),r=u.forwardRef((e,r)=>{let{asChild:i,...a}=e,o=i?n:t;return typeof window<`u`&&(window[Symbol.for(`radix-ui`)]=!0),(0,m.jsx)(o,{...a,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function A(e){let t=e+`CollectionProvider`,[n,r]=h(t),[i,a]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:n}=e,r=u.useRef(null),a=u.useRef(new Map).current;return(0,m.jsx)(i,{scope:t,itemMap:a,collectionRef:r,children:n})};s.displayName=t;let c=e+`CollectionSlot`,l=o(c),d=u.forwardRef((e,t)=>{let{scope:n,children:r}=e,i=a(c,n),o=p(t,i.collectionRef);return(0,m.jsx)(l,{ref:o,children:r})});d.displayName=c;let f=e+`CollectionItemSlot`,g=`data-radix-collection-item`,_=o(f),v=u.forwardRef((e,t)=>{let{scope:n,children:r,...i}=e,o=u.useRef(null),s=p(t,o),c=a(f,n);return u.useEffect(()=>(c.itemMap.set(o,{ref:o,...i}),()=>void c.itemMap.delete(o))),(0,m.jsx)(_,{[g]:``,ref:s,children:r})});v.displayName=f;function y(t){let n=a(e+`CollectionConsumer`,t),r=u.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${g}]`)),r=Array.from(n.itemMap.values()),i=r.sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current));return i},[n.collectionRef,n.itemMap]);return r}return[{Provider:s,Slot:d,ItemSlot:v},y,r]}var j=[`PageUp`,`PageDown`],M=[`ArrowUp`,`ArrowDown`,`ArrowLeft`,`ArrowRight`],N={"from-left":[`Home`,`PageDown`,`ArrowDown`,`ArrowLeft`],"from-right":[`Home`,`PageDown`,`ArrowDown`,`ArrowRight`],"from-bottom":[`Home`,`PageDown`,`ArrowDown`,`ArrowLeft`],"from-top":[`Home`,`PageDown`,`ArrowUp`,`ArrowLeft`]},P=`Slider`,[F,ee,te]=A(P),[I,ne]=h(P,[te]),[re,L]=I(P),R=u.forwardRef((e,t)=>{let{name:n,min:r=0,max:i=100,step:a=1,orientation:o=`horizontal`,disabled:s=!1,minStepsBetweenThumbs:d=0,defaultValue:f=[r],value:p,onValueChange:h=()=>{},onValueCommit:g=()=>{},inverted:_=!1,form:v,...b}=e,x=u.useRef(new Set),S=u.useRef(0),C=o===`horizontal`,w=C?ie:ae,[T=[],E]=y({prop:p,defaultProp:f,onChange:e=>{let t=[...x.current];t[S.current]?.focus(),h(e)}}),D=u.useRef(T);function O(e){let t=ue(T,e);N(e,t)}function k(e){N(e,S.current)}function A(){let e=D.current[S.current],t=T[S.current],n=t!==e;n&&g(T)}function N(e,t,{commit:n}={commit:!1}){let o=me(a),s=he(Math.round((e-r)/a)*a+r,o),l=c(s,[r,i]);E((e=[])=>{let r=ce(e,l,t);if(pe(r,d*a)){S.current=r.indexOf(l);let t=String(r)!==String(e);return t&&n&&g(r),t?r:e}else return e})}return(0,m.jsx)(re,{scope:e.__scopeSlider,name:n,disabled:s,min:r,max:i,valueIndexToChangeRef:S,thumbs:x.current,values:T,orientation:o,form:v,children:(0,m.jsx)(F.Provider,{scope:e.__scopeSlider,children:(0,m.jsx)(F.Slot,{scope:e.__scopeSlider,children:(0,m.jsx)(w,{"aria-disabled":s,"data-disabled":s?``:void 0,...b,ref:t,onPointerDown:l(b.onPointerDown,()=>{s||(D.current=T)}),min:r,max:i,inverted:_,onSlideStart:s?void 0:O,onSlideMove:s?void 0:k,onSlideEnd:s?void 0:A,onHomeKeyDown:()=>!s&&N(r,0,{commit:!0}),onEndKeyDown:()=>!s&&N(i,T.length-1,{commit:!0}),onStepKeyDown:({event:e,direction:t})=>{if(!s){let n=j.includes(e.key),r=n||e.shiftKey&&M.includes(e.key),i=r?10:1,o=S.current,s=T[o],c=a*i*t;N(s+c,o,{commit:!0})}}})})})})});R.displayName=P;var[z,B]=I(P,{startEdge:`left`,endEdge:`right`,size:`width`,direction:1}),ie=u.forwardRef((e,t)=>{let{min:n,max:r,dir:i,inverted:a,onSlideStart:o,onSlideMove:s,onSlideEnd:c,onStepKeyDown:l,...d}=e,[f,h]=u.useState(null),g=p(t,e=>h(e)),_=u.useRef(void 0),v=w(i),y=v===`ltr`,b=y&&!a||!y&&a;function x(e){let t=_.current||f.getBoundingClientRect(),i=[0,t.width],a=b?[n,r]:[r,n],o=X(i,a);return _.current=t,o(e-t.left)}return(0,m.jsx)(z,{scope:e.__scopeSlider,startEdge:b?`left`:`right`,endEdge:b?`right`:`left`,direction:b?1:-1,size:`width`,children:(0,m.jsx)(V,{dir:v,"data-orientation":`horizontal`,...d,ref:g,style:{...d.style,"--radix-slider-thumb-transform":`translateX(-50%)`},onSlideStart:e=>{let t=x(e.clientX);o?.(t)},onSlideMove:e=>{let t=x(e.clientX);s?.(t)},onSlideEnd:()=>{_.current=void 0,c?.()},onStepKeyDown:e=>{let t=b?`from-left`:`from-right`,n=N[t].includes(e.key);l?.({event:e,direction:n?-1:1})}})})}),ae=u.forwardRef((e,t)=>{let{min:n,max:r,inverted:i,onSlideStart:a,onSlideMove:o,onSlideEnd:s,onStepKeyDown:c,...l}=e,d=u.useRef(null),f=p(t,d),h=u.useRef(void 0),g=!i;function _(e){let t=h.current||d.current.getBoundingClientRect(),i=[0,t.height],a=g?[r,n]:[n,r],o=X(i,a);return h.current=t,o(e-t.top)}return(0,m.jsx)(z,{scope:e.__scopeSlider,startEdge:g?`bottom`:`top`,endEdge:g?`top`:`bottom`,size:`height`,direction:g?1:-1,children:(0,m.jsx)(V,{"data-orientation":`vertical`,...l,ref:f,style:{...l.style,"--radix-slider-thumb-transform":`translateY(50%)`},onSlideStart:e=>{let t=_(e.clientY);a?.(t)},onSlideMove:e=>{let t=_(e.clientY);o?.(t)},onSlideEnd:()=>{h.current=void 0,s?.()},onStepKeyDown:e=>{let t=g?`from-bottom`:`from-top`,n=N[t].includes(e.key);c?.({event:e,direction:n?-1:1})}})})}),V=u.forwardRef((e,t)=>{let{__scopeSlider:n,onSlideStart:r,onSlideMove:i,onSlideEnd:a,onHomeKeyDown:o,onEndKeyDown:s,onStepKeyDown:c,...u}=e,d=L(P,n);return(0,m.jsx)(k.span,{...u,ref:t,onKeyDown:l(e.onKeyDown,e=>{e.key===`Home`?(o(e),e.preventDefault()):e.key===`End`?(s(e),e.preventDefault()):j.concat(M).includes(e.key)&&(c(e),e.preventDefault())}),onPointerDown:l(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),d.thumbs.has(t)?t.focus():r(e)}),onPointerMove:l(e.onPointerMove,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&i(e)}),onPointerUp:l(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),a(e))})})}),H=`SliderTrack`,U=u.forwardRef((e,t)=>{let{__scopeSlider:n,...r}=e,i=L(H,n);return(0,m.jsx)(k.span,{"data-disabled":i.disabled?``:void 0,"data-orientation":i.orientation,...r,ref:t})});U.displayName=H;var W=`SliderRange`,G=u.forwardRef((e,t)=>{let{__scopeSlider:n,...r}=e,i=L(W,n),a=B(W,n),o=u.useRef(null),s=p(t,o),c=i.values.length,l=i.values.map(e=>Y(e,i.min,i.max)),d=c>1?Math.min(...l):0,f=100-Math.max(...l);return(0,m.jsx)(k.span,{"data-orientation":i.orientation,"data-disabled":i.disabled?``:void 0,...r,ref:s,style:{...e.style,[a.startEdge]:d+`%`,[a.endEdge]:f+`%`}})});G.displayName=W;var K=`SliderThumb`,q=u.forwardRef((e,t)=>{let n=ee(e.__scopeSlider),[r,i]=u.useState(null),a=p(t,e=>i(e)),o=u.useMemo(()=>r?n().findIndex(e=>e.ref.current===r):-1,[n,r]);return(0,m.jsx)(oe,{...e,ref:a,index:o})}),oe=u.forwardRef((e,t)=>{let{__scopeSlider:n,index:r,name:i,...a}=e,o=L(K,n),s=B(K,n),[c,d]=u.useState(null),f=p(t,e=>d(e)),h=c?o.form||!!c.closest(`form`):!0,g=E(c),_=o.values[r],v=_===void 0?0:Y(_,o.min,o.max),y=le(r,o.values.length),b=g?.[s.size],x=b?de(b,v,s.direction):0;return u.useEffect(()=>{if(c)return o.thumbs.add(c),()=>{o.thumbs.delete(c)}},[c,o.thumbs]),(0,m.jsxs)(`span`,{style:{transform:`var(--radix-slider-thumb-transform)`,position:`absolute`,[s.startEdge]:`calc(${v}% + ${x}px)`},children:[(0,m.jsx)(F.ItemSlot,{scope:e.__scopeSlider,children:(0,m.jsx)(k.span,{role:`slider`,"aria-label":e[`aria-label`]||y,"aria-valuemin":o.min,"aria-valuenow":_,"aria-valuemax":o.max,"aria-orientation":o.orientation,"data-orientation":o.orientation,"data-disabled":o.disabled?``:void 0,tabIndex:o.disabled?void 0:0,...a,ref:f,style:_===void 0?{display:`none`}:e.style,onFocus:l(e.onFocus,()=>{o.valueIndexToChangeRef.current=r})})}),h&&(0,m.jsx)(J,{name:i??(o.name?o.name+(o.values.length>1?`[]`:``):void 0),form:o.form,value:_},r)]})});q.displayName=K;var se=`RadioBubbleInput`,J=u.forwardRef(({__scopeSlider:e,value:t,...n},r)=>{let i=u.useRef(null),a=p(i,r),o=T(t);return u.useEffect(()=>{let e=i.current;if(!e)return;let n=window.HTMLInputElement.prototype,r=Object.getOwnPropertyDescriptor(n,`value`),a=r.set;if(o!==t&&a){let n=new Event(`input`,{bubbles:!0});a.call(e,t),e.dispatchEvent(n)}},[o,t]),(0,m.jsx)(k.input,{style:{display:`none`},...n,ref:a,defaultValue:t})});J.displayName=se;function ce(e=[],t,n){let r=[...e];return r[n]=t,r.sort((e,t)=>e-t)}function Y(e,t,n){let r=n-t,i=100/r,a=i*(e-t);return c(a,[0,100])}function le(e,t){if(t>2)return`Value ${e+1} of ${t}`;if(t===2)return[`Minimum`,`Maximum`][e]}function ue(e,t){if(e.length===1)return 0;let n=e.map(e=>Math.abs(e-t)),r=Math.min(...n);return n.indexOf(r)}function de(e,t,n){let r=e/2,i=50,a=X([0,i],[0,r]);return(r-a(t)*n)*n}function fe(e){return e.slice(0,-1).map((t,n)=>e[n+1]-t)}function pe(e,t){if(t>0){let n=fe(e),r=Math.min(...n);return r>=t}return!0}function X(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}function me(e){return(String(e).split(`.`)[1]||``).length}function he(e,t){let n=10**t;return Math.round(e*n)/n}var Z=R,ge=U,_e=G,Q=q;const $=u.forwardRef(({className:e,...n},r)=>(0,m.jsxs)(Z,{ref:r,className:t(`relative flex w-full touch-none select-none items-center`,e),...n,children:[(0,m.jsx)(ge,{className:`relative h-1.5 w-full grow overflow-hidden rounded-full bg-graphite-60/20`,children:(0,m.jsx)(_e,{className:`absolute h-full bg-gradient-primary`})}),(0,m.jsx)(Q,{className:`block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-gradient-primary shadow-md`})]}));$.displayName=Z.displayName;export{s as RefreshCw,$ as Slider};