import{e as t,n as s,i as a,j as r,b as n,d as o,f as e,g as i,h as p,O as u,k as g}from"./routeTree.gen-BFK54byf.mjs";function useRoutingProps(r,n,o){const e=n.path||(null==o?void 0:o.path);return"path"===(n.routing||(null==o?void 0:o.routing)||"path")?e?{...o,...n,routing:"path"}:t.throw(s(r)):n.path?t.throw(a(r)):{...o,...n,path:void 0}}var usePathnameWithoutSplatRouteParams=()=>{const{_splat:t}=i({strict:!1}),{pathname:s}=p(),a=t||"";return`/${s.replace(a,"").replace(/\/$/,"").replace(/^\//,"").trim()}`},h=Object.assign(t=>{const s=usePathnameWithoutSplatRouteParams();return r.jsx(e,{...useRoutingProps("UserProfile",t,{path:s})})},{...e});Object.assign(t=>{const s=usePathnameWithoutSplatRouteParams();return r.jsx(u,{...useRoutingProps("OrganizationProfile",t,{path:s})})},{...u}),Object.assign(t=>{const s=usePathnameWithoutSplatRouteParams();return r.jsx(g,{...useRoutingProps("OrganizationList",t,{path:s})})},{...g});var SignIn=t=>{const s=usePathnameWithoutSplatRouteParams();return r.jsx(n,{...useRoutingProps("SignIn",t,{path:s})})},SignUp=t=>{const s=usePathnameWithoutSplatRouteParams();return r.jsx(o,{...useRoutingProps("SignUp",t,{path:s})})};export{SignIn as S,h as U,SignUp as a};
//# sourceMappingURL=uiComponents.mjs.map
