{"version": 3, "file": "performance.mjs", "sources": ["../../../../node_modules/nitropack/node_modules/unenv/dist/runtime/web/performance/_polyfills.mjs", "../../../../node_modules/nitropack/node_modules/unenv/dist/runtime/web/performance/index.mjs", "../../../../node_modules/nitropack/node_modules/unenv/dist/runtime/polyfill/performance.mjs"], "sourcesContent": null, "names": ["_time<PERSON><PERSON><PERSON>", "globalThis", "performance", "<PERSON><PERSON><PERSON><PERSON>", "Date", "now", "_performanceNow", "bind", "_supportedEntryTypes", "_PerformanceEntry", "__unenv__", "detail", "entryType", "name", "startTime", "constructor", "options", "this", "duration", "toJSON", "_PerformanceMark", "_PerformanceMeasure", "_Performance", "eventCounts", "Map", "_entries", "_resourceTimingBufferSize", "navigation", "undefined", "timing", "onresourcetimingbufferfull", "clearMarks", "<PERSON><PERSON><PERSON>", "filter", "e", "clearMeasures", "measureName", "clearResourceTimings", "getEntries", "getEntriesByName", "type", "getEntriesByType", "mark", "entry", "push", "measure", "startOrMeasureOptions", "endMark", "start", "end", "Number", "parseFloat", "setResourceTimingBufferSize", "maxSize", "addEventListener", "listener", "createNotImplementedError", "removeEventListener", "dispatchEvent", "event", "PerformanceEntry", "PerformanceMark", "PerformanceMeasure", "PerformanceResourceTiming", "serverTiming", "connectEnd", "connectStart", "decodedBodySize", "domainLookupEnd", "domainLookupStart", "encodedBodySize", "fetchStart", "initiatorType", "nextHopProtocol", "redirectEnd", "redirectStart", "requestStart", "responseEnd", "responseStart", "secureConnectionStart", "transferSize", "workerStart", "responseStatus", "PerformanceObserver", "static", "_callback", "callback", "takeRecords", "disconnect", "observe", "Performance", "PerformanceObserverEntryList", "_name", "_type"], "mappings": "", "x_google_ignoreList": [0, 1, 2]}