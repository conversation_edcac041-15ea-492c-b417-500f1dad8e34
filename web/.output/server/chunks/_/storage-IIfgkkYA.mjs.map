{"version": 3, "file": "storage-IIfgkkYA.mjs", "sources": ["../../../../../src/lib/storage.ts"], "sourcesContent": null, "names": ["_a", "STORE_NAME", "SESSION_KEY", "Session<PERSON>anager", "constructor", "__publicField", "this", "getInstance", "instance", "getCurrentSession", "currentSession", "stored", "localStorage", "getItem", "JSON", "parse", "e", "console", "warn", "id", "Date", "now", "Math", "random", "toString", "substring", "createdAt", "imageIds", "results", "saveSession", "addImageToSession", "imageId: string", "session", "includes", "imageId", "push", "removeImageFromSession", "filter", "saveAnalysisResult", "result: any", "result", "getAnalysisResult", "clearSession", "removeItem", "setItem", "stringify", "imageStorage", "init", "Promise", "resolve", "reject", "request", "indexedDB", "open", "onerror", "error", "onsuccess", "db", "onupgradeneeded", "event", "target", "objectStoreNames", "contains", "store", "createObjectStore", "keyP<PERSON>", "createIndex", "unique", "storeImage", "file: File", "sessionId: string", "sessionId", "storedImage: StoredImage", "fileName", "file", "name", "blob", "mimeType", "type", "uploadedAt", "transaction", "objectStore", "add", "storedImage", "getImage", "id: string", "get", "getSessionImages", "index", "getAll", "deleteImage", "delete", "deletePromises", "map", "img", "all", "cleanup", "cutoff", "openCursor", "IDBKeyRange", "upperBound", "cursor", "continue", "session<PERSON>anager", "async", "convertBlobToBase64", "blob: Blob", "reader", "FileReader", "onload", "split", "readAsDataURL", "createImagePreview", "URL", "createObjectURL", "revokeImagePreview", "url: string", "revokeObjectURL", "url", "initStorage"], "mappings": "IACAA,oKAAA,MAEMC,EAAa,SACbC,EAAc,mBAkBpB,IAyHaC,GAAbH,EAAA,MAAA,WAAAI,GAEEC,cAAAC,KAAA,iBAAiD,KAAA,CAEjD,kBAAOC,GAIL,OAHKP,EAAeQ,WAClBR,EAAeQ,SAAW,IAAIR,GAEzBA,EAAeQ,QACvB,CAED,iBAAAC,GACE,GAAIH,KAAKI,eAAgB,OAAOJ,KAAKI,eAErC,MAAMC,EAASC,aAAaC,QAAQX,GACpC,GAAIS,EACF,IAEE,OADAL,KAAKI,eAAiBI,KAAKC,MAAMJ,GAC1BL,KAAKI,cACb,OAAQM,GACPC,QAAQC,KAAK,mDACd,CAWH,OARAZ,KAAKI,eAAiB,CACpBS,GAAA,WAAeC,KAAKC,SAASC,KAAKC,SAASC,SAAS,IAAIC,UAAU,EAAG,MACrEC,UAAWN,KAAKC,MAChBM,SAAU,GACVC,QAAS,CAAA,GAGXtB,KAAKuB,cACEvB,KAAKI,cACb,CAED,iBAAAoB,CAAkBC,GAChB,MAAMC,EAAU1B,KAAKG,oBAChBuB,EAAQL,SAASM,SAASC,KAC7BF,EAAQL,SAASQ,KAAKD,GACtB5B,KAAKuB,cAER,CAED,sBAAAO,CAAuBL,GACrB,MAAMC,EAAU1B,KAAKG,oBACrBuB,EAAQL,SAAWK,EAAQL,SAASU,OAAQlB,GAAOA,IAAOe,UACnDF,EAAQJ,QAAQM,GACvB5B,KAAKuB,aACN,CAED,kBAAAS,CAAmBP,EAAiBQ,GAClBjC,KAAKG,oBACbmB,QAAQM,GAAWM,EAC3BlC,KAAKuB,aACN,CAED,iBAAAY,CAAkBV,GAEhB,OADgBzB,KAAKG,oBACNmB,QAAQM,EACxB,CAED,YAAAQ,GACE9B,aAAa+B,WAAWzC,GACxBI,KAAKI,eAAiB,IACvB,CAED,WAAAmB,GACMvB,KAAKI,gBACPE,aAAagC,QAAQ1C,EAAaY,KAAK+B,UAAUvC,KAAKI,gBAEzD,GArEDL,cADFL,EACiB,YADjBA,GA0EA,MAAa8C,EAAe,IAnM5B,MAAA,WAAA1C,GACEC,cAAAC,KAAA,KAAiC,KAAA,CAEjC,UAAMyC,GACJ,OAAO,IAAIC,QAAQ,CAACC,EAASC,KAC3B,MAAMC,EAAUC,UAAUC,KA1BhB,kBACG,GA2BbF,EAAQG,QAAU,IAAMJ,EAAOC,EAAQI,OACvCJ,EAAQK,UAAY,KAClBlD,KAAKmD,GAAKN,EAAQX,OAClBS,KAGFE,EAAQO,gBAAmBC,IACzB,MAAMF,EAAME,EAAMC,OAA4BpB,OAC9C,IAAKiB,EAAGI,iBAAiBC,SAAS7D,GAAa,CAC7C,MAAM8D,EAAQN,EAAGO,kBAAkB/D,EAAY,CAAEgE,QAAS,OAC1DF,EAAMG,YAAY,YAAa,YAAa,CAAEC,QAAQ,IACtDJ,EAAMG,YAAY,aAAc,aAAc,CAAEC,QAAQ,GACzD,IAGN,CAED,gBAAMC,CAAWC,EAAYC,GACtBhE,KAAKmD,UAAUnD,KAAKyC,OAEzB,MAAM5B,EAAA,GAAQoD,KAAanD,KAAKC,SAASC,KAAKC,SAASC,SAAS,IAAIC,UAAU,EAAG,MAC3E+C,EAA2B,CAC/BrD,KACAsD,SAAUC,EAAKC,KACfC,KAAMF,EACNG,SAAUH,EAAKI,KACfC,WAAY3D,KAAKC,MACjBkD,aAGF,OAAO,IAAIvB,QAAQ,CAACC,EAASC,KAC3B,MAEMC,EAFc7C,KAAKmD,GAAIuB,YAAY,CAAC/E,GAAa,aAC7BgF,YAAYhF,GAChBiF,IAAIC,GAE1BhC,EAAQK,UAAY,IAAMP,EAAQ9B,GAClCgC,EAAQG,QAAU,IAAMJ,EAAOC,EAAQI,QAE1C,CAED,cAAM6B,CAASC,GAGb,OAFK/E,KAAKmD,UAAUnD,KAAKyC,OAElB,IAAIC,QAAQ,CAACC,EAASC,KAC3B,MAEMC,EAFc7C,KAAKmD,GAAIuB,YAAY,CAAC/E,GAAa,YAC7BgF,YAAYhF,GAChBqF,IAAInE,GAE1BgC,EAAQK,UAAY,IAAMP,EAAQE,EAAQX,QAAU,MACpDW,EAAQG,QAAU,IAAMJ,EAAOC,EAAQI,QAE1C,CAED,sBAAMgC,CAAiBjB,GAGrB,OAFKhE,KAAKmD,UAAUnD,KAAKyC,OAElB,IAAIC,QAAQ,CAACC,EAASC,KAC3B,MAGMC,EAHc7C,KAAKmD,GAAIuB,YAAY,CAAC/E,GAAa,YAC7BgF,YAAYhF,GAClBuF,MAAM,aACJC,OAAOlB,GAE7BpB,EAAQK,UAAY,IAAMP,EAAQE,EAAQX,QAC1CW,EAAQG,QAAU,IAAMJ,EAAOC,EAAQI,QAE1C,CAED,iBAAMmC,CAAYL,GAGhB,OAFK/E,KAAKmD,UAAUnD,KAAKyC,OAElB,IAAIC,QAAQ,CAACC,EAASC,KAC3B,MAEMC,EAFc7C,KAAKmD,GAAIuB,YAAY,CAAC/E,GAAa,aAC7BgF,YAAYhF,GAChB0F,OAAOxE,GAE7BgC,EAAQK,UAAY,IAAMP,IAC1BE,EAAQG,QAAU,IAAMJ,EAAOC,EAAQI,QAE1C,CAED,kBAAMb,CAAa4B,GACZhE,KAAKmD,UAAUnD,KAAKyC,OAEzB,MACM6C,SADetF,KAAKiF,iBAAiBhB,IACbsB,IAAKC,GAAQxF,KAAKoF,YAAYI,EAAI3E,WAC1D6B,QAAQ+C,IAAIH,EACnB,CAED,aAAMI,GACC1F,KAAKmD,UAAUnD,KAAKyC,OAEzB,MAAMkD,EAAS7E,KAAKC,MAAQ,MAE5B,OAAO,IAAI2B,QAAQ,CAACC,EAASC,KAC3B,MAGMC,EAHc7C,KAAKmD,GAAIuB,YAAY,CAAC/E,GAAa,aAC7BgF,YAAYhF,GAClBuF,MAAM,cACJU,WAAWC,YAAYC,WAAWH,IAExD9C,EAAQK,UAAaG,IACnB,MAAM0C,EAAU1C,EAAMC,OAAsBpB,OACxC6D,GACFA,EAAOV,SACPU,EAAOC,YAEPrD,KAGJE,EAAQG,QAAU,IAAMJ,EAAOC,EAAQI,QAE1C,GA+EUgD,EAAiBpG,EAAeI,cAG7CiG,eAAsBC,oBAAoBC,GACxC,OAAO,IAAI1D,QAAQ,CAACC,EAASC,KAC3B,MAAMyD,EAAS,IAAIC,WACnBD,EAAOE,OAAS,KACd,MAAMrE,EAASmE,EAAOnE,OACtBS,EAAQT,EAAOsE,MAAM,KAAK,KAE5BH,EAAOrD,QAAUJ,EACjByD,EAAOI,cAAcnC,IAExB,CAED,SAAgBoC,mBAAmBN,GACjC,OAAOO,IAAIC,gBAAgBtC,EAC5B,CAED,SAAgBuC,mBAAmBC,GACjCH,IAAII,gBAAgBC,EACrB,CAGDd,eAAsBe,oBACdzE,EAAaC,aACbD,EAAakD,SACpB"}