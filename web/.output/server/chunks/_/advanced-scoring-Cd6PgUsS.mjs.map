{"version": 3, "file": "advanced-scoring-Cd6PgUsS.mjs", "sources": ["../../../../../src/lib/advanced/types/expert-personas.ts", "../../../../../src/lib/advanced/scoring/advanced-scoring.ts"], "sourcesContent": null, "names": ["EXPERT_PERSONAS: Record<string, ExpertPersona>", "photography", "type", "name", "credentials", "background", "expertise", "analysisApproach", "specializations", "psychology", "fashion", "data_science", "dating_coach", "getExpertPersona", "expertType: string", "persona", "EXPERT_PERSONAS", "expertType", "Error", "getAllExpertTypes", "Object", "keys", "getExpertCredentials", "DEFAULT_IMAGE_WEIGHTS: ScoringWeights", "DEFAULT_BIO_WEIGHTS: ScoringWeights", "IMAGE_CATEGORY_WEIGHTS: CategoryWeights", "technical_quality", "attractiveness", "style_presentation", "market_appeal", "authenticity", "BIO_CATEGORY_WEIGHTS: CategoryWeights", "AdvancedScoringEngine", "calculateOverallScore", "expertAnalyses: ExpertAnalysis[]", "weights: ScoringWeights", "DEFAULT_IMAGE_WEIGHTS", "totalScore", "totalWeight", "analysis", "expertAnalyses", "weight", "weights", "score", "Math", "round", "calculateDetailedScoring", "categoryWeights: CategoryWeights", "scoringWeights: <PERSON><PERSON><PERSON>eight<PERSON>", "subScores: DetailedScoring['subScores']", "categorizedScores", "this", "categorizeExpertScores", "category", "scores", "entries", "categoryWeight", "categoryWeights", "avgScore", "reduce", "sum", "length", "subScores", "components", "getComponentScores", "overallScore", "scoringWeights", "percentileRank", "calculatePercentileRank", "improvementPotential", "calculateImprovementPotential", "marketCompetitiveness", "calculateMarketCompetitiveness", "categories: Record<string, number[]>", "categories", "push", "category: string", "components: Record<string, number>", "isExpertRelevantToCategory", "_a", "includes", "score: number", "values", "map", "s", "variance", "max", "min", "roomForImprovement", "overallScore: number", "categoryScores", "balance", "calculateBalance", "scores: number[]", "mean", "pow", "standardDeviation", "sqrt", "generateComparativeAnalysis", "detailedScoring: DetailedScoring", "detailedScoring", "marketPosition: ComparativeData['marketPosition']", "marketPosition", "competitiveAdvantages", "extractCompetitiveAdvantages", "areasForImprovement", "extractAreasForImprovement", "topPercentile", "calculateTopPercentile", "averageScore", "percentileRank: number", "advantages: string[]", "data", "replace", "advantages", "keyObservations", "slice", "improvements: string[]", "recommendations", "r", "recommendation", "improvements"], "mappings": "AAYA,MAAaA,EAAiD,CAC5DC,YAAa,CACXC,KAAM,cACNC,KAAM,iBACNC,YAAa,+EACbC,WAAY,uNACZC,UAAW,CACT,mCACA,qCACA,iCACA,qCACA,wCACA,0BACA,wCAEFC,iBAAkB,sIAClBC,gBAAiB,CACf,8BACA,yBACA,wBACA,kCAIJC,WAAY,CACVP,KAAM,aACNC,KAAM,wBACNC,YAAa,kFACbC,WAAY,+MACZC,UAAW,CACT,mCACA,+BACA,gCACA,mCACA,0BACA,qCACA,gCAEFC,iBAAkB,qHAClBC,gBAAiB,CACf,kCACA,4BACA,8BACA,gCAIJE,QAAS,CACPR,KAAM,UACNC,KAAM,oBACNC,YAAa,4EACbC,WAAY,gNACZC,UAAW,CACT,4BACA,gCACA,+BACA,mCACA,kCACA,4BACA,mCAEFC,iBAAkB,sIAClBC,gBAAiB,CACf,qCACA,6BACA,2BACA,mCAIJG,aAAc,CACZT,KAAM,eACNC,KAAM,eACNC,YAAa,6EACbC,WAAY,sLACZC,UAAW,CACT,oCACA,2BACA,uCACA,oCACA,yCACA,oCACA,4BAEFC,iBAAkB,gIAClBC,gBAAiB,CACf,0BACA,6BACA,wBACA,mCAIJI,aAAc,CACZV,KAAM,eACNC,KAAM,kBACNC,YAAa,uFACbC,WAAY,+MACZC,UAAW,CACT,kCACA,iCACA,+BACA,gCACA,kCACA,8BACA,0CAEFC,iBAAkB,2HAClBC,gBAAiB,CACf,+BACA,+BACA,+BACA,wCAKOK,iBAAoBC,IAC/B,MAAMC,EAAUC,EAAgBC,GAChC,IAAKF,EACH,MAAM,IAAIG,MAAA,wBAA8BD,KAE1C,OAAOF,GAGII,kBAAoB,IACxBC,OAAOC,KAAKL,GAGRM,qBAAwBR,IACnC,MAAMC,EAAUF,iBAAiBI,GACjC,MAAA,GAAUF,EAAQZ,SAASY,EAAQX,eC1HxBmB,EAAwC,CACnDtB,YAAa,IACbQ,WAAY,IACZC,QAAS,GACTC,aAAc,IACdC,aAAc,KAGHY,EAAsC,CACjDf,WAAY,GACZG,aAAc,IACdD,aAAc,GACdV,YAAa,GACbS,QAAS,KAGEe,EAA0C,CACrDC,kBAAmB,GACnBC,eAAgB,GAChBC,mBAAoB,IACpBC,cAAe,IACfC,aAAc,IAGHC,EAAwC,CACnDL,kBAAmB,IACnBC,eAAgB,IAChBC,mBAAoB,GACpBC,cAAe,IACfC,aAAc,KAGhB,IAAaE,EAAb,MAKE,qBAAAC,CACEC,EACAC,EAA0BC,GAE1B,IAAIC,EAAa,EACbC,EAAc,EAElB,IAAA,MAAWC,KAAYC,EAAgB,CACrC,MAAMC,EAASC,EAAQH,EAAStB,aAAe,EAC/CoB,GAAcE,EAASI,MAAQF,EAC/BH,GAAeG,CAChB,CAED,OAAOH,EAAc,EAAIM,KAAKC,MAAMR,EAAaC,GAAe,CACjE,CAKD,wBAAAQ,CACEZ,EACAa,EACAC,GAEA,MAAMC,EAA0C,CAAA,EAG1CC,EAAoBC,KAAKC,uBAAuBZ,GAGtD,IAAA,MAAYa,EAAUC,KAAWlC,OAAOmC,QAAQL,GAAoB,CAClE,MAAMM,EAAiBC,EAAgBJ,IAAsC,EACvEK,EAAWJ,EAAOK,OAAO,CAACC,EAAKjB,IAAUiB,EAAMjB,EAAO,GAAKW,EAAOO,OAExEC,EAAUT,GAAY,CACpBV,MAAOC,KAAKC,MAAMa,GAClBjB,OAAQe,EACRO,WAAYZ,KAAKa,mBAAmBX,EAAUb,GAEjD,CAED,MAAMyB,EAAed,KAAKlB,sBAAsBO,EAAgB0B,GAEhE,MAAO,CACLD,eACAH,YACAK,eAAgBhB,KAAKiB,wBAAwBH,GAC7CI,qBAAsBlB,KAAKmB,8BAA8BR,GACzDS,sBAAuBpB,KAAKqB,+BAA+BP,EAAcH,GAE5E,CAKD,sBAAAV,CAA+BlB,GAC7B,MAAMuC,EAAuC,CAC3C/C,kBAAmB,GACnBC,eAAgB,GAChBC,mBAAoB,GACpBC,cAAe,GACfC,aAAc,IAGhB,IAAA,MAAWS,KAAYC,EACrB,OAAQD,EAAStB,YACf,IAAK,cACHyD,EAAWhD,kBAAkBiD,KAAKpC,EAASI,OAC3C,MACF,IAAK,aACH+B,EAAW/C,eAAegD,KAAKpC,EAASI,OACxC+B,EAAW5C,aAAa6C,KAAKpC,EAASI,OACtC,MACF,IAAK,UACH+B,EAAW9C,mBAAmB+C,KAAKpC,EAASI,OAC5C,MACF,IAAK,eACH+B,EAAW7C,cAAc8C,KAAKpC,EAASI,OACvC,MACF,IAAK,eACH+B,EAAW7C,cAAc8C,KAAKpC,EAASI,OACvC+B,EAAW5C,aAAa6C,KAAKpC,EAASI,OAK5C,OAAO+B,CACR,CAKD,kBAAAV,CAA2BY,EAAkB1C,GAC3C,MAAM2C,EAAqC,CAAA,EAI3C,IAAA,MAAWtC,KAAYC,EACjBW,KAAK2B,2BAA2BvC,EAAStB,WAAYoC,KACvDU,EAAWxB,EAAStB,YAAcsB,EAASI,OAI/C,OAAOoB,CACR,CAKD,0BAAAe,CAAmChE,EAAoB8D,GD3JzD,IAAAG,ECoKI,OAAO,OAAAA,EARwC,CAC7CrD,kBAAmB,CAAC,eACpBC,eAAgB,CAAC,aAAc,eAC/BC,mBAAoB,CAAC,UAAW,eAChCC,cAAe,CAAC,eAAgB,gBAChCC,aAAc,CAAC,aAAc,iBAGXuB,SAAb,EAAA0B,EAAwBC,SAAS/D,MAAe,CACxD,CAKD,uBAAAmD,CAAwBa,GAGtB,OAAItC,GAAS,GAAW,GACpBA,GAAS,GAAW,GACpBA,GAAS,GAAW,GACpBA,GAAS,GAAW,GACpBA,GAAS,GAAW,GACpBA,GAAS,GAAW,GACpBA,GAAS,GAAW,GACjB,CACR,CAKD,6BAAA2B,CAA8BrB,GAC5B,MAAMK,EAASlC,OAAO8D,OAAOpB,GAAWqB,IAAIC,GAAKA,EAAEzC,OAM7C0C,EALWzC,KAAK0C,OAAOhC,GACZV,KAAK2C,OAAOjC,GAKvBkC,EAAqB,IAJVlC,EAAOK,OAAO,CAACC,EAAKjB,IAAUiB,EAAMjB,EAAO,GAAKW,EAAOO,OAMxE,OAAOjB,KAAK2C,IAAI,IAAK3C,KAAKC,MAAkB,GAAXwC,EAAsC,GAArBG,GACnD,CAKD,8BAAAhB,CAA+BiB,EAAsBxC,GAEnD,MAAMyC,EAAiBtE,OAAO8D,OAAOpB,GAAWqB,IAAIC,GAAKA,EAAEzC,OACrDgD,EAAUxC,KAAKyC,iBAAiBF,GAGtC,OAAO9C,KAAKC,MAAqB,GAAfoB,EAA+B,GAAV0B,EACxC,CAKD,gBAAAC,CAAyBC,GACvB,GAAsB,IAAlBvC,EAAOO,OAAc,OAAO,EAEhC,MAAMiC,EAAOxC,EAAOK,OAAO,CAACC,EAAKjB,IAAUiB,EAAMjB,EAAO,GAAKW,EAAOO,OAC9DwB,EAAW/B,EAAOK,OAAO,CAACC,EAAKjB,IAAUiB,EAAMhB,KAAKmD,IAAIpD,EAAQmD,EAAM,GAAI,GAAKxC,EAAOO,OACtFmC,EAAoBpD,KAAKqD,KAAKZ,GAIpC,OAAOzC,KAAK0C,IAAI,EAAG1C,KAAKC,MAAM,IAA2B,EAApBmD,GACtC,CAKD,2BAAAE,CACET,EACAU,EACAjE,GAEA,MAAMiC,EAAiBiC,EAAgBjC,eAGvC,IAAIkC,EACsBC,EAAtBnC,GAAkB,GAAqB,WAClCA,GAAkB,GAAqB,gBACvCA,GAAkB,GAAqB,UACvCA,GAAkB,GAAqB,gBAC1B,aAGtB,MAAMoC,EAAwBpD,KAAKqD,6BAA6BJ,EAAiB5D,GAC3EiE,EAAsBtD,KAAKuD,2BAA2BN,EAAiB5D,GAE7E,MAAO,CACL2B,iBACAwC,cAAexD,KAAKyD,uBAAuBzC,GAC3C0C,aAAc,GACdN,wBACAE,sBACAH,iBAEH,CAED,sBAAAM,CAA+BE,GAC7B,OAAOlE,KAAK0C,IAAI,EAAG,IAAMnB,EAC1B,CAED,4BAAAqC,CACEL,EACAjE,GAEA,MAAM6E,EAAuB,GAG7B,IAAA,MAAY1D,EAAU2D,KAAS5F,OAAOmC,QAAQ6C,EAAgBtC,WACxDkD,EAAKrE,OAAS,MACLgC,KAAA,UAAetB,EAAS4D,QAAQ,IAAK,QAKpD,IAAA,MAAW1E,KAAYC,EACjBD,EAASI,OAAS,IACpBuE,EAAWvC,QAAQpC,EAAS4E,gBAAgBC,MAAM,EAAG,IAIzD,OAAOF,EAAWE,MAAM,EAAG,EAC5B,CAED,0BAAAV,CACEP,EACAjE,GAEA,MAAMmF,EAAyB,GAG/B,IAAA,MAAYhE,EAAU2D,KAAS5F,OAAOmC,QAAQ6C,EAAgBtC,WACxDkD,EAAKrE,MAAQ,MACFgC,KAAA,WAAgBtB,EAAS4D,QAAQ,IAAK,QAKvD,IAAA,MAAW1E,KAAYC,EACjBD,EAASI,MAAQ,MACNgC,QAAQpC,EAAS+E,gBAAgBF,MAAM,EAAG,GAAGjC,IAAIoC,GAAKA,EAAEC,iBAIzE,OAAOC,EAAaL,MAAM,EAAG,EAC9B"}