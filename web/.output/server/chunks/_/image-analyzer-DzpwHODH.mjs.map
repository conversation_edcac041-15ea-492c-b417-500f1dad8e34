{"version": 3, "file": "image-analyzer-DzpwHODH.mjs", "sources": ["../../../../../src/pages/ImageAnalyzer.tsx", "../../../../../src/routes/_authed/image-analyzer.tsx?tsr-split=component"], "sourcesContent": null, "names": ["ScoreCard", "result", "className", "children", "jsx", "src", "preview", "alt", "fileName", "jsxs", "score: number", "overallScore", "score", "steps", "map", "step", "<PERSON><PERSON><PERSON>", "stepId", "recommendations", "slice", "rec", "i", "<PERSON><PERSON><PERSON>", "error", "AnalyzingCard", "image", "progress", "currentStep", "ANALYSIS_STEPS", "length", "Math", "round", "Progress", "value", "id", "name", "SplitComponent", "images", "setImages", "useState", "results", "setResults", "status", "setStatus", "setProgress", "isStorageReady", "setIsStorageReady", "useEffect", "initStorage", "then", "loadSessionImages", "for<PERSON>ach", "img", "revokeImagePreview", "async", "session", "session<PERSON>anager", "getCurrentSession", "imageList: ImageWithPreview[]", "imageStorage", "getSessionImages", "stored", "createImagePreview", "blob", "storedImage", "imageList", "existingResults: AnalysisResult[]", "getAnalysisResult", "existingResults", "push", "console", "onDrop", "useCallback", "acceptedFiles: File[]", "newImages: ImageWithPreview[]", "file", "acceptedFiles", "imageId", "storeImage", "addImageToSession", "getImage", "prev", "newImages", "getRootProps", "getInputProps", "isDragActive", "useDropzone", "accept", "maxFiles", "disabled", "Link", "to", "ArrowLeft", "PrivacyNotice", "style", "width", "Object", "values", "reduce", "avg", "p", "max", "keys", "Badge", "variant", "Shield", "Database", "FileUp", "onClick", "imageId: string", "imageToRemove", "find", "deleteImage", "removeImageFromSession", "filter", "removeImage", "X", "<PERSON><PERSON>", "size", "newResults: AnalysisResult[]", "analysisService", "analyzeImage", "onProgress", "progressData", "onComplete", "analysisResult", "saveAnalysisResult", "newResults", "onError", "indexOf", "Promise", "resolve", "setTimeout", "Fragment", "Loader2", "<PERSON><PERSON><PERSON><PERSON>", "Crown"], "mappings": "usBA0BA,MAAMA,UAAY,EAAGC,aAOnB,cACG,MAAA,CAAIC,UAAU,gEACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CACCC,IAAKJ,EAAOK,SAAW,mBACvBC,IAAKN,EAAOO,SACZN,UAAU,6BAEZO,EAAAA,KAAC,MAAA,CAAIP,UAAU,uBACZ,MAAA,CAAIA,UAAU,yCACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,wDAAgDD,EAAOO,WACrEJ,EAAAA,IAAC,MAAA,CACCF,UAAA,wDAjBaQ,EAiBmET,EAAOU,aAhB3FC,GAAS,GAAW,sBACpBA,GAAS,GAAW,mBACjB,6BAgBEX,EAAOU,wBAKX,MAAA,CAAIT,UAAU,iBACZC,SAAAF,EAAOY,MAAMC,IAAKC,UAChB,MAAA,CAAsBb,UAAU,4CAC/BC,SAAA,CAAAC,EAAAA,IAAC,OAAA,CAAKF,UAAU,4BAAoBa,EAAKC,WACzCZ,EAAAA,IAAC,OAAA,CACCF,UAAA,gBAA0Ba,EAAKH,OAAS,GAAK,qBAAuBG,EAAKH,OAAS,GAAK,qBAAuB,+BAE7GG,EAAKH,UALAG,EAAKE,iBAYlB,KAAA,CAAGf,UAAU,qBACXD,EAAOiB,gBAAgBC,MAAM,EAAG,GAAGL,IAAI,CAACM,EAAKC,IAC5CZ,EAAAA,KAAC,KAAA,CAAWP,UAAU,sDACpBE,EAAAA,IAACkB,EAAA,CAASpB,UAAU,2DACnB,OAAA,CAAAC,SAAMiB,MAFAC,MAOZpB,EAAOsB,OACNnB,EAAAA,IAAC,MAAA,CAAIF,UAAU,2EACZD,EAAOsB,cAjDI,IAACb,GAyDnBc,cAAgB,EACpBC,QACAC,qBAKC,MAAA,CAAIxB,UAAU,gDACbC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CAAIC,IAAKoB,EAAMnB,QAASC,IAAKkB,EAAMjB,SAAUN,UAAU,6BACxDO,EAAAA,KAAC,MAAA,CAAIP,UAAU,gBACbE,EAAAA,IAAC,MAAA,CAAIF,UAAU,qDAEdwB,UACE,MAAA,CAAIxB,UAAU,6BACZ,MAAA,CAAIA,UAAU,+BACbC,SAAA,CAAAM,EAAAA,KAAC,OAAA,CAAKP,UAAU,6BAAmB,QAAMwB,EAASC,YAAY,IAAEC,EAAeC,UAC/EpB,EAAAA,KAAC,OAAA,CAAKP,UAAU,6BAA8BC,SAAA,CAAA2B,KAAKC,MAAML,EAASA,UAAU,gBAE7E,MAAA,CAAIxB,UAAU,gDAAwCwB,EAASV,iBAC/DgB,EAAA,CAASC,MAAOP,EAASA,SAAUxB,UAAU,iBAIjD,MAAA,CAAIA,UAAU,iBACZC,SAAAyB,EAAed,IAAKC,UAClB,MAAA,CAECb,UAAA,8BACEwB,GAAYA,EAASC,YAAcZ,EAAKmB,GACpC,qBACAR,GAAYA,EAASC,cAAgBZ,EAAKmB,GACxC,iBACA,2BAGR9B,EAAAA,IAAC,MAAA,CACCF,UAAA,8BACEwB,GAAYA,EAASC,YAAcZ,EAAKmB,GACpC,mBACAR,GAAYA,EAASC,cAAgBZ,EAAKmB,GACxC,6BACA,iBAGV9B,EAAAA,IAAC,OAAA,CAAAD,SAAMY,EAAKoB,SAlBPpB,EAAKmB,aC7GgC,MAAAE,eDuItD,WACE,MAAOC,EAAQC,GAAaC,EAAAA,SAA6B,KAClDC,EAASC,GAAcF,EAAAA,SAA2B,KAClDG,EAAQC,GAAaJ,EAAAA,SAAyC,SAC9Db,EAAUkB,GAAeL,EAAAA,SAA2C,CAAA,IACpEM,EAAgBC,GAAqBP,EAAAA,UAAS,GAErDQ,EAAAA,UAAU,KACRC,IAAcC,KAAK,KACjBH,GAAkB,GAElBI,sBAGK,KAELb,EAAOc,QAASC,GAAQC,EAAmBD,EAAI9C,YAEhD,IAEH,MAAM4C,kBAAoBI,UACxB,IACE,MAAMC,EAAUC,EAAeC,oBAGzBC,SAFqBC,EAAaC,iBAAiBL,EAAQrB,KAEdpB,IAAK+C,IAAA,CACtD3B,GAAI2B,EAAO3B,GACX1B,SAAUqD,EAAOrD,SACjBF,QAASwD,EAAmBD,EAAOE,MACnCC,YAAaH,KAGfvB,EAAU2B,GAGV,MAAMC,EAAoC,GAC1C,IAAA,MAAWd,KAAOa,EAAW,CAC3B,MAAMhE,EAASuD,EAAeW,kBAAkBf,EAAIlB,IAChDjC,GACFmE,EAAgBC,KAAKpE,EAExB,CAEGmE,EAAgBvC,OAAS,IAC3BY,EAAW2B,GACXzB,EAAU,QAEb,OAAQpB,GACP+C,QAAQ/C,MAAM,iCAAkCA,EACjD,GAGGgD,EAASC,cACblB,MAAOmB,IACL,GAAK5B,EAEL,IACE,MAAMU,EAAUC,EAAeC,oBACzBiB,EAAgC,GAEtC,IAAA,MAAWC,KAAQC,EAAczD,MAAM,EAAG,GAAKkB,EAAOR,QAAS,CAC7D,MAAMgD,QAAgBlB,EAAamB,WAAWH,EAAMpB,EAAQrB,IAC5DsB,EAAeuB,kBAAkBF,GAEjC,MAAMb,QAAoBL,EAAaqB,SAASH,GAC5Cb,KACQK,KAAK,CACbnC,GAAI2C,EACJrE,SAAUmE,EAAKxC,KACf7B,QAASwD,EAAmBE,EAAYD,MACxCC,eAGL,CAED1B,EAAW2C,GAAS,IAAIA,KAASC,GAClC,OAAQ3D,GACP+C,QAAQ/C,MAAM,0BAA2BA,EAC1C,GAEH,CAACc,EAAOR,OAAQgB,KAGZsC,aAAEA,EAAAC,cAAcA,EAAAC,aAAeA,GAAiBC,EAAY,CAChEf,SACAgB,OAAQ,CAAE,UAAW,IACrBC,SAAU,GAAKnD,EAAOR,OACtB4D,UAAW5C,GAAkBR,EAAOR,QAAU,KA2DhD,cACG,MAAA,CAAI3B,UAAU,0BACbC,SAAA,CAAAM,EAAAA,KAAC,SAAA,CAAOP,UAAU,6CAChBC,SAAA,CAAAM,EAAAA,KAAC,MAAA,CAAIP,UAAU,wEACbC,SAAA,CAAAM,EAAAA,KAACiF,EAAA,CAAKC,GAAG,IAAIzF,UAAU,kEACrBC,SAAA,CAAAC,EAAAA,IAACwF,EAAA,CAAU1F,UAAU,YACrBE,EAAAA,IAAC,OAAA,CAAKF,UAAU,gBAAgBC,SAAA,oBAElCC,EAAAA,IAACyF,EAAA,OAES,eAAXnD,SACE,MAAA,CAAIxC,UAAU,kBACbC,eAAC,MAAA,CACCD,UAAU,yDACV4F,MAAO,CACLC,MAAUC,OAAOC,OAAOvE,GAAUwE,OAAO,CAACC,EAAKC,IAAMD,EAAMC,EAAE1E,SAAU,GAAKI,KAAKuE,IAAIL,OAAOM,KAAK5E,GAAUG,OAAQ,GAAnH,YAOVzB,EAAAA,IAAC,OAAA,CAAKF,UAAU,sCACdC,gBAAC,MAAA,CAAID,UAAU,qCACZ,MAAA,CAAIA,UAAU,8BACZ,KAAA,CAAGA,UAAU,4BAA4BC,SAAA,yBACzC,IAAA,CAAED,UAAU,qCAAqCC,SAAA,0DAIjD,MAAA,CAAID,UAAU,iCACbC,SAAA,CAAAM,EAAAA,KAAC8F,EAAA,CAAMC,QAAQ,YAAYtG,UAAU,0BACnCC,SAAA,CAAAC,EAAAA,IAACqG,EAAA,CAAOvG,UAAU,YAAY,kCAGhCO,EAAAA,KAAC8F,EAAA,CAAMC,QAAQ,UAAUtG,UAAU,0BACjCC,SAAA,CAAAC,EAAAA,IAACsG,EAAA,CAASxG,UAAU,YAAY,iCAM1B,SAAXwC,GACCjC,EAAAA,KAAC,MAAA,CAAIP,UAAU,OACbC,SAAA,CAAAM,EAAAA,KAAC,MAAA,IACK0E,IACJjF,UAAA,+FACEmF,EACI,mCACA,4DAGL,QAAA,IAAUD,MACXhF,EAAAA,IAACuG,EAAA,CAAOzG,UAAU,8CACjB,IAAA,CAAEA,UAAU,qCACVC,SAAAkF,EACG,yBACA,kEAEL,IAAA,CAAEnF,UAAU,wCAAwCC,SAAA,yBAGtDkC,EAAOR,OAAS,UACd,MAAA,CAAI3B,UAAU,wBACZ,KAAA,CAAGA,UAAU,0BAAgB,oBAAkBmC,EAAOR,OAAO,gBAC7D,KAAA,CAAG3B,UAAU,4DACXC,SAAAkC,EAAOvB,IAAKW,UACV,KAAA,CAAkBvB,UAAU,iBAC3BC,SAAA,CAAAC,EAAAA,IAAC,MAAA,CACCC,IAAKoB,EAAMnB,QACXC,IAAKkB,EAAMjB,SACXN,UAAU,wCAEZE,EAAAA,IAAC,SAAA,CACCwG,QAAS,IAlIbtD,OAAOuD,IACzB,IACE,MAAMC,EAAgBzE,EAAO0E,KAAM3D,GAAQA,EAAIlB,KAAO2C,GAClDiC,IACFzD,EAAmByD,EAAcxG,eAC3BqD,EAAaqD,YAAYnC,GAC/BrB,EAAeyD,uBAAuBpC,GACtCvC,EAAW2C,GAASA,EAAKiC,OAAQ9D,GAAQA,EAAIlB,KAAO2C,IACpDpC,EAAYwC,GAASA,EAAKiC,OAAQjH,GAAWA,EAAOO,WAAasG,EAActG,WAElF,OAAQe,GACP+C,QAAQ/C,MAAM,0BAA2BA,EAC1C,GAsHoC4F,CAAY1F,EAAMS,IACjChC,UAAU,wHACVuF,SAAqB,eAAX/C,EAEVvC,SAAAC,EAAAA,IAACgH,EAAA,CAAElH,UAAU,gBAXRuB,EAAMS,aAgBlBmF,EAAA,CACCC,KAAK,KACLV,QA9HItD,UACpB,GAAsB,IAAlBjB,EAAOR,OAAc,OAEzBc,EAAU,cACVF,EAAW,IACXG,EAAY,CAAA,GAEZ,MAAM2E,EAA+B,GAErC,IACE,IAAA,MAAW9F,KAASY,QACZmF,EAAgBC,aAAahG,EAAMuC,YAAa,CACpD0D,WAAaC,IACX/E,EAAaqC,IAAA,IACRA,EACF,CAAAxD,EAAMS,IAAKyF,MAGhBC,WAAaC,IACXrE,EAAesE,mBAAmBrG,EAAMS,GAAI2F,GAC5CE,EAAW1D,KAAKwD,GAChBpF,EAAW,IAAIsF,KAEjBC,QAAUzG,IACR+C,QAAQ/C,MAAA,uBAA6BE,EAAMjB,YAAae,MAKxDc,EAAO4F,QAAQxG,GAASY,EAAOR,OAAS,SACpC,IAAIqG,QAASC,GAAYC,WAAWD,EAAS,MAIvDxF,EAAU,OACX,OAAQpB,GACP+C,QAAQ/C,MAAM,mBAAoBA,GAClCoB,EAAU,OACX,GAyFe8C,SAAqB,eAAX/C,IAA4BG,EACtC3C,UAAU,uBAEE,eAAXwC,EACCjC,EAAAA,KAAA4H,EAAAA,SAAA,CAAAlI,SAAA,CACEC,MAACkI,EAAA,CAAQpI,UAAU,8BAA8B,0BAEhD,WAEQmC,EAAOR,eAAiC,IAAlBQ,EAAOR,OAAe,IAAM,qBAQ7D,eAAXa,GACCtC,EAAAA,IAAC,MAAA,CAAIF,UAAU,iDACZC,SAAAkC,EAAOvB,IAAKW,SACVD,cAAA,CAAoCC,QAAOC,SAAUA,EAASD,EAAMS,KAAjDT,EAAMS,OAKpB,SAAXQ,GAAqBF,EAAQX,OAAS,UACpC,MAAA,CAAI3B,UAAU,yBACZ,MAAA,CAAIA,UAAU,mBACbC,SAAA,CAAAC,EAAAA,IAAC,KAAA,CAAGF,UAAU,wBAAwBC,SAAA,qBACtCM,EAAAA,KAAC,IAAA,CAAEP,UAAU,kCAAwB,4CACO0B,EAAeC,OAAO,kCAInE,MAAA,CAAI3B,UAAU,oDACZsC,EAAQ1B,IAAKb,GACZG,EAAAA,IAACJ,UAAA,CAAwCC,UAAzBA,EAAOO,oBAI1B,MAAA,CAAIN,UAAU,6BACbC,SAAA,CAAAM,EAAAA,KAAC,MAAA,CAAIP,UAAU,gCACbC,SAAA,CAAAC,EAAAA,IAACiH,EAAA,CACCT,QAAS,KACPjE,EAAU,QACVF,EAAW,IACXG,EAAY,CAAA,IAEd4D,QAAQ,YACTrG,SAAA,wBAGDC,EAAAA,IAACiH,EAAA,CAAOkB,SAAA,EAAQjB,KAAK,KACnBnH,gBAACuF,EAAA,CAAKC,GAAG,gBAAgBxF,SAAA,CAAA,oBACNC,EAAAA,IAACkB,EAAA,CAASpB,UAAU,yBAK3CO,EAAAA,KAAC,MAAA,CAAIP,UAAU,gBACbC,SAAA,CAAAC,EAAAA,IAAC,IAAA,CAAEF,UAAU,6BAA6BC,SAAA,sCAC1CC,EAAAA,IAACiH,EAAA,CAAOkB,SAAA,EAAQ/B,QAAQ,UAAUtG,UAAU,uDAC1CC,gBAACuF,EAAA,CAAKC,GAAG,sBAAsBxF,SAAA,CAAA,yBACPC,EAAAA,IAACoI,EAAA,CAAMtI,UAAU,kCAM9C,MAAA,CAAIA,UAAU,yBACbC,eAACkH,EAAA,CAAOkB,SAAA,EAAQjB,KAAK,KAAKpH,UAAU,yBAClCC,eAACuF,EAAA,CAAKC,GAAG,gBACPxF,SAAAC,EAAAA,IAACkB,EAAA,CAASpB,UAAU,4BAUvC"}