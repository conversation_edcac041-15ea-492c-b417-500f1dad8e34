{"version": 3, "file": "index3.mjs", "sources": ["../../../../node_modules/@radix-ui/primitive/dist/index.mjs", "../../../../node_modules/@radix-ui/react-context/dist/index.mjs", "../../../../node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "../../../../node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "../../../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs"], "sourcesContent": null, "names": ["composeEventHandlers", "originalEventHandler", "ourEventHandler", "checkForDefaultPrevented", "event", "defaultPrevented", "createContext2", "rootComponentName", "defaultContext", "Context", "React.createContext", "Provider", "props", "children", "context", "value", "React.useMemo", "Object", "values", "jsx", "displayName", "consumerName", "React.useContext", "Error", "createContextScope", "scopeName", "createContextScopeDeps", "defaultContexts", "createScope", "scopeContexts", "map", "scope", "contexts", "BaseContext", "index", "length", "composeContextScopes", "scopes", "baseScope", "scopeHooks", "createScope2", "useScope", "overrideScopes", "nextScopes", "reduce", "nextScopes2", "useCallbackRef", "callback", "callback<PERSON><PERSON>", "React.useRef", "React.useEffect", "current", "args", "useControllableState", "prop", "defaultProp", "onChange", "uncontrolledProp", "setUncontrolledProp", "uncontrolledState", "React.useState", "prevValueRef", "handleChange", "useUncontrolledState", "isControlled", "React.useCallback", "nextValue", "value2", "useLayoutEffect2", "Boolean", "globalThis", "document", "React.useLayoutEffect"], "mappings": "", "x_google_ignoreList": [0, 1, 2, 3, 4]}