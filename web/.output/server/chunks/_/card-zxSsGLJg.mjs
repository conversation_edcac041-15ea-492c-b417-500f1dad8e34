import{r as a,j as e,c as s}from"./routeTree.gen-BFK54byf.mjs";const r=a.forwardRef(({className:a,...r},d)=>e.jsx("div",{ref:d,className:s("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r}));r.displayName="Card";const d=a.forwardRef(({className:a,...r},d)=>e.jsx("div",{ref:d,className:s("flex flex-col space-y-1.5 p-6",a),...r}));d.displayName="CardHeader";const o=a.forwardRef(({className:a,...r},d)=>e.jsx("div",{ref:d,className:s("text-2xl font-semibold leading-none tracking-tight",a),...r}));o.displayName="CardTitle";const t=a.forwardRef(({className:a,...r},d)=>e.jsx("div",{ref:d,className:s("text-sm text-muted-foreground",a),...r}));t.displayName="CardDescription";const l=a.forwardRef(({className:a,...r},d)=>e.jsx("div",{ref:d,className:s("p-6 pt-0",a),...r}));l.displayName="CardContent";a.forwardRef(({className:a,...r},d)=>e.jsx("div",{ref:d,className:s("flex items-center p-6 pt-0",a),...r})).displayName="CardFooter";export{r as C,d as a,o as b,t as c,l as d};
//# sourceMappingURL=card-zxSsGLJg.mjs.map
