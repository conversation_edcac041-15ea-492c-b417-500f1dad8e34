{"version": 3, "file": "welcome-UozZH9ui.mjs", "sources": ["../../../../../src/routes/welcome.tsx?tsr-split=component"], "sourcesContent": null, "names": ["WelcomeCard", "onSignIn", "onSignUp", "Card", "className", "children", "jsxs", "<PERSON><PERSON><PERSON><PERSON>", "jsx", "Heart", "<PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "Star", "<PERSON><PERSON>", "variant", "onClick", "SignInCard", "onBack", "size", "ArrowLeft", "SignIn", "routing", "signUpUrl", "afterSignInUrl", "appearance", "elements", "rootBox", "card", "headerTitle", "headerSubtitle", "socialButtonsBlockButton", "formButtonPrimary", "footerActionLink", "SignUpCard", "SignUp", "signInUrl", "afterSignUpUrl", "WelcomeBack", "<PERSON><PERSON><PERSON><PERSON>", "Link", "to", "SplitComponent", "showSignIn", "setShowSignIn", "useState", "showSignUp", "setShowSignUp", "SignedOut", "SignedIn"], "mappings": "8cAqCA,SAASA,aAAYC,SAAEA,EAAAA,SAAUC,IAC/B,cACGC,EAAA,CAAKC,UAAU,yDACdC,SAAA,CAAAC,EAAAA,KAACC,EAAA,CAAWH,UAAU,mCACnB,MAAA,CAAIA,UAAU,2BACbC,gBAAC,MAAA,CAAID,UAAU,qBACbI,EAAAA,IAACC,EAAA,CAAML,UAAU,gDAChBM,EAAA,CAASN,UAAU,iEAGvBO,EAAA,CAAUP,UAAU,6CAA4CC,SAAA,8BAGhEO,EAAA,CAAgBR,UAAU,gCAA+BC,SAAA,+EAI5DC,EAAAA,KAACO,EAAA,CAAYT,UAAU,YACrBC,SAAA,CAAAC,EAAAA,KAAC,MAAA,CAAIF,UAAU,2CACZ,MAAA,CAAIA,UAAU,iEACbI,EAAAA,IAACM,EAAA,CAAKV,UAAU,mCACf,OAAA,CAAAC,SAAK,wCAEP,MAAA,CAAID,UAAU,iEACbI,EAAAA,IAACE,EAAA,CAASN,UAAU,mCACnB,OAAA,CAAAC,SAAK,2CAEP,MAAA,CAAID,UAAU,iEACbI,EAAAA,IAACC,EAAA,CAAML,UAAU,mCAChB,OAAA,CAAAC,SAAK,mCAIVC,EAAAA,KAAC,MAAA,CAAIF,UAAU,YACbC,SAAA,CAAAC,EAAAA,KAAC,MAAA,CAAIF,UAAU,WACbC,SAAA,CAAAG,EAAAA,IAAC,MAAA,CAAIJ,UAAU,qCACbC,SAAAG,EAAAA,IAAC,MAAA,CAAIJ,UAAU,yCAEjBI,EAAAA,IAAC,MAAA,CAAIJ,UAAU,uCACbC,eAAC,OAAA,CAAKD,UAAU,uCAAuCC,SAAA,qBAI3DC,EAAAA,KAAC,MAAA,CAAIF,UAAU,yBACbC,SAAA,CAAAG,EAAAA,IAACO,EAAA,CACCC,QAAQ,UACRZ,UAAU,SACVa,QAASf,EAASG,SAAA,YAIpBG,EAAAA,IAACO,EAAA,CACCC,QAAQ,YACRZ,UAAU,SACVa,QAAShB,EAASI,SAAA,uBAShC,CAMA,SAASa,YAAWC,OAAEA,IACpB,cACGhB,EAAA,CAAKC,UAAU,yDACdC,SAAA,CAAAC,EAAAA,KAACC,EAAA,CAAWH,UAAU,oCACnB,MAAA,CAAIA,UAAU,yDACZW,EAAA,CACCC,QAAQ,QACRI,KAAK,OACLH,QAASE,EACTf,UAAU,0CAEVC,SAAAG,EAAAA,IAACa,EAAA,CAAUjB,UAAU,oBAEtB,MAAA,CAAIA,UAAU,6BACbC,SAAAG,EAAAA,IAACC,EAAA,CAAML,UAAU,0CAEnBI,EAAAA,IAAC,MAAA,CAAIJ,UAAU,kBAEhBO,EAAA,CAAUP,UAAU,6CAA4CC,SAAA,kBAGhEO,EAAA,CAAgBR,UAAU,gCAA+BC,SAAA,iEAI5DG,EAAAA,IAACK,EAAA,CAAAR,eACE,MAAA,CAAID,UAAU,sBACbC,eAACiB,EAAA,CACCC,QAAQ,OACRC,UAAU,WACVC,eAAe,aACfC,WAAY,CACVC,SAAU,CACRC,QAAS,SACTC,KAAM,sCACNC,YAAa,SACbC,eAAgB,SAChBC,yBAA0B,oEAC1BC,kBAAmB,oEACnBC,iBAAkB,mDAQlC,CAEA,SAASC,YAAWhB,OAAEA,IACpB,cACGhB,EAAA,CAAKC,UAAU,yDACdC,SAAA,CAAAC,EAAAA,KAACC,EAAA,CAAWH,UAAU,oCACnB,MAAA,CAAIA,UAAU,yDACZW,EAAA,CACCC,QAAQ,QACRI,KAAK,OACLH,QAASE,EACTf,UAAU,0CAEVC,SAAAG,EAAAA,IAACa,EAAA,CAAUjB,UAAU,oBAEtB,MAAA,CAAIA,UAAU,6BACbC,SAAAG,EAAAA,IAACC,EAAA,CAAML,UAAU,0CAEnBI,EAAAA,IAAC,MAAA,CAAIJ,UAAU,kBAEhBO,EAAA,CAAUP,UAAU,6CAA4CC,SAAA,kBAGhEO,EAAA,CAAgBR,UAAU,gCAA+BC,SAAA,kEAI5DG,EAAAA,IAACK,EAAA,CAAAR,eACE,MAAA,CAAID,UAAU,sBACbC,eAAC+B,EAAA,CACCb,QAAQ,OACRc,UAAU,WACVC,eAAe,aACfZ,WAAY,CACVC,SAAU,CACRC,QAAS,SACTC,KAAM,sCACNC,YAAa,SACbC,eAAgB,SAChBC,yBAA0B,oEAC1BC,kBAAmB,oEACnBC,iBAAkB,mDAQlC,CAEA,SAASK,cACP,cACGpC,EAAA,CAAKC,UAAU,yDACdC,SAAA,CAAAC,EAAAA,KAACC,EAAA,CAAWH,UAAU,mCACnB,MAAA,CAAIA,UAAU,2BACbC,gBAAC,MAAA,CAAID,UAAU,qBACbI,EAAAA,IAACC,EAAA,CAAML,UAAU,gDAChBM,EAAA,CAASN,UAAU,iEAGvBO,EAAA,CAAUP,UAAU,6CAA4CC,SAAA,wBAGhEO,EAAA,CAAgBR,UAAU,gCAA+BC,SAAA,8CAI5DC,EAAAA,KAACO,EAAA,CAAYT,UAAU,YACrBC,SAAA,CAAAC,EAAAA,KAAC,MAAA,CAAIF,UAAU,0CACZW,EAAA,CAAOyB,SAAA,EAAQxB,QAAQ,UAAUZ,UAAU,SAC1CC,eAACoC,EAAA,CAAKC,GAAG,aAAarC,SAAA,4BAEvBU,EAAA,CAAOyB,SAAA,EAAQxB,QAAQ,YAAYZ,UAAU,SAC5CC,eAACoC,EAAA,CAAKC,GAAG,kBAAkBrC,SAAA,2BAE5BU,EAAA,CAAOyB,SAAA,EAAQxB,QAAQ,YAAYZ,UAAU,SAC5CC,eAACoC,EAAA,CAAKC,GAAG,gBAAgBrC,SAAA,sBAI7BG,EAAAA,IAAC,MAAA,CAAIJ,UAAU,mBACbC,eAACU,EAAA,CAAOyB,SAAA,EAAQxB,QAAQ,WAAWZ,UAAU,UAC3CC,eAACoC,EAAA,CAAKC,GAAG,IAAIrC,SAAA,0BAMzB,CAAC,MAAAsC,eAAA,WArOC,MAAOC,EAAYC,GAAiBC,EAAAA,UAAS,IACtCC,EAAYC,GAAiBF,EAAAA,UAAS,GAE7C,aACG,MAAA,CAAI1C,UAAU,qEACbC,gBAAC,MAAA,CAAID,UAAU,4BACbE,EAAAA,KAAC2C,EAAA,CAAA5C,SAAA,EACGuC,IAAeG,SAAe/C,YAAA,CAAYC,SAAU,IAAM4C,GAAc,GAAO3C,SAAU,IAAM8C,GAAc,KAC9GJ,SAAe1B,WAAA,CAAWC,OAAQ,IAAM0B,GAAc,KACtDE,SAAeZ,WAAA,CAAWhB,OAAQ,IAAM6B,GAAc,QAEzDxC,EAAAA,IAAC0C,EAAA,CAAA7C,SACCG,MAAC+B,YAAA,CAAA,SAKV"}