import{m as e}from"../nitro/nitro.mjs";import{C as s,a,b as t,d as i,c as n}from"./card-zxSsGLJg.mjs";import{B as r,A as o,C as l,b as c,T as m,S as d,F as p,X as h,c as g,L as x}from"./lucide-react.mjs";import{B as u}from"./badge-Cwk-ZwYz.mjs";import{P as y,g as f}from"./progress-WcEdq6Og.mjs";import{T as v,a as j,b,c as N}from"./tabs-D9-6MReb.mjs";import{A as w,I as A,D as E,g as _,a as S,b as I}from"./advanced-scoring-Cd6PgUsS.mjs";import{u as k,P as R}from"./index.mjs";import{r as T,j as P,L as O}from"./routeTree.gen-BFK54byf.mjs";import{o as C}from"./index2.mjs";import"node:process";import"cloudflare:workers";import"node:events";import"node:buffer";import"node:timers";import"./storage-IIfgkkYA.mjs";import"./index3.mjs";import"./index4.mjs";import"node:stream";import"node:stream/web";import"node:async_hooks";var M=Object.defineProperty,__publicField=(e,s,a)=>((e,s,a)=>s in e?M(e,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[s]=a)(e,"symbol"!=typeof s?s+"":s,a),L=class{generateExpertPrompt(e,s){const a=I(e);return{systemPrompt:this.buildSystemPrompt(a),userPrompt:this.buildUserPrompt(a,s),chainOfThoughtStructure:this.buildChainOfThoughtStructure(a),examples:this.getExamples(e)}}buildSystemPrompt(e){return`You are ${e.name}, ${e.credentials}.\n\nBACKGROUND & EXPERTISE:\n${e.background}\n\nYour core expertise includes:\n${e.expertise.map(e=>`• ${e}`).join("\n")}\n\nANALYSIS APPROACH:\n${e.analysisApproach}\n\nCRITICAL ANALYSIS REQUIREMENTS:\nYou are a BRUTALLY HONEST expert who provides OBJECTIVE, UNFORGIVING analysis. Your reputation depends on accuracy, not kindness.\n\nSCORING PHILOSOPHY:\n- 90-100: EXCEPTIONAL - Top 5% of all dating profile images, near-perfect execution\n- 80-89: EXCELLENT - Top 15% of images, very strong with minor flaws\n- 70-79: GOOD - Above average, solid but with notable improvement areas\n- 60-69: AVERAGE - Typical image, significant room for improvement\n- 50-59: BELOW AVERAGE - Multiple issues, needs major work\n- 40-49: POOR - Serious problems, likely to perform badly\n- 30-39: VERY POOR - Major red flags, would repel most matches\n- 20-29: TERRIBLE - Fundamentally broken, needs complete reshoot\n- 10-19: AWFUL - Actively harmful to dating prospects\n- 0-9: CATASTROPHIC - Should not be used under any circumstances\n\nANALYSIS REQUIREMENTS:\n1. BE RUTHLESSLY CRITICAL - Most images are mediocre and deserve low scores\n2. IDENTIFY EVERY FLAW - No matter how small, call out problems\n3. DEMAND EXCELLENCE - Only exceptional images deserve high scores\n4. PROVIDE HARSH TRUTH - Your job is accuracy, not making people feel good\n5. USE REAL-WORLD STANDARDS - Compare to actual successful dating profiles\n\nRESPONSE REQUIREMENTS:\n- Be uncompromisingly honest about weaknesses\n- Support every criticism with specific visual evidence\n- Score based on real dating market performance\n- Prioritize brutal honesty over politeness\n- Remember: Average images get average results (poor performance)\n\nYour analysis will be combined with other experts to provide comprehensive feedback.`}buildUserPrompt(e,s){return`${s?this.buildContextInfo(s):""}\n\nPlease analyze this dating profile image using your expertise as a ${e.type} expert.\n\nANALYSIS FRAMEWORK:\nFollow this systematic approach:\n\n1. INITIAL OBSERVATION PHASE:\n   - What are the key visual elements I observe?\n   - What stands out immediately from my expert perspective?\n   - What technical/aesthetic/psychological factors are present?\n\n2. DETAILED EXPERT ANALYSIS:\n   - What specific improvements are needed from my expert perspective?\n   - What concrete changes would optimize this for dating success?\n   - How can this be enhanced to follow best practices in my field?\n   - Focus on ACTIONABLE IMPROVEMENTS, not just observations\n\n3. CRITICAL SCORING RATIONALE:\n   - What score (0-100) does this image ACTUALLY deserve? (Be harsh - most images are 40-60)\n   - What SPECIFIC FLAWS and weaknesses lower this score?\n   - Why would this image FAIL in the competitive dating market?\n   - What evidence PROVES this score is accurate?\n\n4. CONFIDENCE ASSESSMENT:\n   - How confident am I in this analysis (0-100)?\n   - What factors increase or decrease my confidence?\n   - What additional information would improve my assessment?\n\n5. ACTIONABLE RECOMMENDATIONS:\n   - What are the top 3-5 most impactful improvements needed?\n   - Which changes would have the highest impact on dating success?\n   - What is the effort level required for each improvement?\n   - Be specific about HOW to implement each improvement\n\nRESPONSE FORMAT:\nProvide your analysis in this exact JSON structure:\n\n{\n  "observation_phase": {\n    "key_elements": ["element1", "element2", "element3"],\n    "immediate_impressions": "Your first impressions as an expert",\n    "technical_factors": ["factor1", "factor2"]\n  },\n  "expert_evaluation": {\n    "expert_specific_analysis": "Focus on SPECIFIC IMPROVEMENTS needed, not just observations. What exactly should be changed, improved, or optimized? Provide actionable insights.",\n    "strengths": ["strength1", "strength2"],\n    "weaknesses": ["weakness1", "weakness2"]\n  },\n  "scoring_methodology": {\n    "score": 45,\n    "harsh_reality_check": "Why this image would struggle in the real dating market",\n    "critical_flaws": ["major_visual_flaw1", "major_flaw2", "major_flaw3"],\n    "evidence_for_low_score": ["specific_visual_evidence1", "specific_evidence2"],\n    "market_performance_prediction": "How this would actually perform (be realistic)"\n  },\n  "confidence_evaluation": {\n    "confidence": 92,\n    "supporting_factors": ["factor1", "factor2"],\n    "limiting_factors": ["area1", "area2"]\n  },\n  "strategic_recommendations": [\n    {\n      "recommendation": "Specific actionable improvement advice",\n      "impact_level": "high",\n      "implementation_difficulty": "easy",\n      "reasoning": "Why this improvement matters and how it helps"\n    },\n    {\n      "recommendation": "Another specific improvement",\n      "impact_level": "medium",\n      "implementation_difficulty": "moderate",\n      "reasoning": "Detailed reasoning for this improvement"\n    }\n  ]\n}\n\nRemember: Your analysis should reflect your specific expertise while being constructive and actionable.`}buildChainOfThoughtStructure(e){return`\nCHAIN-OF-THOUGHT REASONING STRUCTURE:\n\nStep 1: Initial Expert Observation\n- "As a ${e.type} expert, I immediately notice..."\n- "The key elements that stand out to me are..."\n- "From my professional perspective, this image shows..."\n\nStep 2: Technical/Professional Analysis\n- "Applying my expertise in ${e.expertise[0]}, I can see..."\n- "The technical quality/psychological factors/style elements indicate..."\n- "Based on my experience with ${e.specializations[0]}, this demonstrates..."\n\nStep 3: Comparative Assessment\n- "Compared to successful profiles I've analyzed..."\n- "This ranks in the [percentile] of images I've evaluated because..."\n- "The market positioning would be..."\n\nStep 4: Evidence-Based Scoring\n- "I'm scoring this [X]/100 because..."\n- "The evidence supporting this score includes..."\n- "The main factors influencing this score are..."\n\nStep 5: Strategic Recommendations\n- "The highest impact improvement would be..."\n- "Based on my expertise, I recommend..."\n- "The priority order for improvements should be..."\n`}buildContextInfo(e){let s="ANALYSIS CONTEXT:\n";return e.targetDemographic&&(s+=`• Target Demographic: ${e.targetDemographic}\n`),e.platform&&(s+=`• Platform: ${e.platform}\n`),e.analysisDepth&&(s+=`• Analysis Depth: ${e.analysisDepth}\n`),s+"\n"}getExamples(e){return{photography:["Example: \"As a photography expert, I immediately notice the harsh overhead lighting creating unflattering shadows under the eyes and nose. The composition is off-center without artistic intent, and the background is cluttered with distracting elements. Score: 23/100 because this amateur photography actively hurts the subject's appeal and screams 'low effort selfie.'\"",'Example: "This image demonstrates decent use of natural window light and acceptable composition. However, the lighting could be more flattering, the background lacks visual interest, and the pose appears stiff. Score: 67/100 - while technically competent, it lacks the polish needed to stand out in today\'s competitive dating market."'],psychology:['Example: "From a psychological perspective, while the subject attempts a smile, it lacks genuine Duchenne markers and appears practiced rather than spontaneous. The eye contact is adequate but not particularly engaging. Body language shows some tension. Score: 54/100 because these psychological cues suggest someone trying to appear confident rather than naturally being so."',"Example: \"The facial expression is clearly forced, with visible tension in the jaw and a smile that screams 'fake.' The averted gaze signals insecurity and discomfort. Body language appears defensive. Score: 19/100 because these psychological red flags would actively repel potential matches who can sense inauthenticity.\""],fashion:["Example: \"The styling shows basic understanding of color coordination with the navy shirt working adequately with the subject's skin tone. However, the fit is standard rather than tailored, and the overall look lacks sophistication or personality. Score: 58/100 - while not offensive, it's forgettable and doesn't create any visual impact.\"","Example: \"The outfit choice is a complete disaster - the oversized graphic tee looks juvenile and sloppy, the colors clash horribly with the subject's complexion, and the overall styling suggests someone who doesn't understand basic fashion principles. Score: 14/100 because this actively damages the subject's attractiveness and signals poor judgment.\""],data_science:["Example: \"Based on platform analytics, this image type shows moderate performance indicators but lacks the standout elements that drive top-tier engagement. While technically adequate, it falls into the oversaturated 'decent but forgettable' category. Score: 52/100 based on competitive analysis showing this style performs below median in current market conditions.\"",'Example: "This image style (poor lighting, forced expression, cluttered background) correlates with bottom 15% performance metrics. Platform algorithms actively deprioritize such images, leading to 73% fewer profile views. Score: 11/100 based on conversion rate analysis showing this type generates virtually zero quality matches."'],dating_coach:["Example: \"This image shows decent approachability but lacks the magnetic confidence needed for standout dating success. The expression is pleasant but forgettable, and the setting provides minimal conversation value. Score: 59/100 because while it won't actively hurt, it won't generate the excitement needed in today's competitive dating market.\"",'Example: "While the subject may be physically attractive, this image is a dating disaster. The bland expression, generic setting, and complete lack of personality make it instantly forgettable. Score: 31/100 because it would generate few matches and even fewer meaningful conversations - a waste of potential."']}[e]||[]}},D=class{constructor(){if(__publicField(this,"apiKey"),__publicField(this,"promptGenerator"),__publicField(this,"scoringEngine"),this.apiKey="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",!this.apiKey)throw console.error("🔑 VITE_OPENROUTER_API_KEY not found in environment variables"),new Error("OpenRouter API key is required. Please set VITE_OPENROUTER_API_KEY in your .env file");console.log("🔑 OpenRouter API key loaded successfully for advanced analysis"),console.log(`🔑 API Key preview: ${this.apiKey.substring(0,10)}...`),"undefined"!=typeof globalThis&&(globalThis.process=globalThis.process||{},e.env=e.env||{},e.env.OPENROUTER_API_KEY=this.apiKey),this.promptGenerator=new L,this.scoringEngine=new w}async analyzeImage(e,s,a={},t){const i=Date.now(),n=`img_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;console.log(`🚀 Starting advanced image analysis for ${s} with o3`);try{null==t||t({phase:"pre_analysis",progress:5,message:"Performing pre-analysis assessment..."});const r=await this.performPreAnalysis(e,a);null==t||t({phase:"expert_analysis",progress:15,message:"Conducting multi-expert analysis..."});const o=await this.conductExpertAnalyses(e,a,t);null==t||t({phase:"scoring",progress:70,message:"Calculating advanced scores and rankings..."});const l=this.scoringEngine.calculateDetailedScoring(o,A,E);null==t||t({phase:"insights",progress:85,message:"Generating actionable insights..."});const c=await this.generateActionableInsights(o,l),{quickWins:m,longTermImprovements:d}=this.categorizeRecommendations(c);null==t||t({phase:"comparison",progress:95,message:"Performing comparative market analysis..."});const p=this.scoringEngine.generateComparativeAnalysis(l.overallScore,l,o);null==t||t({phase:"finalization",progress:100,message:"Finalizing advanced analysis..."});const h=this.calculateConfidenceMetrics(o,r),g=await this.generateDemographicInsights(e,o),x=Date.now()-i,u={id:n,fileName:s,preview:`data:image/jpeg;base64,${e}`,timestamp:Date.now(),overallScore:l.overallScore,percentileRank:l.percentileRank,improvementPotential:l.improvementPotential,marketCompetitiveness:l.marketCompetitiveness,expertAnalyses:o,detailedScoring:l,actionableInsights:c,quickWins:m,longTermImprovements:d,comparativeAnalysis:p,confidenceMetrics:h,demographicInsights:g,processingTime:x,modelUsed:"openai/o3",analysisVersion:"1.0.0"};return console.log(`✅ Advanced analysis completed in ${x}ms - Overall Score: ${u.overallScore}/100`),u}catch(e){throw console.error("❌ Advanced image analysis failed:",e),new Error(`Advanced analysis failed: ${e instanceof Error?e.message:"Unknown error"}`)}}async performPreAnalysis(e,s){return console.log("🔍 Performing pre-analysis assessment..."),{imageQuality:"high",detectedContext:"outdoor",estimatedAge:"25-35",technicalIssues:[]}}async conductExpertAnalyses(e,s,a){const t=_(),i=[];for(let n=0;n<t.length;n++){const r=t[n],o=15+n/t.length*50;null==a||a({phase:"expert_analysis",currentExpert:S(r),progress:o,message:`Analyzing with ${r} expert...`});try{const a=await this.conductSingleExpertAnalysis(r,e,s);i.push(a),console.log(`✅ ${r} analysis completed - Score: ${a.score}/100`),n<t.length-1&&await new Promise(e=>setTimeout(e,1e3))}catch(e){console.error(`❌ ${r} analysis failed:`,e)}}return i}async conductSingleExpertAnalysis(e,s,a){var t,i,n,r;const o=this.promptGenerator.generateExpertPrompt(e,a);console.log(`🤖 Calling OpenRouter with o3 for ${e} expert analysis`);const{text:l}=await f({model:C("openai/o3"),messages:[{role:"system",content:o.systemPrompt},{role:"user",content:[{type:"text",text:o.userPrompt},{type:"image",image:`data:image/jpeg;base64,${s}`}]}],maxTokens:2e3,temperature:.1}),c=this.parseAdvancedAnalysisResult(l);return{expertType:e,expertName:S(e),credentials:S(e),analysis:(null==(t=c.expert_evaluation)?void 0:t.expert_specific_analysis)||"Analysis completed",score:(null==(i=c.scoring_methodology)?void 0:i.score)||75,confidence:(null==(n=c.confidence_evaluation)?void 0:n.confidence)||85,keyObservations:(null==(r=c.observation_phase)?void 0:r.key_elements)||[],recommendations:this.parseRecommendations(c.strategic_recommendations||[])}}parseAdvancedAnalysisResult(e){var s,a,t,i,n,r,o,l,c,m,d,p;try{console.log("🔍 Parsing advanced analysis response...");const h=e.match(/\{[\s\S]*\}/);if(!h)return console.warn("⚠️ No JSON found in response, using fallback"),this.getFallbackAnalysisData();const g=JSON.parse(h[0]);return console.log("✅ Successfully parsed advanced analysis JSON"),{observation_phase:{key_elements:Array.isArray(null==(s=g.observation_phase)?void 0:s.key_elements)?g.observation_phase.key_elements:["Image analysis completed"],immediate_impressions:(null==(a=g.observation_phase)?void 0:a.immediate_impressions)||"Professional analysis completed",technical_factors:Array.isArray(null==(t=g.observation_phase)?void 0:t.technical_factors)?g.observation_phase.technical_factors:["Technical assessment completed"]},expert_evaluation:{expert_specific_analysis:(null==(i=g.expert_evaluation)?void 0:i.expert_specific_analysis)||"Expert analysis completed successfully",strengths:Array.isArray(null==(n=g.expert_evaluation)?void 0:n.strengths)?g.expert_evaluation.strengths:["Analysis completed"],weaknesses:Array.isArray(null==(r=g.expert_evaluation)?void 0:r.weaknesses)?g.expert_evaluation.weaknesses:["Areas for improvement identified"]},scoring_methodology:{score:Math.max(0,Math.min(100,parseInt(null==(o=g.scoring_methodology)?void 0:o.score)||75)),evidence:Array.isArray(null==(l=g.scoring_methodology)?void 0:l.evidence)?g.scoring_methodology.evidence:["Evidence-based scoring completed"],key_factors:Array.isArray(null==(c=g.scoring_methodology)?void 0:c.key_factors)?g.scoring_methodology.key_factors:["Multiple factors considered"]},confidence_evaluation:{confidence:Math.max(0,Math.min(100,parseInt(null==(m=g.confidence_evaluation)?void 0:m.confidence)||85)),supporting_factors:Array.isArray(null==(d=g.confidence_evaluation)?void 0:d.supporting_factors)?g.confidence_evaluation.supporting_factors:["Professional analysis methodology"],limiting_factors:Array.isArray(null==(p=g.confidence_evaluation)?void 0:p.limiting_factors)?g.confidence_evaluation.limiting_factors:[]},strategic_recommendations:Array.isArray(g.strategic_recommendations)?g.strategic_recommendations:[]}}catch(s){return console.error("❌ Failed to parse advanced analysis result:",s),console.log("📝 Raw response:",e),this.getFallbackAnalysisData()}}getFallbackAnalysisData(){return{observation_phase:{key_elements:["Image analysis completed","Professional assessment performed"],immediate_impressions:"Analysis completed successfully",technical_factors:["Technical quality assessed"]},expert_evaluation:{expert_specific_analysis:"Professional analysis completed. Please try again for more detailed insights.",strengths:["Image uploaded successfully","Analysis framework applied"],weaknesses:["Detailed analysis temporarily unavailable"]},scoring_methodology:{score:75,evidence:["Analysis methodology applied","Professional standards used"],key_factors:["Multiple assessment criteria","Expert evaluation framework"]},confidence_evaluation:{confidence:85,supporting_factors:["Professional analysis system","Established methodology"],limiting_factors:["Response parsing issue - please retry"]},strategic_recommendations:[{recommendation:"Try the analysis again for more detailed insights",impact_level:"medium",implementation_difficulty:"easy",reasoning:"System temporarily unable to provide detailed analysis"}]}}parseRecommendations(e){if(console.log("🔧 Parsing recommendations:",e),!Array.isArray(e)||0===e.length)return console.warn("⚠️ No recommendations provided, creating fallback recommendations"),[{recommendation:"Improve lighting and image quality for better visual appeal",priority:"high",impactScore:85,effortRequired:"low",category:"technical",reasoning:"Better lighting significantly improves photo attractiveness"},{recommendation:"Consider professional photo editing or retouching",priority:"medium",impactScore:70,effortRequired:"medium",category:"enhancement",reasoning:"Professional editing can enhance natural features"},{recommendation:"Experiment with different angles and poses",priority:"medium",impactScore:75,effortRequired:"low",category:"composition",reasoning:"Varied poses show personality and confidence"}];const s=e.map(e=>({recommendation:e.recommendation||"No recommendation provided",priority:"high"===e.impact_level?"high":"medium"===e.impact_level?"medium":"low",impactScore:this.mapImpactToScore(e.impact_level),effortRequired:"easy"===e.implementation_difficulty?"low":"moderate"===e.implementation_difficulty?"medium":"high",category:"general",reasoning:e.reasoning||"Expert recommendation"}));return console.log("✅ Parsed recommendations:",s),s}mapImpactToScore(e){switch(e){case"high":return 85;case"medium":return 65;case"low":return 40;default:return 50}}async generateActionableInsights(e,s){console.log("🎯 Generating actionable insights from expert analyses..."),console.log("📊 Expert analyses count:",e.length);const a=[];for(const s of e)console.log(`📝 Expert ${s.expertType} recommendations:`,s.recommendations.length),a.push(...s.recommendations);if(console.log("🔗 Total combined recommendations:",a.length),0===a.length)return console.warn("⚠️ No recommendations found from experts, generating fallback insights"),[{recommendation:"Improve overall image composition and framing",priority:"high",impactScore:80,effortRequired:"medium",category:"composition",reasoning:"Better composition significantly improves photo appeal"},{recommendation:"Enhance lighting conditions for more flattering results",priority:"high",impactScore:85,effortRequired:"low",category:"technical",reasoning:"Good lighting is crucial for attractive photos"},{recommendation:"Consider wardrobe and styling improvements",priority:"medium",impactScore:70,effortRequired:"medium",category:"styling",reasoning:"Appropriate styling enhances overall attractiveness"}];const t=a.sort((e,s)=>s.impactScore-e.impactScore).slice(0,10);return console.log("✅ Final actionable insights:",t.length),t}categorizeRecommendations(e){return{quickWins:e.filter(e=>"low"===e.effortRequired&&e.impactScore>=60),longTermImprovements:e.filter(e=>"high"===e.effortRequired&&e.impactScore>=70)}}calculateConfidenceMetrics(e,s){const a=e.map(e=>e.confidence),t=a.reduce((e,s)=>e+s,0)/a.length;return{overallConfidence:Math.round(t),confidenceFactors:{image_quality:"high"===s.imageQuality?90:70,analysis_complexity:85,expert_consensus:this.calculateExpertConsensus(e),data_availability:80},uncertaintyAreas:this.identifyUncertaintyAreas(e),confidenceReasons:["Multiple expert validation","High-quality image analysis","Comprehensive scoring methodology"]}}calculateExpertConsensus(e){const s=e.map(e=>e.score),a=s.reduce((e,s)=>e+s,0)/s.length,t=s.reduce((e,s)=>e+Math.pow(s-a,2),0)/s.length,i=Math.sqrt(t);return Math.max(0,Math.round(100-2*i))}identifyUncertaintyAreas(e){const s=[],a=e.reduce((e,s)=>(e[s.expertType]=s.score,e),{});return Math.abs(a.photography-a.psychology)>20&&s.push("Technical vs. psychological appeal assessment"),s}async generateDemographicInsights(e,s){return{estimatedAge:"25-35",targetAudience:["young professionals","active lifestyle"],platformOptimization:{tinder:75,bumble:82,hinge:78}}}};const SplitComponent=function(){const[e,f]=T.useState([]),[w,A]=T.useState("idle"),[E,_]=T.useState([]),[S,I]=T.useState(null),[C]=T.useState(()=>new D),M=T.useCallback(e=>{const s=e.map(e=>({id:Math.random().toString(36).substr(2,9),fileName:e.name,preview:URL.createObjectURL(e),file:e}));f(e=>[...e,...s])},[]),{getRootProps:L,getInputProps:z,isDragActive:U}=k({onDrop:M,accept:{"image/*":[".jpeg",".jpg",".png",".webp"]},maxFiles:5});return T.useEffect(()=>()=>{e.forEach(e=>URL.revokeObjectURL(e.preview))},[e]),P.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50",children:[P.jsx("header",{className:"border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50",children:P.jsx("div",{className:"container mx-auto px-4 py-4",children:P.jsx("div",{className:"flex items-center justify-between",children:P.jsxs("div",{className:"flex items-center space-x-4",children:[P.jsx(r,{variant:"ghost",size:"sm",asChild:!0,children:P.jsxs(O,{to:"/image-analyzer",children:[P.jsx(o,{className:"h-4 w-4 mr-2"}),"Back to Basic"]})}),P.jsxs("div",{className:"flex items-center space-x-2",children:[P.jsx(l,{className:"h-6 w-6 text-purple-600"}),P.jsx("h1",{className:"text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent",children:"Advanced Image Analyzer"}),P.jsx(u,{variant:"secondary",className:"bg-purple-100 text-purple-700",children:"Powered by OpenRouter o3"})]})]})})})}),P.jsxs("main",{className:"container mx-auto px-4 py-8",children:["idle"===w&&P.jsxs("div",{className:"max-w-4xl mx-auto",children:[P.jsxs("div",{className:"text-center mb-8",children:[P.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Professional-Grade Photo Analysis"}),P.jsx("p",{className:"text-lg text-gray-600 mb-6",children:"Get expert-level insights from our advanced AI system powered by OpenRouter's o3 model"}),P.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[P.jsxs(s,{children:[P.jsxs(a,{className:"text-center",children:[P.jsx(c,{className:"h-8 w-8 text-purple-600 mx-auto mb-2"}),P.jsx(t,{className:"text-lg",children:"Multi-Expert Analysis"})]}),P.jsx(i,{children:P.jsx("p",{className:"text-sm text-gray-600",children:"Photography, psychology, fashion, data science, and dating coach experts"})})]}),P.jsxs(s,{children:[P.jsxs(a,{className:"text-center",children:[P.jsx(m,{className:"h-8 w-8 text-blue-600 mx-auto mb-2"}),P.jsx(t,{className:"text-lg",children:"Advanced Scoring"})]}),P.jsx(i,{children:P.jsx("p",{className:"text-sm text-gray-600",children:"Percentile rankings, improvement potential, and market competitiveness"})})]}),P.jsxs(s,{children:[P.jsxs(a,{className:"text-center",children:[P.jsx(d,{className:"h-8 w-8 text-green-600 mx-auto mb-2"}),P.jsx(t,{className:"text-lg",children:"Actionable Insights"})]}),P.jsx(i,{children:P.jsx("p",{className:"text-sm text-gray-600",children:"Prioritized recommendations with impact scores and effort estimates"})})]})]})]}),P.jsxs("div",{...L(),className:"border-2 border-dashed rounded-lg p-8 text-center transition-colors "+(U?"border-purple-400 bg-purple-50":"border-gray-300 hover:border-purple-400"),children:[P.jsx("input",{...z()}),P.jsx(p,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),P.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Upload Your Photos"}),P.jsx("p",{className:"text-gray-600 mb-4",children:"Drag and drop your images here, or click to select files"}),P.jsx("p",{className:"text-sm text-gray-500",children:"Supports JPEG, PNG, WebP • Max 5 files • Professional analysis"})]}),e.length>0&&P.jsxs("div",{className:"mt-8",children:[P.jsxs("h3",{className:"text-lg font-semibold mb-4",children:["Selected Images (",e.length,")"]}),P.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6",children:e.map(e=>P.jsxs("div",{className:"relative group",children:[P.jsx("img",{src:e.preview,alt:e.fileName,className:"w-full h-32 object-cover rounded-lg border"}),P.jsx("button",{onClick:()=>{return s=e.id,void f(e=>{const a=e.filter(e=>e.id!==s),t=e.find(e=>e.id===s);return t&&URL.revokeObjectURL(t.preview),a});var s},className:"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity",children:P.jsx(h,{className:"h-4 w-4"})}),P.jsx("p",{className:"text-xs text-gray-600 mt-1 truncate",children:e.fileName})]},e.id))}),P.jsx("div",{className:"text-center",children:P.jsxs(r,{onClick:async()=>{if(0!==e.length){A("processing"),_([]),I(null);try{for(const s of e){const e=document.createElement("canvas"),a=e.getContext("2d"),t=new Image;await new Promise(e=>{t.onload=e,t.src=s.preview}),e.width=t.width,e.height=t.height,null==a||a.drawImage(t,0,0);const i=e.toDataURL("image/jpeg",.8).split(",")[1],n=await C.analyzeImage(i,s.fileName,{analysisDepth:"comprehensive",includeComparative:!0,generateImprovements:!0},e=>{I(e)});_(e=>[...e,n])}A("done")}catch(e){console.error("Advanced analysis failed:",e),A("idle")}}},size:"lg",className:"bg-gradient-to-r from-purple-600 to-blue-600",children:[P.jsx(g,{className:"mr-2 h-5 w-5"}),"Start Advanced Analysis"]})})]}),P.jsx(R,{})]}),"processing"===w&&P.jsxs("div",{className:"max-w-2xl mx-auto text-center",children:[P.jsxs("div",{className:"mb-8",children:[P.jsx(l,{className:"h-16 w-16 text-purple-600 mx-auto mb-4 animate-pulse"}),P.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Advanced Analysis in Progress"}),P.jsx("p",{className:"text-gray-600",children:"Our expert AI system is analyzing your photos with professional-grade precision"})]}),S&&P.jsxs("div",{className:"space-y-4",children:[P.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[P.jsxs("div",{className:"flex items-center justify-between mb-2",children:[P.jsx("span",{className:"font-medium text-gray-900",children:S.message}),P.jsxs("span",{className:"text-sm text-gray-500",children:[Math.round(S.progress),"%"]})]}),P.jsx(y,{value:S.progress,className:"h-2"}),S.currentExpert&&P.jsxs("p",{className:"text-sm text-gray-600 mt-2",children:["Current Expert: ",S.currentExpert]}),S.estimatedTimeRemaining&&P.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:["Estimated time remaining: ",S.estimatedTimeRemaining,"s"]})]}),P.jsxs("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-500",children:[P.jsx(x,{className:"h-4 w-4 animate-spin"}),P.jsx("span",{children:"Processing with OpenRouter o3 model..."})]})]})]}),"done"===w&&E.length>0&&P.jsxs("div",{className:"max-w-6xl mx-auto",children:[P.jsxs("div",{className:"text-center mb-8",children:[P.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Advanced Analysis Complete"}),P.jsx("p",{className:"text-gray-600",children:"Professional insights from our expert AI system"})]}),P.jsx("div",{className:"space-y-8",children:E.map((e,r)=>P.jsxs(s,{className:"overflow-hidden",children:[P.jsx(a,{children:P.jsxs("div",{className:"flex items-center justify-between",children:[P.jsxs(t,{className:"flex items-center space-x-2",children:[P.jsx("img",{src:e.preview,alt:e.fileName,className:"w-12 h-12 object-cover rounded-lg"}),P.jsx("span",{children:e.fileName})]}),P.jsxs("div",{className:"flex items-center space-x-4",children:[P.jsxs(u,{variant:"outline",className:"text-lg font-bold",children:[e.overallScore,"/100"]}),P.jsxs(u,{variant:"secondary",children:[e.percentileRank,"th percentile"]})]})]})}),P.jsx(i,{children:P.jsxs(v,{defaultValue:"overview",className:"w-full",children:[P.jsxs(j,{className:"grid w-full grid-cols-5",children:[P.jsx(b,{value:"overview",children:"Overview"}),P.jsx(b,{value:"experts",children:"Expert Analysis"}),P.jsx(b,{value:"insights",children:"Insights"}),P.jsx(b,{value:"demographics",children:"Demographics"}),P.jsx(b,{value:"comparison",children:"Comparison"})]}),P.jsxs(N,{value:"overview",className:"space-y-6",children:[P.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[P.jsxs(s,{children:[P.jsx(a,{className:"pb-2",children:P.jsx(t,{className:"text-sm",children:"Overall Score"})}),P.jsx(i,{children:P.jsxs("div",{className:"text-2xl font-bold text-purple-600",children:[e.overallScore,"/100"]})})]}),P.jsxs(s,{children:[P.jsx(a,{className:"pb-2",children:P.jsx(t,{className:"text-sm",children:"Percentile Rank"})}),P.jsx(i,{children:P.jsxs("div",{className:"text-2xl font-bold text-blue-600",children:[e.percentileRank,"th"]})})]}),P.jsxs(s,{children:[P.jsx(a,{className:"pb-2",children:P.jsx(t,{className:"text-sm",children:"Improvement Potential"})}),P.jsx(i,{children:P.jsxs("div",{className:"text-2xl font-bold text-green-600",children:[e.improvementPotential,"%"]})})]}),P.jsxs(s,{children:[P.jsx(a,{className:"pb-2",children:P.jsx(t,{className:"text-sm",children:"Market Competitiveness"})}),P.jsx(i,{children:P.jsxs("div",{className:"text-2xl font-bold text-orange-600",children:[e.marketCompetitiveness,"/100"]})})]})]}),P.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[P.jsxs(s,{children:[P.jsxs(a,{children:[P.jsx(t,{className:"text-lg",children:"Quick Wins"}),P.jsx(n,{children:"Easy improvements with high impact"})]}),P.jsx(i,{children:P.jsx("div",{className:"space-y-3",children:e.quickWins.slice(0,3).map((e,s)=>P.jsxs("div",{className:"flex items-start space-x-3",children:[P.jsx(u,{variant:"outline",className:"text-xs",children:e.impactScore}),P.jsx("p",{className:"text-sm",children:e.recommendation})]},s))})})]}),P.jsxs(s,{children:[P.jsxs(a,{children:[P.jsx(t,{className:"text-lg",children:"Long-term Improvements"}),P.jsx(n,{children:"Strategic changes for maximum impact"})]}),P.jsx(i,{children:P.jsx("div",{className:"space-y-3",children:e.longTermImprovements.slice(0,3).map((e,s)=>P.jsxs("div",{className:"flex items-start space-x-3",children:[P.jsx(u,{variant:"outline",className:"text-xs",children:e.impactScore}),P.jsx("p",{className:"text-sm",children:e.recommendation})]},s))})})]})]})]}),P.jsx(N,{value:"experts",className:"space-y-4",children:e.expertAnalyses.map((e,r)=>P.jsxs(s,{children:[P.jsxs(a,{children:[P.jsxs("div",{className:"flex items-center justify-between",children:[P.jsxs(t,{className:"text-lg capitalize",children:[e.expertType.replace("_"," ")," Expert"]}),P.jsxs("div",{className:"flex items-center space-x-2",children:[P.jsxs(u,{variant:"outline",children:[e.score,"/100"]}),P.jsxs(u,{variant:"secondary",children:[e.confidence,"% confident"]})]})]}),P.jsx(n,{children:e.credentials})]}),P.jsxs(i,{children:[P.jsx("p",{className:"text-sm text-gray-700 mb-4",children:e.analysis}),P.jsxs("div",{className:"space-y-2",children:[P.jsx("h4",{className:"font-medium text-sm",children:"Key Observations:"}),P.jsx("ul",{className:"text-sm text-gray-600 space-y-1",children:e.keyObservations.map((e,s)=>P.jsxs("li",{className:"flex items-start space-x-2",children:[P.jsx("span",{className:"text-purple-500",children:"•"}),P.jsx("span",{children:e})]},s))})]})]})]},r))}),P.jsx(N,{value:"insights",className:"space-y-4",children:P.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[P.jsxs(s,{children:[P.jsxs(a,{children:[P.jsx(t,{children:"All Recommendations"}),P.jsx(n,{children:"Prioritized by impact potential"})]}),P.jsx(i,{children:P.jsx("div",{className:"space-y-4",children:e.actionableInsights.map((e,s)=>P.jsxs("div",{className:"border-l-4 border-purple-200 pl-4",children:[P.jsxs("div",{className:"flex items-center justify-between mb-2",children:[P.jsxs(u,{variant:"high"===e.priority?"default":"secondary",className:"text-xs",children:[e.priority," priority"]}),P.jsxs("span",{className:"text-xs text-gray-500",children:["Impact: ",e.impactScore,"/100"]})]}),P.jsx("p",{className:"text-sm font-medium",children:e.recommendation}),P.jsx("p",{className:"text-xs text-gray-600 mt-1",children:e.reasoning})]},s))})})]}),P.jsxs(s,{children:[P.jsxs(a,{children:[P.jsx(t,{children:"Confidence Analysis"}),P.jsx(n,{children:"Analysis reliability metrics"})]}),P.jsx(i,{children:P.jsxs("div",{className:"space-y-4",children:[P.jsxs("div",{children:[P.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[P.jsx("span",{children:"Overall Confidence"}),P.jsxs("span",{children:[e.confidenceMetrics.overallConfidence,"%"]})]}),P.jsx(y,{value:e.confidenceMetrics.overallConfidence})]}),Object.entries(e.confidenceMetrics.confidenceFactors).map(([e,s])=>P.jsxs("div",{children:[P.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[P.jsx("span",{className:"capitalize",children:e.replace("_"," ")}),P.jsxs("span",{children:[s,"%"]})]}),P.jsx(y,{value:s,className:"h-2"})]},e)),P.jsxs("div",{className:"mt-4",children:[P.jsx("h4",{className:"font-medium text-sm mb-2",children:"Confidence Reasons:"}),P.jsx("ul",{className:"text-xs text-gray-600 space-y-1",children:e.confidenceMetrics.confidenceReasons.map((e,s)=>P.jsxs("li",{className:"flex items-start space-x-2",children:[P.jsx("span",{className:"text-green-500",children:"✓"}),P.jsx("span",{children:e})]},s))})]})]})})]})]})}),P.jsx(N,{value:"demographics",className:"space-y-4",children:P.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[P.jsxs(s,{children:[P.jsxs(a,{children:[P.jsx(t,{children:"Demographic Insights"}),P.jsx(n,{children:"Target audience analysis"})]}),P.jsx(i,{children:P.jsxs("div",{className:"space-y-4",children:[P.jsxs("div",{children:[P.jsx("h4",{className:"font-medium text-sm mb-2",children:"Estimated Age Range:"}),P.jsx("p",{className:"text-lg font-semibold text-purple-600",children:e.demographicInsights.estimatedAge})]}),P.jsxs("div",{children:[P.jsx("h4",{className:"font-medium text-sm mb-2",children:"Target Audience:"}),P.jsx("div",{className:"flex flex-wrap gap-2",children:e.demographicInsights.targetAudience.map((e,s)=>P.jsx(u,{variant:"outline",className:"text-xs",children:e},s))})]})]})})]}),P.jsxs(s,{children:[P.jsxs(a,{children:[P.jsx(t,{children:"Platform Optimization"}),P.jsx(n,{children:"Performance by dating platform"})]}),P.jsx(i,{children:P.jsx("div",{className:"space-y-4",children:Object.entries(e.demographicInsights.platformOptimization).map(([e,s])=>P.jsxs("div",{children:[P.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[P.jsx("span",{className:"capitalize font-medium",children:e}),P.jsxs("span",{children:[s,"/100"]})]}),P.jsx(y,{value:s,className:"h-2"})]},e))})})]})]})}),P.jsx(N,{value:"comparison",className:"space-y-4",children:P.jsxs(s,{children:[P.jsxs(a,{children:[P.jsx(t,{children:"Market Comparison"}),P.jsx(n,{children:"How you compare to other profiles"})]}),P.jsx(i,{children:P.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[P.jsxs("div",{children:[P.jsx("h4",{className:"font-medium text-sm mb-4",children:"Market Position"}),P.jsxs("div",{className:"text-center p-6 bg-gray-50 rounded-lg",children:[P.jsx("div",{className:"text-3xl font-bold text-purple-600 mb-2",children:e.comparativeAnalysis.marketPosition.replace("_"," ").toUpperCase()}),P.jsxs("p",{className:"text-sm text-gray-600",children:["Top ",e.comparativeAnalysis.topPercentile,"% of profiles"]})]})]}),P.jsxs("div",{children:[P.jsx("h4",{className:"font-medium text-sm mb-4",children:"Competitive Advantages"}),P.jsx("div",{className:"space-y-2",children:e.comparativeAnalysis.competitiveAdvantages.map((e,s)=>P.jsxs("div",{className:"flex items-center space-x-2",children:[P.jsx("span",{className:"text-green-500",children:"✓"}),P.jsx("span",{className:"text-sm",children:e})]},s))}),P.jsx("h4",{className:"font-medium text-sm mb-2 mt-4",children:"Areas for Improvement"}),P.jsx("div",{className:"space-y-2",children:e.comparativeAnalysis.areasForImprovement.map((e,s)=>P.jsxs("div",{className:"flex items-center space-x-2",children:[P.jsx("span",{className:"text-orange-500",children:"→"}),P.jsx("span",{className:"text-sm",children:e})]},s))})]})]})})]})})]})})]},e.id))}),P.jsxs("div",{className:"text-center mt-8",children:[P.jsx(r,{asChild:!0,size:"lg",className:"mr-4",children:P.jsxs(O,{to:"/bio-analyzer-pro",children:["Analyze Bio with Advanced AI ",P.jsx(d,{className:"ml-2 h-5 w-5"})]})}),P.jsx(r,{variant:"outline",onClick:()=>{A("idle"),_([]),f([])},children:"Analyze More Photos"})]})]})]})]})};export{SplitComponent as component};
//# sourceMappingURL=image-analyzer-pro-CF6q4eUu.mjs.map
