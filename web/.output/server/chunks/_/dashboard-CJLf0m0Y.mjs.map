{"version": 3, "file": "dashboard-CJLf0m0Y.mjs", "sources": ["../../../../../src/pages/Dashboard.tsx", "../../../../../src/routes/_authed/dashboard.tsx?tsr-split=component"], "sourcesContent": null, "names": ["Dashboard", "user", "useUser", "className", "children", "firstName", "jsx", "UserButton", "<PERSON><PERSON>", "variant", "size", "Settings", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "Star", "jsxs", "<PERSON><PERSON><PERSON><PERSON>", "Heart", "MessageCircle", "TrendingUp", "Camera", "CardDescription", "Badge", "<PERSON><PERSON><PERSON><PERSON>", "Link", "to", "FileText", "BarChart3", "SplitComponent", "ProtectedRoute"], "mappings": "ghBAsBA,SAAgBA,YACd,MAAMC,KAAEA,GAASC,IAEjB,aACG,MAAA,CAAIC,UAAU,gEACbC,gBAAC,MAAA,CAAID,UAAU,qCAEZ,MAAA,CAAIA,UAAU,0DACZ,MAAA,CAAAC,SAAA,QACE,KAAA,CAAGD,UAAU,8CAAoC,wBACjCF,WAAMI,YAAa,OAAO,OAE3CC,EAAAA,IAAC,IAAA,CAAEH,UAAU,sBAAsBC,SAAA,6EAKrCE,EAAAA,IAACC,EAAA,WACAC,EAAA,CAAOC,QAAQ,YAAYC,KAAK,KAC/BN,SAAA,CAAAE,EAAAA,IAACK,EAAA,CAASR,UAAU,iBAAiB,wBAMxC,MAAA,CAAIA,UAAU,8DACZS,EAAA,CAAAR,SAAA,QACES,EAAA,CAAWV,UAAU,4DACpBC,SAAA,CAAAE,EAAAA,IAACQ,EAAA,CAAUX,UAAU,sBAAsBC,SAAA,kBAG3CE,EAAAA,IAACS,EAAA,CAAKZ,UAAU,8BAElBa,EAAAA,KAACC,EAAA,CAAAb,SAAA,OACE,MAAA,CAAID,UAAU,qBAAqBC,SAAA,QACpCE,EAAAA,IAAC,IAAA,CAAEH,UAAU,gCAAgCC,SAAA,qCAMhDQ,EAAA,CAAAR,SAAA,QACES,EAAA,CAAWV,UAAU,4DACpBC,SAAA,CAAAE,EAAAA,IAACQ,EAAA,CAAUX,UAAU,sBAAsBC,SAAA,YAC3CE,EAAAA,IAACY,EAAA,CAAMf,UAAU,4BAEnBa,EAAAA,KAACC,EAAA,CAAAb,SAAA,OACE,MAAA,CAAID,UAAU,qBAAqBC,SAAA,OACpCE,EAAAA,IAAC,IAAA,CAAEH,UAAU,gCAAgCC,SAAA,qCAMhDQ,EAAA,CAAAR,SAAA,QACES,EAAA,CAAWV,UAAU,4DACpBC,SAAA,CAAAE,EAAAA,IAACQ,EAAA,CAAUX,UAAU,sBAAsBC,SAAA,aAC3CE,EAAAA,IAACa,EAAA,CAAchB,UAAU,6BAE3Ba,EAAAA,KAACC,EAAA,CAAAb,SAAA,OACE,MAAA,CAAID,UAAU,qBAAqBC,SAAA,QACpCE,EAAAA,IAAC,IAAA,CAAEH,UAAU,gCAAgCC,SAAA,oCAMhDQ,EAAA,CAAAR,SAAA,QACES,EAAA,CAAWV,UAAU,4DACpBC,SAAA,CAAAE,EAAAA,IAACQ,EAAA,CAAUX,UAAU,sBAAsBC,SAAA,kBAG3CE,EAAAA,IAACc,EAAA,CAAWjB,UAAU,8BAExBa,EAAAA,KAACC,EAAA,CAAAb,SAAA,OACE,MAAA,CAAID,UAAU,qBAAqBC,SAAA,QACpCE,EAAAA,IAAC,IAAA,CAAEH,UAAU,gCAAgCC,SAAA,wCAQlD,MAAA,CAAID,UAAU,uDACba,EAAAA,KAACJ,EAAA,CAAAR,SAAA,CACCY,OAACH,EAAA,CAAAT,SAAA,QACEU,EAAA,CAAUX,UAAU,0BACnBC,SAAA,CAAAE,EAAAA,IAACe,EAAA,CAAOlB,UAAU,2BAA2B,oBAG/CG,EAAAA,IAACgB,EAAA,CAAAlB,SAAgB,sDAIlBa,EAAA,CAAYd,UAAU,6BACpB,MAAA,CAAIA,UAAU,oCACbC,SAAA,CAAAE,EAAAA,IAAC,OAAA,CAAKH,UAAU,UAAUC,SAAA,kBAC1BE,EAAAA,IAACiB,EAAA,CAAMd,QAAQ,YAAYL,SAAA,yBAE5B,MAAA,CAAID,UAAU,oCACbC,SAAA,CAAAE,EAAAA,IAAC,OAAA,CAAKH,UAAU,UAAUC,SAAA,oBAC1BE,EAAAA,IAAC,OAAA,CAAKH,UAAU,sBAAsBC,SAAA,kBAEvCI,EAAA,CACCgB,SAAA,EACArB,UAAU,yEAEVC,eAACqB,EAAA,CAAKC,GAAG,kBAAkBtB,SAAA,sCAKhCQ,EAAA,CAAAR,SAAA,QACES,EAAA,CAAAT,SAAA,QACEU,EAAA,CAAUX,UAAU,0BACnBC,SAAA,CAAAE,EAAAA,IAACqB,EAAA,CAASxB,UAAU,2BAA2B,sBAGjDG,EAAAA,IAACgB,EAAA,CAAAlB,SAAgB,uDAIlBa,EAAA,CAAYd,UAAU,6BACpB,MAAA,CAAIA,UAAU,oCACbC,SAAA,CAAAE,EAAAA,IAAC,OAAA,CAAKH,UAAU,UAAUC,SAAA,cAC1BE,EAAAA,IAACiB,EAAA,CAAMd,QAAQ,YAAYL,SAAA,qBAE5B,MAAA,CAAID,UAAU,oCACbC,SAAA,CAAAE,EAAAA,IAAC,OAAA,CAAKH,UAAU,UAAUC,SAAA,iBAC1BE,EAAAA,IAAC,OAAA,CAAKH,UAAU,sBAAsBC,SAAA,wBAEvCI,EAAA,CACCgB,SAAA,EACArB,UAAU,yEAEVC,eAACqB,EAAA,CAAKC,GAAG,gBAAgBtB,SAAA,4BAOjCY,OAACJ,EAAA,CAAAR,SAAA,CACCY,EAAAA,KAACH,EAAA,CAAAT,SAAA,CACCY,EAAAA,KAACF,EAAA,CAAUX,UAAU,0BACnBC,SAAA,CAAAE,EAAAA,IAACsB,EAAA,CAAUzB,UAAU,YAAY,qBAGnCG,EAAAA,IAACgB,EAAA,CAAAlB,SAAgB,oDAInBE,EAAAA,IAACW,EAAA,CAAAb,gBACE,MAAA,CAAID,UAAU,6BACZ,MAAA,CAAIA,UAAU,kDACbC,SAAA,CAAAY,EAAAA,KAAC,MAAA,CAAIb,UAAU,0BACbC,SAAA,CAAAE,EAAAA,IAAC,MAAA,CAAIH,UAAU,sCACfG,EAAAA,IAAC,OAAA,CAAKH,UAAU,UAAUC,SAAA,gCAE5BE,EAAAA,IAAC,OAAA,CAAKH,UAAU,gCAAgCC,SAAA,0BAIjD,MAAA,CAAID,UAAU,kDACbC,SAAA,CAAAY,EAAAA,KAAC,MAAA,CAAIb,UAAU,0BACbC,SAAA,CAAAE,EAAAA,IAAC,MAAA,CAAIH,UAAU,qCACfG,EAAAA,IAAC,OAAA,CAAKH,UAAU,UAAUC,SAAA,uCAI5BE,EAAAA,IAAC,OAAA,CAAKH,UAAU,gCAAgCC,SAAA,wBAEjD,MAAA,CAAID,UAAU,kDACbC,SAAA,CAAAY,EAAAA,KAAC,MAAA,CAAIb,UAAU,0BACbC,SAAA,CAAAE,EAAAA,IAAC,MAAA,CAAIH,UAAU,sCACfG,EAAAA,IAAC,OAAA,CAAKH,UAAU,UAAUC,SAAA,mCAE5BE,EAAAA,IAAC,OAAA,CAAKH,UAAU,gCAAgCC,SAAA,yBAIjD,MAAA,CAAID,UAAU,oCACbC,SAAA,CAAAY,EAAAA,KAAC,MAAA,CAAIb,UAAU,0BACbC,SAAA,CAAAE,EAAAA,IAAC,MAAA,CAAIH,UAAU,uCACfG,EAAAA,IAAC,OAAA,CAAKH,UAAU,UAAUC,SAAA,iDAI5BE,EAAAA,IAAC,OAAA,CAAKH,UAAU,gCAAgCC,SAAA,gCASvD,MAAA,CAAID,UAAU,2BACbC,eAACI,EAAA,CACCgB,SAAA,EACAf,QAAQ,YACRN,UAAU,0BAEVC,gBAACqB,EAAA,CAAKC,GAAG,oBACPtB,SAAA,CAAAE,EAAAA,IAACK,EAAA,CAASR,UAAU,YAAY,8BAQ7C,CCzO4D,MAAA0B,eAAA,WAO3D,OACEvB,EAAAA,IAACwB,EAAA,CAAA1B,eACEJ,UAAA,CAAA,IAGN"}