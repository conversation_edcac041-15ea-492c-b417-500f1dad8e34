const e={photography:{type:"photography",name:"Dr. <PERSON>",credentials:"Professional Portrait Photographer, 15+ years experience, MFA in Photography",background:"Award-winning portrait photographer specializing in dating profile photography with over 10,000 successful profile shoots. Published in major photography magazines and featured speaker at photography conferences.",expertise:["Portrait composition and framing","Lighting analysis and optimization","Color theory and visual appeal","Technical image quality assessment","Visual storytelling and mood creation","Camera angle psychology","Background and environment selection"],analysisApproach:"Technical precision combined with artistic vision, focusing on how photographic elements contribute to attraction and visual appeal",specializations:["Dating profile optimization","Professional headshots","Lifestyle photography","Mobile photography techniques"]},psychology:{type:"psychology",name:"Dr. <PERSON>",credentials:"Clinical Psychologist, PhD in Social Psychology, Attraction Research Specialist",background:"Leading researcher in attraction psychology with 12+ years studying facial attractiveness, body language, and romantic appeal. Published 50+ peer-reviewed papers on attraction science and dating behavior.",expertise:["Facial attractiveness assessment","Body language interpretation","Emotional expression analysis","Attraction psychology principles","Nonverbal communication","Confidence and charisma indicators","Psychological appeal factors"],analysisApproach:"Evidence-based psychological assessment using established attraction research and behavioral psychology principles",specializations:["Facial symmetry and proportions","Micro-expression analysis","Attachment style indicators","Personality trait detection"]},fashion:{type:"fashion",name:"<PERSON> <PERSON>",credentials:"Celebrity Fashion Stylist, 10+ years experience, Featured in Vogue and GQ",background:"High-profile fashion stylist who has worked with A-list celebrities and influencers. Specializes in personal branding through fashion and has styled over 500 dating profiles for high-net-worth individuals.",expertise:["Personal style assessment","Color coordination and theory","Fit and tailoring evaluation","Trend awareness and timelessness","Accessory selection and styling","Grooming and presentation","Brand alignment through fashion"],analysisApproach:"Holistic style evaluation considering personal brand, target audience, and current fashion trends while maintaining timeless appeal",specializations:["Executive and professional styling","Casual and lifestyle looks","Formal and event styling","Seasonal and trend integration"]},data_science:{type:"data_science",name:"Dr. Alex Kim",credentials:"Senior Data Scientist, PhD in Statistics, 8+ years in dating app analytics",background:"Former lead data scientist at major dating platforms with access to millions of profile interactions. Expert in predictive modeling for dating success and conversion optimization.",expertise:["Dating app algorithm optimization","Conversion rate analysis","A/B testing and statistical analysis","User behavior pattern recognition","Predictive modeling for dating success","Market segmentation and targeting","Performance benchmarking"],analysisApproach:"Data-driven analysis using statistical models and machine learning insights from millions of dating profiles and interactions",specializations:["Swipe rate optimization","Match probability modeling","Demographic targeting","Platform-specific optimization"]},dating_coach:{type:"dating_coach",name:"Rachel Thompson",credentials:"Certified Dating Coach, 1000+ successful client transformations, 7+ years experience",background:"Professional dating coach with proven track record of helping clients find meaningful relationships. Specializes in profile optimization and has worked with clients across all age groups and demographics.",expertise:["Profile optimization strategies","Target audience identification","Personal branding for dating","Conversation starter creation","Authenticity and genuine appeal","Relationship goal alignment","Market positioning and differentiation"],analysisApproach:"Practical, results-oriented coaching focused on authentic self-presentation and strategic positioning for dating success",specializations:["Professional singles (25-45)","Post-divorce dating re-entry","Serious relationship seekers","Executive and high-achiever dating"]}},getExpertPersona=a=>{const t=e[a];if(!t)throw new Error(`Unknown expert type: ${a}`);return t},getAllExpertTypes=()=>Object.keys(e),getExpertCredentials=e=>{const a=getExpertPersona(e);return`${a.name}, ${a.credentials}`},a={photography:.25,psychology:.25,fashion:.2,data_science:.15,dating_coach:.15},t={psychology:.3,dating_coach:.25,data_science:.2,photography:.1,fashion:.15},i={technical_quality:.2,attractiveness:.3,style_presentation:.25,market_appeal:.15,authenticity:.1},n={technical_quality:.15,attractiveness:.25,style_presentation:.2,market_appeal:.25,authenticity:.15};var o=class{calculateOverallScore(e,t=a){let i=0,n=0;for(const a of e){const e=t[a.expertType]||0;i+=a.score*e,n+=e}return n>0?Math.round(i/n):0}calculateDetailedScoring(e,a,t){const i={},n=this.categorizeExpertScores(e);for(const[t,o]of Object.entries(n)){const n=a[t]||0,s=o.reduce((e,a)=>e+a,0)/o.length;i[t]={score:Math.round(s),weight:n,components:this.getComponentScores(t,e)}}const o=this.calculateOverallScore(e,t);return{overallScore:o,subScores:i,percentileRank:this.calculatePercentileRank(o),improvementPotential:this.calculateImprovementPotential(i),marketCompetitiveness:this.calculateMarketCompetitiveness(o,i)}}categorizeExpertScores(e){const a={technical_quality:[],attractiveness:[],style_presentation:[],market_appeal:[],authenticity:[]};for(const t of e)switch(t.expertType){case"photography":a.technical_quality.push(t.score);break;case"psychology":a.attractiveness.push(t.score),a.authenticity.push(t.score);break;case"fashion":a.style_presentation.push(t.score);break;case"data_science":a.market_appeal.push(t.score);break;case"dating_coach":a.market_appeal.push(t.score),a.authenticity.push(t.score)}return a}getComponentScores(e,a){const t={};for(const i of a)this.isExpertRelevantToCategory(i.expertType,e)&&(t[i.expertType]=i.score);return t}isExpertRelevantToCategory(e,a){var t;return(null==(t={technical_quality:["photography"],attractiveness:["psychology","photography"],style_presentation:["fashion","photography"],market_appeal:["data_science","dating_coach"],authenticity:["psychology","dating_coach"]}[a])?void 0:t.includes(e))||!1}calculatePercentileRank(e){return e>=90?95:e>=80?85:e>=70?70:e>=60?55:e>=50?40:e>=40?25:e>=30?15:5}calculateImprovementPotential(e){const a=Object.values(e).map(e=>e.score),t=Math.max(...a)-Math.min(...a),i=100-a.reduce((e,a)=>e+a,0)/a.length;return Math.min(100,Math.round(.3*t+.7*i))}calculateMarketCompetitiveness(e,a){const t=Object.values(a).map(e=>e.score),i=this.calculateBalance(t);return Math.round(.7*e+.3*i)}calculateBalance(e){if(0===e.length)return 0;const a=e.reduce((e,a)=>e+a,0)/e.length,t=e.reduce((e,t)=>e+Math.pow(t-a,2),0)/e.length,i=Math.sqrt(t);return Math.max(0,Math.round(100-2*i))}generateComparativeAnalysis(e,a,t){const i=a.percentileRank;let n;n=i>=90?"top_tier":i>=70?"above_average":i>=40?"average":i>=20?"below_average":"needs_work";const o=this.extractCompetitiveAdvantages(a,t),s=this.extractAreasForImprovement(a,t);return{percentileRank:i,topPercentile:this.calculateTopPercentile(i),averageScore:65,competitiveAdvantages:o,areasForImprovement:s,marketPosition:n}}calculateTopPercentile(e){return Math.max(1,100-e)}extractCompetitiveAdvantages(e,a){const t=[];for(const[a,i]of Object.entries(e.subScores))i.score>=80&&t.push(`Strong ${a.replace("_"," ")}`);for(const e of a)e.score>=80&&t.push(...e.keyObservations.slice(0,1));return t.slice(0,5)}extractAreasForImprovement(e,a){const t=[];for(const[a,i]of Object.entries(e.subScores))i.score<60&&t.push(`Improve ${a.replace("_"," ")}`);for(const e of a)e.score<70&&t.push(...e.recommendations.slice(0,1).map(e=>e.recommendation));return t.slice(0,5)}};export{o as A,n as B,a as D,i as I,getExpertCredentials as a,getExpertPersona as b,t as c,getAllExpertTypes as g};
//# sourceMappingURL=advanced-scoring-Cd6PgUsS.mjs.map
