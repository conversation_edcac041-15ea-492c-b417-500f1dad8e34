var e,s=Object.defineProperty,__publicField=(e,t,n)=>((e,t,n)=>t in e?s(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n)(e,"symbol"!=typeof t?t+"":t,n);const t="images",n="tinderop_session";var r=(e=class{constructor(){__publicField(this,"currentSession",null)}static getInstance(){return e.instance||(e.instance=new e),e.instance}getCurrentSession(){if(this.currentSession)return this.currentSession;const e=localStorage.getItem(n);if(e)try{return this.currentSession=JSON.parse(e),this.currentSession}catch(e){console.warn("Failed to parse stored session, creating new one")}return this.currentSession={id:`session_${Date.now()}_${Math.random().toString(36).substring(2,11)}`,createdAt:Date.now(),imageIds:[],results:{}},this.saveSession(),this.currentSession}addImageToSession(e){const s=this.getCurrentSession();s.imageIds.includes(e)||(s.imageIds.push(e),this.saveSession())}removeImageFromSession(e){const s=this.getCurrentSession();s.imageIds=s.imageIds.filter(s=>s!==e),delete s.results[e],this.saveSession()}saveAnalysisResult(e,s){this.getCurrentSession().results[e]=s,this.saveSession()}getAnalysisResult(e){return this.getCurrentSession().results[e]}clearSession(){localStorage.removeItem(n),this.currentSession=null}saveSession(){this.currentSession&&localStorage.setItem(n,JSON.stringify(this.currentSession))}},__publicField(e,"instance"),e);const i=new class{constructor(){__publicField(this,"db",null)}async init(){return new Promise((e,s)=>{const n=indexedDB.open("TinderOpImageDB",1);n.onerror=()=>s(n.error),n.onsuccess=()=>{this.db=n.result,e()},n.onupgradeneeded=e=>{const s=e.target.result;if(!s.objectStoreNames.contains(t)){const e=s.createObjectStore(t,{keyPath:"id"});e.createIndex("sessionId","sessionId",{unique:!1}),e.createIndex("uploadedAt","uploadedAt",{unique:!1})}}})}async storeImage(e,s){this.db||await this.init();const n=`${s}_${Date.now()}_${Math.random().toString(36).substring(2,11)}`,r={id:n,fileName:e.name,blob:e,mimeType:e.type,uploadedAt:Date.now(),sessionId:s};return new Promise((e,s)=>{const i=this.db.transaction([t],"readwrite").objectStore(t).add(r);i.onsuccess=()=>e(n),i.onerror=()=>s(i.error)})}async getImage(e){return this.db||await this.init(),new Promise((s,n)=>{const r=this.db.transaction([t],"readonly").objectStore(t).get(e);r.onsuccess=()=>s(r.result||null),r.onerror=()=>n(r.error)})}async getSessionImages(e){return this.db||await this.init(),new Promise((s,n)=>{const r=this.db.transaction([t],"readonly").objectStore(t).index("sessionId").getAll(e);r.onsuccess=()=>s(r.result),r.onerror=()=>n(r.error)})}async deleteImage(e){return this.db||await this.init(),new Promise((s,n)=>{const r=this.db.transaction([t],"readwrite").objectStore(t).delete(e);r.onsuccess=()=>s(),r.onerror=()=>n(r.error)})}async clearSession(e){this.db||await this.init();const s=(await this.getSessionImages(e)).map(e=>this.deleteImage(e.id));await Promise.all(s)}async cleanup(){this.db||await this.init();const e=Date.now()-864e5;return new Promise((s,n)=>{const r=this.db.transaction([t],"readwrite").objectStore(t).index("uploadedAt").openCursor(IDBKeyRange.upperBound(e));r.onsuccess=e=>{const t=e.target.result;t?(t.delete(),t.continue()):s()},r.onerror=()=>n(r.error)})}},o=r.getInstance();async function convertBlobToBase64(e){return new Promise((s,t)=>{const n=new FileReader;n.onload=()=>{const e=n.result;s(e.split(",")[1])},n.onerror=t,n.readAsDataURL(e)})}function createImagePreview(e){return URL.createObjectURL(e)}function revokeImagePreview(e){URL.revokeObjectURL(e)}async function initStorage(){await i.init(),await i.cleanup()}export{i as a,convertBlobToBase64 as b,createImagePreview as c,initStorage as i,revokeImagePreview as r,o as s};
//# sourceMappingURL=storage-IIfgkkYA.mjs.map
