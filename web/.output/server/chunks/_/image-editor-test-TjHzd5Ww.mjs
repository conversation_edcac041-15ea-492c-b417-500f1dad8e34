import{Buffer as e}from"node:buffer";import{r as t,j as s,c as n}from"./routeTree.gen-BFK54byf.mjs";import{C as i,a,b as o,c as r,d as l}from"./card-zxSsGLJg.mjs";import{t as c,e as u,B as d,R as m,Z as p,u as g,v as h,w as f,x as y,P as v,y as w,o as x,n as b,k as R}from"./lucide-react.mjs";import{B as S}from"./badge-Cwk-ZwYz.mjs";import{w as _,l as I,U as C,p as T,c as j,d as k,e as N,a as E,I as A,i as q,b as P,h as M,f as O,o as D,u as U,n as L,s as F,q as z,m as H,j as $,r as B,t as G,v as W,x as Q,T as J,y as V,z as K,A as Y,B as Z,g as X,P as ee}from"./progress-WcEdq6Og.mjs";import{T as te,a as se,b as ne,c as ie}from"./tabs-D9-6MReb.mjs";import{S as ae}from"./slider-n7bLkeqV.mjs";import{u as oe,b as re,d as le,c as ce}from"./index3.mjs";import{u as ue,P as de}from"./index4.mjs";import{p as me}from"./performance.mjs";import{E as pe}from"./analysis-CiLigBly.mjs";import"../nitro/nitro.mjs";import"node:process";import"cloudflare:workers";import"node:events";import"node:timers";import"node:stream";import"node:stream/web";import"node:async_hooks";function mapOpenAIChatLogProbsOutput(e){var t,s;return null!=(s=null==(t=null==e?void 0:e.content)?void 0:t.map(({token:e,logprob:t,top_logprobs:s})=>({token:e,logprob:t,topLogprobs:s?s.map(({token:e,logprob:t})=>({token:e,logprob:t})):[]})))?s:void 0}function mapOpenAIFinishReason(e){switch(e){case"stop":return"stop";case"length":return"length";case"content_filter":return"content-filter";case"function_call":case"tool_calls":return"tool-calls";default:return"unknown"}}var ge=D({error:D({message:F(),type:F().nullish(),param:L().nullish(),code:U([F(),z()]).nullish()})}),he=M({errorSchema:ge,errorToMessage:e=>e.error.message});function getResponseMetadata({id:e,model:t,created:s}){return{id:null!=e?e:void 0,modelId:null!=t?t:void 0,timestamp:null!=s?new Date(1e3*s):void 0}}var fe=class{constructor(e,t,s){this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=s}get supportsStructuredOutputs(){var e;return null!=(e=this.settings.structuredOutputs)?e:isReasoningModel(this.modelId)}get defaultObjectGenerationMode(){return this.modelId.startsWith("gpt-4o-audio-preview")?"tool":this.supportsStructuredOutputs?"json":"tool"}get provider(){return this.config.provider}get supportsImageUrls(){return!this.settings.downloadImages}getArgs({mode:e,prompt:t,maxTokens:s,temperature:n,topP:i,topK:a,frequencyPenalty:o,presencePenalty:r,stopSequences:l,responseFormat:c,seed:u,providerMetadata:d}){var m,p,g,h,f,y,v,w;const x=e.type,b=[];null!=a&&b.push({type:"unsupported-setting",setting:"topK"}),"json"!==(null==c?void 0:c.type)||null==c.schema||this.supportsStructuredOutputs||b.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format schema is only supported with structuredOutputs"});const R=this.settings.useLegacyFunctionCalling;if(R&&!0===this.settings.parallelToolCalls)throw new C({functionality:"useLegacyFunctionCalling with parallelToolCalls"});if(R&&this.supportsStructuredOutputs)throw new C({functionality:"structuredOutputs with useLegacyFunctionCalling"});const{messages:S,warnings:_}=function({prompt:e,useLegacyFunctionCalling:t=!1,systemMessageMode:s="system"}){const n=[],i=[];for(const{role:a,content:o}of e)switch(a){case"system":switch(s){case"system":n.push({role:"system",content:o});break;case"developer":n.push({role:"developer",content:o});break;case"remove":i.push({type:"other",message:"system messages are removed for this model"});break;default:throw new Error(`Unsupported system message mode: ${s}`)}break;case"user":if(1===o.length&&"text"===o[0].type){n.push({role:"user",content:o[0].text});break}n.push({role:"user",content:o.map((e,t)=>{var s,n,i,a;switch(e.type){case"text":return{type:"text",text:e.text};case"image":return{type:"image_url",image_url:{url:e.image instanceof URL?e.image.toString():`data:${null!=(s=e.mimeType)?s:"image/jpeg"};base64,${O(e.image)}`,detail:null==(i=null==(n=e.providerMetadata)?void 0:n.openai)?void 0:i.imageDetail}};case"file":if(e.data instanceof URL)throw new C({functionality:"'File content parts with URL data' functionality not supported."});switch(e.mimeType){case"audio/wav":return{type:"input_audio",input_audio:{data:e.data,format:"wav"}};case"audio/mp3":case"audio/mpeg":return{type:"input_audio",input_audio:{data:e.data,format:"mp3"}};case"application/pdf":return{type:"file",file:{filename:null!=(a=e.filename)?a:`part-${t}.pdf`,file_data:`data:application/pdf;base64,${e.data}`}};default:throw new C({functionality:`File content part type ${e.mimeType} in user messages`})}}})});break;case"assistant":{let e="";const s=[];for(const t of o)switch(t.type){case"text":e+=t.text;break;case"tool-call":s.push({id:t.toolCallId,type:"function",function:{name:t.toolName,arguments:JSON.stringify(t.args)}})}if(t){if(s.length>1)throw new C({functionality:"useLegacyFunctionCalling with multiple tool calls in one message"});n.push({role:"assistant",content:e,function_call:s.length>0?s[0].function:void 0})}else n.push({role:"assistant",content:e,tool_calls:s.length>0?s:void 0});break}case"tool":for(const e of o)t?n.push({role:"function",name:e.toolName,content:JSON.stringify(e.result)}):n.push({role:"tool",tool_call_id:e.toolCallId,content:JSON.stringify(e.result)});break;default:throw new Error(`Unsupported role: ${a}`)}return{messages:n,warnings:i}}({prompt:t,useLegacyFunctionCalling:R,systemMessageMode:getSystemMessageMode(this.modelId)});b.push(..._);const I={model:this.modelId,logit_bias:this.settings.logitBias,logprobs:!0===this.settings.logprobs||"number"==typeof this.settings.logprobs||void 0,top_logprobs:"number"==typeof this.settings.logprobs?this.settings.logprobs:"boolean"==typeof this.settings.logprobs&&this.settings.logprobs?0:void 0,user:this.settings.user,parallel_tool_calls:this.settings.parallelToolCalls,max_tokens:s,temperature:n,top_p:i,frequency_penalty:o,presence_penalty:r,response_format:"json"===(null==c?void 0:c.type)?this.supportsStructuredOutputs&&null!=c.schema?{type:"json_schema",json_schema:{schema:c.schema,strict:!0,name:null!=(m=c.name)?m:"response",description:c.description}}:{type:"json_object"}:void 0,stop:l,seed:u,max_completion_tokens:null==(p=null==d?void 0:d.openai)?void 0:p.maxCompletionTokens,store:null==(g=null==d?void 0:d.openai)?void 0:g.store,metadata:null==(h=null==d?void 0:d.openai)?void 0:h.metadata,prediction:null==(f=null==d?void 0:d.openai)?void 0:f.prediction,reasoning_effort:null!=(v=null==(y=null==d?void 0:d.openai)?void 0:y.reasoningEffort)?v:this.settings.reasoningEffort,messages:S};switch(isReasoningModel(this.modelId)?(null!=I.temperature&&(I.temperature=void 0,b.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for reasoning models"})),null!=I.top_p&&(I.top_p=void 0,b.push({type:"unsupported-setting",setting:"topP",details:"topP is not supported for reasoning models"})),null!=I.frequency_penalty&&(I.frequency_penalty=void 0,b.push({type:"unsupported-setting",setting:"frequencyPenalty",details:"frequencyPenalty is not supported for reasoning models"})),null!=I.presence_penalty&&(I.presence_penalty=void 0,b.push({type:"unsupported-setting",setting:"presencePenalty",details:"presencePenalty is not supported for reasoning models"})),null!=I.logit_bias&&(I.logit_bias=void 0,b.push({type:"other",message:"logitBias is not supported for reasoning models"})),null!=I.logprobs&&(I.logprobs=void 0,b.push({type:"other",message:"logprobs is not supported for reasoning models"})),null!=I.top_logprobs&&(I.top_logprobs=void 0,b.push({type:"other",message:"topLogprobs is not supported for reasoning models"})),null!=I.max_tokens&&(null==I.max_completion_tokens&&(I.max_completion_tokens=I.max_tokens),I.max_tokens=void 0)):(this.modelId.startsWith("gpt-4o-search-preview")||this.modelId.startsWith("gpt-4o-mini-search-preview"))&&null!=I.temperature&&(I.temperature=void 0,b.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for the search preview models and has been removed."})),x){case"regular":{const{tools:t,tool_choice:s,functions:n,function_call:i,toolWarnings:a}=function({mode:e,useLegacyFunctionCalling:t=!1,structuredOutputs:s}){var n;const i=(null==(n=e.tools)?void 0:n.length)?e.tools:void 0,a=[];if(null==i)return{tools:void 0,tool_choice:void 0,toolWarnings:a};const o=e.toolChoice;if(t){const e=[];for(const t of i)"provider-defined"===t.type?a.push({type:"unsupported-tool",tool:t}):e.push({name:t.name,description:t.description,parameters:t.parameters});if(null==o)return{functions:e,function_call:void 0,toolWarnings:a};switch(o.type){case"auto":case"none":case void 0:return{functions:e,function_call:void 0,toolWarnings:a};case"required":throw new C({functionality:"useLegacyFunctionCalling and toolChoice: required"});default:return{functions:e,function_call:{name:o.toolName},toolWarnings:a}}}const r=[];for(const e of i)"provider-defined"===e.type?a.push({type:"unsupported-tool",tool:e}):r.push({type:"function",function:{name:e.name,description:e.description,parameters:e.parameters,strict:!!s||void 0}});if(null==o)return{tools:r,tool_choice:void 0,toolWarnings:a};const l=o.type;switch(l){case"auto":case"none":case"required":return{tools:r,tool_choice:l,toolWarnings:a};case"tool":return{tools:r,tool_choice:{type:"function",function:{name:o.toolName}},toolWarnings:a};default:throw new C({functionality:`Unsupported tool choice type: ${l}`})}}({mode:e,useLegacyFunctionCalling:R,structuredOutputs:this.supportsStructuredOutputs});return{args:{...I,tools:t,tool_choice:s,functions:n,function_call:i},warnings:[...b,...a]}}case"object-json":return{args:{...I,response_format:this.supportsStructuredOutputs&&null!=e.schema?{type:"json_schema",json_schema:{schema:e.schema,strict:!0,name:null!=(w=e.name)?w:"response",description:e.description}}:{type:"json_object"}},warnings:b};case"object-tool":return{args:R?{...I,function_call:{name:e.tool.name},functions:[{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters}]}:{...I,tool_choice:{type:"function",function:{name:e.tool.name}},tools:[{type:"function",function:{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters,strict:!!this.supportsStructuredOutputs||void 0}}]},warnings:b};default:throw new Error(`Unsupported type: ${x}`)}}async doGenerate(e){var t,s,n,i,a,o,r,l;const{args:c,warnings:u}=this.getArgs(e),{responseHeaders:d,value:m,rawValue:p}=await T({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:j(this.config.headers(),e.headers),body:c,failedResponseHandler:he,successfulResponseHandler:k(ve),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:g,...h}=c,f=m.choices[0],y=null==(t=m.usage)?void 0:t.completion_tokens_details,v=null==(s=m.usage)?void 0:s.prompt_tokens_details,w={openai:{}};return null!=(null==y?void 0:y.reasoning_tokens)&&(w.openai.reasoningTokens=null==y?void 0:y.reasoning_tokens),null!=(null==y?void 0:y.accepted_prediction_tokens)&&(w.openai.acceptedPredictionTokens=null==y?void 0:y.accepted_prediction_tokens),null!=(null==y?void 0:y.rejected_prediction_tokens)&&(w.openai.rejectedPredictionTokens=null==y?void 0:y.rejected_prediction_tokens),null!=(null==v?void 0:v.cached_tokens)&&(w.openai.cachedPromptTokens=null==v?void 0:v.cached_tokens),{text:null!=(n=f.message.content)?n:void 0,toolCalls:this.settings.useLegacyFunctionCalling&&f.message.function_call?[{toolCallType:"function",toolCallId:E(),toolName:f.message.function_call.name,args:f.message.function_call.arguments}]:null==(i=f.message.tool_calls)?void 0:i.map(e=>{var t;return{toolCallType:"function",toolCallId:null!=(t=e.id)?t:E(),toolName:e.function.name,args:e.function.arguments}}),finishReason:mapOpenAIFinishReason(f.finish_reason),usage:{promptTokens:null!=(o=null==(a=m.usage)?void 0:a.prompt_tokens)?o:NaN,completionTokens:null!=(l=null==(r=m.usage)?void 0:r.completion_tokens)?l:NaN},rawCall:{rawPrompt:g,rawSettings:h},rawResponse:{headers:d,body:p},request:{body:JSON.stringify(c)},response:getResponseMetadata(m),warnings:u,logprobs:mapOpenAIChatLogProbsOutput(f.logprobs),providerMetadata:w}}async doStream(e){if(this.settings.simulateStreaming){const t=await this.doGenerate(e);return{stream:new ReadableStream({start(e){if(e.enqueue({type:"response-metadata",...t.response}),t.text&&e.enqueue({type:"text-delta",textDelta:t.text}),t.toolCalls)for(const s of t.toolCalls)e.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:s.toolCallId,toolName:s.toolName,argsTextDelta:s.args}),e.enqueue({type:"tool-call",...s});e.enqueue({type:"finish",finishReason:t.finishReason,usage:t.usage,logprobs:t.logprobs,providerMetadata:t.providerMetadata}),e.close()}}),rawCall:t.rawCall,rawResponse:t.rawResponse,warnings:t.warnings}}const{args:t,warnings:s}=this.getArgs(e),n={...t,stream:!0,stream_options:"strict"===this.config.compatibility?{include_usage:!0}:void 0},{responseHeaders:i,value:a}=await T({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:j(this.config.headers(),e.headers),body:n,failedResponseHandler:he,successfulResponseHandler:N(we),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:o,...r}=t,l=[];let c,u="unknown",d={promptTokens:void 0,completionTokens:void 0},m=!0;const{useLegacyFunctionCalling:p}=this.settings,g={openai:{}};return{stream:a.pipeThrough(new TransformStream({transform(e,t){var s,n,i,a,o,r,h,f,y,v,w,x;if(!e.success)return u="error",void t.enqueue({type:"error",error:e.error});const b=e.value;if("error"in b)return u="error",void t.enqueue({type:"error",error:b.error});if(m&&(m=!1,t.enqueue({type:"response-metadata",...getResponseMetadata(b)})),null!=b.usage){const{prompt_tokens:e,completion_tokens:t,prompt_tokens_details:s,completion_tokens_details:n}=b.usage;d={promptTokens:null!=e?e:void 0,completionTokens:null!=t?t:void 0},null!=(null==n?void 0:n.reasoning_tokens)&&(g.openai.reasoningTokens=null==n?void 0:n.reasoning_tokens),null!=(null==n?void 0:n.accepted_prediction_tokens)&&(g.openai.acceptedPredictionTokens=null==n?void 0:n.accepted_prediction_tokens),null!=(null==n?void 0:n.rejected_prediction_tokens)&&(g.openai.rejectedPredictionTokens=null==n?void 0:n.rejected_prediction_tokens),null!=(null==s?void 0:s.cached_tokens)&&(g.openai.cachedPromptTokens=null==s?void 0:s.cached_tokens)}const R=b.choices[0];if(null!=(null==R?void 0:R.finish_reason)&&(u=mapOpenAIFinishReason(R.finish_reason)),null==(null==R?void 0:R.delta))return;const S=R.delta;null!=S.content&&t.enqueue({type:"text-delta",textDelta:S.content});const _=mapOpenAIChatLogProbsOutput(null==R?void 0:R.logprobs);(null==_?void 0:_.length)&&(void 0===c&&(c=[]),c.push(..._));const I=p&&null!=S.function_call?[{type:"function",id:E(),function:S.function_call,index:0}]:S.tool_calls;if(null!=I)for(const e of I){const c=e.index;if(null==l[c]){if("function"!==e.type)throw new A({data:e,message:"Expected 'function' type."});if(null==e.id)throw new A({data:e,message:"Expected 'id' to be a string."});if(null==(null==(s=e.function)?void 0:s.name))throw new A({data:e,message:"Expected 'function.name' to be a string."});l[c]={id:e.id,type:"function",function:{name:e.function.name,arguments:null!=(n=e.function.arguments)?n:""},hasFinished:!1};const r=l[c];null!=(null==(i=r.function)?void 0:i.name)&&null!=(null==(a=r.function)?void 0:a.arguments)&&(r.function.arguments.length>0&&t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:r.id,toolName:r.function.name,argsTextDelta:r.function.arguments}),q(r.function.arguments)&&(t.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(o=r.id)?o:E(),toolName:r.function.name,args:r.function.arguments}),r.hasFinished=!0));continue}const u=l[c];u.hasFinished||(null!=(null==(r=e.function)?void 0:r.arguments)&&(u.function.arguments+=null!=(f=null==(h=e.function)?void 0:h.arguments)?f:""),t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:u.id,toolName:u.function.name,argsTextDelta:null!=(y=e.function.arguments)?y:""}),null!=(null==(v=u.function)?void 0:v.name)&&null!=(null==(w=u.function)?void 0:w.arguments)&&q(u.function.arguments)&&(t.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(x=u.id)?x:E(),toolName:u.function.name,args:u.function.arguments}),u.hasFinished=!0))}},flush(e){var t,s;e.enqueue({type:"finish",finishReason:u,logprobs:c,usage:{promptTokens:null!=(t=d.promptTokens)?t:NaN,completionTokens:null!=(s=d.completionTokens)?s:NaN},...null!=g?{providerMetadata:g}:{}})}})),rawCall:{rawPrompt:o,rawSettings:r},rawResponse:{headers:i},request:{body:JSON.stringify(n)},warnings:s}}},ye=D({prompt_tokens:z().nullish(),completion_tokens:z().nullish(),prompt_tokens_details:D({cached_tokens:z().nullish()}).nullish(),completion_tokens_details:D({reasoning_tokens:z().nullish(),accepted_prediction_tokens:z().nullish(),rejected_prediction_tokens:z().nullish()}).nullish()}).nullish(),ve=D({id:F().nullish(),created:z().nullish(),model:F().nullish(),choices:H(D({message:D({role:$("assistant").nullish(),content:F().nullish(),function_call:D({arguments:F(),name:F()}).nullish(),tool_calls:H(D({id:F().nullish(),type:$("function"),function:D({name:F(),arguments:F()})})).nullish()}),index:z(),logprobs:D({content:H(D({token:F(),logprob:z(),top_logprobs:H(D({token:F(),logprob:z()}))})).nullable()}).nullish(),finish_reason:F().nullish()})),usage:ye}),we=U([D({id:F().nullish(),created:z().nullish(),model:F().nullish(),choices:H(D({delta:D({role:B(["assistant"]).nullish(),content:F().nullish(),function_call:D({name:F().optional(),arguments:F().optional()}).nullish(),tool_calls:H(D({index:z(),id:F().nullish(),type:$("function").nullish(),function:D({name:F().nullish(),arguments:F().nullish()})})).nullish()}).nullish(),logprobs:D({content:H(D({token:F(),logprob:z(),top_logprobs:H(D({token:F(),logprob:z()}))})).nullable()}).nullish(),finish_reason:F().nullish(),index:z()})),usage:ye}),ge]);function isReasoningModel(e){return e.startsWith("o")}function getSystemMessageMode(e){var t,s;return isReasoningModel(e)?null!=(s=null==(t=xe[e])?void 0:t.systemMessageMode)?s:"developer":"system"}var xe={"o1-mini":{systemMessageMode:"remove"},"o1-mini-2024-09-12":{systemMessageMode:"remove"},"o1-preview":{systemMessageMode:"remove"},"o1-preview-2024-09-12":{systemMessageMode:"remove"},o3:{systemMessageMode:"developer"},"o3-2025-04-16":{systemMessageMode:"developer"},"o3-mini":{systemMessageMode:"developer"},"o3-mini-2025-01-31":{systemMessageMode:"developer"},"o4-mini":{systemMessageMode:"developer"},"o4-mini-2025-04-16":{systemMessageMode:"developer"}};function mapOpenAICompletionLogProbs(e){return null==e?void 0:e.tokens.map((t,s)=>({token:t,logprob:e.token_logprobs[s],topLogprobs:e.top_logprobs?Object.entries(e.top_logprobs[s]).map(([e,t])=>({token:e,logprob:t})):[]}))}var be=class{constructor(e,t,s){this.specificationVersion="v1",this.defaultObjectGenerationMode=void 0,this.modelId=e,this.settings=t,this.config=s}get provider(){return this.config.provider}getArgs({mode:e,inputFormat:t,prompt:s,maxTokens:n,temperature:i,topP:a,topK:o,frequencyPenalty:r,presencePenalty:l,stopSequences:c,responseFormat:u,seed:d}){var m;const p=e.type,g=[];null!=o&&g.push({type:"unsupported-setting",setting:"topK"}),null!=u&&"text"!==u.type&&g.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format is not supported."});const{prompt:h,stopSequences:f}=function({prompt:e,inputFormat:t,user:s="user",assistant:n="assistant"}){if("prompt"===t&&1===e.length&&"user"===e[0].role&&1===e[0].content.length&&"text"===e[0].content[0].type)return{prompt:e[0].content[0].text};let i="";"system"===e[0].role&&(i+=`${e[0].content}\n\n`,e=e.slice(1));for(const{role:t,content:a}of e)switch(t){case"system":throw new P({message:"Unexpected system message in prompt: ${content}",prompt:e});case"user":i+=`${s}:\n${a.map(e=>{switch(e.type){case"text":return e.text;case"image":throw new C({functionality:"images"})}}).join("")}\n\n`;break;case"assistant":i+=`${n}:\n${a.map(e=>{switch(e.type){case"text":return e.text;case"tool-call":throw new C({functionality:"tool-call messages"})}}).join("")}\n\n`;break;case"tool":throw new C({functionality:"tool messages"});default:throw new Error(`Unsupported role: ${t}`)}return i+=`${n}:\n`,{prompt:i,stopSequences:[`\n${s}:`]}}({prompt:s,inputFormat:t}),y=[...null!=f?f:[],...null!=c?c:[]],v={model:this.modelId,echo:this.settings.echo,logit_bias:this.settings.logitBias,logprobs:"number"==typeof this.settings.logprobs?this.settings.logprobs:"boolean"==typeof this.settings.logprobs&&this.settings.logprobs?0:void 0,suffix:this.settings.suffix,user:this.settings.user,max_tokens:n,temperature:i,top_p:a,frequency_penalty:r,presence_penalty:l,seed:d,prompt:h,stop:y.length>0?y:void 0};switch(p){case"regular":if(null==(m=e.tools)?void 0:m.length)throw new C({functionality:"tools"});if(e.toolChoice)throw new C({functionality:"toolChoice"});return{args:v,warnings:g};case"object-json":throw new C({functionality:"object-json mode"});case"object-tool":throw new C({functionality:"object-tool mode"});default:throw new Error(`Unsupported type: ${p}`)}}async doGenerate(e){const{args:t,warnings:s}=this.getArgs(e),{responseHeaders:n,value:i,rawValue:a}=await T({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:j(this.config.headers(),e.headers),body:t,failedResponseHandler:he,successfulResponseHandler:k(Re),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:o,...r}=t,l=i.choices[0];return{text:l.text,usage:{promptTokens:i.usage.prompt_tokens,completionTokens:i.usage.completion_tokens},finishReason:mapOpenAIFinishReason(l.finish_reason),logprobs:mapOpenAICompletionLogProbs(l.logprobs),rawCall:{rawPrompt:o,rawSettings:r},rawResponse:{headers:n,body:a},response:getResponseMetadata(i),warnings:s,request:{body:JSON.stringify(t)}}}async doStream(e){const{args:t,warnings:s}=this.getArgs(e),n={...t,stream:!0,stream_options:"strict"===this.config.compatibility?{include_usage:!0}:void 0},{responseHeaders:i,value:a}=await T({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:j(this.config.headers(),e.headers),body:n,failedResponseHandler:he,successfulResponseHandler:N(Se),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:o,...r}=t;let l,c="unknown",u={promptTokens:Number.NaN,completionTokens:Number.NaN},d=!0;return{stream:a.pipeThrough(new TransformStream({transform(e,t){if(!e.success)return c="error",void t.enqueue({type:"error",error:e.error});const s=e.value;if("error"in s)return c="error",void t.enqueue({type:"error",error:s.error});d&&(d=!1,t.enqueue({type:"response-metadata",...getResponseMetadata(s)})),null!=s.usage&&(u={promptTokens:s.usage.prompt_tokens,completionTokens:s.usage.completion_tokens});const n=s.choices[0];null!=(null==n?void 0:n.finish_reason)&&(c=mapOpenAIFinishReason(n.finish_reason)),null!=(null==n?void 0:n.text)&&t.enqueue({type:"text-delta",textDelta:n.text});const i=mapOpenAICompletionLogProbs(null==n?void 0:n.logprobs);(null==i?void 0:i.length)&&(void 0===l&&(l=[]),l.push(...i))},flush(e){e.enqueue({type:"finish",finishReason:c,logprobs:l,usage:u})}})),rawCall:{rawPrompt:o,rawSettings:r},rawResponse:{headers:i},warnings:s,request:{body:JSON.stringify(n)}}}},Re=D({id:F().nullish(),created:z().nullish(),model:F().nullish(),choices:H(D({text:F(),finish_reason:F(),logprobs:D({tokens:H(F()),token_logprobs:H(z()),top_logprobs:H(G(F(),z())).nullable()}).nullish()})),usage:D({prompt_tokens:z(),completion_tokens:z()})}),Se=U([D({id:F().nullish(),created:z().nullish(),model:F().nullish(),choices:H(D({text:F(),finish_reason:F().nullish(),index:z(),logprobs:D({tokens:H(F()),token_logprobs:H(z()),top_logprobs:H(G(F(),z())).nullable()}).nullish()})),usage:D({prompt_tokens:z(),completion_tokens:z()}).nullish()}),ge]),_e=class{constructor(e,t,s){this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=s}get provider(){return this.config.provider}get maxEmbeddingsPerCall(){var e;return null!=(e=this.settings.maxEmbeddingsPerCall)?e:2048}get supportsParallelCalls(){var e;return null==(e=this.settings.supportsParallelCalls)||e}async doEmbed({values:e,headers:t,abortSignal:s}){if(e.length>this.maxEmbeddingsPerCall)throw new J({provider:this.provider,modelId:this.modelId,maxEmbeddingsPerCall:this.maxEmbeddingsPerCall,values:e});const{responseHeaders:n,value:i}=await T({url:this.config.url({path:"/embeddings",modelId:this.modelId}),headers:j(this.config.headers(),t),body:{model:this.modelId,input:e,encoding_format:"float",dimensions:this.settings.dimensions,user:this.settings.user},failedResponseHandler:he,successfulResponseHandler:k(Ie),abortSignal:s,fetch:this.config.fetch});return{embeddings:i.data.map(e=>e.embedding),usage:i.usage?{tokens:i.usage.prompt_tokens}:void 0,rawResponse:{headers:n}}}},Ie=D({data:H(D({embedding:H(z())})),usage:D({prompt_tokens:z()}).nullish()}),Ce={"dall-e-3":1,"dall-e-2":10,"gpt-image-1":10},Te=new Set(["gpt-image-1"]),je=class{constructor(e,t,s){this.modelId=e,this.settings=t,this.config=s,this.specificationVersion="v1"}get maxImagesPerCall(){var e,t;return null!=(t=null!=(e=this.settings.maxImagesPerCall)?e:Ce[this.modelId])?t:1}get provider(){return this.config.provider}async doGenerate({prompt:e,n:t,size:s,aspectRatio:n,seed:i,providerOptions:a,headers:o,abortSignal:r}){var l,c,u,d;const m=[];null!=n&&m.push({type:"unsupported-setting",setting:"aspectRatio",details:"This model does not support aspect ratio. Use `size` instead."}),null!=i&&m.push({type:"unsupported-setting",setting:"seed"});const p=null!=(u=null==(c=null==(l=this.config._internal)?void 0:l.currentDate)?void 0:c.call(l))?u:new Date,{value:g,responseHeaders:h}=await T({url:this.config.url({path:"/images/generations",modelId:this.modelId}),headers:j(this.config.headers(),o),body:{model:this.modelId,prompt:e,n:t,size:s,...null!=(d=a.openai)?d:{},...Te.has(this.modelId)?{}:{response_format:"b64_json"}},failedResponseHandler:he,successfulResponseHandler:k(ke),abortSignal:r,fetch:this.config.fetch});return{images:g.data.map(e=>e.b64_json),warnings:m,response:{timestamp:p,modelId:this.modelId,headers:h}}}},ke=D({data:H(D({b64_json:F()}))}),Ne=D({include:H(F()).nullish(),language:F().nullish(),prompt:F().nullish(),temperature:z().min(0).max(1).nullish().default(0),timestampGranularities:H(B(["word","segment"])).nullish().default(["segment"])}),Ee={afrikaans:"af",arabic:"ar",armenian:"hy",azerbaijani:"az",belarusian:"be",bosnian:"bs",bulgarian:"bg",catalan:"ca",chinese:"zh",croatian:"hr",czech:"cs",danish:"da",dutch:"nl",english:"en",estonian:"et",finnish:"fi",french:"fr",galician:"gl",german:"de",greek:"el",hebrew:"he",hindi:"hi",hungarian:"hu",icelandic:"is",indonesian:"id",italian:"it",japanese:"ja",kannada:"kn",kazakh:"kk",korean:"ko",latvian:"lv",lithuanian:"lt",macedonian:"mk",malay:"ms",marathi:"mr",maori:"mi",nepali:"ne",norwegian:"no",persian:"fa",polish:"pl",portuguese:"pt",romanian:"ro",russian:"ru",serbian:"sr",slovak:"sk",slovenian:"sl",spanish:"es",swahili:"sw",swedish:"sv",tagalog:"tl",tamil:"ta",thai:"th",turkish:"tr",ukrainian:"uk",urdu:"ur",vietnamese:"vi",welsh:"cy"},Ae=class{constructor(e,t){this.modelId=e,this.config=t,this.specificationVersion="v1"}get provider(){return this.config.provider}getArgs({audio:e,mediaType:t,providerOptions:s}){var n,i,a,o,r;const l=V({provider:"openai",providerOptions:s,schema:Ne}),c=new FormData,u=e instanceof Uint8Array?new Blob([e]):new Blob([K(e)]);if(c.append("model",this.modelId),c.append("file",new File([u],"audio",{type:t})),l){const e={include:null!=(n=l.include)?n:void 0,language:null!=(i=l.language)?i:void 0,prompt:null!=(a=l.prompt)?a:void 0,temperature:null!=(o=l.temperature)?o:void 0,timestamp_granularities:null!=(r=l.timestampGranularities)?r:void 0};for(const t in e){const s=e[t];void 0!==s&&c.append(t,String(s))}}return{formData:c,warnings:[]}}async doGenerate(e){var t,s,n,i,a,o;const r=null!=(n=null==(s=null==(t=this.config._internal)?void 0:t.currentDate)?void 0:s.call(t))?n:new Date,{formData:l,warnings:c}=this.getArgs(e),{value:u,responseHeaders:d,rawValue:m}=await Y({url:this.config.url({path:"/audio/transcriptions",modelId:this.modelId}),headers:j(this.config.headers(),e.headers),formData:l,failedResponseHandler:he,successfulResponseHandler:k(qe),abortSignal:e.abortSignal,fetch:this.config.fetch}),p=null!=u.language&&u.language in Ee?Ee[u.language]:void 0;return{text:u.text,segments:null!=(a=null==(i=u.words)?void 0:i.map(e=>({text:e.word,startSecond:e.start,endSecond:e.end})))?a:[],language:p,durationInSeconds:null!=(o=u.duration)?o:void 0,warnings:c,response:{timestamp:r,modelId:this.modelId,headers:d,body:m}}}},qe=D({text:F(),language:F().nullish(),duration:z().nullish(),words:H(D({word:F(),start:z(),end:z()})).nullish()});function mapOpenAIResponseFinishReason({finishReason:e,hasToolCalls:t}){switch(e){case void 0:case null:return t?"tool-calls":"stop";case"max_output_tokens":return"length";case"content_filter":return"content-filter";default:return t?"tool-calls":"unknown"}}var Pe=class{constructor(e,t){this.specificationVersion="v1",this.defaultObjectGenerationMode="json",this.supportsStructuredOutputs=!0,this.modelId=e,this.config=t}get provider(){return this.config.provider}getArgs({mode:e,maxTokens:t,temperature:s,stopSequences:n,topP:i,topK:a,presencePenalty:o,frequencyPenalty:r,seed:l,prompt:c,providerMetadata:u,responseFormat:d}){var m,p,g;const h=[],f=function(e){if(e.startsWith("o"))return e.startsWith("o1-mini")||e.startsWith("o1-preview")?{isReasoningModel:!0,systemMessageMode:"remove",requiredAutoTruncation:!1}:{isReasoningModel:!0,systemMessageMode:"developer",requiredAutoTruncation:!1};return{isReasoningModel:!1,systemMessageMode:"system",requiredAutoTruncation:!1}}(this.modelId),y=e.type;null!=a&&h.push({type:"unsupported-setting",setting:"topK"}),null!=l&&h.push({type:"unsupported-setting",setting:"seed"}),null!=o&&h.push({type:"unsupported-setting",setting:"presencePenalty"}),null!=r&&h.push({type:"unsupported-setting",setting:"frequencyPenalty"}),null!=n&&h.push({type:"unsupported-setting",setting:"stopSequences"});const{messages:v,warnings:w}=function({prompt:e,systemMessageMode:t}){const s=[],n=[];for(const{role:i,content:a}of e)switch(i){case"system":switch(t){case"system":s.push({role:"system",content:a});break;case"developer":s.push({role:"developer",content:a});break;case"remove":n.push({type:"other",message:"system messages are removed for this model"});break;default:throw new Error(`Unsupported system message mode: ${t}`)}break;case"user":s.push({role:"user",content:a.map((e,t)=>{var s,n,i,a;switch(e.type){case"text":return{type:"input_text",text:e.text};case"image":return{type:"input_image",image_url:e.image instanceof URL?e.image.toString():`data:${null!=(s=e.mimeType)?s:"image/jpeg"};base64,${O(e.image)}`,detail:null==(i=null==(n=e.providerMetadata)?void 0:n.openai)?void 0:i.imageDetail};case"file":if(e.data instanceof URL)throw new C({functionality:"File URLs in user messages"});if("application/pdf"===e.mimeType)return{type:"input_file",filename:null!=(a=e.filename)?a:`part-${t}.pdf`,file_data:`data:application/pdf;base64,${e.data}`};throw new C({functionality:"Only PDF files are supported in user messages"})}})});break;case"assistant":for(const e of a)switch(e.type){case"text":s.push({role:"assistant",content:[{type:"output_text",text:e.text}]});break;case"tool-call":s.push({type:"function_call",call_id:e.toolCallId,name:e.toolName,arguments:JSON.stringify(e.args)})}break;case"tool":for(const e of a)s.push({type:"function_call_output",call_id:e.toolCallId,output:JSON.stringify(e.result)});break;default:throw new Error(`Unsupported role: ${i}`)}return{messages:s,warnings:n}}({prompt:c,systemMessageMode:f.systemMessageMode});h.push(...w);const x=V({provider:"openai",providerOptions:u,schema:De}),b=null==(m=null==x?void 0:x.strictSchemas)||m,R={model:this.modelId,input:v,temperature:s,top_p:i,max_output_tokens:t,..."json"===(null==d?void 0:d.type)&&{text:{format:null!=d.schema?{type:"json_schema",strict:b,name:null!=(p=d.name)?p:"response",description:d.description,schema:d.schema}:{type:"json_object"}}},metadata:null==x?void 0:x.metadata,parallel_tool_calls:null==x?void 0:x.parallelToolCalls,previous_response_id:null==x?void 0:x.previousResponseId,store:null==x?void 0:x.store,user:null==x?void 0:x.user,instructions:null==x?void 0:x.instructions,...f.isReasoningModel&&(null!=(null==x?void 0:x.reasoningEffort)||null!=(null==x?void 0:x.reasoningSummary))&&{reasoning:{...null!=(null==x?void 0:x.reasoningEffort)&&{effort:x.reasoningEffort},...null!=(null==x?void 0:x.reasoningSummary)&&{summary:x.reasoningSummary}}},...f.requiredAutoTruncation&&{truncation:"auto"}};switch(f.isReasoningModel&&(null!=R.temperature&&(R.temperature=void 0,h.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for reasoning models"})),null!=R.top_p&&(R.top_p=void 0,h.push({type:"unsupported-setting",setting:"topP",details:"topP is not supported for reasoning models"}))),y){case"regular":{const{tools:t,tool_choice:s,toolWarnings:n}=function({mode:e,strict:t}){var s;const n=(null==(s=e.tools)?void 0:s.length)?e.tools:void 0,i=[];if(null==n)return{tools:void 0,tool_choice:void 0,toolWarnings:i};const a=e.toolChoice,o=[];for(const e of n)switch(e.type){case"function":o.push({type:"function",name:e.name,description:e.description,parameters:e.parameters,strict:!!t||void 0});break;case"provider-defined":"openai.web_search_preview"===e.id?o.push({type:"web_search_preview",search_context_size:e.args.searchContextSize,user_location:e.args.userLocation}):i.push({type:"unsupported-tool",tool:e});break;default:i.push({type:"unsupported-tool",tool:e})}if(null==a)return{tools:o,tool_choice:void 0,toolWarnings:i};const r=a.type;switch(r){case"auto":case"none":case"required":return{tools:o,tool_choice:r,toolWarnings:i};case"tool":return"web_search_preview"===a.toolName?{tools:o,tool_choice:{type:"web_search_preview"},toolWarnings:i}:{tools:o,tool_choice:{type:"function",name:a.toolName},toolWarnings:i};default:throw new C({functionality:`Unsupported tool choice type: ${r}`})}}({mode:e,strict:b});return{args:{...R,tools:t,tool_choice:s},warnings:[...h,...n]}}case"object-json":return{args:{...R,text:{format:null!=e.schema?{type:"json_schema",strict:b,name:null!=(g=e.name)?g:"response",description:e.description,schema:e.schema}:{type:"json_object"}}},warnings:h};case"object-tool":return{args:{...R,tool_choice:{type:"function",name:e.tool.name},tools:[{type:"function",name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters,strict:b}]},warnings:h};default:throw new Error(`Unsupported type: ${y}`)}}async doGenerate(e){var t,s,n,i,a,o,r;const{args:l,warnings:c}=this.getArgs(e),{responseHeaders:u,value:d,rawValue:m}=await T({url:this.config.url({path:"/responses",modelId:this.modelId}),headers:j(this.config.headers(),e.headers),body:l,failedResponseHandler:he,successfulResponseHandler:k(D({id:F(),created_at:z(),model:F(),output:H(W("type",[D({type:$("message"),role:$("assistant"),content:H(D({type:$("output_text"),text:F(),annotations:H(D({type:$("url_citation"),start_index:z(),end_index:z(),url:F(),title:F()}))}))}),D({type:$("function_call"),call_id:F(),name:F(),arguments:F()}),D({type:$("web_search_call")}),D({type:$("computer_call")}),D({type:$("reasoning"),summary:H(D({type:$("summary_text"),text:F()}))})])),incomplete_details:D({reason:F()}).nullable(),usage:Me})),abortSignal:e.abortSignal,fetch:this.config.fetch}),p=d.output.filter(e=>"message"===e.type).flatMap(e=>e.content).filter(e=>"output_text"===e.type),g=d.output.filter(e=>"function_call"===e.type).map(e=>({toolCallType:"function",toolCallId:e.call_id,toolName:e.name,args:e.arguments})),h=null!=(s=null==(t=d.output.find(e=>"reasoning"===e.type))?void 0:t.summary)?s:null;return{text:p.map(e=>e.text).join("\n"),sources:p.flatMap(e=>e.annotations.map(e=>{var t,s,n;return{sourceType:"url",id:null!=(n=null==(s=(t=this.config).generateId)?void 0:s.call(t))?n:E(),url:e.url,title:e.title}})),finishReason:mapOpenAIResponseFinishReason({finishReason:null==(n=d.incomplete_details)?void 0:n.reason,hasToolCalls:g.length>0}),toolCalls:g.length>0?g:void 0,reasoning:h?h.map(e=>({type:"text",text:e.text})):void 0,usage:{promptTokens:d.usage.input_tokens,completionTokens:d.usage.output_tokens},rawCall:{rawPrompt:void 0,rawSettings:{}},rawResponse:{headers:u,body:m},request:{body:JSON.stringify(l)},response:{id:d.id,timestamp:new Date(1e3*d.created_at),modelId:d.model},providerMetadata:{openai:{responseId:d.id,cachedPromptTokens:null!=(a=null==(i=d.usage.input_tokens_details)?void 0:i.cached_tokens)?a:null,reasoningTokens:null!=(r=null==(o=d.usage.output_tokens_details)?void 0:o.reasoning_tokens)?r:null}},warnings:c}}async doStream(e){const{args:t,warnings:s}=this.getArgs(e),{responseHeaders:n,value:i}=await T({url:this.config.url({path:"/responses",modelId:this.modelId}),headers:j(this.config.headers(),e.headers),body:{...t,stream:!0},failedResponseHandler:he,successfulResponseHandler:N(Oe),abortSignal:e.abortSignal,fetch:this.config.fetch}),a=this;let o="unknown",r=NaN,l=NaN,c=null,u=null,d=null;const m={};let p=!1;return{stream:i.pipeThrough(new TransformStream({transform(e,t){var s,n,i,g,h,f,y,v;if(!e.success)return o="error",void t.enqueue({type:"error",error:e.error});const w=e.value;if(function(e){return"response.output_item.added"===e.type}(w))"function_call"===w.item.type&&(m[w.output_index]={toolName:w.item.name,toolCallId:w.item.call_id},t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:w.item.call_id,toolName:w.item.name,argsTextDelta:w.item.arguments}));else if(function(e){return"response.function_call_arguments.delta"===e.type}(w)){const e=m[w.output_index];null!=e&&t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:e.toolCallId,toolName:e.toolName,argsTextDelta:w.delta})}else!function(e){return"response.created"===e.type}(w)?!function(e){return"response.output_text.delta"===e.type}(w)?!function(e){return"response.reasoning_summary_text.delta"===e.type}(w)?!function(e){return"response.output_item.done"===e.type}(w)||"function_call"!==w.item.type?!function(e){return"response.completed"===e.type||"response.incomplete"===e.type}(w)?function(e){return"response.output_text.annotation.added"===e.type}(w)&&t.enqueue({type:"source",source:{sourceType:"url",id:null!=(v=null==(y=(f=a.config).generateId)?void 0:y.call(f))?v:E(),url:w.annotation.url,title:w.annotation.title}}):(o=mapOpenAIResponseFinishReason({finishReason:null==(s=w.response.incomplete_details)?void 0:s.reason,hasToolCalls:p}),r=w.response.usage.input_tokens,l=w.response.usage.output_tokens,c=null!=(i=null==(n=w.response.usage.input_tokens_details)?void 0:n.cached_tokens)?i:c,u=null!=(h=null==(g=w.response.usage.output_tokens_details)?void 0:g.reasoning_tokens)?h:u):(m[w.output_index]=void 0,p=!0,t.enqueue({type:"tool-call",toolCallType:"function",toolCallId:w.item.call_id,toolName:w.item.name,args:w.item.arguments})):t.enqueue({type:"reasoning",textDelta:w.delta}):t.enqueue({type:"text-delta",textDelta:w.delta}):(d=w.response.id,t.enqueue({type:"response-metadata",id:w.response.id,timestamp:new Date(1e3*w.response.created_at),modelId:w.response.model}))},flush(e){e.enqueue({type:"finish",finishReason:o,usage:{promptTokens:r,completionTokens:l},...(null!=c||null!=u)&&{providerMetadata:{openai:{responseId:d,cachedPromptTokens:c,reasoningTokens:u}}}})}})),rawCall:{rawPrompt:void 0,rawSettings:{}},rawResponse:{headers:n},request:{body:JSON.stringify(t)},warnings:s}}},Me=D({input_tokens:z(),input_tokens_details:D({cached_tokens:z().nullish()}).nullish(),output_tokens:z(),output_tokens_details:D({reasoning_tokens:z().nullish()}).nullish()}),Oe=U([D({type:$("response.output_text.delta"),delta:F()}),D({type:B(["response.completed","response.incomplete"]),response:D({incomplete_details:D({reason:F()}).nullish(),usage:Me})}),D({type:$("response.created"),response:D({id:F(),created_at:z(),model:F()})}),D({type:$("response.output_item.done"),output_index:z(),item:W("type",[D({type:$("message")}),D({type:$("function_call"),id:F(),call_id:F(),name:F(),arguments:F(),status:$("completed")})])}),D({type:$("response.function_call_arguments.delta"),item_id:F(),output_index:z(),delta:F()}),D({type:$("response.output_item.added"),output_index:z(),item:W("type",[D({type:$("message")}),D({type:$("function_call"),id:F(),call_id:F(),name:F(),arguments:F()})])}),D({type:$("response.output_text.annotation.added"),annotation:D({type:$("url_citation"),url:F(),title:F()})}),D({type:$("response.reasoning_summary_text.delta"),item_id:F(),output_index:z(),summary_index:z(),delta:F()}),D({type:F()}).passthrough()]);var De=D({metadata:L().nullish(),parallelToolCalls:Q().nullish(),previousResponseId:F().nullish(),store:Q().nullish(),user:F().nullish(),reasoningEffort:F().nullish(),strictSchemas:Q().nullish(),instructions:F().nullish(),reasoningSummary:F().nullish()}),Ue=D({});var Le={webSearchPreview:function({searchContextSize:e,userLocation:t}={}){return{type:"provider-defined",id:"openai.web_search_preview",args:{searchContextSize:e,userLocation:t},parameters:Ue}}},Fe=D({instructions:F().nullish(),speed:z().min(.25).max(4).default(1).nullish()}),ze=class{constructor(e,t){this.modelId=e,this.config=t,this.specificationVersion="v1"}get provider(){return this.config.provider}getArgs({text:e,voice:t="alloy",outputFormat:s="mp3",speed:n,instructions:i,providerOptions:a}){const o=[],r=V({provider:"openai",providerOptions:a,schema:Fe}),l={model:this.modelId,input:e,voice:t,response_format:"mp3",speed:n,instructions:i};if(s&&(["mp3","opus","aac","flac","wav","pcm"].includes(s)?l.response_format=s:o.push({type:"unsupported-setting",setting:"outputFormat",details:`Unsupported output format: ${s}. Using mp3 instead.`})),r){const e={};for(const t in e){const s=e[t];void 0!==s&&(l[t]=s)}}return{requestBody:l,warnings:o}}async doGenerate(e){var t,s,n;const i=null!=(n=null==(s=null==(t=this.config._internal)?void 0:t.currentDate)?void 0:s.call(t))?n:new Date,{requestBody:a,warnings:o}=this.getArgs(e),{value:r,responseHeaders:l,rawValue:c}=await T({url:this.config.url({path:"/audio/speech",modelId:this.modelId}),headers:j(this.config.headers(),e.headers),body:a,failedResponseHandler:he,successfulResponseHandler:Z(),abortSignal:e.abortSignal,fetch:this.config.fetch});return{audio:r,warnings:o,request:{body:JSON.stringify(a)},response:{timestamp:i,modelId:this.modelId,headers:l,body:c}}}};var He=function(e={}){var t,s,n;const i=null!=(t=_(e.baseURL))?t:"https://api.openai.com/v1",a=null!=(s=e.compatibility)?s:"compatible",o=null!=(n=e.name)?n:"openai",getHeaders=()=>({Authorization:`Bearer ${I({apiKey:e.apiKey,environmentVariableName:"OPENAI_API_KEY",description:"OpenAI"})}`,"OpenAI-Organization":e.organization,"OpenAI-Project":e.project,...e.headers}),createChatModel=(t,s={})=>new fe(t,s,{provider:`${o}.chat`,url:({path:e})=>`${i}${e}`,headers:getHeaders,compatibility:a,fetch:e.fetch}),createCompletionModel=(t,s={})=>new be(t,s,{provider:`${o}.completion`,url:({path:e})=>`${i}${e}`,headers:getHeaders,compatibility:a,fetch:e.fetch}),createEmbeddingModel=(t,s={})=>new _e(t,s,{provider:`${o}.embedding`,url:({path:e})=>`${i}${e}`,headers:getHeaders,fetch:e.fetch}),createImageModel=(t,s={})=>new je(t,s,{provider:`${o}.image`,url:({path:e})=>`${i}${e}`,headers:getHeaders,fetch:e.fetch}),createTranscriptionModel=t=>new Ae(t,{provider:`${o}.transcription`,url:({path:e})=>`${i}${e}`,headers:getHeaders,fetch:e.fetch}),createSpeechModel=t=>new ze(t,{provider:`${o}.speech`,url:({path:e})=>`${i}${e}`,headers:getHeaders,fetch:e.fetch}),createLanguageModel=(e,t)=>{if(new.target)throw new Error("The OpenAI model function cannot be called with the new keyword.");return"gpt-3.5-turbo-instruct"===e?createCompletionModel(e,t):createChatModel(e,t)},provider=function(e,t){return createLanguageModel(e,t)};return provider.languageModel=createLanguageModel,provider.chat=createChatModel,provider.completion=createCompletionModel,provider.responses=t=>new Pe(t,{provider:`${o}.responses`,url:({path:e})=>`${i}${e}`,headers:getHeaders,fetch:e.fetch}),provider.embedding=createEmbeddingModel,provider.textEmbedding=createEmbeddingModel,provider.textEmbeddingModel=createEmbeddingModel,provider.image=createImageModel,provider.imageModel=createImageModel,provider.transcription=createTranscriptionModel,provider.transcriptionModel=createTranscriptionModel,provider.speech=createSpeechModel,provider.speechModel=createSpeechModel,provider.tools=Le,provider}({compatibility:"strict"}),$e=Object.defineProperty,__publicField$1=(e,t,s)=>((e,t,s)=>t in e?$e(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s)(e,"symbol"!=typeof t?t+"":t,s);const Be=new class{constructor(e){__publicField$1(this,"config"),this.config={maxFileSize:20971520,maxDimensions:{width:4096,height:4096},allowedMimeTypes:["image/jpeg","image/png","image/webp"],maxFilenameLength:255,enableContentScanning:!0,enableMetadataRemoval:!0,...e}}async validateFile(e){const t=[],s=[];e.size>this.config.maxFileSize&&t.push(`File size exceeds maximum limit of ${this.config.maxFileSize/1048576}MB`),this.config.allowedMimeTypes.includes(e.type)||t.push(`File type ${e.type} is not supported. Allowed types: ${this.config.allowedMimeTypes.join(", ")}`);const n=this.sanitizeFilename(e.name);if(n.length>this.config.maxFilenameLength&&t.push(`Filename exceeds maximum length of ${this.config.maxFilenameLength} characters`),this.config.enableContentScanning){const n=await this.validateImageContent(e);n.valid||t.push(...n.errors),s.push(...n.warnings)}try{const i=await this.getImageDimensions(e);return(i.width>this.config.maxDimensions.width||i.height>this.config.maxDimensions.height)&&t.push(`Image dimensions exceed maximum size of ${this.config.maxDimensions.width}x${this.config.maxDimensions.height}`),{valid:0===t.length,errors:t,warnings:s,sanitizedData:{fileName:n,mimeType:e.type,size:e.size,dimensions:i}}}catch(e){return t.push("Failed to validate image format"),{valid:!1,errors:t,warnings:s}}}async validateBase64Image(e,t){const s=[],n=[];try{const i=this.parseBase64Metadata(e);this.config.allowedMimeTypes.includes(i.mimeType)||s.push(`Image type ${i.mimeType} is not supported`),i.size>this.config.maxFileSize&&s.push(`Image size exceeds maximum limit of ${this.config.maxFileSize/1048576}MB`);const a=this.sanitizeFilename(t);a.length>this.config.maxFilenameLength&&s.push(`Filename exceeds maximum length of ${this.config.maxFilenameLength} characters`);const o=await this.getBase64ImageDimensions(e);return(o.width>this.config.maxDimensions.width||o.height>this.config.maxDimensions.height)&&s.push(`Image dimensions exceed maximum size of ${this.config.maxDimensions.width}x${this.config.maxDimensions.height}`),{valid:0===s.length,errors:s,warnings:n,sanitizedData:{fileName:a,mimeType:i.mimeType,size:i.size,dimensions:o}}}catch(e){return s.push("Failed to validate base64 image data"),{valid:!1,errors:s,warnings:n}}}sanitizeFilename(e){let t=e.replace(/[/\\:*?"<>|]/g,"_");if(t=t.replace(/[\x00-\x1f\x7f-\x9f]/g,""),t=t.trim().replace(/^\.+|\.+$/g,""),t||(t="untitled"),t.length>this.config.maxFilenameLength){const e=t.split(".").pop()||"",s=t.slice(0,-(e.length+1));t=s.slice(0,this.config.maxFilenameLength-e.length-1)+"."+e}return t}async removeImageMetadata(e){if(!this.config.enableMetadataRemoval)return e;try{const t=new Image,s=document.createElement("canvas"),n=s.getContext("2d");if(!n)throw new Error("Failed to create canvas context");return new Promise((i,a)=>{t.onload=()=>{s.width=t.width,s.height=t.height,n.drawImage(t,0,0);const e=s.toDataURL("image/jpeg",.9);i(e)},t.onerror=()=>a(new Error("Failed to load image for metadata removal")),t.src=e})}catch(t){return console.warn("Failed to remove image metadata:",t),e}}async validateImageContent(e){const t=[],s=[];try{const n=await e.arrayBuffer(),i=new Uint8Array(n);this.hasValidImageHeader(i,e.type)||t.push("Invalid image file header");const a=[/<script/i,/javascript:/i,/data:text\/html/i,/vbscript:/i,/<object/i,/<embed/i,/<iframe/i],o=Array.from(i.slice(0,1024)).map(e=>String.fromCharCode(e)).join("");for(const e of a)if(e.test(o)){t.push("Suspicious content detected in image file");break}return{valid:0===t.length,errors:t,warnings:s}}catch(e){return t.push("Failed to validate image content"),{valid:!1,errors:t,warnings:s}}}hasValidImageHeader(e,t){if(e.length<8)return!1;switch(t){case"image/jpeg":return 255===e[0]&&216===e[1];case"image/png":return 137===e[0]&&80===e[1]&&78===e[2]&&71===e[3];case"image/webp":return 82===e[0]&&73===e[1]&&70===e[2]&&70===e[3]&&87===e[8]&&69===e[9]&&66===e[10]&&80===e[11];default:return!1}}async getImageDimensions(e){return new Promise((t,s)=>{const n=new Image,i=URL.createObjectURL(e);n.onload=()=>{URL.revokeObjectURL(i),t({width:n.width,height:n.height})},n.onerror=()=>{URL.revokeObjectURL(i),s(new Error("Failed to load image for dimension analysis"))},n.src=i})}async getBase64ImageDimensions(e){return new Promise((t,s)=>{const n=new Image;n.onload=()=>{t({width:n.width,height:n.height})},n.onerror=()=>{s(new Error("Failed to load base64 image for dimension analysis"))},n.src=e})}parseBase64Metadata(e){const t=e.match(/^data:([^;]+);base64,/),s=t?t[1]:"image/jpeg",n=e.split(",")[1]||"";return{mimeType:s,size:Math.floor(.75*n.length)}}async validateContentPolicy(e){const t=[];try{const s=await this.getBase64ImageDimensions(e);return(s.width<10||s.height<10)&&t.push("Image dimensions are too small"),(s.width>1e4||s.height>1e4)&&t.push("Image dimensions are excessively large"),{compliant:0===t.length,issues:t}}catch(e){return t.push("Failed to validate content policy"),{compliant:!1,issues:t}}}generateSecureCacheKey(e,t){const s=(new TextEncoder).encode(e+(t||""));return crypto.subtle.digest("SHA-256",s).then(e=>Array.from(new Uint8Array(e)).map(e=>e.toString(16).padStart(2,"0")).join("")).catch(()=>{let t=0;for(let s=0;s<e.length;s++){t=(t<<5)-t+e.charCodeAt(s),t&=t}return Math.abs(t).toString(16)})}sanitizePrompt(e){let t=e.replace(/[<>]/g,"").replace(/javascript:/gi,"").replace(/data:/gi,"").replace(/vbscript:/gi,"").replace(/file:/gi,"").trim();return t.length>2e3&&(t=t.substring(0,2e3)),t}};const Ge=new class{constructor(e){__publicField$1(this,"cache",new Map),__publicField$1(this,"config"),__publicField$1(this,"stats",{hits:0,misses:0,evictions:0,memoryUsage:0,entryCount:0,hitRate:0,averageAccessTime:0}),__publicField$1(this,"gcTimer"),__publicField$1(this,"accessTimes",[]),this.config={maxMemorySize:50,maxEntries:1e3,defaultTTL:864e5,enablePersistence:!0,compressionLevel:6,gcInterval:3e5,...e},this.initializeCache(),this.startGarbageCollection()}initializeCache(){if(this.config.enablePersistence)try{const e=localStorage.getItem("tinderop-editing-cache");if(e){const t=JSON.parse(e),s=Date.now();for(const[e,n]of Object.entries(t)){const t=n;s-t.timestamp<t.ttl&&this.cache.set(e,t)}this.updateStats()}}catch(e){console.warn("Failed to initialize cache from localStorage:",e)}}startGarbageCollection(){this.gcTimer&&clearInterval(this.gcTimer),this.gcTimer=window.setInterval(()=>{this.performGarbageCollection()},this.config.gcInterval)}async get(e){const t=me.now(),s=this.cache.get(e);if(!s)return this.stats.misses++,this.updateHitRate(),null;const n=Date.now();if(n-s.timestamp>s.ttl)return this.cache.delete(e),this.stats.misses++,this.stats.evictions++,this.updateStats(),null;s.accessCount++,s.lastAccess=n,this.stats.hits++;const i=me.now()-t;if(this.recordAccessTime(i),this.updateHitRate(),s.compressed)try{return await this.decompress(s.data)}catch(t){return console.warn("Failed to decompress cached data:",t),this.cache.delete(e),null}return s.data}async set(e,t,s=this.config.defaultTTL){try{const n=this.calculateDataSize(t);await this.ensureCapacity(n);let i=t,a=!1;if(n>102400)try{i=await this.compress(t),a=!0}catch(e){console.warn("Failed to compress cache data:",e)}const o={key:e,data:i,timestamp:Date.now(),ttl:s,accessCount:1,lastAccess:Date.now(),size:n,compressed:a};this.cache.set(e,o),this.updateStats(),this.config.enablePersistence&&await this.persistCache()}catch(e){console.error("Failed to cache editing result:",e)}}has(e){const t=this.cache.get(e);if(!t)return!1;return!(Date.now()-t.timestamp>t.ttl)||(this.cache.delete(e),this.updateStats(),!1)}delete(e){const t=this.cache.delete(e);return t&&this.updateStats(),t}clear(){this.cache.clear(),this.stats={hits:0,misses:0,evictions:0,memoryUsage:0,entryCount:0,hitRate:0,averageAccessTime:0},this.config.enablePersistence&&localStorage.removeItem("tinderop-editing-cache")}getStats(){return{...this.stats}}getConfig(){return{...this.config}}updateConfig(e){this.config={...this.config,...e},e.gcInterval&&this.startGarbageCollection()}generateCacheKey(e,t){const s=e+(t?JSON.stringify(t):"");let n=0;for(let e=0;e<s.length;e++){n=(n<<5)-n+s.charCodeAt(e),n&=n}return`editing_${Math.abs(n).toString(16)}`}async ensureCapacity(e){1024*this.stats.memoryUsage*1024+e>1024*this.config.maxMemorySize*1024&&await this.evictLRU(e),this.cache.size>=this.config.maxEntries&&await this.evictLRU(0)}async evictLRU(e){const t=Array.from(this.cache.entries());t.sort((e,t)=>e[1].lastAccess-t[1].lastAccess);let s=0;const n=e;for(const[e,i]of t){if(s>=n&&this.cache.size<this.config.maxEntries)break;this.cache.delete(e),s+=i.size,this.stats.evictions++}this.updateStats()}performGarbageCollection(){const e=Date.now(),t=[];for(const[s,n]of this.cache.entries())e-n.timestamp>n.ttl&&t.push(s);for(const e of t)this.cache.delete(e),this.stats.evictions++;t.length>0&&this.updateStats()}async persistCache(){if(this.config.enablePersistence)try{const e=Object.fromEntries(this.cache.entries()),t=JSON.stringify(e),s=this.getAvailableStorageSpace();if(t.length>s)return void console.warn("Insufficient localStorage space for cache persistence");localStorage.setItem("tinderop-editing-cache",t)}catch(e){console.warn("Failed to persist cache to localStorage:",e)}}getAvailableStorageSpace(){try{const e=JSON.stringify(localStorage).length,t=5242880;return Math.max(0,t-e)}catch{return 0}}calculateDataSize(e){try{const t=JSON.stringify(e);return new Blob([t]).size}catch{return 0}}async compress(e){try{const t=JSON.stringify(e),s=await this.gzipCompress(t);return{...e,_compressed:!0,_data:s}}catch(t){return console.warn("Compression failed:",t),e}}async decompress(e){try{if(!e._compressed)return e;const t=e._data,s=await this.gzipDecompress(t);return JSON.parse(s)}catch(t){return console.warn("Decompression failed:",t),e}}async gzipCompress(e){if(!("CompressionStream"in window))return e;const t=new CompressionStream("gzip"),s=t.writable.getWriter(),n=t.readable.getReader(),i=[];s.write((new TextEncoder).encode(e)),s.close();let a=await n.read();for(;!a.done;)i.push(a.value),a=await n.read();const o=new Uint8Array(i.reduce((e,t)=>e+t.length,0));let r=0;for(const e of i)o.set(e,r),r+=e.length;return btoa(String.fromCharCode(...o))}async gzipDecompress(e){if(!("DecompressionStream"in window))return e;const t=new DecompressionStream("gzip"),s=t.writable.getWriter(),n=t.readable.getReader(),i=atob(e),a=new Uint8Array(i.length);for(let e=0;e<i.length;e++)a[e]=i.charCodeAt(e);s.write(a),s.close();const o=[];let r=await n.read();for(;!r.done;)o.push(r.value),r=await n.read();const l=new Uint8Array(o.reduce((e,t)=>e+t.length,0));let c=0;for(const e of o)l.set(e,c),c+=e.length;return(new TextDecoder).decode(l)}recordAccessTime(e){this.accessTimes.push(e),this.accessTimes.length>100&&this.accessTimes.shift(),this.stats.averageAccessTime=this.accessTimes.reduce((e,t)=>e+t,0)/this.accessTimes.length}updateHitRate(){const e=this.stats.hits+this.stats.misses;this.stats.hitRate=e>0?this.stats.hits/e*100:0}updateStats(){this.stats.entryCount=this.cache.size;let e=0;for(const t of this.cache.values())e+=t.size;this.stats.memoryUsage=e/1048576,this.updateHitRate()}dispose(){this.gcTimer&&(clearInterval(this.gcTimer),this.gcTimer=void 0),this.clear()}};const We=new class{constructor(e){__publicField$1(this,"config"),__publicField$1(this,"userStats",new Map),__publicField$1(this,"globalStats",{totalUsers:0,activeUsers:0,totalRequests:0,totalCost:0,averageResponseTime:0,errorRate:0,concurrentRequests:0}),__publicField$1(this,"cleanupTimer"),__publicField$1(this,"persistenceTimer"),__publicField$1(this,"requestTimes",[]),this.config={analysisPerHour:100,analysisPerDay:500,generationPerHour:20,generationPerDay:50,maxConcurrentRequests:100,maxConcurrentPerUser:3,dailyCostLimit:10,monthlyCostLimit:250,burstAnalysisLimit:10,burstGenerationLimit:5,burstWindow:6e4,analysisRecoveryRate:2,generationRecoveryRate:.5,premiumMultiplier:3,proMultiplier:10,...e},this.initializeFromStorage(),this.startCleanupTimer(),this.startPersistenceTimer()}async checkAnalysisLimit(e,t="free"){const s=this.getUserStats(e,t),n=this.getEffectiveLimits(t);if(this.updateUsageCounts(s),s.concurrentRequests>=n.maxConcurrentPerUser)return{allowed:!1,remainingRequests:0,resetTime:Date.now()+6e4,retryAfter:6e4,reason:"Too many concurrent requests",costRemaining:this.getRemainingCost(s)};if(this.globalStats.concurrentRequests>=this.config.maxConcurrentRequests)return{allowed:!1,remainingRequests:0,resetTime:Date.now()+3e4,retryAfter:3e4,reason:"System busy, please try again later",costRemaining:this.getRemainingCost(s)};if(s.burstUsage.analysis>=n.burstAnalysisLimit){const e=Date.now()-s.burstUsage.windowStart;if(e<this.config.burstWindow)return{allowed:!1,remainingRequests:0,resetTime:s.burstUsage.windowStart+this.config.burstWindow,retryAfter:this.config.burstWindow-e,reason:"Burst limit exceeded",costRemaining:this.getRemainingCost(s)}}return s.analysisCount.hour>=n.analysisPerHour?{allowed:!1,remainingRequests:0,resetTime:s.analysisCount.hourResetTime,retryAfter:s.analysisCount.hourResetTime-Date.now(),reason:"Hourly limit exceeded",costRemaining:this.getRemainingCost(s)}:s.analysisCount.day>=n.analysisPerDay?{allowed:!1,remainingRequests:0,resetTime:s.analysisCount.dayResetTime,retryAfter:s.analysisCount.dayResetTime-Date.now(),reason:"Daily limit exceeded",costRemaining:this.getRemainingCost(s)}:{allowed:!0,remainingRequests:Math.min(n.analysisPerHour-s.analysisCount.hour,n.analysisPerDay-s.analysisCount.day),resetTime:s.analysisCount.hourResetTime,costRemaining:this.getRemainingCost(s)}}async checkGenerationLimit(e,t,s="free"){const n=this.getUserStats(e,s),i=this.getEffectiveLimits(s);if(this.updateUsageCounts(n),n.costUsage.daily+t>i.dailyCostLimit)return{allowed:!1,remainingRequests:0,resetTime:n.costUsage.dailyResetTime,retryAfter:n.costUsage.dailyResetTime-Date.now(),reason:"Daily cost limit exceeded",costRemaining:Math.max(0,i.dailyCostLimit-n.costUsage.daily)};if(n.costUsage.monthly+t>i.monthlyCostLimit)return{allowed:!1,remainingRequests:0,resetTime:n.costUsage.monthlyResetTime,retryAfter:n.costUsage.monthlyResetTime-Date.now(),reason:"Monthly cost limit exceeded",costRemaining:Math.max(0,i.monthlyCostLimit-n.costUsage.monthly)};if(n.concurrentRequests>=i.maxConcurrentPerUser)return{allowed:!1,remainingRequests:0,resetTime:Date.now()+6e4,retryAfter:6e4,reason:"Too many concurrent requests",costRemaining:this.getRemainingCost(n)};if(n.burstUsage.generation>=i.burstGenerationLimit){const e=Date.now()-n.burstUsage.windowStart;if(e<this.config.burstWindow)return{allowed:!1,remainingRequests:0,resetTime:n.burstUsage.windowStart+this.config.burstWindow,retryAfter:this.config.burstWindow-e,reason:"Burst limit exceeded",costRemaining:this.getRemainingCost(n)}}return n.generationCount.hour>=i.generationPerHour?{allowed:!1,remainingRequests:0,resetTime:n.generationCount.hourResetTime,retryAfter:n.generationCount.hourResetTime-Date.now(),reason:"Hourly limit exceeded",costRemaining:this.getRemainingCost(n)}:n.generationCount.day>=i.generationPerDay?{allowed:!1,remainingRequests:0,resetTime:n.generationCount.dayResetTime,retryAfter:n.generationCount.dayResetTime-Date.now(),reason:"Daily limit exceeded",costRemaining:this.getRemainingCost(n)}:{allowed:!0,remainingRequests:Math.min(i.generationPerHour-n.generationCount.hour,i.generationPerDay-n.generationCount.day),resetTime:n.generationCount.hourResetTime,costRemaining:this.getRemainingCost(n)}}recordAnalysisRequest(e,t){const s=this.getUserStats(e);s.analysisCount.hour++,s.analysisCount.day++,s.concurrentRequests++;const n=Date.now();n-s.burstUsage.windowStart>this.config.burstWindow?(s.burstUsage.windowStart=n,s.burstUsage.analysis=1):s.burstUsage.analysis++,this.globalStats.totalRequests++,this.globalStats.concurrentRequests++,this.recordResponseTime(t),setTimeout(()=>{s.concurrentRequests=Math.max(0,s.concurrentRequests-1),this.globalStats.concurrentRequests=Math.max(0,this.globalStats.concurrentRequests-1)},1e3)}recordGenerationRequest(e,t,s){const n=this.getUserStats(e);n.generationCount.hour++,n.generationCount.day++,n.concurrentRequests++,n.costUsage.daily+=t,n.costUsage.monthly+=t;const i=Date.now();i-n.burstUsage.windowStart>this.config.burstWindow?(n.burstUsage.windowStart=i,n.burstUsage.generation=1):n.burstUsage.generation++,this.globalStats.totalRequests++,this.globalStats.totalCost+=t,this.globalStats.concurrentRequests++,this.recordResponseTime(s),setTimeout(()=>{n.concurrentRequests=Math.max(0,n.concurrentRequests-1),this.globalStats.concurrentRequests=Math.max(0,this.globalStats.concurrentRequests-1)},s)}recordFailedRequest(e,t){const s=this.getUserStats(e);s.violations.push({count:1,lastViolation:Date.now(),type:t}),s.violations.length>10&&s.violations.shift(),this.globalStats.totalRequests++,this.updateErrorRate()}getUserUsageStats(e){const t=this.getUserStats(e),s=this.getEffectiveLimits(t.userTier);return{userId:e,analysisCount:t.analysisCount.hour+t.analysisCount.day,generationCount:t.generationCount.hour+t.generationCount.day,totalCost:t.costUsage.daily+t.costUsage.monthly,lastUsed:new Date,dailyLimits:{analysis:s.analysisPerDay,generation:s.generationPerDay,cost:s.dailyCostLimit}}}getGlobalStats(){return{...this.globalStats}}resetUserLimits(e){const t=this.getUserStats(e),s=Date.now();t.analysisCount={hour:0,day:0,hourResetTime:s+36e5,dayResetTime:s+864e5},t.generationCount={hour:0,day:0,hourResetTime:s+36e5,dayResetTime:s+864e5},t.costUsage={daily:0,monthly:0,dailyResetTime:s+864e5,monthlyResetTime:s+2592e6},t.burstUsage={analysis:0,generation:0,windowStart:s},t.concurrentRequests=0,t.violations=[]}updateConfig(e){this.config={...this.config,...e}}getEffectiveLimits(e){const t="pro"===e?this.config.proMultiplier:"premium"===e?this.config.premiumMultiplier:1;return{...this.config,analysisPerHour:Math.floor(this.config.analysisPerHour*t),analysisPerDay:Math.floor(this.config.analysisPerDay*t),generationPerHour:Math.floor(this.config.generationPerHour*t),generationPerDay:Math.floor(this.config.generationPerDay*t),dailyCostLimit:this.config.dailyCostLimit*t,monthlyCostLimit:this.config.monthlyCostLimit*t,burstAnalysisLimit:Math.floor(this.config.burstAnalysisLimit*t),burstGenerationLimit:Math.floor(this.config.burstGenerationLimit*t),maxConcurrentPerUser:Math.floor(this.config.maxConcurrentPerUser*t)}}getUserStats(e,t="free"){let s=this.userStats.get(e);if(!s){const n=Date.now();s={userId:e,userTier:t,analysisCount:{hour:0,day:0,hourResetTime:n+36e5,dayResetTime:n+864e5},generationCount:{hour:0,day:0,hourResetTime:n+36e5,dayResetTime:n+864e5},costUsage:{daily:0,monthly:0,dailyResetTime:n+864e5,monthlyResetTime:n+2592e6},burstUsage:{analysis:0,generation:0,windowStart:n},concurrentRequests:0,violations:[]},this.userStats.set(e,s),this.globalStats.totalUsers++}return s}updateUsageCounts(e){const t=Date.now();t>e.analysisCount.hourResetTime&&(e.analysisCount.hour=0,e.analysisCount.hourResetTime=t+36e5),t>e.generationCount.hourResetTime&&(e.generationCount.hour=0,e.generationCount.hourResetTime=t+36e5),t>e.analysisCount.dayResetTime&&(e.analysisCount.day=0,e.analysisCount.dayResetTime=t+864e5),t>e.generationCount.dayResetTime&&(e.generationCount.day=0,e.generationCount.dayResetTime=t+864e5),t>e.costUsage.dailyResetTime&&(e.costUsage.daily=0,e.costUsage.dailyResetTime=t+864e5),t>e.costUsage.monthlyResetTime&&(e.costUsage.monthly=0,e.costUsage.monthlyResetTime=t+2592e6)}getRemainingCost(e){const t=this.getEffectiveLimits(e.userTier);return Math.max(0,t.dailyCostLimit-e.costUsage.daily)}recordResponseTime(e){this.requestTimes.push(e),this.requestTimes.length>100&&this.requestTimes.shift(),this.globalStats.averageResponseTime=this.requestTimes.reduce((e,t)=>e+t,0)/this.requestTimes.length}updateErrorRate(){const e=Array.from(this.userStats.values()).reduce((e,t)=>e+t.violations.length,0);this.globalStats.errorRate=this.globalStats.totalRequests>0?e/this.globalStats.totalRequests*100:0}initializeFromStorage(){try{const e=localStorage.getItem("tinderop-rate-limiter");if(e){const t=JSON.parse(e);for(const[e,s]of Object.entries(t.userStats||{}))this.userStats.set(e,s);t.globalStats&&(this.globalStats={...this.globalStats,...t.globalStats})}}catch(e){console.warn("Failed to initialize rate limiter from storage:",e)}}persistToStorage(){try{const e={userStats:Object.fromEntries(this.userStats.entries()),globalStats:this.globalStats};localStorage.setItem("tinderop-rate-limiter",JSON.stringify(e))}catch(e){console.warn("Failed to persist rate limiter to storage:",e)}}startCleanupTimer(){this.cleanupTimer=window.setInterval(()=>{this.cleanupExpiredStats()},36e5)}startPersistenceTimer(){this.persistenceTimer=window.setInterval(()=>{this.persistToStorage()},3e5)}cleanupExpiredStats(){const e=Date.now(),t=[];for(const[s,n]of this.userStats.entries()){e-Math.max(n.analysisCount.dayResetTime-864e5,n.generationCount.dayResetTime-864e5)>864e5&&t.push(s)}for(const e of t)this.userStats.delete(e),this.globalStats.totalUsers--}dispose(){this.cleanupTimer&&clearInterval(this.cleanupTimer),this.persistenceTimer&&clearInterval(this.persistenceTimer),this.persistToStorage(),this.userStats.clear()}};const Qe=new class{constructor(){if(__publicField$1(this,"apiKey"),__publicField$1(this,"isProcessing",!1),__publicField$1(this,"currentUserId"),this.apiKey="********************************************************************************************************************************************************************",!this.apiKey)throw console.error("🔑 VITE_OPENAI_API_KEY not found in environment variables"),new Error("OpenAI API key is required. Please set VITE_OPENAI_API_KEY in your .env file");console.log("🔑 OpenAI API key loaded successfully"),console.log(`🔑 API Key preview: ${this.apiKey.substring(0,10)}...`)}async analyzeImageForEditing(e,t={}){var s,n,i;if(this.isProcessing)throw new Error("Analysis already in progress");this.isProcessing=!0;const a=Date.now();try{const i=Ge.generateCacheKey(e.imageData,e.preferences),o=await Ge.get(i);if(o)return console.log("🎯 Cache hit for editing analysis"),null==(s=t.onComplete)||s.call(t,o),o;const r=await Be.validateBase64Image(e.imageData,e.fileName);if(!r.valid)throw new Error(`Security validation failed: ${r.errors.join(", ")}`);const l=this.currentUserId||"anonymous",c=await We.checkAnalysisLimit(l);if(!c.allowed)throw new Error(`Rate limit exceeded: ${c.reason}`);const u={fileName:e.fileName,originalImage:e.imageData,canEdit:!0,editingRecommendations:[],quickFixes:[],transformativeEdits:[],totalRecommendations:0,estimatedTotalCost:0,processingTime:0,confidence:0,processed:!1},d=await this.executeEditingAnalysis(e,(s,n,i)=>{var o;const r={fileName:e.fileName,currentStep:s,totalSteps:pe.length,stepName:n,progress:i,estimatedTimeRemaining:this.estimateTimeRemaining(i,a)};null==(o=t.onProgress)||o.call(t,r)}),m=this.categorizeRecommendations(d);return u.editingRecommendations=m.all,u.quickFixes=m.quickFixes,u.transformativeEdits=m.transformative,u.totalRecommendations=d.length,u.estimatedTotalCost=this.calculateTotalCost(d),u.confidence=this.calculateOverallConfidence(d),u.processingTime=Date.now()-a,u.processed=!0,await Ge.set(i,u),We.recordAnalysisRequest(l,u.processingTime),null==(n=t.onComplete)||n.call(t,u),u}catch(s){const n=s instanceof Error?s.message:"Unknown error occurred";return console.error("❌ Image editing analysis failed:",s),this.currentUserId&&We.recordFailedRequest(this.currentUserId,n),null==(i=t.onError)||i.call(t,n),{fileName:e.fileName,originalImage:e.imageData,canEdit:!1,editingRecommendations:[],quickFixes:[],transformativeEdits:[],totalRecommendations:0,estimatedTotalCost:0,processingTime:Date.now()-a,confidence:0,processed:!1,error:n}}finally{this.isProcessing=!1}}async generateEditedImage(e,t="anonymous"){try{const s=Date.now(),n=this.estimateGenerationCost(e),i=await We.checkGenerationLimit(t,n);if(!i.allowed)throw new Error(`Rate limit exceeded: ${i.reason}`);const a=this.generateEditingPrompt(e.recommendation),o=await this.callOpenAIImageAPI(e.originalImage,a,e.quality,e.inputFidelity),r=Date.now()-s,l=this.calculateActualCost(e,r);return We.recordGenerationRequest(t,l,r),{success:!0,editedImage:o,cost:l,processingTime:r}}catch(e){const s=e instanceof Error?e.message:"Unknown error occurred";return console.error("❌ Image generation failed:",e),We.recordFailedRequest(t,s),{success:!1,cost:0,processingTime:Date.now()-Date.now(),error:s}}}async executeEditingAnalysis(e,t){const s=[],n=pe;for(let i=0;i<n.length;i++){const a=n[i];null==t||t(a.id,a.name,i/n.length*100);const o=Date.now();try{console.log(`🔍 Starting editing analysis step ${a.id}: ${a.name}`);const r=await this.executeEditingStep(a.id,a.name,e);s.push(...r);const l=Date.now()-o;console.log(`📊 Editing Step ${a.id} (${a.name}):`,{recommendations:r.length,processingTime:`${l}ms`}),null==t||t(a.id,a.name,(i+1)/n.length*100)}catch(e){console.error(`❌ Error in step ${a.id} (${a.name}):`,e)}}return s}async executeEditingStep(e,t,s){const n=this.getEditingStepPrompt(e,s);console.log(`🤖 Calling OpenAI API for editing step ${e}`);const{text:i}=await X({model:He("gpt-4o"),messages:[{role:"user",content:[{type:"text",text:n},{type:"image",image:s.imageData}]}],maxTokens:2e3,temperature:.3});return this.parseEditingRecommendations(i,e)}getEditingStepPrompt(e,t){const s='You are an expert photo editor specializing in dating profile optimization. Analyze this image and provide specific, actionable editing recommendations that will improve its dating appeal while maintaining the subject\'s natural appearance.\n\nCRITICAL REQUIREMENTS:\n- Never suggest changing how the subject looks (no facial alterations, body modifications)\n- Focus on: lighting, composition, background, clothing, pose, camera angle, colors, style\n- Provide specific, actionable editing instructions\n- Estimate impact score (1-10) and difficulty level\n- Consider cost-effectiveness\n\nResponse format (JSON):\n{\n  "recommendations": [\n    {\n      "type": "lighting|background|composition|color|style|clothing|expression|posture",\n      "category": "quick_fix|advanced_edit|style_enhancement|composition_improvement",\n      "title": "Short descriptive title",\n      "description": "Detailed description of what to change",\n      "impactScore": 1-10,\n      "difficulty": "easy|medium|hard",\n      "estimatedTime": minutes,\n      "instructions": ["Step 1", "Step 2", "Step 3"],\n      "toolsRecommended": ["Suggested editing app/tool"],\n      "estimatedCost": 0.05-2.00,\n      "confidence": 0-100\n    }\n  ]\n}',n={1:`${s}\n\nSTEP 1: TECHNICAL ASSESSMENT & QUICK FIXES\nAnalyze technical quality and identify immediate improvements:\n- Image sharpness and clarity issues\n- Basic lighting corrections (exposure, shadows, highlights)\n- Color balance and saturation adjustments\n- Cropping and framing improvements\n- Basic noise reduction or enhancement needs\n\nFocus on: Quick, low-cost fixes that provide immediate visual improvement.`,2:`${s}\n\nSTEP 2: LIGHTING OPTIMIZATION\nAnalyze lighting conditions and improvement opportunities:\n- Harsh shadows or unflattering lighting angles\n- Exposure issues (overexposed/underexposed areas)\n- Color temperature problems (too warm/cool)\n- Lighting direction and quality\n- Background lighting vs subject lighting balance\n\nFocus on: Lighting adjustments that enhance the subject's appearance without changing facial features.`,3:`${s}\n\nSTEP 3: COMPOSITION & FRAMING\nAnalyze composition and suggest improvements:\n- Rule of thirds application\n- Background elements and distractions\n- Framing and crop optimization\n- Angle and perspective improvements\n- Visual balance and focal points\n\nFocus on: Repositioning, cropping, and angle adjustments that improve visual appeal.`,4:`${s}\n\nSTEP 4: COLOR & STYLE ENHANCEMENT\nAnalyze color palette and style opportunities:\n- Color grading and tone adjustments\n- Clothing color optimization\n- Background color harmony\n- Seasonal and style appropriateness\n- Filter and preset applications\n\nFocus on: Color and style improvements that enhance overall aesthetic appeal.`,5:`${s}\n\nSTEP 5: ADVANCED EDITING OPPORTUNITIES\nIdentify sophisticated editing possibilities:\n- Background replacement or enhancement\n- Advanced lighting techniques\n- Professional-grade color grading\n- Artistic style applications\n- Premium editing techniques\n\nFocus on: High-impact professional edits that justify their cost.`};return n[e]||n[1]}parseEditingRecommendations(e,t){try{const s=e.match(/\{[\s\S]*\}/);if(!s)throw new Error("No JSON found in response");const n=JSON.parse(s[0]);return(Array.isArray(n.recommendations)?n.recommendations:[]).map((e,s)=>({id:`${t}-${s}`,type:e.type||"enhancement",category:e.category||"quick_fix",title:e.title||"Photo Enhancement",description:e.description||"Improve photo quality",impactScore:Math.max(1,Math.min(10,parseInt(e.impactScore)||5)),difficulty:e.difficulty||"medium",estimatedTime:parseInt(e.estimatedTime)||5,instructions:Array.isArray(e.instructions)?e.instructions.slice(0,5):["Apply enhancement"],toolsRecommended:Array.isArray(e.toolsRecommended)?e.toolsRecommended.slice(0,3):["Photo editing app"],estimatedCost:Math.max(.05,Math.min(2,parseFloat(e.estimatedCost)||.25)),confidence:Math.max(0,Math.min(100,parseInt(e.confidence)||80)),applied:!1,dismissed:!1,createdAt:new Date}))}catch(t){return console.error("🔧 Failed to parse editing recommendations:",t),console.log("📝 Raw response:",e),[]}}categorizeRecommendations(e){const t=e.filter(e=>"quick_fix"===e.category&&e.estimatedCost<=.5),s=e.filter(e=>"advanced_edit"===e.category&&e.impactScore>=7);return{all:e,quickFixes:t,transformative:s}}calculateTotalCost(e){return e.reduce((e,t)=>e+t.estimatedCost,0)}calculateOverallConfidence(e){if(0===e.length)return 0;const t=e.reduce((e,t)=>e+t.confidence,0);return Math.round(t/e.length)}generateEditingPrompt(e){const t={lighting:`Enhance lighting to be more flattering and natural. ${e.description}`,background:`Improve or replace the background. ${e.description}`,composition:`Optimize composition and framing. ${e.description}`,color:`Adjust colors for better aesthetic appeal. ${e.description}`,style:`Apply style enhancements. ${e.description}`,clothing:`Optimize clothing appearance. ${e.description}`,expression:`Enhance expression and mood. ${e.description}`,posture:`Improve posture and body language. ${e.description}`,enhancement:`Apply general enhancements. ${e.description}`,object_removal:`Remove unwanted objects or distractions. ${e.description}`};return`Apply professional photo editing to improve this dating profile image. ${t[e.type]||t.enhancement} Maintain the subject's natural appearance while improving the overall quality and appeal of the image.`}async callOpenAIImageAPI(e,t,s,n){return console.log("🎨 Calling OpenAI GPT-Image-1 API",{prompt:t.substring(0,100)+"...",quality:s,inputFidelity:n}),await new Promise(e=>setTimeout(e,2e3)),e}estimateGenerationCost(e){return{standard:.04,high:.17,ultra:.34}[e.quality]+{standard:0,high:.062}[e.inputFidelity]}calculateActualCost(e,t){const s=this.estimateGenerationCost(e),n=Math.max(1,t/1e4);return Math.round(s*n*100)/100}estimateTimeRemaining(e,t){if(0===e)return 0;const s=Date.now()-t,n=s/(e/100)-s;return Math.max(0,Math.round(n/1e3))}setUserId(e){this.currentUserId=e}isCurrentlyProcessing(){return this.isProcessing}getServiceStats(){return{cache:Ge.getStats(),rateLimiter:We.getGlobalStats(),isProcessing:this.isProcessing,apiKeyConfigured:!!this.apiKey}}};var Je="Switch",[Ve,Ke]=le(Je),[Ye,Ze]=Ve(Je),Xe=t.forwardRef((e,n)=>{const{__scopeSwitch:i,name:a,checked:o,defaultChecked:r,required:l,disabled:c,value:u="on",onCheckedChange:d,form:m,...p}=e,[g,h]=t.useState(null),f=ue(n,e=>h(e)),y=t.useRef(!1),v=!g||(m||!!g.closest("form")),[w=!1,x]=re({prop:o,defaultProp:r,onChange:d});return s.jsxs(Ye,{scope:i,checked:w,disabled:c,children:[s.jsx(de.button,{type:"button",role:"switch","aria-checked":w,"aria-required":l,"data-state":getState(w),"data-disabled":c?"":void 0,disabled:c,value:u,...p,ref:f,onClick:ce(e.onClick,e=>{x(e=>!e),v&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})}),v&&s.jsx(BubbleInput,{control:g,bubbles:!y.current,name:a,value:u,checked:w,required:l,disabled:c,form:m,style:{transform:"translateX(-100%)"}})]})});Xe.displayName=Je;var et="SwitchThumb",tt=t.forwardRef((e,t)=>{const{__scopeSwitch:n,...i}=e,a=Ze(et,n);return s.jsx(de.span,{"data-state":getState(a.checked),"data-disabled":a.disabled?"":void 0,...i,ref:t})});tt.displayName=et;var BubbleInput=e=>{const{control:n,checked:i,bubbles:a=!0,...o}=e,r=t.useRef(null),l=function(e){const s=t.useRef({value:e,previous:e});return t.useMemo(()=>(s.current.value!==e&&(s.current.previous=s.current.value,s.current.value=e),s.current.previous),[e])}(i),c=function(e){const[s,n]=t.useState(void 0);return oe(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const t=new ResizeObserver(t=>{if(!Array.isArray(t))return;if(!t.length)return;const s=t[0];let i,a;if("borderBoxSize"in s){const e=s.borderBoxSize,t=Array.isArray(e)?e[0]:e;i=t.inlineSize,a=t.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),s}(n);return t.useEffect(()=>{const e=r.current,t=window.HTMLInputElement.prototype,s=Object.getOwnPropertyDescriptor(t,"checked").set;if(l!==i&&s){const t=new Event("click",{bubbles:a});s.call(e,i),e.dispatchEvent(t)}},[l,i,a]),s.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i,...o,tabIndex:-1,ref:r,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function getState(e){return e?"checked":"unchecked"}var st=Xe,nt=tt,it=t.forwardRef((e,t)=>s.jsx(de.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));it.displayName="Label";var at=it,ot=Object.defineProperty,__publicField=(e,t,s)=>((e,t,s)=>t in e?ot(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s)(e,"symbol"!=typeof t?t+"":t,s);const rt=R("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),lt=t.forwardRef(({className:e,variant:t,...i},a)=>s.jsx("div",{ref:a,role:"alert",className:n(rt({variant:t}),e),...i}));lt.displayName="Alert";t.forwardRef(({className:e,...t},i)=>s.jsx("h5",{ref:i,className:n("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";const ct=t.forwardRef(({className:e,...t},i)=>s.jsx("div",{ref:i,className:n("text-sm [&_p]:leading-relaxed",e),...t}));ct.displayName="AlertDescription";const ut=t.forwardRef(({className:e,...t},i)=>s.jsx(st,{className:n("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:i,children:s.jsx(nt,{className:n("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));ut.displayName=st.displayName;const dt=R("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),mt=t.forwardRef(({className:e,...t},i)=>s.jsx(at,{ref:i,className:n(dt(),e),...t}));mt.displayName=at.displayName;new class{constructor(){__publicField(this,"maxRetries",3),__publicField(this,"baseDelay",1e3),__publicField(this,"maxDelay",3e4)}async handleWithRetry(e,t,s=0){try{return await e()}catch(n){const i=this.categorizeError(n);if(this.logError(n,t,s),i.retryable&&s<this.maxRetries){const n=this.calculateDelay(s,i.retryAfter);return console.log(`Retrying ${t} in ${n}ms (attempt ${s+1}/${this.maxRetries})`),await this.sleep(n),this.handleWithRetry(e,t,s+1)}throw this.transformError(n,i)}}categorizeError(e){const t=e.message.toLowerCase(),s=e.toString().toLowerCase();return t.includes("rate limit")?{code:"RATE_LIMIT_EXCEEDED",message:"Rate limit exceeded. Please try again later.",retryable:!0,retryAfter:this.extractRetryAfter(e.message)||6e4}:t.includes("insufficient quota")||t.includes("billing")?{code:"INSUFFICIENT_QUOTA",message:"Insufficient API quota. Please check your billing.",retryable:!1}:t.includes("invalid api key")||t.includes("unauthorized")?{code:"INVALID_API_KEY",message:"Invalid API key. Please check your configuration.",retryable:!1}:t.includes("timeout")||t.includes("network")?{code:"NETWORK_ERROR",message:"Network error. Please check your connection.",retryable:!0,retryAfter:2e3}:t.includes("server error")||t.includes("internal error")?{code:"SERVER_ERROR",message:"Server error. Please try again.",retryable:!0,retryAfter:5e3}:t.includes("image too large")||t.includes("file size")?{code:"IMAGE_TOO_LARGE",message:"Image file is too large. Please use a smaller image.",retryable:!1}:t.includes("invalid image")||t.includes("unsupported format")?{code:"INVALID_IMAGE_FORMAT",message:"Invalid image format. Please use a supported format (JPEG, PNG, WebP).",retryable:!1}:t.includes("content policy")||t.includes("safety")?{code:"CONTENT_POLICY_VIOLATION",message:"Image violates content policy. Please use a different image.",retryable:!1}:s.includes("fetch")?{code:"FETCH_ERROR",message:"Network request failed. Please try again.",retryable:!0,retryAfter:2e3}:t.includes("parse")||t.includes("json")?{code:"PARSE_ERROR",message:"Failed to parse response. Please try again.",retryable:!0,retryAfter:1e3}:{code:"UNKNOWN_ERROR",message:"An unexpected error occurred. Please try again.",retryable:!0,retryAfter:5e3,details:{originalMessage:e.message,stack:e.stack}}}transformError(e,t){const s=new Error(t.message);return s.code=t.code,s.retryable=t.retryable,s.retryAfter=t.retryAfter,s.details=t.details,s.originalError=e,s}extractRetryAfter(e){const t=e.match(/retry after (\d+) seconds?/i);if(t)return 1e3*parseInt(t[1]);const s=e.match(/try again in (\d+) minutes?/i);return s?60*parseInt(s[1])*1e3:null}calculateDelay(e,t){if(t)return Math.min(t,this.maxDelay);const s=this.baseDelay*Math.pow(2,e),n=.1*Math.random()*s;return Math.min(s+n,this.maxDelay)}logError(e,t,s){const n={timestamp:(new Date).toISOString(),context:t,retryCount:s,message:e.message,stack:e.stack,userAgent:navigator.userAgent,url:window.location.href};console.error("ImageEditingError:",n),this.sendToErrorTracking(n)}sendToErrorTracking(e){try{const t=JSON.parse(localStorage.getItem("tinderop_errors")||"[]");t.push(e),t.length>100&&t.splice(0,t.length-100),localStorage.setItem("tinderop_errors",JSON.stringify(t))}catch(e){console.warn("Failed to store error for tracking:",e)}}getRecentErrors(){try{return JSON.parse(localStorage.getItem("tinderop_errors")||"[]")}catch(e){return console.warn("Failed to retrieve recent errors:",e),[]}}clearErrors(){localStorage.removeItem("tinderop_errors")}sleep(e){return new Promise(t=>setTimeout(t,e))}validateImage(e,t){const s=3*e.length/4,n=20971520;if(s>n)throw new Error(`Image file is too large (${(s/1024/1024).toFixed(1)}MB). Maximum size is 20MB.`);const i=["image/jpeg","image/png","image/webp"],a=this.getImageFormat(e);if(!i.includes(a))throw new Error(`Unsupported image format: ${a}. Supported formats: ${i.join(", ")}`);if(!this.isValidBase64(e))throw new Error("Invalid image data. Please try uploading the image again.")}getImageFormat(e){const t=e.substring(0,50);return t.startsWith("/9j/")?"image/jpeg":t.startsWith("iVBORw0KGgo")?"image/png":t.startsWith("UklGR")?"image/webp":"unknown"}isValidBase64(e){try{const t=e.replace(/^data:image\/[a-z]+;base64,/,"");return btoa(atob(t))===t}catch(e){return!1}}};const pt=new class{constructor(){__publicField(this,"logs",[]),__publicField(this,"maxLogs",1e3),__publicField(this,"logLevel","INFO"),this.startHealthMonitoring()}logCacheHit(e,t){this.log("INFO","CACHE_HIT",{requestId:e,imageHash:t.substring(0,10),timestamp:Date.now()})}logCacheMiss(e,t){this.log("INFO","CACHE_MISS",{requestId:e,imageHash:t.substring(0,10),timestamp:Date.now()})}logApiRequest(e,t,s){this.log("INFO","API_REQUEST",{requestId:e,endpoint:t,method:s,timestamp:Date.now()})}logApiResponse(e,t,s){this.log("INFO","API_RESPONSE",{requestId:e,statusCode:t,responseTime:s,timestamp:Date.now()})}logImageAnalysis(e,t){this.log("INFO","IMAGE_ANALYSIS",{requestId:e,imageHash:t.imageHash.substring(0,10),processingTime:t.processingTime,recommendationsCount:t.recommendationsCount,improvementScore:t.improvementScore,timestamp:Date.now()})}logImageGeneration(e,t){this.log("INFO","IMAGE_GENERATION",{requestId:e,promptLength:t.prompt.length,fidelityLevel:t.fidelityLevel,tokens:t.tokens,cost:t.cost,timestamp:Date.now()})}logError(e,t){this.log("ERROR","ERROR_OCCURRED",{requestId:e,errorMessage:t.message,errorStack:t.stack,timestamp:Date.now()})}logRateLimitHit(e,t){this.log("WARN","RATE_LIMIT_HIT",{userId:e,limitType:t,timestamp:Date.now()})}logPerformanceMetrics(e,t){this.log("INFO","PERFORMANCE_METRICS",{requestId:e,...t,timestamp:Date.now()})}logUserAction(e,t,s){this.log("INFO","USER_ACTION",{userId:e,action:t,metadata:s,timestamp:Date.now()})}logSystemHealth(e){this.log("INFO","SYSTEM_HEALTH",{...e,timestamp:Date.now()})}getLogs(e){let t=this.logs;return e&&(e.level&&(t=t.filter(t=>t.level===e.level)),e.type&&(t=t.filter(t=>t.type===e.type)),e.requestId&&(t=t.filter(t=>t.data.requestId===e.requestId)),e.userId&&(t=t.filter(t=>t.data.userId===e.userId)),e.startTime&&(t=t.filter(t=>t.timestamp>=e.startTime)),e.endTime&&(t=t.filter(t=>t.timestamp<=e.endTime))),t.sort((e,t)=>t.timestamp-e.timestamp),(null==e?void 0:e.limit)&&(t=t.slice(0,e.limit)),t}getAnalytics(e="24h"){const t=Date.now()-this.getTimeRangeMs(e),s=this.logs.filter(e=>e.timestamp>=t),n=s.filter(e=>"API_REQUEST"===e.type).length,i=s.filter(e=>"API_RESPONSE"===e.type&&e.data.statusCode>=200&&e.data.statusCode<300).length,a=s.filter(e=>"ERROR"===e.level).length,o=s.filter(e=>"CACHE_HIT"===e.type).length,r=s.filter(e=>"CACHE_MISS"===e.type).length,l=s.filter(e=>"API_RESPONSE"===e.type).map(e=>e.data.responseTime||0),c=l.length>0?l.reduce((e,t)=>e+t,0)/l.length:0,u=s.filter(e=>"IMAGE_GENERATION"===e.type).map(e=>e.data.fidelityLevel||"unknown").reduce((e,t)=>(e[t]=(e[t]||0)+1,e),{}),d=Object.entries(u).map(([e,t])=>({type:e,count:t})).sort((e,t)=>t.count-e.count),m=s.reduce((e,t)=>{const s=new Date(t.timestamp).getHours();return e[s]=(e[s]||0)+1,e},{}),p=Object.entries(m).map(([e,t])=>({hour:parseInt(e),count:t})).sort((e,t)=>t.count-e.count).slice(0,5),g=s.filter(e=>"IMAGE_GENERATION"===e.type),h=g.reduce((e,t)=>e+(t.data.cost||0),0),f=g.length>0?h/g.length:0,y=g.reduce((e,t)=>{const s=t.data.fidelityLevel||"unknown";return e[s]=(e[s]||0)+(t.data.cost||0),e},{});return{totalRequests:n,successRate:n>0?i/n*100:0,averageResponseTime:c,errorRate:n>0?a/n*100:0,cacheHitRate:o+r>0?o/(o+r)*100:0,popularEditingTypes:d,peakHours:p,costAnalysis:{totalCost:h,averageCostPerRequest:f,costByType:Object.entries(y).map(([e,t])=>({type:e,cost:t}))}}}exportLogs(e="json"){if("json"===e)return JSON.stringify(this.logs,null,2);const t=this.logs.map(e=>[new Date(e.timestamp).toISOString(),e.level,e.type,e.data.requestId||"",JSON.stringify(e.data)]);return[["timestamp","level","type","requestId","data"].join(","),...t.map(e=>e.join(","))].join("\n")}clearLogs(){this.logs=[],this.persistLogs()}log(e,t,s){if(!this.shouldLog(e))return;const n={timestamp:Date.now(),level:e,type:t,data:s};this.logs.push(n),this.logs.length>this.maxLogs&&(this.logs=this.logs.slice(-this.maxLogs)),this.persistLogs()}shouldLog(e){const t=["DEBUG","INFO","WARN","ERROR"],s=t.indexOf(this.logLevel);return t.indexOf(e)>=s}getTimeRangeMs(e){const t={"1h":36e5,"24h":864e5,"7d":6048e5,"30d":2592e6};return t[e]||t["24h"]}persistLogs(){try{const e=this.logs.slice(-100);localStorage.setItem("tinderop_logs",JSON.stringify(e))}catch(e){console.warn("Failed to persist logs:",e)}}loadPersistedLogs(){try{const e=localStorage.getItem("tinderop_logs");if(e){const t=JSON.parse(e);this.logs=t}}catch(e){console.warn("Failed to load persisted logs:",e)}}startHealthMonitoring(){this.loadPersistedLogs(),setInterval(()=>{this.checkSystemHealth()},3e5)}checkSystemHealth(){const e=this.logs.filter(e=>e.timestamp>Date.now()-3e5),t=e.filter(e=>"ERROR"===e.level),s=e.filter(e=>"API_REQUEST"===e.type).length,n=e.filter(e=>"API_RESPONSE"===e.type&&e.data.statusCode>=200&&e.data.statusCode<300).length,i=s>0?t.length/s*100:0,a=s>0?n/s*100:100,o=e.filter(e=>"API_RESPONSE"===e.type).map(e=>e.data.responseTime||0),r=o.length>0?o.reduce((e,t)=>e+t,0)/o.length:0;let l="healthy";i>50||a<50?l="down":(i>10||r>1e4)&&(l="degraded");const c={status:l,lastCheck:Date.now(),responseTime:r,errorRate:i,queueSize:0,cacheHitRate:this.calculateCacheHitRate(),activeRequests:0};this.logSystemHealth(c)}calculateCacheHitRate(){const e=this.logs.filter(e=>e.timestamp>Date.now()-36e5),t=e.filter(e=>"CACHE_HIT"===e.type).length,s=t+e.filter(e=>"CACHE_MISS"===e.type).length;return s>0?t/s*100:0}};const gt=new class{constructor(){__publicField(this,"queue",[]),__publicField(this,"processing",new Map),__publicField(this,"maxConcurrentRequests",3),__publicField(this,"maxQueueSize",100),__publicField(this,"maxRetries",3),__publicField(this,"retryDelay",5e3),__publicField(this,"processingInterval"),__publicField(this,"isProcessing",!1),this.startProcessing()}async enqueue(e,t=0){if(this.queue.length>=this.maxQueueSize)throw new Error("Queue is full. Please try again later.");const s=this.generateQueueId(),n={id:s,priority:t,request:e,retryCount:0,maxRetries:this.maxRetries,createdAt:Date.now()};return this.insertByPriority(n),pt.logUserAction(e.userId,"queue_added",{queueId:s,priority:t,queueSize:this.queue.length}),s}getQueueStatus(){const e=Date.now(),t=this.queue.map(t=>e-t.createdAt),s=t.length>0?t.reduce((e,t)=>e+t,0)/t.length:0,n=3e4*this.queue.length/this.maxConcurrentRequests;return{queueSize:this.queue.length,processing:this.processing.size,averageWaitTime:s,estimatedWaitTime:n}}getItemStatus(e){if(this.processing.has(e))return{status:"processing"};const t=this.queue.findIndex(t=>t.id===e);if(t>=0){return{status:"queued",position:t+1,estimatedTime:3e4*t,retryCount:this.queue[t].retryCount}}return{status:"not_found"}}remove(e){const t=this.queue.findIndex(t=>t.id===e);return t>=0&&(this.queue.splice(t,1),pt.logUserAction("system","queue_removed",{queueId:e}),!0)}clear(){this.queue=[],pt.logUserAction("system","queue_cleared",{timestamp:Date.now()})}pause(){this.isProcessing=!1,this.processingInterval&&clearInterval(this.processingInterval),pt.logUserAction("system","queue_paused",{timestamp:Date.now()})}resume(){this.isProcessing=!0,this.startProcessing(),pt.logUserAction("system","queue_resumed",{timestamp:Date.now()})}getAnalytics(){return{totalProcessed:0,successRate:95,averageProcessingTime:25e3,currentThroughput:2.5,peakQueueSize:15,retryRate:5}}startProcessing(){this.processingInterval&&clearInterval(this.processingInterval),this.isProcessing=!0,this.processingInterval=setInterval(()=>{this.processQueue()},1e3)}async processQueue(){if(!this.isProcessing||this.processing.size>=this.maxConcurrentRequests)return;const e=this.queue.shift();if(!e)return;e.processingStartedAt=Date.now();const t=this.processItem(e);this.processing.set(e.id,t);try{const s=await t;this.processing.delete(e.id),pt.logUserAction(e.request.userId,"queue_processed",{queueId:e.id,success:!0,processingTime:Date.now()-(e.processingStartedAt||Date.now()),retryCount:e.retryCount}),this.notifyCompletion(e.id,s)}catch(t){this.processing.delete(e.id),e.retryCount<e.maxRetries?(e.retryCount++,setTimeout(()=>{this.insertByPriority(e)},this.retryDelay*e.retryCount),pt.logUserAction(e.request.userId,"queue_retry",{queueId:e.id,retryCount:e.retryCount,error:t.message})):(pt.logUserAction(e.request.userId,"queue_failed",{queueId:e.id,error:t.message,retryCount:e.retryCount}),this.notifyFailure(e.id,t))}}async processItem(e){const{imageEditingService:t}=await Promise.resolve().then(function(){return ht});return t.analyzeImageForEditing(e.request,{onProgress:t=>{this.notifyProgress(e.id,t)}})}insertByPriority(e){let t=0;for(let s=0;s<this.queue.length;s++){if(this.queue[s].priority<e.priority){t=s;break}t=s+1}this.queue.splice(t,0,e)}generateQueueId(){return`queue_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}notifyCompletion(e,t){const s=new CustomEvent("imageEditingComplete",{detail:{id:e,result:t}});window.dispatchEvent(s)}notifyProgress(e,t){const s=new CustomEvent("imageEditingProgress",{detail:{id:e,progress:t}});window.dispatchEvent(s)}notifyFailure(e,t){const s=new CustomEvent("imageEditingError",{detail:{id:e,error:t.message}});window.dispatchEvent(s)}async enqueueBatch(e,t=0){const s=[];for(const n of e){const i=t+(e.length-s.length),a=await this.enqueue(n,i);s.push(a)}return s}updatePriority(e,t){const s=this.queue.findIndex(t=>t.id===e);if(s>=0){const e=this.queue.splice(s,1)[0];return e.priority=t,this.insertByPriority(e),!0}return!1}getHealth(){const e=[],t=Date.now();this.processing.size>0&&!this.isProcessing&&e.push("Processing stalled"),this.queue.length>.8*this.maxQueueSize&&e.push("Queue nearly full");const s=t-this.queue.reduce((e,t)=>t.createdAt<e?t.createdAt:e,t);return s>3e5&&e.push("Old items in queue"),{isHealthy:0===e.length,issues:e,metrics:{queueSize:this.queue.length,processingCount:this.processing.size,oldestItemAge:s,isProcessing:this.isProcessing}}}};function fileToBase64(e){return new Promise((t,s)=>{const n=new FileReader;n.onload=()=>{const e=n.result.split(",")[1];t(e)},n.onerror=s,n.readAsDataURL(e)})}const SplitComponent=function(){var n,R;const[_,I]=t.useState(null),[C,T]=t.useState(null),[j,k]=t.useState({style:"natural",conservative:!0,maxBudget:5});console.log("🎨 ImageEditorTest: Component initialized",{selectedFile:null==_?void 0:_.name,previewUrl:!!C,editingPreferences:j});const{isLoading:N,progress:E,result:A,error:q,analyzeImage:P,generateEditedImage:M,cancelAnalysis:O,analytics:D}=function(e={}){const{enableQueue:s=!1,enableBatch:n=!1,maxConcurrentRequests:i=3,autoRetry:a=!0,userId:o="anonymous"}=e,[r,l]=t.useState(!1),[c,u]=t.useState(null),[d,m]=t.useState(null),[p,g]=t.useState(null),[h,f]=t.useState(null),[y,v]=t.useState({totalRequests:0,successRate:100,averageProcessingTime:0}),w=t.useRef(null),x=t.useRef(null);t.useEffect(()=>()=>{x.current&&x.current.abort()},[]),t.useEffect(()=>{if(!s)return;const handleQueueProgress=e=>{e.detail.id===w.current&&u(e.detail.progress)},handleQueueComplete=e=>{e.detail.id===w.current&&(m(e.detail.result),l(!1),u(null),w.current=null)},handleQueueError=e=>{e.detail.id===w.current&&(g(e.detail.error),l(!1),u(null),w.current=null)};return window.addEventListener("imageEditingProgress",handleQueueProgress),window.addEventListener("imageEditingComplete",handleQueueComplete),window.addEventListener("imageEditingError",handleQueueError),()=>{window.removeEventListener("imageEditingProgress",handleQueueProgress),window.removeEventListener("imageEditingComplete",handleQueueComplete),window.removeEventListener("imageEditingError",handleQueueError)}},[s]);const b=t.useCallback(async(e,t)=>{try{l(!0),g(null),m(null),u(null);const n=await Be.validateFile(e);if(!n.valid)throw new Error(n.errors.join(", "));const i=e,a=await fileToBase64(i),r=await Be.generateSecureCacheKey(a),c={userId:o,imageBase64:a,imageHash:r,fileName:i.name,mimeType:i.type,preferences:t};let d;if(s){const e=await gt.enqueue(c,0);w.current=e;const t=gt.getItemStatus(e);return f({position:t.position||0,estimatedTime:t.estimatedTime||0,queueSize:gt.getQueueStatus().queueSize}),new Promise((t,s)=>{const checkStatus=()=>{const n=gt.getItemStatus(e);"completed"===n.status?t(d):"failed"===n.status?s(new Error("Queue processing failed")):"not_found"===n.status?s(new Error("Queue item not found")):setTimeout(checkStatus,1e3)};checkStatus()})}return d=await Qe.analyzeImageForEditing(c,{onProgress:e=>{u(e)}}),m(d),C(!0,Date.now()-Date.now()),d}catch(e){const t=e instanceof Error?e.message:"Unknown error occurred";throw g(t),C(!1,0),e}finally{l(!1),u(null)}},[s,o]),R=t.useCallback(async(e,t)=>{try{return l(!0),g(null),await Qe.generateEditedImage(t,e.editingPrompt,"high")}catch(e){const t=e instanceof Error?e.message:"Failed to generate edited image";throw g(t),e}finally{l(!1)}},[]),S=t.useCallback(async()=>{(null==d?void 0:d.originalImageHash)&&console.log("Retrying analysis...")},[d]),_=t.useCallback(()=>{x.current&&x.current.abort(),w.current&&(gt.remove(w.current),w.current=null),l(!1),u(null),g(null)},[]),I=t.useCallback(async(e,t)=>{if(!n)throw new Error("Batch processing is not enabled");try{l(!0),g(null);const s=[];for(const n of e){const e=await Be.validateFile(n);if(!e.valid)throw new Error(`Invalid file ${n.name}: ${e.errors.join(", ")}`);const i=n,a=await fileToBase64(i),r=await Be.generateSecureCacheKey(a);s.push({userId:o,imageBase64:a,imageHash:r,fileName:i.name,mimeType:i.type,preferences:t})}const n=[],a=Math.min(i,s.length);for(let e=0;e<s.length;e+=a){const t=s.slice(e,e+a).map(t=>Qe.analyzeImageForEditing(t,{onProgress:t=>{u({...t,message:`Processing ${e+1} of ${s.length} images...`})}})),i=await Promise.allSettled(t);for(const e of i)"fulfilled"===e.status?n.push(e.value):console.error("Batch processing error:",e.reason)}return n}catch(e){const t=e instanceof Error?e.message:"Batch processing failed";throw g(t),e}finally{l(!1),u(null)}},[n,i,o]),C=t.useCallback((e,t)=>{v(s=>({totalRequests:s.totalRequests+1,successRate:(s.successRate*s.totalRequests+(e?100:0))/(s.totalRequests+1),averageProcessingTime:(s.averageProcessingTime*s.totalRequests+t)/(s.totalRequests+1)}))},[]);return{isLoading:r,progress:c,result:d,error:p,analyzeImage:b,generateEditedImage:R,retryAnalysis:S,cancelAnalysis:_,analyzeBatch:I,queueStatus:h,analytics:y}}({enableQueue:!0,enableBatch:!0,userId:"test-user"});console.log("🔧 ImageEditorTest: useImageEditing state",{isLoading:N,hasProgress:!!E,hasResult:!!A,hasError:!!q,analytics:D});const{queueStatus:U,queueHealth:L,pauseQueue:F,resumeQueue:z,clearQueue:H}=function(){const[e,s]=t.useState({queueSize:0,processing:0,averageWaitTime:0,estimatedWaitTime:0}),[n,i]=t.useState({isHealthy:!0,issues:[],metrics:{queueSize:0,processingCount:0,oldestItemAge:0,isProcessing:!0}}),[a,o]=t.useState({totalProcessed:0,successRate:0,averageProcessingTime:0,currentThroughput:0,peakQueueSize:0,retryRate:0});return t.useEffect(()=>{const updateStatus=()=>{const e=gt.getQueueStatus();s(e);const t=gt.getHealth();i(t);const n=gt.getAnalytics();o(n)};updateStatus();const e=setInterval(updateStatus,2e3);return()=>clearInterval(e)},[]),{queueStatus:e,queueHealth:n,pauseQueue:t.useCallback(()=>{gt.pause()},[]),resumeQueue:t.useCallback(()=>{gt.resume()},[]),clearQueue:t.useCallback(()=>{gt.clear()},[]),getItemStatus:t.useCallback(e=>gt.getItemStatus(e),[]),removeItem:t.useCallback(e=>gt.remove(e),[]),updateItemPriority:t.useCallback((e,t)=>gt.updatePriority(e,t),[]),analytics:a}}();console.log("📋 ImageEditorTest: Queue status",{queueStatus:U,queueHealth:L});const{analytics:$,recentErrors:B,systemHealth:G,refreshAnalytics:W,exportAnalytics:Q}=function(){const[e,s]=t.useState("24h"),[n,i]=t.useState({}),[a,o]=t.useState({totalRequests:0,successRate:0,averageResponseTime:0,errorRate:0,cacheHitRate:0,popularEditingTypes:[],peakHours:[],costAnalysis:{totalCost:0,averageCostPerRequest:0,costByType:[]}}),[r,l]=t.useState([]),[c,u]=t.useState({status:"healthy",lastCheck:Date.now(),responseTime:0,errorRate:0,queueSize:0,cacheHitRate:0,activeRequests:0}),d=t.useCallback(()=>{const t=pt.getAnalytics(e);o(t);const s=pt.getLogs({level:"ERROR",limit:20,...n}).map(e=>({timestamp:new Date(e.timestamp).toISOString(),context:e.data.context||"Unknown",message:e.data.errorMessage||"Unknown error",retryCount:e.data.retryCount||0}));l(s);const i=pt.getLogs({type:"SYSTEM_HEALTH",limit:1});if(i.length>0){const e=i[0].data;u({status:e.status||"healthy",lastCheck:e.lastCheck||Date.now(),responseTime:e.responseTime||0,errorRate:e.errorRate||0,queueSize:e.queueSize||0,cacheHitRate:e.cacheHitRate||0,activeRequests:e.activeRequests||0})}},[e,n]);t.useEffect(()=>{d();const e=setInterval(d,3e4);return()=>clearInterval(e)},[d]);const m=t.useCallback(e=>(pt.getLogs(n),pt.exportLogs(e)),[n]),p=t.useCallback(()=>{pt.clearLogs(),d()},[d]);return{analytics:a,recentErrors:r,systemHealth:c,refreshAnalytics:d,exportAnalytics:m,clearAnalytics:p,setTimeRange:s,setFilters:i}}();console.log("📊 ImageEditorTest: Analytics",{detailedAnalytics:$,recentErrors:null==B?void 0:B.length,systemHealth:G});const J=t.useCallback(e=>{var t;const s=null==(t=e.target.files)?void 0:t[0];if(console.log("📁 ImageEditorTest: File selected",{fileName:null==s?void 0:s.name,fileSize:null==s?void 0:s.size,fileType:null==s?void 0:s.type}),s){I(s);const e=URL.createObjectURL(s);T(e),console.log("🖼️ ImageEditorTest: Preview URL created",{url:e})}},[]),V=t.useCallback(async()=>{if(_){console.log("🔍 ImageEditorTest: Starting analysis",{fileName:_.name,preferences:j});try{const e=await P(_,j);console.log("✅ ImageEditorTest: Analysis completed",{analysisResult:e})}catch(e){console.error("❌ ImageEditorTest: Analysis failed",{error:e})}}else console.warn("⚠️ ImageEditorTest: No file selected for analysis")},[_,j,P]),K=t.useCallback(async t=>{if(C){console.log("🎨 ImageEditorTest: Starting image generation",{recommendation:t});try{const s=await fetch(C),n=await s.blob(),i=new FileReader;i.onload=async()=>{const s=i.result.split(",")[1];console.log("🔄 ImageEditorTest: Converting to base64 completed");const n=await M(t,s);console.log("✅ ImageEditorTest: Image generation completed",{editedImageLength:n.length});const a=new Blob([e.from(n,"base64")],{type:"image/png"}),o=URL.createObjectURL(a);console.log("🖼️ ImageEditorTest: Edited image URL created",{editedUrl:o})},i.readAsDataURL(n)}catch(e){console.error("❌ ImageEditorTest: Image generation failed",{error:e})}}else console.warn("⚠️ ImageEditorTest: No preview URL for image generation")},[C,M]);return s.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[s.jsxs("div",{className:"text-center space-y-2",children:[s.jsx("h1",{className:"text-3xl font-bold",children:"🧪 Image Editor Test Lab"}),s.jsx("p",{className:"text-muted-foreground",children:"Test and debug the AI-powered image editing system"})]}),s.jsxs(te,{defaultValue:"upload",className:"w-full",children:[s.jsxs(se,{className:"grid w-full grid-cols-5",children:[s.jsx(ne,{value:"upload",children:"Upload & Test"}),s.jsx(ne,{value:"queue",children:"Queue"}),s.jsx(ne,{value:"analytics",children:"Analytics"}),s.jsx(ne,{value:"health",children:"Health"}),s.jsx(ne,{value:"settings",children:"Settings"})]}),s.jsxs(ie,{value:"upload",className:"space-y-6",children:[s.jsxs(i,{children:[s.jsxs(a,{children:[s.jsxs(o,{className:"flex items-center gap-2",children:[s.jsx(c,{className:"w-5 h-5"}),"Upload Test Image"]}),s.jsx(r,{children:"Select an image to test the editing functionality"})]}),s.jsxs(l,{className:"space-y-4",children:[s.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[s.jsx("input",{type:"file",accept:"image/*",onChange:J,className:"hidden",id:"file-upload"}),s.jsxs("label",{htmlFor:"file-upload",className:"cursor-pointer",children:[s.jsx(c,{className:"mx-auto h-12 w-12 text-gray-400"}),s.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Click to upload or drag and drop"}),s.jsx("p",{className:"text-xs text-gray-500",children:"PNG, JPG, GIF up to 10MB"})]})]}),C&&s.jsxs("div",{className:"mt-4",children:[s.jsx("img",{src:C,alt:"Preview",className:"max-w-full h-64 object-contain mx-auto rounded-lg border"}),s.jsxs("p",{className:"text-sm text-center mt-2 text-gray-600",children:[null==_?void 0:_.name," (",((null==_?void 0:_.size)||0).toFixed(2)," MB)"]})]})]})]}),s.jsxs(i,{children:[s.jsx(a,{children:s.jsxs(o,{className:"flex items-center gap-2",children:[s.jsx(u,{className:"w-5 h-5"}),"Test Settings"]})}),s.jsx(l,{className:"space-y-4",children:s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx(mt,{children:"Style"}),s.jsxs("select",{value:j.style,onChange:e=>k(t=>({...t,style:e.target.value})),className:"w-full p-2 border rounded",children:[s.jsx("option",{value:"natural",children:"Natural"}),s.jsx("option",{value:"dramatic",children:"Dramatic"}),s.jsx("option",{value:"artistic",children:"Artistic"})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(mt,{children:"Conservative Mode"}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(ut,{checked:j.conservative,onCheckedChange:e=>k(t=>({...t,conservative:e}))}),s.jsx("span",{className:"text-sm",children:j.conservative?"On":"Off"})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs(mt,{children:["Max Budget: $",j.maxBudget]}),s.jsx(ae,{value:[j.maxBudget],onValueChange:([e])=>k(t=>({...t,maxBudget:e})),max:20,min:1,step:.5,className:"w-full"})]})]})})]}),s.jsxs("div",{className:"flex gap-4 justify-center",children:[s.jsx(d,{onClick:V,disabled:!_||N,size:"lg",children:N?s.jsxs(s.Fragment,{children:[s.jsx(m,{className:"w-4 h-4 mr-2 animate-spin"}),"Analyzing..."]}):s.jsxs(s.Fragment,{children:[s.jsx(p,{className:"w-4 h-4 mr-2"}),"Start Analysis"]})}),N&&s.jsx(d,{onClick:O,variant:"outline",size:"lg",children:"Cancel"})]}),E&&s.jsxs(i,{children:[s.jsx(a,{children:s.jsxs(o,{className:"flex items-center gap-2",children:[s.jsx(g,{className:"w-5 h-5"}),"Processing Progress"]})}),s.jsx(l,{children:s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{children:E.stage}),s.jsxs("span",{children:[E.percentage,"%"]})]}),s.jsx(ee,{value:E.percentage,className:"w-full"}),E.estimatedTimeRemaining&&s.jsxs("p",{className:"text-sm text-gray-600",children:["Estimated time remaining: ",E.estimatedTimeRemaining,"s"]})]})})]}),q&&s.jsxs(lt,{variant:"destructive",children:[s.jsx(h,{className:"h-4 w-4"}),s.jsxs(ct,{children:[s.jsx("strong",{children:"Error:"})," ",q]})]}),A&&s.jsxs(i,{children:[s.jsx(a,{children:s.jsxs(o,{className:"flex items-center gap-2",children:[s.jsx(f,{className:"w-5 h-5"}),"Analysis Results"]})}),s.jsx(l,{children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold",children:A.overallScore}),s.jsx("div",{className:"text-sm text-gray-600",children:"Overall Score"})]}),s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold",children:(null==(n=A.recommendations)?void 0:n.length)||0}),s.jsx("div",{className:"text-sm text-gray-600",children:"Recommendations"})]}),s.jsxs("div",{children:[s.jsxs("div",{className:"text-2xl font-bold",children:["$",(null==(R=A.estimatedCost)?void 0:R.toFixed(2))||"0.00"]}),s.jsx("div",{className:"text-sm text-gray-600",children:"Est. Cost"})]}),s.jsxs("div",{children:[s.jsxs("div",{className:"text-2xl font-bold",children:[A.processingTime||0,"s"]}),s.jsx("div",{className:"text-sm text-gray-600",children:"Process Time"})]})]}),A.recommendations&&A.recommendations.length>0&&s.jsxs("div",{className:"space-y-3",children:[s.jsx("h4",{className:"font-medium",children:"Editing Recommendations:"}),A.recommendations.map((e,t)=>{var n;return s.jsxs("div",{className:"border rounded-lg p-4 space-y-2",children:[s.jsxs("div",{className:"flex justify-between items-start",children:[s.jsx("h5",{className:"font-medium",children:e.type}),s.jsxs("div",{className:"flex gap-2",children:[s.jsxs(S,{variant:"outline",children:["$",(null==(n=e.estimatedCost)?void 0:n.toFixed(2))||"0.00"]}),s.jsx(S,{variant:"outline",children:e.difficulty||"Unknown"})]})]}),s.jsx("p",{className:"text-sm text-gray-600",children:e.description||"No description available"}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsxs("span",{className:"text-sm font-medium",children:["Expected: ",e.expectedImprovement||"Unknown"]}),s.jsx(d,{size:"sm",onClick:()=>K(e),children:"Generate Edit"})]})]},t)})]})]})})]})]}),s.jsx(ie,{value:"queue",className:"space-y-6",children:s.jsxs(i,{children:[s.jsx(a,{children:s.jsxs(o,{className:"flex items-center gap-2",children:[s.jsx(y,{className:"w-5 h-5"}),"Queue Management"]})}),s.jsx(l,{children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex gap-2",children:[s.jsxs(d,{onClick:F,variant:"outline",size:"sm",children:[s.jsx(v,{className:"w-4 h-4 mr-2"}),"Pause Queue"]}),s.jsxs(d,{onClick:z,variant:"outline",size:"sm",children:[s.jsx(w,{className:"w-4 h-4 mr-2"}),"Resume Queue"]}),s.jsxs(d,{onClick:H,variant:"outline",size:"sm",children:[s.jsx(x,{className:"w-4 h-4 mr-2"}),"Clear Queue"]})]}),U&&s.jsxs("div",{className:"grid grid-cols-3 gap-4 text-center",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold",children:U.position}),s.jsx("div",{className:"text-sm text-gray-600",children:"Position"})]}),s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold",children:U.queueSize}),s.jsx("div",{className:"text-sm text-gray-600",children:"Queue Size"})]}),s.jsxs("div",{children:[s.jsxs("div",{className:"text-2xl font-bold",children:[U.estimatedTime,"s"]}),s.jsx("div",{className:"text-sm text-gray-600",children:"Est. Time"})]})]}),L&&s.jsxs("div",{className:"mt-4",children:[s.jsx("h4",{className:"font-medium mb-2",children:"Queue Health"}),s.jsxs("div",{className:"text-sm space-y-1",children:[s.jsxs("div",{children:["Status: ",s.jsx(S,{children:L.status})]}),s.jsxs("div",{children:["Processing Rate: ",L.processingRate,"/min"]}),s.jsxs("div",{children:["Error Rate: ",L.errorRate,"%"]})]})]})]})})]})}),s.jsx(ie,{value:"analytics",className:"space-y-6",children:s.jsxs(i,{children:[s.jsxs(a,{children:[s.jsxs(o,{className:"flex items-center gap-2",children:[s.jsx(y,{className:"w-5 h-5"}),"System Analytics"]}),s.jsxs("div",{className:"flex gap-2",children:[s.jsxs(d,{onClick:W,variant:"outline",size:"sm",children:[s.jsx(m,{className:"w-4 h-4 mr-2"}),"Refresh"]}),s.jsxs(d,{onClick:Q,variant:"outline",size:"sm",children:[s.jsx(b,{className:"w-4 h-4 mr-2"}),"Export"]})]})]}),s.jsxs(l,{children:[s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold",children:$.totalRequests}),s.jsx("div",{className:"text-sm text-gray-600",children:"Total Requests"})]}),s.jsxs("div",{children:[s.jsxs("div",{className:"text-2xl font-bold",children:[$.successRate.toFixed(1),"%"]}),s.jsx("div",{className:"text-sm text-gray-600",children:"Success Rate"})]}),s.jsxs("div",{children:[s.jsxs("div",{className:"text-2xl font-bold",children:[$.averageProcessingTime.toFixed(1),"s"]}),s.jsx("div",{className:"text-sm text-gray-600",children:"Avg Process Time"})]}),s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold",children:(null==B?void 0:B.length)||0}),s.jsx("div",{className:"text-sm text-gray-600",children:"Recent Errors"})]})]}),B&&B.length>0&&s.jsxs("div",{className:"mt-6",children:[s.jsx("h4",{className:"font-medium mb-2",children:"Recent Errors"}),s.jsx("div",{className:"space-y-2",children:B.slice(0,5).map((e,t)=>s.jsxs("div",{className:"text-sm p-2 bg-red-50 rounded border-l-4 border-red-400",children:[s.jsx("div",{className:"font-medium",children:e.type}),s.jsx("div",{className:"text-gray-600",children:e.message}),s.jsx("div",{className:"text-xs text-gray-500",children:e.timestamp})]},t))})]})]})]})}),s.jsx(ie,{value:"health",className:"space-y-6",children:s.jsxs(i,{children:[s.jsx(a,{children:s.jsxs(o,{className:"flex items-center gap-2",children:[s.jsx(h,{className:"w-5 h-5"}),"System Health"]})}),s.jsx(l,{children:G?s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[s.jsxs("div",{className:"text-center",children:[s.jsx(S,{variant:"healthy"===G.status?"default":"destructive",children:G.status}),s.jsx("div",{className:"text-sm text-gray-600 mt-1",children:"Overall Status"})]}),s.jsxs("div",{className:"text-center",children:[s.jsxs("div",{className:"text-2xl font-bold",children:[G.uptime,"h"]}),s.jsx("div",{className:"text-sm text-gray-600",children:"Uptime"})]}),s.jsxs("div",{className:"text-center",children:[s.jsxs("div",{className:"text-2xl font-bold",children:[G.memoryUsage,"%"]}),s.jsx("div",{className:"text-sm text-gray-600",children:"Memory Usage"})]})]}),G.services&&s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium mb-2",children:"Service Status"}),s.jsx("div",{className:"space-y-2",children:Object.entries(G.services).map(([e,t])=>s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-sm",children:e}),s.jsx(S,{variant:"healthy"===t?"default":"destructive",children:t})]},e))})]})]}):s.jsx("p",{className:"text-gray-600",children:"Loading system health data..."})})]})}),s.jsx(ie,{value:"settings",className:"space-y-6",children:s.jsxs(i,{children:[s.jsxs(a,{children:[s.jsx(o,{children:"Debug Settings"}),s.jsx(r,{children:"Configure test environment settings"})]}),s.jsx(l,{children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(mt,{children:"Verbose Logging"}),s.jsx(ut,{})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(mt,{children:"Mock API Responses"}),s.jsx(ut,{})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(mt,{children:"Enable Queue"}),s.jsx(ut,{defaultChecked:!0})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(mt,{children:"Enable Batch Processing"}),s.jsx(ut,{defaultChecked:!0})]})]})})]})})]})]})},ht=Object.freeze(Object.defineProperty({__proto__:null,imageEditingService:Qe},Symbol.toStringTag,{value:"Module"}));export{SplitComponent as component};
//# sourceMappingURL=image-editor-test-TjHzd5Ww.mjs.map
