import{C as e,a as s,b as a,c,d as i}from"./card-zxSsGLJg.mjs";import{S as t,B as n,c as r,U as l,C as d,b as o,T as m,i as x,j as h}from"./lucide-react.mjs";import{B as p}from"./badge-Cwk-ZwYz.mjs";import{j,L as g}from"./routeTree.gen-BFK54byf.mjs";import"../nitro/nitro.mjs";import"node:process";import"cloudflare:workers";import"node:events";import"node:buffer";import"node:timers";import"node:stream";import"node:stream/web";import"node:async_hooks";const SplitComponent=function(){return j.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50",children:[j.jsx("header",{className:"border-b bg-white/80 backdrop-blur-sm",children:j.jsx("div",{className:"container mx-auto px-4 py-6",children:j.jsxs("div",{className:"text-center",children:[j.jsx("h1",{className:"text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-2",children:"Basic vs Advanced Analysis"}),j.jsx("p",{className:"text-lg text-gray-600",children:"Compare our free basic analyzers with premium advanced AI analysis"})]})})}),j.jsxs("main",{className:"container mx-auto px-4 py-8",children:[j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-12",children:[j.jsxs(e,{className:"border-2",children:[j.jsxs(s,{className:"text-center",children:[j.jsxs("div",{className:"flex items-center justify-center space-x-2 mb-2",children:[j.jsx(t,{className:"h-6 w-6 text-blue-600"}),j.jsx(a,{className:"text-2xl",children:"Basic Analysis"})]}),j.jsx(c,{children:"Fast, reliable, and completely free"}),j.jsx(p,{variant:"secondary",className:"w-fit mx-auto",children:"Free Forever"})]}),j.jsx(i,{className:"text-center",children:j.jsxs("div",{className:"space-y-4",children:[j.jsx("p",{className:"text-sm text-gray-600",children:"Get instant feedback with our proven AI analysis system. Perfect for quick improvements and general guidance."}),j.jsxs("div",{className:"flex justify-center space-x-4",children:[j.jsx(n,{asChild:!0,children:j.jsxs(g,{to:"/image-analyzer",children:[j.jsx(r,{className:"mr-2 h-4 w-4"}),"Try Image Analyzer"]})}),j.jsx(n,{asChild:!0,variant:"outline",children:j.jsxs(g,{to:"/bio-analyzer",children:[j.jsx(l,{className:"mr-2 h-4 w-4"}),"Try Bio Analyzer"]})})]})]})})]}),j.jsxs(e,{className:"border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-blue-50",children:[j.jsxs(s,{className:"text-center",children:[j.jsxs("div",{className:"flex items-center justify-center space-x-2 mb-2",children:[j.jsx(d,{className:"h-6 w-6 text-purple-600"}),j.jsx(a,{className:"text-2xl",children:"Advanced Analysis"})]}),j.jsx(c,{children:"Professional-grade insights powered by OpenRouter o3"}),j.jsx(p,{className:"w-fit mx-auto bg-purple-600",children:"Premium Feature"})]}),j.jsx(i,{className:"text-center",children:j.jsxs("div",{className:"space-y-4",children:[j.jsx("p",{className:"text-sm text-gray-600",children:"Get expert-level analysis with our most advanced AI system. Comprehensive insights, detailed scoring, and professional recommendations."}),j.jsxs("div",{className:"flex justify-center space-x-4",children:[j.jsx(n,{asChild:!0,className:"bg-gradient-to-r from-purple-600 to-blue-600",children:j.jsxs(g,{to:"/image-analyzer-pro",children:[j.jsx(o,{className:"mr-2 h-4 w-4"}),"Try Advanced Image"]})}),j.jsx(n,{asChild:!0,variant:"outline",className:"border-purple-300",children:j.jsxs(g,{to:"/bio-analyzer-pro",children:[j.jsx(m,{className:"mr-2 h-4 w-4"}),"Try Advanced Bio"]})})]})]})})]})]}),j.jsxs(e,{className:"mb-12",children:[j.jsxs(s,{children:[j.jsx(a,{className:"text-2xl text-center",children:"Feature Comparison"}),j.jsx(c,{className:"text-center",children:"See exactly what you get with each analysis tier"})]}),j.jsx(i,{children:j.jsx("div",{className:"overflow-x-auto",children:j.jsxs("table",{className:"w-full",children:[j.jsx("thead",{children:j.jsxs("tr",{className:"border-b",children:[j.jsx("th",{className:"text-left py-4 px-2 font-semibold",children:"Feature"}),j.jsx("th",{className:"text-center py-4 px-2 font-semibold",children:j.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[j.jsx(t,{className:"h-4 w-4 text-blue-600"}),j.jsx("span",{children:"Basic"})]})}),j.jsx("th",{className:"text-center py-4 px-2 font-semibold",children:j.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[j.jsx(d,{className:"h-4 w-4 text-purple-600"}),j.jsx("span",{children:"Advanced"})]})})]})}),j.jsx("tbody",{children:[{category:"AI Model",basic:"Gemini 2.5 Flash / GPT-4o Mini",advanced:"OpenRouter o3 (Latest Reasoning Model)",basicIcon:"⚡",advancedIcon:"🧠"},{category:"Analysis Depth",basic:"6 Standard Steps",advanced:"Multi-Expert, Multi-Phase Analysis",basicIcon:"📊",advancedIcon:"🔬"},{category:"Expert Perspectives",basic:"Single AI Analysis",advanced:"5 Expert Personas (Photography, Psychology, Fashion, Data Science, Dating Coach)",basicIcon:"👤",advancedIcon:"👥"},{category:"Scoring System",basic:"Simple 0-100 Scores",advanced:"Weighted Sub-Scores, Percentile Rankings, Market Competitiveness",basicIcon:"📈",advancedIcon:"📊"},{category:"Insights Quality",basic:"Generic Recommendations",advanced:"Prioritized, Impact-Scored, Evidence-Based Insights",basicIcon:"💡",advancedIcon:"🎯"},{category:"Reasoning Transparency",basic:"Score + Basic Insights",advanced:"Full Chain-of-Thought, Evidence Citations, Confidence Metrics",basicIcon:"📝",advancedIcon:"🔍"},{category:"Comparative Analysis",basic:"Standalone Assessment",advanced:"Market Positioning, Competitive Benchmarking, Percentile Rankings",basicIcon:"📋",advancedIcon:"📊"},{category:"Personalization",basic:"One-Size-Fits-All",advanced:"Demographic-Aware, Platform-Specific, Target Audience Aligned",basicIcon:"🎨",advancedIcon:"🎯"},{category:"Processing Time",basic:"~30 seconds",advanced:"~2-3 minutes",basicIcon:"⚡",advancedIcon:"⏱️"},{category:"Cost",basic:"Free",advanced:"Premium Feature",basicIcon:"🆓",advancedIcon:"👑"}].map((e,s)=>j.jsxs("tr",{className:"border-b hover:bg-gray-50",children:[j.jsx("td",{className:"py-4 px-2 font-medium",children:e.category}),j.jsx("td",{className:"py-4 px-2 text-center",children:j.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[j.jsx("span",{className:"text-lg",children:e.basicIcon}),j.jsx("span",{className:"text-sm text-gray-600",children:e.basic})]})}),j.jsx("td",{className:"py-4 px-2 text-center",children:j.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[j.jsx("span",{className:"text-lg",children:e.advancedIcon}),j.jsx("span",{className:"text-sm text-gray-700 font-medium",children:e.advanced})]})})]},s))})]})})})]}),j.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-12",children:[j.jsxs(e,{children:[j.jsxs(s,{children:[j.jsxs(a,{className:"flex items-center space-x-2",children:[j.jsx(r,{className:"h-5 w-5 text-purple-600"}),j.jsx("span",{children:"Advanced Image Analysis Features"})]}),j.jsx(c,{children:"Professional photography and psychology insights"})]}),j.jsx(i,{children:j.jsx("div",{className:"space-y-3",children:["Pre-analysis demographic detection","Technical quality assessment","Multi-expert visual analysis","Psychological appeal evaluation","Fashion and style assessment","Market performance prediction","Platform-specific optimization","Improvement impact scoring"].map((e,s)=>j.jsxs("div",{className:"flex items-start space-x-3",children:[j.jsx(x,{className:"h-4 w-4 text-green-500 mt-0.5 flex-shrink-0"}),j.jsx("span",{className:"text-sm",children:e})]},s))})})]}),j.jsxs(e,{children:[j.jsxs(s,{children:[j.jsxs(a,{className:"flex items-center space-x-2",children:[j.jsx(l,{className:"h-5 w-5 text-blue-600"}),j.jsx("span",{children:"Advanced Bio Analysis Features"})]}),j.jsx(c,{children:"Psychological profiling and market optimization"})]}),j.jsx(i,{children:j.jsx("div",{className:"space-y-3",children:["Linguistic analysis (readability, sentiment, grammar)","Big 5 personality trait detection","Attachment style assessment","Emotional intelligence indicators","Market positioning analysis","Target audience alignment","Three optimized bio versions","Conversion potential scoring"].map((e,s)=>j.jsxs("div",{className:"flex items-start space-x-3",children:[j.jsx(x,{className:"h-4 w-4 text-green-500 mt-0.5 flex-shrink-0"}),j.jsx("span",{className:"text-sm",children:e})]},s))})})]})]}),j.jsx(e,{className:"bg-gradient-to-r from-purple-600 to-blue-600 text-white",children:j.jsxs(i,{className:"text-center py-12",children:[j.jsx("h2",{className:"text-3xl font-bold mb-4",children:"Ready to Upgrade Your Analysis?"}),j.jsx("p",{className:"text-lg mb-8 opacity-90",children:"Experience the power of professional-grade AI analysis with our advanced system"}),j.jsxs("div",{className:"flex justify-center space-x-4",children:[j.jsx(n,{size:"lg",variant:"secondary",children:j.jsxs(g,{to:"/image-analyzer-pro",className:"flex items-center",children:["Start Advanced Image Analysis",j.jsx(h,{className:"ml-2 h-5 w-5"})]})}),j.jsx(n,{size:"lg",variant:"outline",className:"border-white text-white hover:bg-white hover:text-purple-600",children:j.jsxs(g,{to:"/bio-analyzer-pro",className:"flex items-center",children:["Start Advanced Bio Analysis",j.jsx(h,{className:"ml-2 h-5 w-5"})]})})]})]})})]})]})};export{SplitComponent as component};
//# sourceMappingURL=analysis-comparison-BLR0YpDD.mjs.map
