import{r as e,j as t,o as s,l as n,p as r,R as o,L as a,c as i}from"./routeTree.gen-BFK54byf.mjs";import{B as c}from"./analysis-CiLigBly.mjs";import{q as l,A as d,S as u,R as m,B as p,L as f,h as v,C as h,k as x,X as g}from"./lucide-react.mjs";import{B as w}from"./badge-Cwk-ZwYz.mjs";import{P as y}from"./progress-WcEdq6Og.mjs";import{T as b,a as N,b as j,c as E}from"./tabs-D9-6MReb.mjs";import{T}from"./textarea-Ca6uXUkL.mjs";import{S as C}from"./slider-n7bLkeqV.mjs";import{a as R}from"./analysis-service-Dnjh1f-a.mjs";import"../nitro/nitro.mjs";import"node:process";import"cloudflare:workers";import"node:events";import"node:buffer";import"node:timers";import"node:stream";import"node:stream/web";import"node:async_hooks";import"./storage-IIfgkkYA.mjs";import"./index2.mjs";function composeEventHandlers(e,t,{checkForDefaultPrevented:s=!0}={}){return function(n){if(e?.(n),!1===s||!n.defaultPrevented)return t?.(n)}}function setRef(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function useComposedRefs(...t){return e.useCallback(function(...e){return t=>{let s=!1;const n=e.map(e=>{const n=setRef(e,t);return s||"function"!=typeof n||(s=!0),n});if(s)return()=>{for(let t=0;t<n.length;t++){const s=n[t];"function"==typeof s?s():setRef(e[t],null)}}}}(...t),t)}function createContextScope(s,n=[]){let r=[];const createScope=()=>{const t=r.map(t=>e.createContext(t));return function(n){const r=n?.[s]||t;return e.useMemo(()=>({[`__scope${s}`]:{...n,[s]:r}}),[n,r])}};return createScope.scopeName=s,[function(n,o){const a=e.createContext(o),i=r.length;r=[...r,o];const Provider=n=>{const{scope:r,children:o,...c}=n,l=r?.[s]?.[i]||a,d=e.useMemo(()=>c,Object.values(c));return t.jsx(l.Provider,{value:d,children:o})};return Provider.displayName=n+"Provider",[Provider,function(t,r){const c=r?.[s]?.[i]||a,l=e.useContext(c);if(l)return l;if(void 0!==o)return o;throw new Error(`\`${t}\` must be used within \`${n}\``)}]},composeContextScopes(createScope,...n)]}function composeContextScopes(...t){const s=t[0];if(1===t.length)return s;const createScope=()=>{const n=t.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(t){const r=n.reduce((e,{useScope:s,scopeName:n})=>({...e,...s(t)[`__scope${n}`]}),{});return e.useMemo(()=>({[`__scope${s.scopeName}`]:r}),[r])}};return createScope.scopeName=s.scopeName,createScope}var S=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((s,n)=>{const r=l(`Primitive.${n}`),o=e.forwardRef((e,s)=>{const{asChild:o,...a}=e,i=o?r:n;return t.jsx(i,{...a,ref:s})});return o.displayName=`Primitive.${n}`,{...s,[n]:o}},{});function dispatchDiscreteCustomEvent(e,t){e&&n.flushSync(()=>e.dispatchEvent(t))}function useCallbackRef(t){const s=e.useRef(t);return e.useEffect(()=>{s.current=t}),e.useMemo(()=>(...e)=>s.current?.(...e),[])}var P,A="dismissableLayer.update",D="dismissableLayer.pointerDownOutside",O="dismissableLayer.focusOutside",I=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),L=e.forwardRef((s,n)=>{const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:o,onPointerDownOutside:a,onFocusOutside:i,onInteractOutside:c,onDismiss:l,...d}=s,u=e.useContext(I),[m,p]=e.useState(null),f=m?.ownerDocument??globalThis?.document,[,v]=e.useState({}),h=useComposedRefs(n,e=>p(e)),x=Array.from(u.layers),[g]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),w=x.indexOf(g),y=m?x.indexOf(m):-1,b=u.layersWithOutsidePointerEventsDisabled.size>0,N=y>=w,j=function(t,s=globalThis?.document){const n=useCallbackRef(t),r=e.useRef(!1),o=e.useRef(()=>{});return e.useEffect(()=>{const handlePointerDown=e=>{if(e.target&&!r.current){let handleAndDispatchPointerDownOutsideEvent2=function(){handleAndDispatchCustomEvent$1(D,n,t,{discrete:!0})};const t={originalEvent:e};"touch"===e.pointerType?(s.removeEventListener("click",o.current),o.current=handleAndDispatchPointerDownOutsideEvent2,s.addEventListener("click",o.current,{once:!0})):handleAndDispatchPointerDownOutsideEvent2()}else s.removeEventListener("click",o.current);r.current=!1},e=window.setTimeout(()=>{s.addEventListener("pointerdown",handlePointerDown)},0);return()=>{window.clearTimeout(e),s.removeEventListener("pointerdown",handlePointerDown),s.removeEventListener("click",o.current)}},[s,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{const t=e.target,s=[...u.branches].some(e=>e.contains(t));N&&!s&&(a?.(e),c?.(e),e.defaultPrevented||l?.())},f),E=function(t,s=globalThis?.document){const n=useCallbackRef(t),r=e.useRef(!1);return e.useEffect(()=>{const handleFocus=e=>{if(e.target&&!r.current){handleAndDispatchCustomEvent$1(O,n,{originalEvent:e},{discrete:!1})}};return s.addEventListener("focusin",handleFocus),()=>s.removeEventListener("focusin",handleFocus)},[s,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{const t=e.target;[...u.branches].some(e=>e.contains(t))||(i?.(e),c?.(e),e.defaultPrevented||l?.())},f);return function(t,s=globalThis?.document){const n=useCallbackRef(t);e.useEffect(()=>{const handleKeyDown=e=>{"Escape"===e.key&&n(e)};return s.addEventListener("keydown",handleKeyDown,{capture:!0}),()=>s.removeEventListener("keydown",handleKeyDown,{capture:!0})},[n,s])}(e=>{y===u.layers.size-1&&(o?.(e),!e.defaultPrevented&&l&&(e.preventDefault(),l()))},f),e.useEffect(()=>{if(m)return r&&(0===u.layersWithOutsidePointerEventsDisabled.size&&(P=f.body.style.pointerEvents,f.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(m)),u.layers.add(m),dispatchUpdate(),()=>{r&&1===u.layersWithOutsidePointerEventsDisabled.size&&(f.body.style.pointerEvents=P)}},[m,f,r,u]),e.useEffect(()=>()=>{m&&(u.layers.delete(m),u.layersWithOutsidePointerEventsDisabled.delete(m),dispatchUpdate())},[m,u]),e.useEffect(()=>{const handleUpdate=()=>v({});return document.addEventListener(A,handleUpdate),()=>document.removeEventListener(A,handleUpdate)},[]),t.jsx(S.div,{...d,ref:h,style:{pointerEvents:b?N?"auto":"none":void 0,...s.style},onFocusCapture:composeEventHandlers(s.onFocusCapture,E.onFocusCapture),onBlurCapture:composeEventHandlers(s.onBlurCapture,E.onBlurCapture),onPointerDownCapture:composeEventHandlers(s.onPointerDownCapture,j.onPointerDownCapture)})});L.displayName="DismissableLayer";var k=e.forwardRef((s,n)=>{const r=e.useContext(I),o=e.useRef(null),a=useComposedRefs(n,o);return e.useEffect(()=>{const e=o.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),t.jsx(S.div,{...s,ref:a})});function dispatchUpdate(){const e=new CustomEvent(A);document.dispatchEvent(e)}function handleAndDispatchCustomEvent$1(e,t,s,{discrete:n}){const r=s.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:s});t&&r.addEventListener(e,t,{once:!0}),n?dispatchDiscreteCustomEvent(r,o):r.dispatchEvent(o)}k.displayName="DismissableLayerBranch";var M=L,_=k,F=globalThis?.document?e.useLayoutEffect:()=>{},B=e.forwardRef((s,n)=>{const{container:o,...a}=s,[i,c]=e.useState(!1);F(()=>c(!0),[]);const l=o||i&&globalThis?.document?.body;return l?r.createPortal(t.jsx(S.div,{...a,ref:n}),l):null});B.displayName="Portal";var Presence=t=>{const{present:s,children:n}=t,r=function(t){const[s,n]=e.useState(),r=e.useRef(null),o=e.useRef(t),a=e.useRef("none"),i=t?"mounted":"unmounted",[c,l]=function(t,s){return e.useReducer((e,t)=>s[e][t]??e,t)}(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return e.useEffect(()=>{const e=getAnimationName(r.current);a.current="mounted"===c?e:"none"},[c]),F(()=>{const e=r.current,s=o.current;if(s!==t){const n=a.current,r=getAnimationName(e);if(t)l("MOUNT");else if("none"===r||"none"===e?.display)l("UNMOUNT");else{l(s&&n!==r?"ANIMATION_OUT":"UNMOUNT")}o.current=t}},[t,l]),F(()=>{if(s){let e;const t=s.ownerDocument.defaultView??window,handleAnimationEnd=n=>{const a=getAnimationName(r.current).includes(n.animationName);if(n.target===s&&a&&(l("ANIMATION_END"),!o.current)){const n=s.style.animationFillMode;s.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===s.style.animationFillMode&&(s.style.animationFillMode=n)})}},handleAnimationStart=e=>{e.target===s&&(a.current=getAnimationName(r.current))};return s.addEventListener("animationstart",handleAnimationStart),s.addEventListener("animationcancel",handleAnimationEnd),s.addEventListener("animationend",handleAnimationEnd),()=>{t.clearTimeout(e),s.removeEventListener("animationstart",handleAnimationStart),s.removeEventListener("animationcancel",handleAnimationEnd),s.removeEventListener("animationend",handleAnimationEnd)}}l("ANIMATION_END")},[s,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:e.useCallback(e=>{r.current=e?getComputedStyle(e):null,n(e)},[])}}(s),o="function"==typeof n?n({present:r.isPresent}):e.Children.only(n),a=useComposedRefs(r.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,s=t&&"isReactWarning"in t&&t.isReactWarning;if(s)return e.ref;if(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,s=t&&"isReactWarning"in t&&t.isReactWarning,s)return e.props.ref;return e.props.ref||e.ref}(o));return"function"==typeof n||r.isPresent?e.cloneElement(o,{ref:a}):null};function getAnimationName(e){return e?.animationName||"none"}Presence.displayName="Presence";var z=o[" useInsertionEffect ".trim().toString()]||F;function useControllableState({prop:t,defaultProp:s,onChange:n=()=>{},caller:r}){const[o,a,i]=function({defaultProp:t,onChange:s}){const[n,r]=e.useState(t),o=e.useRef(n),a=e.useRef(s);return z(()=>{a.current=s},[s]),e.useEffect(()=>{o.current!==n&&(a.current?.(n),o.current=n)},[n,o]),[n,r,a]}({defaultProp:s,onChange:n}),c=void 0!==t,l=c?t:o;{const s=e.useRef(void 0!==t);e.useEffect(()=>{const e=s.current;if(e!==c){const t=e?"controlled":"uncontrolled",s=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${t} to ${s}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}s.current=c},[c,r])}const d=e.useCallback(e=>{if(c){const s=function(e){return"function"==typeof e}(e)?e(t):e;s!==t&&i.current?.(s)}else a(e)},[c,t,a,i]);return[l,d]}var $=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),U=e.forwardRef((e,s)=>t.jsx(S.span,{...e,ref:s,style:{...$,...e.style}}));U.displayName="VisuallyHidden";var K="ToastProvider",[H,W,V]=function(e){const n=e+"CollectionProvider",[r,o]=createContextScope(n),[a,i]=r(n,{collectionRef:{current:null},itemMap:new Map}),CollectionProvider=e=>{const{scope:n,children:r}=e,o=s.useRef(null),i=s.useRef(new Map).current;return t.jsx(a,{scope:n,itemMap:i,collectionRef:o,children:r})};CollectionProvider.displayName=n;const c=e+"CollectionSlot",d=l(c),u=s.forwardRef((e,s)=>{const{scope:n,children:r}=e,o=useComposedRefs(s,i(c,n).collectionRef);return t.jsx(d,{ref:o,children:r})});u.displayName=c;const m=e+"CollectionItemSlot",p="data-radix-collection-item",f=l(m),v=s.forwardRef((e,n)=>{const{scope:r,children:o,...a}=e,c=s.useRef(null),l=useComposedRefs(n,c),d=i(m,r);return s.useEffect(()=>(d.itemMap.set(c,{ref:c,...a}),()=>{d.itemMap.delete(c)})),t.jsx(f,{[p]:"",ref:l,children:o})});return v.displayName=m,[{Provider:CollectionProvider,Slot:u,ItemSlot:v},function(t){const n=i(e+"CollectionConsumer",t);return s.useCallback(()=>{const e=n.collectionRef.current;if(!e)return[];const t=Array.from(e.querySelectorAll(`[${p}]`));return Array.from(n.itemMap.values()).sort((e,s)=>t.indexOf(e.ref.current)-t.indexOf(s.ref.current))},[n.collectionRef,n.itemMap])},o]}("Toast"),[q,X]=createContextScope("Toast",[V]),[Y,G]=q(K),ToastProvider$1=s=>{const{__scopeToast:n,label:r="Notification",duration:o=5e3,swipeDirection:a="right",swipeThreshold:i=50,children:c}=s,[l,d]=e.useState(null),[u,m]=e.useState(0),p=e.useRef(!1),f=e.useRef(!1);return r.trim()||console.error(`Invalid prop \`label\` supplied to \`${K}\`. Expected non-empty \`string\`.`),t.jsx(H.Provider,{scope:n,children:t.jsx(Y,{scope:n,label:r,duration:o,swipeDirection:a,swipeThreshold:i,toastCount:u,viewport:l,onViewportChange:d,onToastAdd:e.useCallback(()=>m(e=>e+1),[]),onToastRemove:e.useCallback(()=>m(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:f,children:c})})};ToastProvider$1.displayName=K;var Z="ToastViewport",J=["F8"],Q="toast.viewportPause",ee="toast.viewportResume",te=e.forwardRef((s,n)=>{const{__scopeToast:r,hotkey:o=J,label:a="Notifications ({hotkey})",...i}=s,c=G(Z,r),l=W(r),d=e.useRef(null),u=e.useRef(null),m=e.useRef(null),p=e.useRef(null),f=useComposedRefs(n,p,c.onViewportChange),v=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),h=c.toastCount>0;e.useEffect(()=>{const handleKeyDown=e=>{0!==o.length&&o.every(t=>e[t]||e.code===t)&&p.current?.focus()};return document.addEventListener("keydown",handleKeyDown),()=>document.removeEventListener("keydown",handleKeyDown)},[o]),e.useEffect(()=>{const e=d.current,t=p.current;if(h&&e&&t){const handlePause=()=>{if(!c.isClosePausedRef.current){const e=new CustomEvent(Q);t.dispatchEvent(e),c.isClosePausedRef.current=!0}},handleResume=()=>{if(c.isClosePausedRef.current){const e=new CustomEvent(ee);t.dispatchEvent(e),c.isClosePausedRef.current=!1}},handleFocusOutResume=t=>{!e.contains(t.relatedTarget)&&handleResume()},handlePointerLeaveResume=()=>{e.contains(document.activeElement)||handleResume()};return e.addEventListener("focusin",handlePause),e.addEventListener("focusout",handleFocusOutResume),e.addEventListener("pointermove",handlePause),e.addEventListener("pointerleave",handlePointerLeaveResume),window.addEventListener("blur",handlePause),window.addEventListener("focus",handleResume),()=>{e.removeEventListener("focusin",handlePause),e.removeEventListener("focusout",handleFocusOutResume),e.removeEventListener("pointermove",handlePause),e.removeEventListener("pointerleave",handlePointerLeaveResume),window.removeEventListener("blur",handlePause),window.removeEventListener("focus",handleResume)}}},[h,c.isClosePausedRef]);const x=e.useCallback(({tabbingDirection:e})=>{const t=l().map(t=>{const s=t.ref.current,n=[s,...getTabbableCandidates(s)];return"forwards"===e?n:n.reverse()});return("forwards"===e?t.reverse():t).flat()},[l]);return e.useEffect(()=>{const e=p.current;if(e){const handleKeyDown=t=>{const s=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!s){const s=document.activeElement,n=t.shiftKey;if(t.target===e&&n)return void u.current?.focus();const r=x({tabbingDirection:n?"backwards":"forwards"}),o=r.findIndex(e=>e===s);focusFirst(r.slice(o+1))?t.preventDefault():n?u.current?.focus():m.current?.focus()}};return e.addEventListener("keydown",handleKeyDown),()=>e.removeEventListener("keydown",handleKeyDown)}},[l,x]),t.jsxs(_,{ref:d,role:"region","aria-label":a.replace("{hotkey}",v),tabIndex:-1,style:{pointerEvents:h?void 0:"none"},children:[h&&t.jsx(ne,{ref:u,onFocusFromOutsideViewport:()=>{focusFirst(x({tabbingDirection:"forwards"}))}}),t.jsx(H.Slot,{scope:r,children:t.jsx(S.ol,{tabIndex:-1,...i,ref:f})}),h&&t.jsx(ne,{ref:m,onFocusFromOutsideViewport:()=>{focusFirst(x({tabbingDirection:"backwards"}))}})]})});te.displayName=Z;var se="ToastFocusProxy",ne=e.forwardRef((e,s)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,a=G(se,n);return t.jsx(U,{"aria-hidden":!0,tabIndex:0,...o,ref:s,style:{position:"fixed"},onFocus:e=>{const t=e.relatedTarget;!a.viewport?.contains(t)&&r()}})});ne.displayName=se;var re="Toast",oe=e.forwardRef((e,s)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:a,...i}=e,[c,l]=useControllableState({prop:r,defaultProp:o??!0,onChange:a,caller:re});return t.jsx(Presence,{present:n||c,children:t.jsx(ce,{open:c,...i,ref:s,onClose:()=>l(!1),onPause:useCallbackRef(e.onPause),onResume:useCallbackRef(e.onResume),onSwipeStart:composeEventHandlers(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:composeEventHandlers(e.onSwipeMove,e=>{const{x:t,y:s}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${s}px`)}),onSwipeCancel:composeEventHandlers(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:composeEventHandlers(e.onSwipeEnd,e=>{const{x:t,y:s}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${s}px`),l(!1)})})})});oe.displayName=re;var[ae,ie]=q(re,{onClose(){}}),ce=e.forwardRef((s,r)=>{const{__scopeToast:o,type:a="foreground",duration:i,open:c,onClose:l,onEscapeKeyDown:d,onPause:u,onResume:m,onSwipeStart:p,onSwipeMove:f,onSwipeCancel:v,onSwipeEnd:h,...x}=s,g=G(re,o),[w,y]=e.useState(null),b=useComposedRefs(r,e=>y(e)),N=e.useRef(null),j=e.useRef(null),E=i||g.duration,T=e.useRef(0),C=e.useRef(E),R=e.useRef(0),{onToastAdd:P,onToastRemove:A}=g,D=useCallbackRef(()=>{const e=w?.contains(document.activeElement);e&&g.viewport?.focus(),l()}),O=e.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(R.current),T.current=(new Date).getTime(),R.current=window.setTimeout(D,e))},[D]);e.useEffect(()=>{const e=g.viewport;if(e){const handleResume=()=>{O(C.current),m?.()},handlePause=()=>{const e=(new Date).getTime()-T.current;C.current=C.current-e,window.clearTimeout(R.current),u?.()};return e.addEventListener(Q,handlePause),e.addEventListener(ee,handleResume),()=>{e.removeEventListener(Q,handlePause),e.removeEventListener(ee,handleResume)}}},[g.viewport,E,u,m,O]),e.useEffect(()=>{c&&!g.isClosePausedRef.current&&O(E)},[c,E,g.isClosePausedRef,O]),e.useEffect(()=>(P(),()=>A()),[P,A]);const I=e.useMemo(()=>w?getAnnounceTextContent(w):null,[w]);return g.viewport?t.jsxs(t.Fragment,{children:[I&&t.jsx(ToastAnnounce,{__scopeToast:o,role:"status","aria-live":"foreground"===a?"assertive":"polite","aria-atomic":!0,children:I}),t.jsx(ae,{scope:o,onClose:D,children:n.createPortal(t.jsx(H.ItemSlot,{scope:o,children:t.jsx(M,{asChild:!0,onEscapeKeyDown:composeEventHandlers(d,()=>{g.isFocusedToastEscapeKeyDownRef.current||D(),g.isFocusedToastEscapeKeyDownRef.current=!1}),children:t.jsx(S.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":g.swipeDirection,...x,ref:b,style:{userSelect:"none",touchAction:"none",...s.style},onKeyDown:composeEventHandlers(s.onKeyDown,e=>{"Escape"===e.key&&(d?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(g.isFocusedToastEscapeKeyDownRef.current=!0,D()))}),onPointerDown:composeEventHandlers(s.onPointerDown,e=>{0===e.button&&(N.current={x:e.clientX,y:e.clientY})}),onPointerMove:composeEventHandlers(s.onPointerMove,e=>{if(!N.current)return;const t=e.clientX-N.current.x,s=e.clientY-N.current.y,n=Boolean(j.current),r=["left","right"].includes(g.swipeDirection),o=["left","up"].includes(g.swipeDirection)?Math.min:Math.max,a=r?o(0,t):0,i=r?0:o(0,s),c="touch"===e.pointerType?10:2,l={x:a,y:i},d={originalEvent:e,delta:l};n?(j.current=l,handleAndDispatchCustomEvent("toast.swipeMove",f,d,{discrete:!1})):isDeltaInDirection(l,g.swipeDirection,c)?(j.current=l,handleAndDispatchCustomEvent("toast.swipeStart",p,d,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>c||Math.abs(s)>c)&&(N.current=null)}),onPointerUp:composeEventHandlers(s.onPointerUp,e=>{const t=j.current,s=e.target;if(s.hasPointerCapture(e.pointerId)&&s.releasePointerCapture(e.pointerId),j.current=null,N.current=null,t){const s=e.currentTarget,n={originalEvent:e,delta:t};isDeltaInDirection(t,g.swipeDirection,g.swipeThreshold)?handleAndDispatchCustomEvent("toast.swipeEnd",h,n,{discrete:!0}):handleAndDispatchCustomEvent("toast.swipeCancel",v,n,{discrete:!0}),s.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),g.viewport)})]}):null}),ToastAnnounce=s=>{const{__scopeToast:n,children:r,...o}=s,a=G(re,n),[i,c]=e.useState(!1),[l,d]=e.useState(!1);return function(e=()=>{}){const t=useCallbackRef(e);F(()=>{let e=0,s=0;return e=window.requestAnimationFrame(()=>s=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(s)}},[t])}(()=>c(!0)),e.useEffect(()=>{const e=window.setTimeout(()=>d(!0),1e3);return()=>window.clearTimeout(e)},[]),l?null:t.jsx(B,{asChild:!0,children:t.jsx(U,{...o,children:i&&t.jsxs(t.Fragment,{children:[a.label," ",r]})})})},le=e.forwardRef((e,s)=>{const{__scopeToast:n,...r}=e;return t.jsx(S.div,{...r,ref:s})});le.displayName="ToastTitle";var de=e.forwardRef((e,s)=>{const{__scopeToast:n,...r}=e;return t.jsx(S.div,{...r,ref:s})});de.displayName="ToastDescription";var ue="ToastAction",me=e.forwardRef((e,s)=>{const{altText:n,...r}=e;return n.trim()?t.jsx(ve,{altText:n,asChild:!0,children:t.jsx(fe,{...r,ref:s})}):(console.error(`Invalid prop \`altText\` supplied to \`${ue}\`. Expected non-empty \`string\`.`),null)});me.displayName=ue;var pe="ToastClose",fe=e.forwardRef((e,s)=>{const{__scopeToast:n,...r}=e,o=ie(pe,n);return t.jsx(ve,{asChild:!0,children:t.jsx(S.button,{type:"button",...r,ref:s,onClick:composeEventHandlers(e.onClick,o.onClose)})})});fe.displayName=pe;var ve=e.forwardRef((e,s)=>{const{__scopeToast:n,altText:r,...o}=e;return t.jsx(S.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:s})});function getAnnounceTextContent(e){const t=[];return Array.from(e.childNodes).forEach(e=>{if(e.nodeType===e.TEXT_NODE&&e.textContent&&t.push(e.textContent),function(e){return e.nodeType===e.ELEMENT_NODE}(e)){const s=e.ariaHidden||e.hidden||"none"===e.style.display,n=""===e.dataset.radixToastAnnounceExclude;if(!s)if(n){const s=e.dataset.radixToastAnnounceAlt;s&&t.push(s)}else t.push(...getAnnounceTextContent(e))}}),t}function handleAndDispatchCustomEvent(e,t,s,{discrete:n}){const r=s.originalEvent.currentTarget,o=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:s});t&&r.addEventListener(e,t,{once:!0}),n?dispatchDiscreteCustomEvent(r,o):r.dispatchEvent(o)}var isDeltaInDirection=(e,t,s=0)=>{const n=Math.abs(e.x),r=Math.abs(e.y),o=n>r;return"left"===t||"right"===t?o&&n>s:!o&&r>s};function getTabbableCandidates(e){const t=[],s=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)t.push(s.currentNode);return t}function focusFirst(e){const t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var he=ToastProvider$1,xe=te,ge=oe,we=le,ye=de,be=me,Ne=fe;let je=0;const Ee=new Map,addToRemoveQueue=e=>{if(Ee.has(e))return;const t=setTimeout(()=>{Ee.delete(e),dispatch({type:"REMOVE_TOAST",toastId:e})},5e3);Ee.set(e,t)},reducer=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{const{toastId:s}=t;return s?addToRemoveQueue(s):e.toasts.forEach(e=>{addToRemoveQueue(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},Te=[];let Ce={toasts:[]};function dispatch(e){Ce=reducer(Ce,e),Te.forEach(e=>{e(Ce)})}function toast({...e}){const t=(je=(je+1)%Number.MAX_SAFE_INTEGER,je.toString()),dismiss=()=>dispatch({type:"DISMISS_TOAST",toastId:t});return dispatch({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||dismiss()}}}),{id:t,dismiss:dismiss,update:e=>dispatch({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function useToast(){const[t,s]=e.useState(Ce);return e.useEffect(()=>(Te.push(s),()=>{const e=Te.indexOf(s);e>-1&&Te.splice(e,1)}),[t]),{...t,toast:toast,dismiss:e=>dispatch({type:"DISMISS_TOAST",toastId:e})}}const Re=he,Se=e.forwardRef(({className:e,...s},n)=>t.jsx(xe,{ref:n,className:i("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...s}));Se.displayName=xe.displayName;const Pe=x("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),Ae=e.forwardRef(({className:e,variant:s,...n},r)=>t.jsx(ge,{ref:r,className:i(Pe({variant:s}),e),...n}));Ae.displayName=ge.displayName;e.forwardRef(({className:e,...s},n)=>t.jsx(be,{ref:n,className:i("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...s})).displayName=be.displayName;const De=e.forwardRef(({className:e,...s},n)=>t.jsx(Ne,{ref:n,className:i("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"data-toast-close":"",...s,children:t.jsx(g,{className:"h-4 w-4"})}));De.displayName=Ne.displayName;const Oe=e.forwardRef(({className:e,...s},n)=>t.jsx(we,{ref:n,className:i("text-sm font-semibold",e),...s}));Oe.displayName=we.displayName;const Ie=e.forwardRef(({className:e,...s},n)=>t.jsx(ye,{ref:n,className:i("text-sm opacity-90",e),...s}));function Toaster(){const{toasts:e}=useToast();return t.jsxs(Re,{children:[e.map(({id:e,title:s,description:n,action:r,...o})=>t.jsxs(Ae,{...o,children:[t.jsxs("div",{className:"grid gap-1",children:[s&&t.jsx(Oe,{children:s}),n&&t.jsx(Ie,{children:n})]}),r,t.jsx(De,{})]},e)),t.jsx(Se,{})]})}Ie.displayName=ye.displayName;const BioScoreCard=({result:e})=>{return t.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden animate-fade-in",children:t.jsxs("div",{className:"p-6",children:[t.jsxs("div",{className:"flex justify-between items-center mb-4",children:[t.jsx("h3",{className:"text-xl font-semibold text-graphite-90",children:"Bio Analysis"}),t.jsx("div",{className:"px-4 py-2 text-lg font-bold text-white rounded-full "+(s=e.overallScore,s>=80?"bg-gradient-primary":s>=50?"bg-warning-amber":"bg-error-crimson"),children:e.overallScore})]}),t.jsx("div",{className:"mb-6 space-y-2",children:e.steps.map(e=>t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-graphite-60 font-medium",children:e.stepName}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("span",{className:"font-semibold "+(e.score>=70?"text-success-green":e.score>=50?"text-warning-amber":"text-error-crimson"),children:e.score}),t.jsx("div",{className:"w-16 h-2 bg-gray-200 rounded-full",children:t.jsx("div",{className:"h-full rounded-full "+(e.score>=70?"bg-success-green":e.score>=50?"bg-warning-amber":"bg-error-crimson"),style:{width:`${e.score}%`}})})]})]},e.stepId))}),t.jsxs("div",{className:"mb-4",children:[t.jsx("h4",{className:"font-semibold text-graphite-90 mb-2",children:"Key Recommendations"}),t.jsx("ul",{className:"space-y-2",children:e.recommendations.slice(0,3).map((e,s)=>t.jsxs("li",{className:"flex items-start text-sm text-graphite-60",children:[t.jsx(u,{className:"h-4 w-4 mr-2 mt-0.5 text-flame-red flex-shrink-0"}),t.jsx("span",{children:e})]},s))})]}),e.error&&t.jsx("div",{className:"mt-3 p-3 bg-error-crimson/10 rounded text-sm text-error-crimson",children:e.error})]})});var s},BioAnalyzingCard=({progress:e})=>t.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:t.jsxs("div",{className:"p-6",children:[t.jsxs("div",{className:"mb-4",children:[t.jsx("div",{className:"h-6 w-3/4 rounded bg-gray-200 animate-pulse mb-2"}),t.jsx("div",{className:"h-4 w-1/2 rounded bg-gray-200 animate-pulse"})]}),e&&t.jsxs("div",{className:"space-y-3 mb-6",children:[t.jsxs("div",{className:"flex justify-between text-sm",children:[t.jsxs("span",{className:"text-graphite-60",children:["Step ",e.currentStep,"/5"]}),t.jsxs("span",{className:"text-flame-red font-medium",children:[Math.round(e.progress),"%"]})]}),t.jsx("div",{className:"text-sm text-graphite-90 font-medium",children:e.stepName}),t.jsx(y,{value:e.progress,className:"h-2"})]}),t.jsx("div",{className:"space-y-2",children:c.map(s=>t.jsxs("div",{className:"flex items-center text-sm "+(e&&e.currentStep>s.id?"text-success-green":e&&e.currentStep===s.id?"text-flame-red":"text-gray-400"),children:[t.jsx("div",{className:"w-2 h-2 rounded-full mr-3 "+(e&&e.currentStep>s.id?"bg-success-green":e&&e.currentStep===s.id?"bg-flame-red animate-pulse":"bg-gray-300")}),t.jsx("span",{children:s.name})]},s.id))})]})}),DiffViewer=({original:e,rewritten:s})=>{const n=e.split(/(\s+)/);return t.jsx("p",{className:"text-body-md whitespace-pre-wrap",children:s.split(/(\s+)/).map((e,s)=>n.includes(e)?t.jsx("span",{children:e},s):t.jsx("span",{className:"bg-success-green/20 text-success-green rounded px-1",children:e},s))})};const SplitComponent=function(){const[s,n]=e.useState(""),[r,o]=e.useState(1),[i,c]=e.useState(null),[l,x]=e.useState(null),[g,y]=e.useState("idle"),[S,P]=e.useState(null),{toast:A}=useToast(),D=["Witty","Sincere","Adventurous"],O={0:"witty",1:"sincere",2:"adventurous"},handleCopy=e=>{navigator.clipboard.writeText(e),A({title:"Copied to clipboard!",description:"The bio is now ready to paste."})},I=e.useCallback(async()=>{if(s.length<20)x("Bio must be at least 20 characters.");else if(s.length>500)x("Bio must be 500 characters or less.");else{y("processing"),x(null),P(null);try{await R.analyzeBio(s,O[r],{onProgress:e=>{P(e)},onComplete:e=>{c(e),y("done")},onError:e=>{x(e),y("idle")}})}catch(e){console.error("Bio analysis failed:",e),x("Analysis failed. Please try again."),y("idle")}}},[s,r]);return t.jsxs(t.Fragment,{children:[t.jsx(Toaster,{}),t.jsxs("div",{className:"min-h-screen bg-gray-50",children:[t.jsxs("header",{className:"bg-cloud-white shadow-sm sticky top-0 z-10",children:[t.jsx("div",{className:"container mx-auto px-4 md:px-6 h-16 flex items-center justify-between",children:t.jsxs(a,{to:"/image-analyzer",className:"flex items-center gap-2 text-graphite-60 hover:text-graphite-90",children:[t.jsx(d,{className:"h-5 w-5"}),t.jsx("span",{className:"font-semibold",children:"Back to Images"})]})}),"processing"===g&&S&&t.jsx("div",{className:"h-1 bg-gray-200",children:t.jsx("div",{className:"h-full bg-gradient-primary transition-all duration-300",style:{width:`${S.progress}%`}})})]}),t.jsx("main",{className:"container mx-auto px-4 md:px-6 py-8",children:t.jsxs("div",{className:"max-w-4xl mx-auto",children:[t.jsxs("div",{className:"text-center",children:[t.jsx("h1",{className:"text-h2-mobile md:text-h2",children:"Bio Analyzer"}),t.jsx("p",{className:"text-body-lg text-graphite-60 mt-2",children:"Get AI-powered analysis and optimization for your dating bio."}),t.jsxs("div",{className:"flex justify-center gap-2 mt-4",children:[t.jsxs(w,{variant:"secondary",className:"flex items-center gap-1",children:[t.jsx(u,{className:"h-3 w-3"}),"Multi-step AI analysis"]}),t.jsxs(w,{variant:"outline",className:"flex items-center gap-1",children:[t.jsx(m,{className:"h-3 w-3"}),"Personalized improvements"]})]})]}),"done"!==g&&t.jsxs("div",{className:"mt-8 max-w-2xl mx-auto",children:[t.jsxs("form",{onSubmit:e=>{e.preventDefault(),I()},className:"space-y-6",children:[t.jsxs("div",{children:[t.jsx(T,{placeholder:"Paste your current bio here...",value:s,onChange:e=>n(e.target.value),className:"min-h-[160px] md:min-h-[200px] bg-white",maxLength:500,required:!0,disabled:"processing"===g}),t.jsxs("p",{className:"text-right text-caption text-graphite-60 mt-1",children:[s.length," / 500"]})]}),t.jsxs("div",{children:[t.jsx("label",{className:"font-semibold text-body-md",children:"Select Improvement Tone"}),t.jsxs("div",{className:"mt-4",children:[t.jsx(C,{value:[r],onValueChange:e=>o(e[0]),max:2,step:1,className:"w-full",disabled:"processing"===g}),t.jsx("div",{className:"flex justify-between text-caption text-graphite-60 mt-2",children:D.map(e=>t.jsx("span",{children:e},e))})]})]}),t.jsx(p,{type:"submit",size:"lg",disabled:s.length<20||"processing"===g,className:"w-full",children:"processing"===g?t.jsxs(t.Fragment,{children:[t.jsx(f,{className:"mr-2 h-5 w-5 animate-spin"}),"Analyzing with AI..."]}):"Analyze Bio with AI"})]}),l&&t.jsx("p",{className:"mt-4 text-center text-error-crimson",children:l})]}),"processing"===g&&t.jsx("div",{className:"mt-12 max-w-2xl mx-auto",children:t.jsx(BioAnalyzingCard,{progress:S||void 0})}),"done"===g&&i&&t.jsxs("div",{className:"mt-12",children:[t.jsxs("div",{className:"text-center mb-6",children:[t.jsx("h2",{className:"text-xl font-semibold",children:"Analysis Complete"}),t.jsx("p",{className:"text-graphite-60 mt-2",children:"Your bio has been analyzed using our 5-step AI assessment"})]}),t.jsxs("div",{className:"grid lg:grid-cols-2 gap-8",children:[t.jsx(BioScoreCard,{result:i}),t.jsx("div",{className:"space-y-6",children:t.jsxs(b,{defaultValue:"original",className:"w-full",children:[t.jsxs(N,{className:"grid w-full grid-cols-2",children:[t.jsx(j,{value:"original",children:"Original Bio"}),t.jsx(j,{value:"improved",disabled:!i.improvedBio,children:"AI Improved"})]}),t.jsxs(E,{value:"original",className:"mt-4 p-6 bg-white rounded-lg border",children:[t.jsx("div",{className:"mb-4",children:t.jsx("h4",{className:"font-semibold text-graphite-90 mb-2",children:"Your Original Bio"})}),t.jsx("p",{className:"text-body-md whitespace-pre-wrap text-graphite-70",children:i.originalBio}),t.jsx("div",{className:"mt-4",children:t.jsxs(p,{onClick:()=>handleCopy(i.originalBio),size:"sm",variant:"secondary",children:[t.jsx(v,{className:"mr-2 h-4 w-4"})," Copy Original"]})})]}),i.improvedBio&&t.jsxs(E,{value:"improved",className:"mt-4 p-6 bg-white rounded-lg border",children:[t.jsxs("div",{className:"mb-4",children:[t.jsx("h4",{className:"font-semibold text-graphite-90 mb-2",children:"AI-Improved Bio"}),t.jsxs(w,{variant:"secondary",children:[D[r]," tone"]})]}),t.jsx(DiffViewer,{original:i.originalBio,rewritten:i.improvedBio}),t.jsx("div",{className:"mt-4",children:t.jsxs(p,{onClick:()=>handleCopy(i.improvedBio),size:"sm",children:[t.jsx(v,{className:"mr-2 h-4 w-4"})," Copy Improved"]})})]})]})})]}),t.jsxs("div",{className:"mt-8 text-center space-y-4",children:[t.jsxs("div",{className:"flex justify-center space-x-4",children:[t.jsx(p,{onClick:()=>{y("idle"),c(null),P(null),x(null)},variant:"secondary",children:"Analyze Another Bio"}),t.jsx(p,{asChild:!0,size:"lg",children:t.jsxs(a,{to:"/image-analyzer",children:["Analyze Photos Next ",t.jsx(u,{className:"ml-2 h-5 w-5"})]})})]}),t.jsxs("div",{className:"pt-4 border-t",children:[t.jsx("p",{className:"text-sm text-gray-600 mb-3",children:"Want professional-grade analysis?"}),t.jsx(p,{asChild:!0,variant:"outline",className:"border-purple-300 text-purple-700 hover:bg-purple-50",children:t.jsxs(a,{to:"/bio-analyzer-pro",children:["Try Advanced Analysis ",t.jsx(h,{className:"ml-2 h-4 w-4"})]})})]})]})]})]})})]})]})};export{SplitComponent as component};
//# sourceMappingURL=bio-analyzer-DT9bYKsp.mjs.map
