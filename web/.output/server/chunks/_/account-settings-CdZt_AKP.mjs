import{r as e,j as s,u as a,L as r,c as t}from"./routeTree.gen-BFK54byf.mjs";import{C as i,a as n,b as l,c,d}from"./card-zxSsGLJg.mjs";import{B as o,A as m,d as x,l as h,m as j,n as u,o as p,p as f}from"./lucide-react.mjs";import{B as N}from"./badge-Cwk-ZwYz.mjs";import{P as v}from"./ProtectedRoute-CFonv99q.mjs";import{P as y}from"./index4.mjs";import{U as g}from"./uiComponents.mjs";import"../nitro/nitro.mjs";import"node:process";import"cloudflare:workers";import"node:events";import"node:buffer";import"node:timers";import"node:stream";import"node:stream/web";import"node:async_hooks";var w="horizontal",b=["horizontal","vertical"],A=e.forwardRef((e,a)=>{const{decorative:r,orientation:t=w,...i}=e,n=function(e){return b.includes(e)}(t)?t:w,l=r?{role:"none"}:{"aria-orientation":"vertical"===n?n:void 0,role:"separator"};return s.jsx(y.div,{"data-orientation":n,...l,...i,ref:a})});A.displayName="Separator";var z=A;const S=e.forwardRef(({className:e,orientation:a="horizontal",decorative:r=!0,...i},n)=>s.jsx(z,{ref:n,decorative:r,orientation:a,className:t("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",e),...i}));function AccountSettings(){var e,t;const{user:v}=a();return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:s.jsxs("div",{className:"max-w-4xl mx-auto",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-8",children:[s.jsx(o,{asChild:!0,variant:"ghost",size:"sm",children:s.jsxs(r,{to:"/dashboard",className:"flex items-center gap-2",children:[s.jsx(m,{className:"h-4 w-4"}),"Back to Dashboard"]})}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-slate-900",children:"Account Settings"}),s.jsx("p",{className:"text-slate-600 mt-1",children:"Manage your account preferences and security settings"})]})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[s.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[s.jsxs(i,{children:[s.jsxs(n,{children:[s.jsx(l,{children:"Profile Information"}),s.jsx(c,{children:"Update your personal information and profile details"})]}),s.jsxs(d,{className:"p-6",children:[s.jsx(g,{appearance:{elements:{rootBox:"w-full max-w-full",card:"shadow-none border-none rounded-none w-full max-w-full",navbarMobileMenuButton:"hidden"}}}),s.jsx("div",{className:"space-y-4",children:s.jsxs("div",{className:"text-center py-8",children:[s.jsx("p",{className:"text-muted-foreground",children:"User profile management temporarily disabled."}),s.jsx("p",{className:"text-sm text-muted-foreground mt-2",children:"Authentication system is currently commented out for development."})]})})]})]}),s.jsxs(i,{children:[s.jsxs(n,{children:[s.jsxs(l,{className:"flex items-center gap-2",children:[s.jsx(x,{className:"h-5 w-5 text-green-600"}),"Privacy & Security"]}),s.jsx(c,{children:"Control your privacy settings and security preferences"})]}),s.jsxs(d,{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"font-medium",children:"Two-Factor Authentication"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Add an extra layer of security"})]}),s.jsx(N,{variant:"secondary",children:"Enabled"})]}),s.jsx(S,{}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"font-medium",children:"Data Privacy"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Control how your data is used"})]}),s.jsx(o,{variant:"secondary",size:"sm",children:"Manage"})]}),s.jsx(S,{}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"font-medium",children:"Active Sessions"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"See where you're logged in"})]}),s.jsx(o,{variant:"secondary",size:"sm",children:"View All"})]})]})]}),s.jsxs(i,{children:[s.jsxs(n,{children:[s.jsxs(l,{className:"flex items-center gap-2",children:[s.jsx(h,{className:"h-5 w-5 text-blue-600"}),"Notification Preferences"]}),s.jsx(c,{children:"Choose what notifications you want to receive"})]}),s.jsxs(d,{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"font-medium",children:"Email Notifications"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Get updates via email"})]}),s.jsx(N,{children:"Enabled"})]}),s.jsx(S,{}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"font-medium",children:"Match Alerts"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Notifications for new matches"})]}),s.jsx(N,{children:"Enabled"})]}),s.jsx(S,{}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"font-medium",children:"Analysis Completed"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"When photo/bio analysis is done"})]}),s.jsx(N,{variant:"secondary",children:"Disabled"})]}),s.jsx(S,{}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"font-medium",children:"Weekly Reports"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Performance summaries"})]}),s.jsx(N,{children:"Enabled"})]})]})]})]}),s.jsxs("div",{className:"space-y-6",children:[s.jsxs(i,{children:[s.jsx(n,{children:s.jsx(l,{children:"Account Status"})}),s.jsxs(d,{className:"space-y-4",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-flame-red to-sparks-pink rounded-full mx-auto mb-3 flex items-center justify-center",children:s.jsx("span",{className:"text-white font-bold text-lg",children:(null==(e=null==v?void 0:v.firstName)?void 0:e[0])||"U"})}),s.jsx("p",{className:"font-medium",children:(null==v?void 0:v.fullName)||"User"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:null==(t=null==v?void 0:v.primaryEmailAddress)?void 0:t.emailAddress})]}),s.jsx(S,{}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{children:"Account Type"}),s.jsx(N,{children:"Free"})]}),s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{children:"Member Since"}),s.jsx("span",{children:new Date((null==v?void 0:v.createdAt)||Date.now()).toLocaleDateString()})]})]})]})]}),s.jsxs(i,{children:[s.jsx(n,{children:s.jsxs(l,{className:"flex items-center gap-2",children:[s.jsx(j,{className:"h-5 w-5 text-purple-600"}),"Subscription"]})}),s.jsx(d,{className:"space-y-4",children:s.jsxs("div",{className:"text-center",children:[s.jsx(N,{variant:"secondary",className:"mb-2",children:"Free Plan"}),s.jsx("p",{className:"text-sm text-muted-foreground mb-4",children:"Upgrade for unlimited analyses and premium features"}),s.jsx(o,{className:"w-full bg-gradient-to-r from-flame-red to-sparks-pink hover:opacity-90",children:"Upgrade to Pro"})]})})]}),s.jsxs(i,{children:[s.jsx(n,{children:s.jsx(l,{children:"Data Management"})}),s.jsxs(d,{className:"space-y-3",children:[s.jsxs(o,{variant:"secondary",className:"w-full justify-start",size:"sm",children:[s.jsx(u,{className:"h-4 w-4 mr-2"}),"Export Data"]}),s.jsxs(o,{variant:"secondary",className:"w-full justify-start text-red-600 hover:text-red-700",size:"sm",children:[s.jsx(p,{className:"h-4 w-4 mr-2"}),"Delete Account"]})]})]}),s.jsxs(i,{children:[s.jsx(n,{children:s.jsx(l,{children:"Need Help?"})}),s.jsxs(d,{className:"space-y-3",children:[s.jsx(o,{variant:"secondary",className:"w-full",size:"sm",children:"Contact Support"}),s.jsx(o,{variant:"secondary",className:"w-full",size:"sm",children:"View Documentation"}),s.jsxs("div",{className:"flex items-start gap-2 p-3 bg-amber-50 rounded-lg",children:[s.jsx(f,{className:"h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0"}),s.jsxs("div",{className:"text-sm",children:[s.jsx("p",{className:"font-medium text-amber-800",children:"Having issues?"}),s.jsx("p",{className:"text-amber-700",children:"Check our FAQ or contact support for help."})]})]})]})]})]})]})]})})}S.displayName=z.displayName;const SplitComponent=()=>s.jsx(v,{children:s.jsx(AccountSettings,{})});export{SplitComponent as component};
//# sourceMappingURL=account-settings-CdZt_AKP.mjs.map
