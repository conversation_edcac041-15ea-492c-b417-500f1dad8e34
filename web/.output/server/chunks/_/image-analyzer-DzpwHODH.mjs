import{A as e}from"./analysis-CiLigBly.mjs";import{A as s,d as a,D as r,F as t,X as i,B as l,L as n,S as o,C as c}from"./lucide-react.mjs";import{B as m}from"./badge-Cwk-ZwYz.mjs";import{P as d}from"./progress-WcEdq6Og.mjs";import{a as h}from"./analysis-service-Dnjh1f-a.mjs";import{i as x,r as p,s as g,a as j,c as f}from"./storage-IIfgkkYA.mjs";import{u,P as N}from"./index.mjs";import{r as v,j as b,L as y}from"./routeTree.gen-BFK54byf.mjs";import"../nitro/nitro.mjs";import"node:process";import"cloudflare:workers";import"node:events";import"node:buffer";import"node:timers";import"./index2.mjs";import"./index3.mjs";import"./index4.mjs";import"node:stream";import"node:stream/web";import"node:async_hooks";const ScoreCard=({result:e})=>{return b.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden animate-fade-in",children:[b.jsx("img",{src:e.preview||"/placeholder.svg",alt:e.fileName,className:"w-full h-48 object-cover"}),b.jsxs("div",{className:"p-4",children:[b.jsxs("div",{className:"flex justify-between items-center mb-3",children:[b.jsx("h3",{className:"font-semibold text-graphite-90 truncate pr-2",children:e.fileName}),b.jsx("div",{className:"px-3 py-1 text-sm font-bold text-white rounded-full "+(s=e.overallScore,s>=80?"bg-gradient-primary":s>=50?"bg-warning-amber":"bg-error-crimson"),children:e.overallScore})]}),b.jsx("div",{className:"mb-4 space-y-1",children:e.steps.map(e=>b.jsxs("div",{className:"flex justify-between items-center text-xs",children:[b.jsx("span",{className:"text-graphite-60",children:e.stepName}),b.jsx("span",{className:"font-medium "+(e.score>=70?"text-success-green":e.score>=50?"text-warning-amber":"text-error-crimson"),children:e.score})]},e.stepId))}),b.jsx("ul",{className:"space-y-2",children:e.recommendations.slice(0,3).map((e,s)=>b.jsxs("li",{className:"flex items-start text-sm text-graphite-60",children:[b.jsx(o,{className:"h-4 w-4 mr-2 mt-0.5 text-flame-red flex-shrink-0"}),b.jsx("span",{children:e})]},s))}),e.error&&b.jsx("div",{className:"mt-3 p-2 bg-error-crimson/10 rounded text-sm text-error-crimson",children:e.error})]})]});var s},AnalyzingCard=({image:s,progress:a})=>b.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[b.jsx("img",{src:s.preview,alt:s.fileName,className:"w-full h-48 object-cover"}),b.jsxs("div",{className:"p-4",children:[b.jsx("div",{className:"h-6 w-3/4 rounded bg-gray-200 animate-pulse mb-4"}),a&&b.jsxs("div",{className:"space-y-2",children:[b.jsxs("div",{className:"flex justify-between text-sm",children:[b.jsxs("span",{className:"text-graphite-60",children:["Step ",a.currentStep,"/",e.length]}),b.jsxs("span",{className:"text-flame-red font-medium",children:[Math.round(a.progress),"%"]})]}),b.jsx("div",{className:"text-sm text-graphite-90 font-medium",children:a.stepName}),b.jsx(d,{value:a.progress,className:"h-2"})]}),b.jsx("div",{className:"mt-4 space-y-2",children:e.map(e=>b.jsxs("div",{className:"flex items-center text-sm "+(a&&a.currentStep>e.id?"text-success-green":a&&a.currentStep===e.id?"text-flame-red":"text-gray-400"),children:[b.jsx("div",{className:"w-2 h-2 rounded-full mr-2 "+(a&&a.currentStep>e.id?"bg-success-green":a&&a.currentStep===e.id?"bg-flame-red animate-pulse":"bg-gray-300")}),b.jsx("span",{children:e.name})]},e.id))})]})]});const SplitComponent=function(){const[d,w]=v.useState([]),[A,I]=v.useState([]),[S,k]=v.useState("idle"),[z,C]=v.useState({}),[P,F]=v.useState(!1);v.useEffect(()=>(x().then(()=>{F(!0),loadSessionImages()}),()=>{d.forEach(e=>p(e.preview))}),[]);const loadSessionImages=async()=>{try{const e=g.getCurrentSession(),s=(await j.getSessionImages(e.id)).map(e=>({id:e.id,fileName:e.fileName,preview:f(e.blob),storedImage:e}));w(s);const a=[];for(const e of s){const s=g.getAnalysisResult(e.id);s&&a.push(s)}a.length>0&&(I(a),k("done"))}catch(e){console.error("Failed to load session images:",e)}},B=v.useCallback(async e=>{if(P)try{const s=g.getCurrentSession(),a=[];for(const r of e.slice(0,10-d.length)){const e=await j.storeImage(r,s.id);g.addImageToSession(e);const t=await j.getImage(e);t&&a.push({id:e,fileName:r.name,preview:f(t.blob),storedImage:t})}w(e=>[...e,...a])}catch(e){console.error("Failed to store images:",e)}},[d.length,P]),{getRootProps:D,getInputProps:E,isDragActive:L}=u({onDrop:B,accept:{"image/*":[]},maxFiles:10-d.length,disabled:!P||d.length>=10});return b.jsxs("div",{className:"min-h-screen bg-gray-50",children:[b.jsxs("header",{className:"bg-cloud-white shadow-sm sticky top-0 z-10",children:[b.jsxs("div",{className:"container mx-auto px-4 md:px-6 h-16 flex items-center justify-between",children:[b.jsxs(y,{to:"/",className:"flex items-center gap-2 text-graphite-60 hover:text-graphite-90",children:[b.jsx(s,{className:"h-5 w-5"}),b.jsx("span",{className:"font-semibold",children:"Back to Home"})]}),b.jsx(N,{})]}),"processing"===S&&b.jsx("div",{className:"h-1 bg-gray-200",children:b.jsx("div",{className:"h-full bg-gradient-primary transition-all duration-300",style:{width:Object.values(z).reduce((e,s)=>e+s.progress,0)/Math.max(Object.keys(z).length,1)+"%"}})})]}),b.jsx("main",{className:"container mx-auto px-4 md:px-6 py-8",children:b.jsxs("div",{className:"max-w-4xl mx-auto",children:[b.jsxs("div",{className:"text-center",children:[b.jsx("h1",{className:"text-h2-mobile md:text-h2",children:"Image Analyzer"}),b.jsx("p",{className:"text-body-lg text-graphite-60 mt-2",children:"Upload your photos to get AI-powered feedback."}),b.jsxs("div",{className:"flex justify-center gap-2 mt-4",children:[b.jsxs(m,{variant:"secondary",className:"flex items-center gap-1",children:[b.jsx(a,{className:"h-3 w-3"}),"Images not stored on servers"]}),b.jsxs(m,{variant:"outline",className:"flex items-center gap-1",children:[b.jsx(r,{className:"h-3 w-3"}),"Local processing only"]})]})]}),"done"!==S&&b.jsxs("div",{className:"mt-8",children:[b.jsxs("div",{...D(),className:"w-full p-10 border-2 border-dashed rounded-lg text-center cursor-pointer transition-colors "+(L?"border-flame-red bg-flame-red/10":"border-flame-red/50 hover:bg-flame-red/5"),children:[b.jsx("input",{...E()}),b.jsx(t,{className:"mx-auto h-12 w-12 text-flame-red/80"}),b.jsx("p",{className:"mt-4 text-body-md text-graphite-60",children:L?"Drop the files here...":"Drag 'n' drop some files here, or click to select files"}),b.jsx("p",{className:"text-caption text-graphite-60/70 mt-1",children:"Maximum 10 photos"})]}),d.length>0&&b.jsxs("div",{className:"mt-6",children:[b.jsxs("h3",{className:"font-semibold",children:["Uploaded Photos (",d.length,"/10)"]}),b.jsx("ul",{className:"mt-4 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4",children:d.map(e=>b.jsxs("li",{className:"relative group",children:[b.jsx("img",{src:e.preview,alt:e.fileName,className:"w-full h-32 object-cover rounded-md"}),b.jsx("button",{onClick:()=>(async e=>{try{const s=d.find(s=>s.id===e);s&&(p(s.preview),await j.deleteImage(e),g.removeImageFromSession(e),w(s=>s.filter(s=>s.id!==e)),I(e=>e.filter(e=>e.fileName!==s.fileName)))}catch(e){console.error("Failed to remove image:",e)}})(e.id),className:"absolute top-1 right-1 bg-black/50 text-white rounded-full p-0.5 opacity-0 group-hover:opacity-100 transition-opacity",disabled:"processing"===S,children:b.jsx(i,{className:"h-4 w-4"})})]},e.id))}),b.jsx(l,{size:"lg",onClick:async()=>{if(0===d.length)return;k("processing"),I([]),C({});const e=[];try{for(const s of d)await h.analyzeImage(s.storedImage,{onProgress:e=>{C(a=>({...a,[s.id]:e}))},onComplete:a=>{g.saveAnalysisResult(s.id,a),e.push(a),I([...e])},onError:e=>{console.error(`Analysis failed for ${s.fileName}:`,e)}}),d.indexOf(s)<d.length-1&&await new Promise(e=>setTimeout(e,1e3));k("done")}catch(e){console.error("Analysis failed:",e),k("idle")}},disabled:"processing"===S||!P,className:"w-full mt-8",children:"processing"===S?b.jsxs(b.Fragment,{children:[b.jsx(n,{className:"mr-2 h-5 w-5 animate-spin"}),"Analyzing with AI..."]}):`Analyze ${d.length} Photo${1!==d.length?"s":""} with AI`})]})]}),"processing"===S&&b.jsx("div",{className:"mt-12 grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:d.map(e=>b.jsx(AnalyzingCard,{image:e,progress:z[e.id]},e.id))}),"done"===S&&A.length>0&&b.jsxs("div",{className:"mt-12",children:[b.jsxs("div",{className:"text-center mb-6",children:[b.jsx("h2",{className:"text-xl font-semibold",children:"Analysis Results"}),b.jsxs("p",{className:"text-graphite-60 mt-2",children:["Your photos have been analyzed using our ",e.length,"-step AI assessment"]})]}),b.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:A.map(e=>b.jsx(ScoreCard,{result:e},e.fileName))}),b.jsxs("div",{className:"mt-8 text-center space-y-4",children:[b.jsxs("div",{className:"flex justify-center space-x-4",children:[b.jsx(l,{onClick:()=>{k("idle"),I([]),C({})},variant:"secondary",children:"Analyze More Photos"}),b.jsx(l,{asChild:!0,size:"lg",children:b.jsxs(y,{to:"/bio-analyzer",children:["Analyze Bio Next ",b.jsx(o,{className:"ml-2 h-5 w-5"})]})})]}),b.jsxs("div",{className:"pt-4 border-t",children:[b.jsx("p",{className:"text-sm text-gray-600 mb-3",children:"Want professional-grade analysis?"}),b.jsx(l,{asChild:!0,variant:"outline",className:"border-purple-300 text-purple-700 hover:bg-purple-50",children:b.jsxs(y,{to:"/image-analyzer-pro",children:["Try Advanced Analysis ",b.jsx(c,{className:"ml-2 h-4 w-4"})]})})]})]}),b.jsx("div",{className:"fixed bottom-8 right-8",children:b.jsx(l,{asChild:!0,size:"lg",className:"rounded-full shadow-lg",children:b.jsx(y,{to:"/bio-analyzer",children:b.jsx(o,{className:"h-5 w-5"})})})})]})]})})]})};export{SplitComponent as component};
//# sourceMappingURL=image-analyzer-DzpwHODH.mjs.map
