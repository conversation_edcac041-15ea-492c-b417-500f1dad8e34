import{r as e,j as r,c as o}from"./routeTree.gen-BFK54byf.mjs";const s=e.forwardRef(({className:e,...s},a)=>r.jsx("textarea",{className:o("flex w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-flame-red focus-visible:ring-offset-0","min-h-[80px]","disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));s.displayName="Textarea";export{s as T};
//# sourceMappingURL=textarea-Ca6uXUkL.mjs.map
