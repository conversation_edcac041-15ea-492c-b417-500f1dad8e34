import{r as e,j as t,R as n,o,c as r}from"./routeTree.gen-BFK54byf.mjs";import{q as i}from"./lucide-react.mjs";function clamp(e,[t,n]){return Math.min(n,Math.max(t,e))}function composeEventHandlers(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e?.(o),!1===n||!o.defaultPrevented)return t?.(o)}}function setRef(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function useComposedRefs(...t){return e.useCallback(function(...e){return t=>{let n=!1;const o=e.map(e=>{const o=setRef(e,t);return n||"function"!=typeof o||(n=!0),o});if(n)return()=>{for(let t=0;t<o.length;t++){const n=o[t];"function"==typeof n?n():setRef(e[t],null)}}}}(...t),t)}function createContextScope(n,o=[]){let r=[];const createScope=()=>{const t=r.map(t=>e.createContext(t));return function(o){const r=o?.[n]||t;return e.useMemo(()=>({[`__scope${n}`]:{...o,[n]:r}}),[o,r])}};return createScope.scopeName=n,[function(o,i){const s=e.createContext(i),a=r.length;r=[...r,i];const Provider=o=>{const{scope:r,children:i,...l}=o,c=r?.[n]?.[a]||s,u=e.useMemo(()=>l,Object.values(l));return t.jsx(c.Provider,{value:u,children:i})};return Provider.displayName=o+"Provider",[Provider,function(t,r){const l=r?.[n]?.[a]||s,c=e.useContext(l);if(c)return c;if(void 0!==i)return i;throw new Error(`\`${t}\` must be used within \`${o}\``)}]},composeContextScopes(createScope,...o)]}function composeContextScopes(...t){const n=t[0];if(1===t.length)return n;const createScope=()=>{const o=t.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(t){const r=o.reduce((e,{useScope:n,scopeName:o})=>({...e,...n(t)[`__scope${o}`]}),{});return e.useMemo(()=>({[`__scope${n.scopeName}`]:r}),[r])}};return createScope.scopeName=n.scopeName,createScope}var s=globalThis?.document?e.useLayoutEffect:()=>{},a=n[" useInsertionEffect ".trim().toString()]||s;function useControllableState({prop:t,defaultProp:n,onChange:o=()=>{},caller:r}){const[i,s,l]=function({defaultProp:t,onChange:n}){const[o,r]=e.useState(t),i=e.useRef(o),s=e.useRef(n);return a(()=>{s.current=n},[n]),e.useEffect(()=>{i.current!==o&&(s.current?.(o),i.current=o)},[o,i]),[o,r,s]}({defaultProp:n,onChange:o}),c=void 0!==t,u=c?t:i;{const n=e.useRef(void 0!==t);e.useEffect(()=>{const e=n.current;if(e!==c){const t=e?"controlled":"uncontrolled",n=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${t} to ${n}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}n.current=c},[c,r])}const d=e.useCallback(e=>{if(c){const n=function(e){return"function"==typeof e}(e)?e(t):e;n!==t&&l.current?.(n)}else s(e)},[c,t,s,l]);return[u,d]}var l=e.createContext(void 0);var c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((n,o)=>{const r=i(`Primitive.${o}`),s=e.forwardRef((e,n)=>{const{asChild:i,...s}=e,a=i?r:o;return t.jsx(a,{...s,ref:n})});return s.displayName=`Primitive.${o}`,{...n,[o]:s}},{});var u=["PageUp","PageDown"],d=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],f={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},m="Slider",[p,v,h]=function(e){const n=e+"CollectionProvider",[r,s]=createContextScope(n),[a,l]=r(n,{collectionRef:{current:null},itemMap:new Map}),CollectionProvider=e=>{const{scope:n,children:r}=e,i=o.useRef(null),s=o.useRef(new Map).current;return t.jsx(a,{scope:n,itemMap:s,collectionRef:i,children:r})};CollectionProvider.displayName=n;const c=e+"CollectionSlot",u=i(c),d=o.forwardRef((e,n)=>{const{scope:o,children:r}=e,i=useComposedRefs(n,l(c,o).collectionRef);return t.jsx(u,{ref:i,children:r})});d.displayName=c;const f=e+"CollectionItemSlot",m="data-radix-collection-item",p=i(f),v=o.forwardRef((e,n)=>{const{scope:r,children:i,...s}=e,a=o.useRef(null),c=useComposedRefs(n,a),u=l(f,r);return o.useEffect(()=>(u.itemMap.set(a,{ref:a,...s}),()=>{u.itemMap.delete(a)})),t.jsx(p,{[m]:"",ref:c,children:i})});return v.displayName=f,[{Provider:CollectionProvider,Slot:d,ItemSlot:v},function(t){const n=l(e+"CollectionConsumer",t);return o.useCallback(()=>{const e=n.collectionRef.current;if(!e)return[];const t=Array.from(e.querySelectorAll(`[${m}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},s]}(m),[g,S]=createContextScope(m,[h]),[w,b]=g(m),x=e.forwardRef((n,o)=>{const{name:r,min:i=0,max:s=100,step:a=1,orientation:l="horizontal",disabled:c=!1,minStepsBetweenThumbs:f=0,defaultValue:m=[i],value:v,onValueChange:h=()=>{},onValueCommit:g=()=>{},inverted:S=!1,form:b,...x}=n,y=e.useRef(new Set),R=e.useRef(0),E="horizontal"===l?C:P,[M=[],D]=useControllableState({prop:v,defaultProp:m,onChange:e=>{const t=[...y.current];t[R.current]?.focus(),h(e)}}),j=e.useRef(M);function updateValues(e,t,{commit:n}={commit:!1}){const o=function(e){return(String(e).split(".")[1]||"").length}(a),r=function(e,t){const n=Math.pow(10,t);return Math.round(e*n)/n}(Math.round((e-i)/a)*a+i,o),l=clamp(r,[i,s]);D((e=[])=>{const o=function(e=[],t,n){const o=[...e];return o[n]=t,o.sort((e,t)=>e-t)}(e,l,t);if(function(e,t){if(t>0){const n=function(e){return e.slice(0,-1).map((t,n)=>e[n+1]-t)}(e);return Math.min(...n)>=t}return!0}(o,f*a)){R.current=o.indexOf(l);const t=String(o)!==String(e);return t&&n&&g(o),t?o:e}return e})}return t.jsx(w,{scope:n.__scopeSlider,name:r,disabled:c,min:i,max:s,valueIndexToChangeRef:R,thumbs:y.current,values:M,orientation:l,form:b,children:t.jsx(p.Provider,{scope:n.__scopeSlider,children:t.jsx(p.Slot,{scope:n.__scopeSlider,children:t.jsx(E,{"aria-disabled":c,"data-disabled":c?"":void 0,...x,ref:o,onPointerDown:composeEventHandlers(x.onPointerDown,()=>{c||(j.current=M)}),min:i,max:s,inverted:S,onSlideStart:c?void 0:function(e){const t=function(e,t){if(1===e.length)return 0;const n=e.map(e=>Math.abs(e-t)),o=Math.min(...n);return n.indexOf(o)}(M,e);updateValues(e,t)},onSlideMove:c?void 0:function(e){updateValues(e,R.current)},onSlideEnd:c?void 0:function(){const e=j.current[R.current];M[R.current]!==e&&g(M)},onHomeKeyDown:()=>!c&&updateValues(i,0,{commit:!0}),onEndKeyDown:()=>!c&&updateValues(s,M.length-1,{commit:!0}),onStepKeyDown:({event:e,direction:t})=>{if(!c){const n=u.includes(e.key)||e.shiftKey&&d.includes(e.key)?10:1,o=R.current;updateValues(M[o]+a*n*t,o,{commit:!0})}}})})})})});x.displayName=m;var[y,R]=g(m,{startEdge:"left",endEdge:"right",size:"width",direction:1}),C=e.forwardRef((n,o)=>{const{min:r,max:i,dir:s,inverted:a,onSlideStart:c,onSlideMove:u,onSlideEnd:d,onStepKeyDown:m,...p}=n,[v,h]=e.useState(null),g=useComposedRefs(o,e=>h(e)),S=e.useRef(void 0),w=function(t){const n=e.useContext(l);return t||n||"ltr"}(s),b="ltr"===w,x=b&&!a||!b&&a;function getValueFromPointer(e){const t=S.current||v.getBoundingClientRect(),n=linearScale([0,t.width],x?[r,i]:[i,r]);return S.current=t,n(e-t.left)}return t.jsx(y,{scope:n.__scopeSlider,startEdge:x?"left":"right",endEdge:x?"right":"left",direction:x?1:-1,size:"width",children:t.jsx(E,{dir:w,"data-orientation":"horizontal",...p,ref:g,style:{...p.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{const t=getValueFromPointer(e.clientX);c?.(t)},onSlideMove:e=>{const t=getValueFromPointer(e.clientX);u?.(t)},onSlideEnd:()=>{S.current=void 0,d?.()},onStepKeyDown:e=>{const t=f[x?"from-left":"from-right"].includes(e.key);m?.({event:e,direction:t?-1:1})}})})}),P=e.forwardRef((n,o)=>{const{min:r,max:i,inverted:s,onSlideStart:a,onSlideMove:l,onSlideEnd:c,onStepKeyDown:u,...d}=n,m=e.useRef(null),p=useComposedRefs(o,m),v=e.useRef(void 0),h=!s;function getValueFromPointer(e){const t=v.current||m.current.getBoundingClientRect(),n=linearScale([0,t.height],h?[i,r]:[r,i]);return v.current=t,n(e-t.top)}return t.jsx(y,{scope:n.__scopeSlider,startEdge:h?"bottom":"top",endEdge:h?"top":"bottom",size:"height",direction:h?1:-1,children:t.jsx(E,{"data-orientation":"vertical",...d,ref:p,style:{...d.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{const t=getValueFromPointer(e.clientY);a?.(t)},onSlideMove:e=>{const t=getValueFromPointer(e.clientY);l?.(t)},onSlideEnd:()=>{v.current=void 0,c?.()},onStepKeyDown:e=>{const t=f[h?"from-bottom":"from-top"].includes(e.key);u?.({event:e,direction:t?-1:1})}})})}),E=e.forwardRef((e,n)=>{const{__scopeSlider:o,onSlideStart:r,onSlideMove:i,onSlideEnd:s,onHomeKeyDown:a,onEndKeyDown:l,onStepKeyDown:f,...p}=e,v=b(m,o);return t.jsx(c.span,{...p,ref:n,onKeyDown:composeEventHandlers(e.onKeyDown,e=>{"Home"===e.key?(a(e),e.preventDefault()):"End"===e.key?(l(e),e.preventDefault()):u.concat(d).includes(e.key)&&(f(e),e.preventDefault())}),onPointerDown:composeEventHandlers(e.onPointerDown,e=>{const t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),v.thumbs.has(t)?t.focus():r(e)}),onPointerMove:composeEventHandlers(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&i(e)}),onPointerUp:composeEventHandlers(e.onPointerUp,e=>{const t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),s(e))})})}),M="SliderTrack",D=e.forwardRef((e,n)=>{const{__scopeSlider:o,...r}=e,i=b(M,o);return t.jsx(c.span,{"data-disabled":i.disabled?"":void 0,"data-orientation":i.orientation,...r,ref:n})});D.displayName=M;var j="SliderRange",_=e.forwardRef((n,o)=>{const{__scopeSlider:r,...i}=n,s=b(j,r),a=R(j,r),l=useComposedRefs(o,e.useRef(null)),u=s.values.length,d=s.values.map(e=>convertValueToPercentage(e,s.min,s.max)),f=u>1?Math.min(...d):0,m=100-Math.max(...d);return t.jsx(c.span,{"data-orientation":s.orientation,"data-disabled":s.disabled?"":void 0,...i,ref:l,style:{...n.style,[a.startEdge]:f+"%",[a.endEdge]:m+"%"}})});_.displayName=j;var N="SliderThumb",V=e.forwardRef((n,o)=>{const r=v(n.__scopeSlider),[i,s]=e.useState(null),a=useComposedRefs(o,e=>s(e)),l=e.useMemo(()=>i?r().findIndex(e=>e.ref.current===i):-1,[r,i]);return t.jsx(A,{...n,ref:a,index:l})}),A=e.forwardRef((n,o)=>{const{__scopeSlider:r,index:i,name:a,...l}=n,u=b(N,r),d=R(N,r),[f,m]=e.useState(null),v=useComposedRefs(o,e=>m(e)),h=!f||(u.form||!!f.closest("form")),g=function(t){const[n,o]=e.useState(void 0);return s(()=>{if(t){o({width:t.offsetWidth,height:t.offsetHeight});const e=new ResizeObserver(e=>{if(!Array.isArray(e))return;if(!e.length)return;const n=e[0];let r,i;if("borderBoxSize"in n){const e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=t.offsetWidth,i=t.offsetHeight;o({width:r,height:i})});return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}o(void 0)},[t]),n}(f),S=u.values[i],w=void 0===S?0:convertValueToPercentage(S,u.min,u.max),x=function(e,t){return t>2?`Value ${e+1} of ${t}`:2===t?["Minimum","Maximum"][e]:void 0}(i,u.values.length),y=g?.[d.size],C=y?function(e,t,n){const o=e/2,r=linearScale([0,50],[0,o]);return(o-r(t)*n)*n}(y,w,d.direction):0;return e.useEffect(()=>{if(f)return u.thumbs.add(f),()=>{u.thumbs.delete(f)}},[f,u.thumbs]),t.jsxs("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[d.startEdge]:`calc(${w}% + ${C}px)`},children:[t.jsx(p.ItemSlot,{scope:n.__scopeSlider,children:t.jsx(c.span,{role:"slider","aria-label":n["aria-label"]||x,"aria-valuemin":u.min,"aria-valuenow":S,"aria-valuemax":u.max,"aria-orientation":u.orientation,"data-orientation":u.orientation,"data-disabled":u.disabled?"":void 0,tabIndex:u.disabled?void 0:0,...l,ref:v,style:void 0===S?{display:"none"}:n.style,onFocus:composeEventHandlers(n.onFocus,()=>{u.valueIndexToChangeRef.current=i})})}),h&&t.jsx(H,{name:a??(u.name?u.name+(u.values.length>1?"[]":""):void 0),form:u.form,value:S},i)]})});V.displayName=N;var H=e.forwardRef(({__scopeSlider:n,value:o,...r},i)=>{const s=e.useRef(null),a=useComposedRefs(s,i),l=function(t){const n=e.useRef({value:t,previous:t});return e.useMemo(()=>(n.current.value!==t&&(n.current.previous=n.current.value,n.current.value=t),n.current.previous),[t])}(o);return e.useEffect(()=>{const e=s.current;if(!e)return;const t=window.HTMLInputElement.prototype,n=Object.getOwnPropertyDescriptor(t,"value").set;if(l!==o&&n){const t=new Event("input",{bubbles:!0});n.call(e,o),e.dispatchEvent(t)}},[l,o]),t.jsx(c.input,{style:{display:"none"},...r,ref:a,defaultValue:o})});function convertValueToPercentage(e,t,n){return clamp(100/(n-t)*(e-t),[0,100])}function linearScale(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const o=(t[1]-t[0])/(e[1]-e[0]);return t[0]+o*(n-e[0])}}H.displayName="RadioBubbleInput";var k=x,$=D,I=_,K=V;const z=e.forwardRef(({className:e,...n},o)=>t.jsxs(k,{ref:o,className:r("relative flex w-full touch-none select-none items-center",e),...n,children:[t.jsx($,{className:"relative h-1.5 w-full grow overflow-hidden rounded-full bg-graphite-60/20",children:t.jsx(I,{className:"absolute h-full bg-gradient-primary"})}),t.jsx(K,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-gradient-primary shadow-md"})]}));z.displayName=k.displayName;export{z as S};
//# sourceMappingURL=slider-n7bLkeqV.mjs.map
