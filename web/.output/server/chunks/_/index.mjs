import{r as e,R as n,j as a,p as i,s as r,c as o,q as c,o as l}from"./routeTree.gen-BFK54byf.mjs";import{B as p,d as s,D as d,o as u,I as m,X as f}from"./lucide-react.mjs";import{B as v}from"./badge-Cwk-ZwYz.mjs";import{s as x,a as g}from"./storage-IIfgkkYA.mjs";import{u as b,a as h,c as y,b as _,d as w,e as j}from"./index3.mjs";import{u as k,P as S,d as E,c as O}from"./index4.mjs";import{m as P}from"../nitro/nitro.mjs";var C=n["useId".toString()]||(()=>{}),R=0;function useId(n){const[a,i]=e.useState(C());return b(()=>{i(e=>e??String(R++))},[n]),n||(a?`radix-${a}`:"")}var D,A="dismissableLayer.update",T="dismissableLayer.pointerDownOutside",N="dismissableLayer.focusOutside",z=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),M=e.forwardRef((n,i)=>{const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:o,onPointerDownOutside:c,onFocusOutside:l,onInteractOutside:p,onDismiss:s,...d}=n,u=e.useContext(z),[m,f]=e.useState(null),v=m?.ownerDocument??globalThis?.document,[,x]=e.useState({}),g=k(i,e=>f(e)),b=Array.from(u.layers),[_]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),w=b.indexOf(_),j=m?b.indexOf(m):-1,E=u.layersWithOutsidePointerEventsDisabled.size>0,O=j>=w,P=function(n,a=globalThis?.document){const i=h(n),r=e.useRef(!1),o=e.useRef(()=>{});return e.useEffect(()=>{const handlePointerDown=e=>{if(e.target&&!r.current){let handleAndDispatchPointerDownOutsideEvent2=function(){handleAndDispatchCustomEvent(T,i,n,{discrete:!0})};const n={originalEvent:e};"touch"===e.pointerType?(a.removeEventListener("click",o.current),o.current=handleAndDispatchPointerDownOutsideEvent2,a.addEventListener("click",o.current,{once:!0})):handleAndDispatchPointerDownOutsideEvent2()}else a.removeEventListener("click",o.current);r.current=!1},e=window.setTimeout(()=>{a.addEventListener("pointerdown",handlePointerDown)},0);return()=>{window.clearTimeout(e),a.removeEventListener("pointerdown",handlePointerDown),a.removeEventListener("click",o.current)}},[a,i]),{onPointerDownCapture:()=>r.current=!0}}(e=>{const n=e.target,a=[...u.branches].some(e=>e.contains(n));O&&!a&&(c?.(e),p?.(e),e.defaultPrevented||s?.())},v),C=function(n,a=globalThis?.document){const i=h(n),r=e.useRef(!1);return e.useEffect(()=>{const handleFocus=e=>{if(e.target&&!r.current){handleAndDispatchCustomEvent(N,i,{originalEvent:e},{discrete:!1})}};return a.addEventListener("focusin",handleFocus),()=>a.removeEventListener("focusin",handleFocus)},[a,i]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{const n=e.target;[...u.branches].some(e=>e.contains(n))||(l?.(e),p?.(e),e.defaultPrevented||s?.())},v);return function(n,a=globalThis?.document){const i=h(n);e.useEffect(()=>{const handleKeyDown=e=>{"Escape"===e.key&&i(e)};return a.addEventListener("keydown",handleKeyDown,{capture:!0}),()=>a.removeEventListener("keydown",handleKeyDown,{capture:!0})},[i,a])}(e=>{j===u.layers.size-1&&(o?.(e),!e.defaultPrevented&&s&&(e.preventDefault(),s()))},v),e.useEffect(()=>{if(m)return r&&(0===u.layersWithOutsidePointerEventsDisabled.size&&(D=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(m)),u.layers.add(m),dispatchUpdate(),()=>{r&&1===u.layersWithOutsidePointerEventsDisabled.size&&(v.body.style.pointerEvents=D)}},[m,v,r,u]),e.useEffect(()=>()=>{m&&(u.layers.delete(m),u.layersWithOutsidePointerEventsDisabled.delete(m),dispatchUpdate())},[m,u]),e.useEffect(()=>{const handleUpdate=()=>x({});return document.addEventListener(A,handleUpdate),()=>document.removeEventListener(A,handleUpdate)},[]),a.jsx(S.div,{...d,ref:g,style:{pointerEvents:E?O?"auto":"none":void 0,...n.style},onFocusCapture:y(n.onFocusCapture,C.onFocusCapture),onBlurCapture:y(n.onBlurCapture,C.onBlurCapture),onPointerDownCapture:y(n.onPointerDownCapture,P.onPointerDownCapture)})});M.displayName="DismissableLayer";var F=e.forwardRef((n,i)=>{const r=e.useContext(z),o=e.useRef(null),c=k(i,o);return e.useEffect(()=>{const e=o.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),a.jsx(S.div,{...n,ref:c})});function dispatchUpdate(){const e=new CustomEvent(A);document.dispatchEvent(e)}function handleAndDispatchCustomEvent(e,n,a,{discrete:i}){const r=a.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:a});n&&r.addEventListener(e,n,{once:!0}),i?E(r,o):r.dispatchEvent(o)}F.displayName="DismissableLayerBranch";var I="focusScope.autoFocusOnMount",q="focusScope.autoFocusOnUnmount",L={bubbles:!1,cancelable:!0},W=e.forwardRef((n,i)=>{const{loop:r=!1,trapped:o=!1,onMountAutoFocus:c,onUnmountAutoFocus:l,...p}=n,[s,d]=e.useState(null),u=h(c),m=h(l),f=e.useRef(null),v=k(i,e=>d(e)),x=e.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;e.useEffect(()=>{if(o){let handleFocusIn2=function(e){if(x.paused||!s)return;const n=e.target;s.contains(n)?f.current=n:focus(f.current,{select:!0})},handleFocusOut2=function(e){if(x.paused||!s)return;const n=e.relatedTarget;null!==n&&(s.contains(n)||focus(f.current,{select:!0}))},handleMutations2=function(e){if(document.activeElement===document.body)for(const n of e)n.removedNodes.length>0&&focus(s)};document.addEventListener("focusin",handleFocusIn2),document.addEventListener("focusout",handleFocusOut2);const e=new MutationObserver(handleMutations2);return s&&e.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",handleFocusIn2),document.removeEventListener("focusout",handleFocusOut2),e.disconnect()}}},[o,s,x.paused]),e.useEffect(()=>{if(s){$.add(x);const n=document.activeElement;if(!s.contains(n)){const a=new CustomEvent(I,L);s.addEventListener(I,u),s.dispatchEvent(a),a.defaultPrevented||(!function(e,{select:n=!1}={}){const a=document.activeElement;for(const i of e)if(focus(i,{select:n}),document.activeElement!==a)return}((e=getTabbableCandidates(s),e.filter(e=>"A"!==e.tagName)),{select:!0}),document.activeElement===n&&focus(s))}return()=>{s.removeEventListener(I,u),setTimeout(()=>{const e=new CustomEvent(q,L);s.addEventListener(q,m),s.dispatchEvent(e),e.defaultPrevented||focus(n??document.body,{select:!0}),s.removeEventListener(q,m),$.remove(x)},0)}}var e},[s,u,m,x]);const g=e.useCallback(e=>{if(!r&&!o)return;if(x.paused)return;const n="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,a=document.activeElement;if(n&&a){const n=e.currentTarget,[i,o]=function(e){const n=getTabbableCandidates(e),a=findVisible(n,e),i=findVisible(n.reverse(),e);return[a,i]}(n);i&&o?e.shiftKey||a!==o?e.shiftKey&&a===i&&(e.preventDefault(),r&&focus(o,{select:!0})):(e.preventDefault(),r&&focus(i,{select:!0})):a===n&&e.preventDefault()}},[r,o,x.paused]);return a.jsx(S.div,{tabIndex:-1,...p,ref:v,onKeyDown:g})});function getTabbableCandidates(e){const n=[],a=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const n="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||n?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;a.nextNode();)n.push(a.currentNode);return n}function findVisible(e,n){for(const a of e)if(!isHidden(a,{upTo:n}))return a}function isHidden(e,{upTo:n}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==n&&e===n)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function focus(e,{select:n=!1}={}){if(e&&e.focus){const a=document.activeElement;e.focus({preventScroll:!0}),e!==a&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&n&&e.select()}}W.displayName="FocusScope";var $=function(){let e=[];return{add(n){const a=e[0];n!==a&&a?.pause(),e=arrayRemove(e,n),e.unshift(n)},remove(n){e=arrayRemove(e,n),e[0]?.resume()}}}();function arrayRemove(e,n){const a=[...e],i=a.indexOf(n);return-1!==i&&a.splice(i,1),a}var B=e.forwardRef((n,r)=>{const{container:o,...c}=n,[l,p]=e.useState(!1);b(()=>p(!0),[]);const s=o||l&&globalThis?.document?.body;return s?i.createPortal(a.jsx(S.div,{...c,ref:r}),s):null});B.displayName="Portal";var Presence=n=>{const{present:a,children:i}=n,r=function(n){const[a,i]=e.useState(),r=e.useRef({}),o=e.useRef(n),c=e.useRef("none"),l=n?"mounted":"unmounted",[p,s]=function(n,a){return e.useReducer((e,n)=>a[e][n]??e,n)}(l,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return e.useEffect(()=>{const e=getAnimationName(r.current);c.current="mounted"===p?e:"none"},[p]),b(()=>{const e=r.current,a=o.current;if(a!==n){const i=c.current,r=getAnimationName(e);if(n)s("MOUNT");else if("none"===r||"none"===e?.display)s("UNMOUNT");else{s(a&&i!==r?"ANIMATION_OUT":"UNMOUNT")}o.current=n}},[n,s]),b(()=>{if(a){let e;const n=a.ownerDocument.defaultView??window,handleAnimationEnd=i=>{const c=getAnimationName(r.current).includes(i.animationName);if(i.target===a&&c&&(s("ANIMATION_END"),!o.current)){const i=a.style.animationFillMode;a.style.animationFillMode="forwards",e=n.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=i)})}},handleAnimationStart=e=>{e.target===a&&(c.current=getAnimationName(r.current))};return a.addEventListener("animationstart",handleAnimationStart),a.addEventListener("animationcancel",handleAnimationEnd),a.addEventListener("animationend",handleAnimationEnd),()=>{n.clearTimeout(e),a.removeEventListener("animationstart",handleAnimationStart),a.removeEventListener("animationcancel",handleAnimationEnd),a.removeEventListener("animationend",handleAnimationEnd)}}s("ANIMATION_END")},[a,s]),{isPresent:["mounted","unmountSuspended"].includes(p),ref:e.useCallback(e=>{e&&(r.current=getComputedStyle(e)),i(e)},[])}}(a),o="function"==typeof i?i({present:r.isPresent}):e.Children.only(i),c=k(r.ref,function(e){let n=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,a=n&&"isReactWarning"in n&&n.isReactWarning;if(a)return e.ref;if(n=Object.getOwnPropertyDescriptor(e,"ref")?.get,a=n&&"isReactWarning"in n&&n.isReactWarning,a)return e.props.ref;return e.props.ref||e.ref}(o));return"function"==typeof i||r.isPresent?e.cloneElement(o,{ref:c}):null};function getAnimationName(e){return e?.animationName||"none"}Presence.displayName="Presence";var H=0;function createFocusGuard(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var U={},extendStatics=function(e,n){return extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,n){e.__proto__=n}||function(e,n){for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])},extendStatics(e,n)};function __extends(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function __(){this.constructor=e}extendStatics(e,n),e.prototype=null===n?Object.create(n):(__.prototype=n.prototype,new __)}var __assign=function(){return __assign=Object.assign||function(e){for(var n,a=1,i=arguments.length;a<i;a++)for(var r in n=arguments[a])Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r]);return e},__assign.apply(this,arguments)};function __rest(e,n){var a={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0&&(a[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(i=Object.getOwnPropertySymbols(e);r<i.length;r++)n.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r])&&(a[i[r]]=e[i[r]])}return a}function __decorate(e,n,a,i){var r,o=arguments.length,c=o<3?n:null===i?i=Object.getOwnPropertyDescriptor(n,a):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)c=Reflect.decorate(e,n,a,i);else for(var l=e.length-1;l>=0;l--)(r=e[l])&&(c=(o<3?r(c):o>3?r(n,a,c):r(n,a))||c);return o>3&&c&&Object.defineProperty(n,a,c),c}function __param(e,n){return function(a,i){n(a,i,e)}}function __esDecorate(e,n,a,i,r,o){function accept(e){if(void 0!==e&&"function"!=typeof e)throw new TypeError("Function expected");return e}for(var c,l=i.kind,p="getter"===l?"get":"setter"===l?"set":"value",s=!n&&e?i.static?e:e.prototype:null,d=n||(s?Object.getOwnPropertyDescriptor(s,i.name):{}),u=!1,m=a.length-1;m>=0;m--){var f={};for(var v in i)f[v]="access"===v?{}:i[v];for(var v in i.access)f.access[v]=i.access[v];f.addInitializer=function(e){if(u)throw new TypeError("Cannot add initializers after decoration has completed");o.push(accept(e||null))};var x=(0,a[m])("accessor"===l?{get:d.get,set:d.set}:d[p],f);if("accessor"===l){if(void 0===x)continue;if(null===x||"object"!=typeof x)throw new TypeError("Object expected");(c=accept(x.get))&&(d.get=c),(c=accept(x.set))&&(d.set=c),(c=accept(x.init))&&r.unshift(c)}else(c=accept(x))&&("field"===l?r.unshift(c):d[p]=c)}s&&Object.defineProperty(s,i.name,d),u=!0}function __runInitializers(e,n,a){for(var i=arguments.length>2,r=0;r<n.length;r++)a=i?n[r].call(e,a):n[r].call(e);return i?a:void 0}function __propKey(e){return"symbol"==typeof e?e:"".concat(e)}function __setFunctionName(e,n,a){return"symbol"==typeof n&&(n=n.description?"[".concat(n.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:a?"".concat(a," ",n):n})}function __metadata(e,n){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,n)}function __awaiter$1(e,n,a,i){return new(a||(a=Promise))(function(r,o){function fulfilled(e){try{step(i.next(e))}catch(e){o(e)}}function rejected(e){try{step(i.throw(e))}catch(e){o(e)}}function step(e){var n;e.done?r(e.value):(n=e.value,n instanceof a?n:new a(function(e){e(n)})).then(fulfilled,rejected)}step((i=i.apply(e,n||[])).next())})}function __generator$1(e,n){var a,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},c=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return c.next=verb(0),c.throw=verb(1),c.return=verb(2),"function"==typeof Symbol&&(c[Symbol.iterator]=function(){return this}),c;function verb(l){return function(p){return function(l){if(a)throw new TypeError("Generator is already executing.");for(;c&&(c=0,l[0]&&(o=0)),o;)try{if(a=1,i&&(r=2&l[0]?i.return:l[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,l[1])).done)return r;switch(i=0,r&&(l=[2&l[0],r.value]),l[0]){case 0:case 1:r=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,i=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!r||l[1]>r[0]&&l[1]<r[3])){o.label=l[1];break}if(6===l[0]&&o.label<r[1]){o.label=r[1],r=l;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(l);break}r[2]&&o.ops.pop(),o.trys.pop();continue}l=n.call(e,o)}catch(e){l=[6,e],i=0}finally{a=r=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,p])}}}var K=Object.create?function(e,n,a,i){void 0===i&&(i=a);var r=Object.getOwnPropertyDescriptor(n,a);r&&!("get"in r?!n.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return n[a]}}),Object.defineProperty(e,i,r)}:function(e,n,a,i){void 0===i&&(i=a),e[i]=n[a]};function __exportStar(e,n){for(var a in e)"default"===a||Object.prototype.hasOwnProperty.call(n,a)||K(n,e,a)}function __values(e){var n="function"==typeof Symbol&&Symbol.iterator,a=n&&e[n],i=0;if(a)return a.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function __read$1(e,n){var a="function"==typeof Symbol&&e[Symbol.iterator];if(!a)return e;var i,r,o=a.call(e),c=[];try{for(;(void 0===n||n-- >0)&&!(i=o.next()).done;)c.push(i.value)}catch(e){r={error:e}}finally{try{i&&!i.done&&(a=o.return)&&a.call(o)}finally{if(r)throw r.error}}return c}function __spread(){for(var e=[],n=0;n<arguments.length;n++)e=e.concat(__read$1(arguments[n]));return e}function __spreadArrays(){for(var e=0,n=0,a=arguments.length;n<a;n++)e+=arguments[n].length;var i=Array(e),r=0;for(n=0;n<a;n++)for(var o=arguments[n],c=0,l=o.length;c<l;c++,r++)i[r]=o[c];return i}function __spreadArray$1(e,n,a){if(a||2===arguments.length)for(var i,r=0,o=n.length;r<o;r++)!i&&r in n||(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))}function __await(e){return this instanceof __await?(this.v=e,this):new __await(e)}function __asyncGenerator(e,n,a){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var i,r=a.apply(e,n||[]),o=[];return i=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),verb("next"),verb("throw"),verb("return",function(e){return function(n){return Promise.resolve(n).then(e,reject)}}),i[Symbol.asyncIterator]=function(){return this},i;function verb(e,n){r[e]&&(i[e]=function(n){return new Promise(function(a,i){o.push([e,n,a,i])>1||resume(e,n)})},n&&(i[e]=n(i[e])))}function resume(e,n){try{(a=r[e](n)).value instanceof __await?Promise.resolve(a.value.v).then(fulfill,reject):settle(o[0][2],a)}catch(e){settle(o[0][3],e)}var a}function fulfill(e){resume("next",e)}function reject(e){resume("throw",e)}function settle(e,n){e(n),o.shift(),o.length&&resume(o[0][0],o[0][1])}}function __asyncDelegator(e){var n,a;return n={},verb("next"),verb("throw",function(e){throw e}),verb("return"),n[Symbol.iterator]=function(){return this},n;function verb(i,r){n[i]=e[i]?function(n){return(a=!a)?{value:__await(e[i](n)),done:!1}:r?r(n):n}:r}}function __asyncValues(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,a=e[Symbol.asyncIterator];return a?a.call(e):(e=__values(e),n={},verb("next"),verb("throw"),verb("return"),n[Symbol.asyncIterator]=function(){return this},n);function verb(a){n[a]=e[a]&&function(n){return new Promise(function(i,r){(function(e,n,a,i){Promise.resolve(i).then(function(n){e({value:n,done:a})},n)})(i,r,(n=e[a](n)).done,n.value)})}}}function __makeTemplateObject(e,n){return Object.defineProperty?Object.defineProperty(e,"raw",{value:n}):e.raw=n,e}var G=Object.create?function(e,n){Object.defineProperty(e,"default",{enumerable:!0,value:n})}:function(e,n){e.default=n},ownKeys$2=function(e){return ownKeys$2=Object.getOwnPropertyNames||function(e){var n=[];for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(n[n.length]=a);return n},ownKeys$2(e)};function __importStar(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var a=ownKeys$2(e),i=0;i<a.length;i++)"default"!==a[i]&&K(n,e,a[i]);return G(n,e),n}function __importDefault(e){return e&&e.__esModule?e:{default:e}}function __classPrivateFieldGet(e,n,a,i){if("a"===a&&!i)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof n?e!==n||!i:!n.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===a?i:"a"===a?i.call(e):i?i.value:n.get(e)}function __classPrivateFieldSet(e,n,a,i,r){if("m"===i)throw new TypeError("Private method is not writable");if("a"===i&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof n?e!==n||!r:!n.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?r.call(e,a):r?r.value=a:n.set(e,a),a}function __classPrivateFieldIn(e,n){if(null===n||"object"!=typeof n&&"function"!=typeof n)throw new TypeError("Cannot use 'in' operator on non-object");return"function"==typeof e?n===e:e.has(n)}function __addDisposableResource(e,n,a){if(null!=n){if("object"!=typeof n&&"function"!=typeof n)throw new TypeError("Object expected.");var i,r;if(a){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=n[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=n[Symbol.dispose],a&&(r=i)}if("function"!=typeof i)throw new TypeError("Object not disposable.");r&&(i=function(){try{r.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:n,dispose:i,async:a})}else a&&e.stack.push({async:!0});return n}var Y="function"==typeof SuppressedError?SuppressedError:function(e,n,a){var i=new Error(a);return i.name="SuppressedError",i.error=e,i.suppressed=n,i};function __disposeResources(e){function fail(n){e.error=e.hasError?new Y(n,e.error,"An error was suppressed during disposal."):n,e.hasError=!0}var n,a=0;return function next(){for(;n=e.stack.pop();)try{if(!n.async&&1===a)return a=0,e.stack.push(n),Promise.resolve().then(next);if(n.dispose){var i=n.dispose.call(n.value);if(n.async)return a|=2,Promise.resolve(i).then(next,function(e){return fail(e),next()})}else a|=1}catch(e){fail(e)}if(1===a)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()}function __rewriteRelativeImportExtension(e,n){return"string"==typeof e&&/^\.\.?\//.test(e)?e.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(e,a,i,r,o){return a?n?".jsx":".js":!i||r&&o?i+r+"."+o.toLowerCase()+"js":e}):e}const V={__extends:__extends,__assign:__assign,__rest:__rest,__decorate:__decorate,__param:__param,__esDecorate:__esDecorate,__runInitializers:__runInitializers,__propKey:__propKey,__setFunctionName:__setFunctionName,__metadata:__metadata,__awaiter:__awaiter$1,__generator:__generator$1,__createBinding:K,__exportStar:__exportStar,__values:__values,__read:__read$1,__spread:__spread,__spreadArrays:__spreadArrays,__spreadArray:__spreadArray$1,__await:__await,__asyncGenerator:__asyncGenerator,__asyncDelegator:__asyncDelegator,__asyncValues:__asyncValues,__makeTemplateObject:__makeTemplateObject,__importStar:__importStar,__importDefault:__importDefault,__classPrivateFieldGet:__classPrivateFieldGet,__classPrivateFieldSet:__classPrivateFieldSet,__classPrivateFieldIn:__classPrivateFieldIn,__addDisposableResource:__addDisposableResource,__disposeResources:__disposeResources,__rewriteRelativeImportExtension:__rewriteRelativeImportExtension},X=r(Object.freeze(Object.defineProperty({__proto__:null,__addDisposableResource:__addDisposableResource,get __assign(){return __assign},__asyncDelegator:__asyncDelegator,__asyncGenerator:__asyncGenerator,__asyncValues:__asyncValues,__await:__await,__awaiter:__awaiter$1,__classPrivateFieldGet:__classPrivateFieldGet,__classPrivateFieldIn:__classPrivateFieldIn,__classPrivateFieldSet:__classPrivateFieldSet,__createBinding:K,__decorate:__decorate,__disposeResources:__disposeResources,__esDecorate:__esDecorate,__exportStar:__exportStar,__extends:__extends,__generator:__generator$1,__importDefault:__importDefault,__importStar:__importStar,__makeTemplateObject:__makeTemplateObject,__metadata:__metadata,__param:__param,__propKey:__propKey,__read:__read$1,__rest:__rest,__rewriteRelativeImportExtension:__rewriteRelativeImportExtension,__runInitializers:__runInitializers,__setFunctionName:__setFunctionName,__spread:__spread,__spreadArray:__spreadArray$1,__spreadArrays:__spreadArrays,__values:__values,default:V},Symbol.toStringTag,{value:"Module"})));var Z,J={},Q={},ee={};function requireConstants(){return Z||(Z=1,Object.defineProperty(ee,"__esModule",{value:!0}),ee.removedBarSizeVariable=ee.noScrollbarsClassName=ee.fullWidthClassName=ee.zeroRightClassName=void 0,ee.zeroRightClassName="right-scroll-bar-position",ee.fullWidthClassName="width-before-scroll-bar",ee.noScrollbarsClassName="with-scroll-bars-hidden",ee.removedBarSizeVariable="--removed-body-scroll-bar-size"),ee}var te,ne={},ae={};function requireAssignRef(){if(te)return ae;return te=1,Object.defineProperty(ae,"__esModule",{value:!0}),ae.assignRef=void 0,ae.assignRef=function(e,n){return"function"==typeof e?e(n):e&&(e.current=n),e},ae}var ie,re={};function requireUseRef(){if(ie)return re;ie=1,Object.defineProperty(re,"__esModule",{value:!0}),re.useCallbackRef=void 0;var n=e;return re.useCallbackRef=function(e,a){var i=(0,n.useState)(function(){return{value:e,callback:a,facade:{get current(){return i.value},set current(e){var n=i.value;n!==e&&(i.value=e,i.callback(e,n))}}}})[0];return i.callback=a,i.facade},re}var oe,ce={};function requireCreateRef(){if(oe)return ce;return oe=1,Object.defineProperty(ce,"__esModule",{value:!0}),ce.createCallbackRef=void 0,ce.createCallbackRef=function(e){var n=null;return{get current(){return n},set current(a){var i=n;i!==a&&(n=a,e(a,i))}}},ce}var le,pe={};function requireMergeRef(){if(le)return pe;le=1,Object.defineProperty(pe,"__esModule",{value:!0}),pe.mergeRefs=void 0;var e=requireAssignRef(),n=requireCreateRef();return pe.mergeRefs=function(a){return(0,n.createCallbackRef)(function(n){return a.forEach(function(a){return(0,e.assignRef)(a,n)})})},pe}var se,de={};function requireUseMergeRef(){if(se)return de;se=1,Object.defineProperty(de,"__esModule",{value:!0}),de.useMergeRefs=void 0;var n=X.__importStar(e),a=requireAssignRef(),i=requireUseRef(),r=n.useEffect,o=new WeakMap;return de.useMergeRefs=function(e,n){var c=(0,i.useCallbackRef)(n||null,function(n){return e.forEach(function(e){return(0,a.assignRef)(e,n)})});return r(function(){var n=o.get(c);if(n){var i=new Set(n),r=new Set(e),l=c.current;i.forEach(function(e){r.has(e)||(0,a.assignRef)(e,null)}),r.forEach(function(e){i.has(e)||(0,a.assignRef)(e,l)})}o.set(c,e)},[e]),c},de}var ue,me={};function requireUseTransformRef(){if(ue)return me;ue=1,Object.defineProperty(me,"__esModule",{value:!0}),me.useTransformRef=void 0;var e=requireAssignRef(),n=requireUseRef();return me.useTransformRef=function(a,i){return(0,n.useCallbackRef)(null,function(n){return(0,e.assignRef)(a,i(n))})},me}var fe,ve={};function requireTransformRef(){if(fe)return ve;fe=1,Object.defineProperty(ve,"__esModule",{value:!0}),ve.transformRef=void 0;var e=requireAssignRef(),n=requireCreateRef();return ve.transformRef=function(a,i){return(0,n.createCallbackRef)(function(n){return(0,e.assignRef)(a,i(n))})},ve}var xe,ge,be={};function requireRefToCallback(){if(xe)return be;function refToCallback$1(e){return function(n){"function"==typeof e?e(n):e&&(e.current=n)}}xe=1,Object.defineProperty(be,"__esModule",{value:!0}),be.useRefToCallback=be.refToCallback=void 0,be.refToCallback=refToCallback$1;var nullCallback=function(){return null},e=new WeakMap;return be.useRefToCallback=function(n){return function(n){var a=n||nullCallback,i=e.get(a);if(i)return i;var r=refToCallback$1(a);return e.set(a,r),r}(n)},be}function requireEs5$4(){return ge||(ge=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.useRefToCallback=e.refToCallback=e.transformRef=e.useTransformRef=e.useMergeRefs=e.mergeRefs=e.createCallbackRef=e.useCallbackRef=e.assignRef=void 0;var n=requireAssignRef();Object.defineProperty(e,"assignRef",{enumerable:!0,get:function(){return n.assignRef}});var a=requireUseRef();Object.defineProperty(e,"useCallbackRef",{enumerable:!0,get:function(){return a.useCallbackRef}});var i=requireCreateRef();Object.defineProperty(e,"createCallbackRef",{enumerable:!0,get:function(){return i.createCallbackRef}});var r=requireMergeRef();Object.defineProperty(e,"mergeRefs",{enumerable:!0,get:function(){return r.mergeRefs}});var o=requireUseMergeRef();Object.defineProperty(e,"useMergeRefs",{enumerable:!0,get:function(){return o.useMergeRefs}});var c=requireUseTransformRef();Object.defineProperty(e,"useTransformRef",{enumerable:!0,get:function(){return c.useTransformRef}});var l=requireTransformRef();Object.defineProperty(e,"transformRef",{enumerable:!0,get:function(){return l.transformRef}});var p=requireRefToCallback();Object.defineProperty(e,"refToCallback",{enumerable:!0,get:function(){return p.refToCallback}}),Object.defineProperty(e,"useRefToCallback",{enumerable:!0,get:function(){return p.useRefToCallback}})}(ne)),ne}var he,ye,_e,we,je={},ke={},Se={},Ee={},Oe={},Pe={};function requireNode(){return he||(he=1,Pe.isNode="[object process]"===Object.prototype.toString.call(void 0!==P?P:0)),Pe}function requireEnv(){if(ye)return Oe;ye=1,Object.defineProperty(Oe,"__esModule",{value:!0}),Oe.env=void 0;var e=requireNode();return Oe.env={isNode:e.isNode,forceCache:!1},Oe}function requireHook$1(){if(_e)return Ee;_e=1,Object.defineProperty(Ee,"__esModule",{value:!0}),Ee.useSidecar=void 0;var n=e,a=requireEnv(),i=new WeakMap,r={};return Ee.useSidecar=function(e,o){var c=o&&o.options||r;return a.env.isNode&&!c.ssr?[null,null]:function(e,o){var c=o&&o.options||r,l=a.env.forceCache||a.env.isNode&&!!c.ssr||!c.async,p=(0,n.useState)(l?function(){return i.get(e)}:void 0),s=p[0],d=p[1],u=(0,n.useState)(null),m=u[0],f=u[1];return(0,n.useEffect)(function(){s||e().then(function(n){var a,r=o?o.read():n.default||n;if(!r)throw console.error("Sidecar error: with importer",e),o?(console.error("Sidecar error: with medium",o),a=new Error("Sidecar medium was not found")):a=new Error("Sidecar was not found in exports"),f(function(){return a}),a;i.set(e,r),d(function(){return r})},function(e){return f(function(){return e})})},[]),[s,m]}(e,o)},Ee}function requireHoc(){if(we)return Se;we=1,Object.defineProperty(Se,"__esModule",{value:!0}),Se.sidecar=void 0;var n=X,a=n.__importStar(e),i=requireHook$1();return Se.sidecar=function(e,r){var ErrorCase=function(){return r};return function(o){var c=(0,i.useSidecar)(e,o.sideCar),l=c[0];return c[1]&&r?ErrorCase:l?a.createElement(l,n.__assign({},o)):null}},Se}var Ce,Re={};function requireConfig(){return Ce||(Ce=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.setConfig=e.config=void 0,e.config={onError:function(e){return console.error(e)}};e.setConfig=function(n){Object.assign(e.config,n)}}(Re)),Re}var De,Ae={};var Te,Ne={};function requireRenderProp(){if(Te)return Ne;Te=1,Object.defineProperty(Ne,"__esModule",{value:!0}),Ne.renderCar=void 0;var n=X,a=n.__importStar(e),i=e;return Ne.renderCar=function(e,r){function State(r){var o=r.stateRef,c=r.props,l=(0,i.useCallback)(function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return(0,i.useLayoutEffect)(function(){o.current(e)}),null},[]);return a.createElement(e,n.__assign({},c,{children:l}))}var o=a.memo(function(e){var n=e.stateRef,a=e.defaultState,r=e.children,o=(0,i.useState)(a.current),c=o[0],l=o[1];return(0,i.useEffect)(function(){n.current=l},[]),r.apply(void 0,c)},function(){return!0});return function(e){var n=a.useRef(r(e)),i=a.useRef(function(e){return n.current=e});return a.createElement(a.Fragment,null,a.createElement(State,{stateRef:i,props:e}),a.createElement(o,{stateRef:i,defaultState:n,children:e.children}))}},Ne}var ze,Me,Fe,Ie,qe={};function requireExports(){if(ze)return qe;ze=1,Object.defineProperty(qe,"__esModule",{value:!0}),qe.exportSidecar=void 0;var n=X,a=n.__importStar(e),SideCar=function(e){var i=e.sideCar,r=n.__rest(e,["sideCar"]);if(!i)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=i.read();if(!o)throw new Error("Sidecar medium not found");return a.createElement(o,n.__assign({},r))};return SideCar.isSideCarExport=!0,qe.exportSidecar=function(e,n){return e.useMedium(n),SideCar},qe}function requireEs5$3(){return Me||(Me=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.exportSidecar=e.renderCar=e.createSidecarMedium=e.createMedium=e.setConfig=e.useSidecar=e.sidecar=void 0;var n=requireHoc();Object.defineProperty(e,"sidecar",{enumerable:!0,get:function(){return n.sidecar}});var a=requireHook$1();Object.defineProperty(e,"useSidecar",{enumerable:!0,get:function(){return a.useSidecar}});var i=requireConfig();Object.defineProperty(e,"setConfig",{enumerable:!0,get:function(){return i.setConfig}});var r=function(){if(De)return Ae;De=1,Object.defineProperty(Ae,"__esModule",{value:!0}),Ae.createSidecarMedium=Ae.createMedium=void 0;var e=X;function ItoI(e){return e}function innerCreateMedium(e,n){void 0===n&&(n=ItoI);var a=[],i=!1;return{read:function(){if(i)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return a.length?a[a.length-1]:e},useMedium:function(e){var r=n(e,i);return a.push(r),function(){a=a.filter(function(e){return e!==r})}},assignSyncMedium:function(e){for(i=!0;a.length;){var n=a;a=[],n.forEach(e)}a={push:function(n){return e(n)},filter:function(){return a}}},assignMedium:function(e){i=!0;var n=[];if(a.length){var r=a;a=[],r.forEach(e),n=a}var executeQueue=function(){var a=n;n=[],a.forEach(e)},cycle=function(){return Promise.resolve().then(executeQueue)};cycle(),a={push:function(e){n.push(e),cycle()},filter:function(e){return n=n.filter(e),a}}}}}return Ae.createMedium=function(e,n){return void 0===n&&(n=ItoI),innerCreateMedium(e,n)},Ae.createSidecarMedium=function(n){void 0===n&&(n={});var a=innerCreateMedium(null);return a.options=e.__assign({async:!0,ssr:!1},n),a},Ae}();Object.defineProperty(e,"createMedium",{enumerable:!0,get:function(){return r.createMedium}}),Object.defineProperty(e,"createSidecarMedium",{enumerable:!0,get:function(){return r.createSidecarMedium}});var o=requireRenderProp();Object.defineProperty(e,"renderCar",{enumerable:!0,get:function(){return o.renderCar}});var c=requireExports();Object.defineProperty(e,"exportSidecar",{enumerable:!0,get:function(){return c.exportSidecar}})}(ke)),ke}function requireMedium(){if(Fe)return je;Fe=1,Object.defineProperty(je,"__esModule",{value:!0}),je.effectCar=void 0;var e=requireEs5$3();return je.effectCar=(0,e.createSidecarMedium)(),je}function requireUI(){if(Ie)return Q;Ie=1,Object.defineProperty(Q,"__esModule",{value:!0}),Q.RemoveScroll=void 0;var n=X,a=n.__importStar(e),i=requireConstants(),r=requireEs5$4(),o=requireMedium(),nothing=function(){},c=a.forwardRef(function(e,i){var c=a.useRef(null),l=a.useState({onScrollCapture:nothing,onWheelCapture:nothing,onTouchMoveCapture:nothing}),p=l[0],s=l[1],d=e.forwardProps,u=e.children,m=e.className,f=e.removeScrollBar,v=e.enabled,x=e.shards,g=e.sideCar,b=e.noRelative,h=e.noIsolation,y=e.inert,_=e.allowPinchZoom,w=e.as,j=void 0===w?"div":w,k=e.gapMode,S=n.__rest(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=g,O=(0,r.useMergeRefs)([c,i]),P=n.__assign(n.__assign({},S),p);return a.createElement(a.Fragment,null,v&&a.createElement(E,{sideCar:o.effectCar,removeScrollBar:f,shards:x,noRelative:b,noIsolation:h,inert:y,setCallbacks:s,allowPinchZoom:!!_,lockRef:c,gapMode:k}),d?a.cloneElement(a.Children.only(u),n.__assign(n.__assign({},P),{ref:O})):a.createElement(j,n.__assign({},P,{className:m,ref:O}),u))});return Q.RemoveScroll=c,c.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},c.classNames={fullWidth:i.fullWidthClassName,zeroRight:i.zeroRightClassName},Q}var Le,We,$e,Be,He,Ue={},Ke={},Ge={},Ye={},Ve={},Xe={},Ze={},Je={},Qe={};function requireEs5$2(){return Le||(Le=1,Object.defineProperty(Qe,"__esModule",{value:!0}),Qe.setNonce=function(n){e=n},Qe.getNonce=function(){return e||("undefined"!=typeof __webpack_nonce__?__webpack_nonce__:void 0)}),Qe;var e}function requireSingleton(){if(We)return Je;We=1,Object.defineProperty(Je,"__esModule",{value:!0}),Je.stylesheetSingleton=void 0;var e=requireEs5$2();return Je.stylesheetSingleton=function(){var n=0,a=null;return{add:function(i){var r,o;0==n&&(a=function(){if(!document)return null;var n=document.createElement("style");n.type="text/css";var a=(0,e.getNonce)();return a&&n.setAttribute("nonce",a),n}())&&(o=i,(r=a).styleSheet?r.styleSheet.cssText=o:r.appendChild(document.createTextNode(o)),function(e){(document.head||document.getElementsByTagName("head")[0]).appendChild(e)}(a)),n++},remove:function(){! --n&&a&&(a.parentNode&&a.parentNode.removeChild(a),a=null)}}},Je}function requireHook(){if($e)return Ze;$e=1,Object.defineProperty(Ze,"__esModule",{value:!0}),Ze.styleHookSingleton=void 0;var n=X.__importStar(e),a=requireSingleton();return Ze.styleHookSingleton=function(){var e=(0,a.stylesheetSingleton)();return function(a,i){n.useEffect(function(){return e.add(a),function(){e.remove()}},[a&&i])}},Ze}function requireComponent$1(){if(Be)return Xe;Be=1,Object.defineProperty(Xe,"__esModule",{value:!0}),Xe.styleSingleton=void 0;var e=requireHook();return Xe.styleSingleton=function(){var n=(0,e.styleHookSingleton)();return function(e){var a=e.styles,i=e.dynamic;return n(a,i),null}},Xe}function requireEs5$1(){return He||(He=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.styleHookSingleton=e.stylesheetSingleton=e.styleSingleton=void 0;var n=requireComponent$1();Object.defineProperty(e,"styleSingleton",{enumerable:!0,get:function(){return n.styleSingleton}});var a=requireSingleton();Object.defineProperty(e,"stylesheetSingleton",{enumerable:!0,get:function(){return a.stylesheetSingleton}});var i=requireHook();Object.defineProperty(e,"styleHookSingleton",{enumerable:!0,get:function(){return i.styleHookSingleton}})}(Ve)),Ve}var et,tt,nt,at={};function requireUtils(){return et||(et=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.getGapWidth=e.zeroGap=void 0,e.zeroGap={left:0,top:0,right:0,gap:0};e.getGapWidth=function(n){return e.zeroGap}}(at)),at}function requireComponent(){return tt||(tt=1,function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.RemoveScrollBar=n.useLockAttribute=n.lockAttribute=void 0;var a=X.__importStar(e),i=requireEs5$1(),r=requireConstants(),o=requireUtils(),c=(0,i.styleSingleton)();n.lockAttribute="data-scroll-locked";var getStyles=function(e,a,i,o){var c=e.left,l=e.top,p=e.right,s=e.gap;return void 0===i&&(i="margin"),"\n  .".concat(r.noScrollbarsClassName," {\n   overflow: hidden ").concat(o,";\n   padding-right: ").concat(s,"px ").concat(o,";\n  }\n  body[").concat(n.lockAttribute,"] {\n    overflow: hidden ").concat(o,";\n    overscroll-behavior: contain;\n    ").concat([a&&"position: relative ".concat(o,";"),"margin"===i&&"\n    padding-left: ".concat(c,"px;\n    padding-top: ").concat(l,"px;\n    padding-right: ").concat(p,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(o,";\n    "),"padding"===i&&"padding-right: ".concat(s,"px ").concat(o,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(r.zeroRightClassName," {\n    right: ").concat(s,"px ").concat(o,";\n  }\n  \n  .").concat(r.fullWidthClassName," {\n    margin-right: ").concat(s,"px ").concat(o,";\n  }\n  \n  .").concat(r.zeroRightClassName," .").concat(r.zeroRightClassName," {\n    right: 0 ").concat(o,";\n  }\n  \n  .").concat(r.fullWidthClassName," .").concat(r.fullWidthClassName," {\n    margin-right: 0 ").concat(o,";\n  }\n  \n  body[").concat(n.lockAttribute,"] {\n    ").concat(r.removedBarSizeVariable,": ").concat(s,"px;\n  }\n")},getCurrentUseCounter=function(){var e=parseInt(document.body.getAttribute(n.lockAttribute)||"0",10);return isFinite(e)?e:0};n.useLockAttribute=function(){a.useEffect(function(){return document.body.setAttribute(n.lockAttribute,(getCurrentUseCounter()+1).toString()),function(){var e=getCurrentUseCounter()-1;e<=0?document.body.removeAttribute(n.lockAttribute):document.body.setAttribute(n.lockAttribute,e.toString())}},[])};n.RemoveScrollBar=function(e){var i=e.noRelative,r=e.noImportant,l=e.gapMode,p=void 0===l?"margin":l;(0,n.useLockAttribute)();var s=a.useMemo(function(){return(0,o.getGapWidth)(p)},[p]);return a.createElement(c,{styles:getStyles(s,!i,p,r?"":"!important")})}}(Ye)),Ye}function requireEs5(){return nt||(nt=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.getGapWidth=e.removedBarSizeVariable=e.noScrollbarsClassName=e.fullWidthClassName=e.zeroRightClassName=e.RemoveScrollBar=void 0;var n=requireComponent();Object.defineProperty(e,"RemoveScrollBar",{enumerable:!0,get:function(){return n.RemoveScrollBar}});var a=requireConstants();Object.defineProperty(e,"zeroRightClassName",{enumerable:!0,get:function(){return a.zeroRightClassName}}),Object.defineProperty(e,"fullWidthClassName",{enumerable:!0,get:function(){return a.fullWidthClassName}}),Object.defineProperty(e,"noScrollbarsClassName",{enumerable:!0,get:function(){return a.noScrollbarsClassName}}),Object.defineProperty(e,"removedBarSizeVariable",{enumerable:!0,get:function(){return a.removedBarSizeVariable}});var i=requireUtils();Object.defineProperty(e,"getGapWidth",{enumerable:!0,get:function(){return i.getGapWidth}})}(Ge)),Ge}var it,rt={};function requireAggresiveCapture(){return it||(it=1,Object.defineProperty(rt,"__esModule",{value:!0}),rt.nonPassive=void 0,rt.nonPassive=!1),rt}var ot,ct,lt,pt,st={};function requireHandleScroll(){if(ot)return st;ot=1,Object.defineProperty(st,"__esModule",{value:!0}),st.handleScroll=st.locationCouldBeScrolled=void 0;var elementCanBeScrolled=function(e,n){if(!(e instanceof Element))return!1;var a=window.getComputedStyle(e);return"hidden"!==a[n]&&!(a.overflowY===a.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===a[n])};st.locationCouldBeScrolled=function(e,n){var a=n.ownerDocument,i=n;do{if("undefined"!=typeof ShadowRoot&&i instanceof ShadowRoot&&(i=i.host),elementCouldBeScrolled(e,i)){var r=getScrollVariables(e,i);if(r[1]>r[2])return!0}i=i.parentNode}while(i&&i!==a.body);return!1};var elementCouldBeScrolled=function(e,n){return"v"===e?function(e){return elementCanBeScrolled(e,"overflowY")}(n):function(e){return elementCanBeScrolled(e,"overflowX")}(n)},getScrollVariables=function(e,n){return"v"===e?[(a=n).scrollTop,a.scrollHeight,a.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(n);var a};return st.handleScroll=function(e,n,a,i,r){var o=function(e,n){return"h"===e&&"rtl"===n?-1:1}(e,window.getComputedStyle(n).direction),c=o*i,l=a.target,p=n.contains(l),s=!1,d=c>0,u=0,m=0;do{if(!l)break;var f=getScrollVariables(e,l),v=f[0],x=f[1]-f[2]-o*v;(v||x)&&elementCouldBeScrolled(e,l)&&(u+=x,m+=v);var g=l.parentNode;l=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!p&&l!==document.body||p&&(n.contains(l)||n===l));return(d&&(r&&Math.abs(u)<1||!r&&c>u)||!d&&(r&&Math.abs(m)<1||!r&&-c>m))&&(s=!0),s},st}function requireSideEffect(){return ct||(ct=1,function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.RemoveScrollSideCar=n.getDeltaXY=n.getTouchXY=void 0;var a=X,i=a.__importStar(e),r=requireEs5(),o=requireEs5$1(),c=requireAggresiveCapture(),l=requireHandleScroll();n.getTouchXY=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]};n.getDeltaXY=function(e){return[e.deltaX,e.deltaY]};var extractRef=function(e){return e&&"current"in e?e.current:e},generateStyle=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},p=0,s=[];function getOutermostShadowParent(e){for(var n=null;null!==e;)e instanceof ShadowRoot&&(n=e.host,e=e.host),e=e.parentNode;return n}n.RemoveScrollSideCar=function(e){var d=i.useRef([]),u=i.useRef([0,0]),m=i.useRef(),f=i.useState(p++)[0],v=i.useState(o.styleSingleton)[0],x=i.useRef(e);i.useEffect(function(){x.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(f));var n=a.__spreadArray([e.lockRef.current],(e.shards||[]).map(extractRef),!0).filter(Boolean);return n.forEach(function(e){return e.classList.add("allow-interactivity-".concat(f))}),function(){document.body.classList.remove("block-interactivity-".concat(f)),n.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(f))})}}},[e.inert,e.lockRef.current,e.shards]);var g=i.useCallback(function(e,a){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!x.current.allowPinchZoom;var i,r=(0,n.getTouchXY)(e),o=u.current,c="deltaX"in e?e.deltaX:o[0]-r[0],p="deltaY"in e?e.deltaY:o[1]-r[1],s=e.target,d=Math.abs(c)>Math.abs(p)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=(0,l.locationCouldBeScrolled)(d,s);if(!f)return!0;if(f?i=d:(i="v"===d?"h":"v",f=(0,l.locationCouldBeScrolled)(d,s)),!f)return!1;if(!m.current&&"changedTouches"in e&&(c||p)&&(m.current=i),!i)return!0;var v=m.current||i;return(0,l.handleScroll)(v,a,e,"h"===v?c:p,!0)},[]),b=i.useCallback(function(e){var a=e;if(s.length&&s[s.length-1]===v){var i="deltaY"in a?(0,n.getDeltaXY)(a):(0,n.getTouchXY)(a),r=d.current.filter(function(e){return e.name===a.type&&(e.target===a.target||a.target===e.shadowParent)&&(n=e.delta,r=i,n[0]===r[0]&&n[1]===r[1]);var n,r})[0];if(r&&r.should)a.cancelable&&a.preventDefault();else if(!r){var o=(x.current.shards||[]).map(extractRef).filter(Boolean).filter(function(e){return e.contains(a.target)});(o.length>0?g(a,o[0]):!x.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),h=i.useCallback(function(e,n,a,i){var r={name:e,delta:n,target:a,should:i,shadowParent:getOutermostShadowParent(a)};d.current.push(r),setTimeout(function(){d.current=d.current.filter(function(e){return e!==r})},1)},[]),y=i.useCallback(function(e){u.current=(0,n.getTouchXY)(e),m.current=void 0},[]),_=i.useCallback(function(a){h(a.type,(0,n.getDeltaXY)(a),a.target,g(a,e.lockRef.current))},[]),w=i.useCallback(function(a){h(a.type,(0,n.getTouchXY)(a),a.target,g(a,e.lockRef.current))},[]);i.useEffect(function(){return s.push(v),e.setCallbacks({onScrollCapture:_,onWheelCapture:_,onTouchMoveCapture:w}),document.addEventListener("wheel",b,c.nonPassive),document.addEventListener("touchmove",b,c.nonPassive),document.addEventListener("touchstart",y,c.nonPassive),function(){s=s.filter(function(e){return e!==v}),document.removeEventListener("wheel",b,c.nonPassive),document.removeEventListener("touchmove",b,c.nonPassive),document.removeEventListener("touchstart",y,c.nonPassive)}},[]);var j=e.removeScrollBar,k=e.inert;return i.createElement(i.Fragment,null,k?i.createElement(v,{styles:generateStyle(f)}):null,j?i.createElement(r.RemoveScrollBar,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}}(Ke)),Ke}function requireCombination(){if(pt)return J;pt=1,Object.defineProperty(J,"__esModule",{value:!0});var n=X,a=n.__importStar(e),i=requireUI(),r=n.__importDefault(function(){if(lt)return Ue;lt=1,Object.defineProperty(Ue,"__esModule",{value:!0});var e=requireEs5$3(),n=requireSideEffect(),a=requireMedium();return Ue.default=(0,e.exportSidecar)(a.effectCar,n.RemoveScrollSideCar),Ue}()),o=a.forwardRef(function(e,o){return a.createElement(i.RemoveScroll,n.__assign({},e,{ref:o,sideCar:r.default}))});return o.classNames=i.RemoveScroll.classNames,J.default=o,J}Object.defineProperty(U,"__esModule",{value:!0});var dt=U.RemoveScroll=void 0,ut=X.__importDefault(requireCombination());dt=U.RemoveScroll=ut.default;var mt={};!function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.suppressOthers=e.supportsInert=e.inertOthers=e.hideOthers=void 0;var getDefaultParent=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},n=new WeakMap,a=new WeakMap,i={},r=0,unwrapHost=function(e){return e&&(e.host||unwrapHost(e.parentNode))},applyAttributeToOthers=function(e,o,c,l){var p=function(e,n){return n.map(function(n){if(e.contains(n))return n;var a=unwrapHost(n);return a&&e.contains(a)?a:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(e){return Boolean(e)})}(o,Array.isArray(e)?e:[e]);i[c]||(i[c]=new WeakMap);var s=i[c],d=[],u=new Set,m=new Set(p),keep=function(e){e&&!u.has(e)&&(u.add(e),keep(e.parentNode))};p.forEach(keep);var deep=function(e){e&&!m.has(e)&&Array.prototype.forEach.call(e.children,function(e){if(u.has(e))deep(e);else try{var i=e.getAttribute(l),r=null!==i&&"false"!==i,o=(n.get(e)||0)+1,p=(s.get(e)||0)+1;n.set(e,o),s.set(e,p),d.push(e),1===o&&r&&a.set(e,!0),1===p&&e.setAttribute(c,"true"),r||e.setAttribute(l,"true")}catch(n){console.error("aria-hidden: cannot operate on ",e,n)}})};return deep(o),u.clear(),r++,function(){d.forEach(function(e){var i=n.get(e)-1,r=s.get(e)-1;n.set(e,i),s.set(e,r),i||(a.has(e)||e.removeAttribute(l),a.delete(e)),r||e.removeAttribute(c)}),--r||(n=new WeakMap,n=new WeakMap,a=new WeakMap,i={})}};e.hideOthers=function(e,n,a){void 0===a&&(a="data-aria-hidden");var i=Array.from(Array.isArray(e)?e:[e]),r=n||getDefaultParent(e);return r?(i.push.apply(i,Array.from(r.querySelectorAll("[aria-live], script"))),applyAttributeToOthers(i,r,a,"aria-hidden")):function(){return null}};e.inertOthers=function(e,n,a){void 0===a&&(a="data-inert-ed");var i=n||getDefaultParent(e);return i?applyAttributeToOthers(e,i,a,"inert"):function(){return null}};e.supportsInert=function(){return"undefined"!=typeof HTMLElement&&HTMLElement.prototype.hasOwnProperty("inert")};e.suppressOthers=function(n,a,i){return void 0===i&&(i="data-suppressed"),((0,e.supportsInert)()?e.inertOthers:e.hideOthers)(n,a,i)}}(mt);var ft=e.forwardRef((n,i)=>{const{children:r,...o}=n,c=e.Children.toArray(r),l=c.find(isSlottable);if(l){const n=l.props.children,r=c.map(a=>a===l?e.Children.count(n)>1?e.Children.only(null):e.isValidElement(n)?n.props.children:null:a);return a.jsx(vt,{...o,ref:i,children:e.isValidElement(n)?e.cloneElement(n,void 0,r):null})}return a.jsx(vt,{...o,ref:i,children:r})});ft.displayName="Slot";var vt=e.forwardRef((n,a)=>{const{children:i,...r}=n;if(e.isValidElement(i)){const n=function(e){let n=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,a=n&&"isReactWarning"in n&&n.isReactWarning;if(a)return e.ref;if(n=Object.getOwnPropertyDescriptor(e,"ref")?.get,a=n&&"isReactWarning"in n&&n.isReactWarning,a)return e.props.ref;return e.props.ref||e.ref}(i);return e.cloneElement(i,{...mergeProps(r,i.props),ref:a?O(a,n):n})}return e.Children.count(i)>1?e.Children.only(null):null});vt.displayName="SlotClone";var Slottable=({children:e})=>a.jsx(a.Fragment,{children:e});function isSlottable(n){return e.isValidElement(n)&&n.type===Slottable}function mergeProps(e,n){const a={...n};for(const i in n){const r=e[i],o=n[i];/^on[A-Z]/.test(i)?r&&o?a[i]=(...e)=>{o(...e),r(...e)}:r&&(a[i]=r):"style"===i?a[i]={...r,...o}:"className"===i&&(a[i]=[r,o].filter(Boolean).join(" "))}return{...e,...a}}var xt="Dialog",[gt,bt]=w(xt),[ht,yt]=gt(xt),Dialog$1=n=>{const{__scopeDialog:i,children:r,open:o,defaultOpen:c,onOpenChange:l,modal:p=!0}=n,s=e.useRef(null),d=e.useRef(null),[u=!1,m]=_({prop:o,defaultProp:c,onChange:l});return a.jsx(ht,{scope:i,triggerRef:s,contentRef:d,contentId:useId(),titleId:useId(),descriptionId:useId(),open:u,onOpenChange:m,onOpenToggle:e.useCallback(()=>m(e=>!e),[m]),modal:p,children:r})};Dialog$1.displayName=xt;var _t="DialogTrigger",wt=e.forwardRef((e,n)=>{const{__scopeDialog:i,...r}=e,o=yt(_t,i),c=k(n,o.triggerRef);return a.jsx(S.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":getState(o.open),...r,ref:c,onClick:y(e.onClick,o.onOpenToggle)})});wt.displayName=_t;var jt="DialogPortal",[kt,St]=gt(jt,{forceMount:void 0}),DialogPortal$1=n=>{const{__scopeDialog:i,forceMount:r,children:o,container:c}=n,l=yt(jt,i);return a.jsx(kt,{scope:i,forceMount:r,children:e.Children.map(o,e=>a.jsx(Presence,{present:r||l.open,children:a.jsx(B,{asChild:!0,container:c,children:e})}))})};DialogPortal$1.displayName=jt;var Et="DialogOverlay",Ot=e.forwardRef((e,n)=>{const i=St(Et,e.__scopeDialog),{forceMount:r=i.forceMount,...o}=e,c=yt(Et,e.__scopeDialog);return c.modal?a.jsx(Presence,{present:r||c.open,children:a.jsx(Pt,{...o,ref:n})}):null});Ot.displayName=Et;var Pt=e.forwardRef((e,n)=>{const{__scopeDialog:i,...r}=e,o=yt(Et,i);return a.jsx(dt,{as:ft,allowPinchZoom:!0,shards:[o.contentRef],children:a.jsx(S.div,{"data-state":getState(o.open),...r,ref:n,style:{pointerEvents:"auto",...r.style}})})}),Ct="DialogContent",Rt=e.forwardRef((e,n)=>{const i=St(Ct,e.__scopeDialog),{forceMount:r=i.forceMount,...o}=e,c=yt(Ct,e.__scopeDialog);return a.jsx(Presence,{present:r||c.open,children:c.modal?a.jsx(Dt,{...o,ref:n}):a.jsx(At,{...o,ref:n})})});Rt.displayName=Ct;var Dt=e.forwardRef((n,i)=>{const r=yt(Ct,n.__scopeDialog),o=e.useRef(null),c=k(i,r.contentRef,o);return e.useEffect(()=>{const e=o.current;if(e)return mt.hideOthers(e)},[]),a.jsx(Tt,{...n,ref:c,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:y(n.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:y(n.onPointerDownOutside,e=>{const n=e.detail.originalEvent,a=0===n.button&&!0===n.ctrlKey;(2===n.button||a)&&e.preventDefault()}),onFocusOutside:y(n.onFocusOutside,e=>e.preventDefault())})}),At=e.forwardRef((n,i)=>{const r=yt(Ct,n.__scopeDialog),o=e.useRef(!1),c=e.useRef(!1);return a.jsx(Tt,{...n,ref:i,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:e=>{n.onCloseAutoFocus?.(e),e.defaultPrevented||(o.current||r.triggerRef.current?.focus(),e.preventDefault()),o.current=!1,c.current=!1},onInteractOutside:e=>{n.onInteractOutside?.(e),e.defaultPrevented||(o.current=!0,"pointerdown"===e.detail.originalEvent.type&&(c.current=!0));const a=e.target,i=r.triggerRef.current?.contains(a);i&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&c.current&&e.preventDefault()}})}),Tt=e.forwardRef((n,i)=>{const{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:c,onCloseAutoFocus:l,...p}=n,s=yt(Ct,r),d=e.useRef(null),u=k(i,d);return e.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??createFocusGuard()),document.body.insertAdjacentElement("beforeend",e[1]??createFocusGuard()),H++,()=>{1===H&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),H--}},[]),a.jsxs(a.Fragment,{children:[a.jsx(W,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:c,onUnmountAutoFocus:l,children:a.jsx(M,{role:"dialog",id:s.contentId,"aria-describedby":s.descriptionId,"aria-labelledby":s.titleId,"data-state":getState(s.open),...p,ref:u,onDismiss:()=>s.onOpenChange(!1)})}),a.jsxs(a.Fragment,{children:[a.jsx(TitleWarning,{titleId:s.titleId}),a.jsx(DescriptionWarning,{contentRef:d,descriptionId:s.descriptionId})]})]})}),Nt="DialogTitle",zt=e.forwardRef((e,n)=>{const{__scopeDialog:i,...r}=e,o=yt(Nt,i);return a.jsx(S.h2,{id:o.titleId,...r,ref:n})});zt.displayName=Nt;var Mt="DialogDescription",Ft=e.forwardRef((e,n)=>{const{__scopeDialog:i,...r}=e,o=yt(Mt,i);return a.jsx(S.p,{id:o.descriptionId,...r,ref:n})});Ft.displayName=Mt;var It="DialogClose",qt=e.forwardRef((e,n)=>{const{__scopeDialog:i,...r}=e,o=yt(It,i);return a.jsx(S.button,{type:"button",...r,ref:n,onClick:y(e.onClick,()=>o.onOpenChange(!1))})});function getState(e){return e?"open":"closed"}qt.displayName=It;var Lt="DialogTitleWarning",[Wt,$t]=j(Lt,{contentName:Ct,titleName:Nt,docsSlug:"dialog"}),TitleWarning=({titleId:n})=>{const a=$t(Lt),i=`\`${a.contentName}\` requires a \`${a.titleName}\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \`${a.titleName}\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${a.docsSlug}`;return e.useEffect(()=>{if(n){document.getElementById(n)||console.error(i)}},[i,n]),null},DescriptionWarning=({contentRef:n,descriptionId:a})=>{const i=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${$t("DialogDescriptionWarning").contentName}}.`;return e.useEffect(()=>{const e=n.current?.getAttribute("aria-describedby");if(a&&e){document.getElementById(a)||console.warn(i)}},[i,n,a]),null},Bt=Ot,Ht=Rt,Ut=zt,Kt=Ft,Gt=qt;const Yt=Dialog$1,Vt=wt,Xt=DialogPortal$1,Zt=e.forwardRef(({className:e,...n},i)=>a.jsx(Bt,{ref:i,className:o("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...n}));Zt.displayName=Bt.displayName;const Jt=e.forwardRef(({className:e,children:n,...i},r)=>a.jsxs(Xt,{children:[a.jsx(Zt,{}),a.jsxs(Ht,{ref:r,className:o("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...i,children:[n,a.jsxs(Gt,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[a.jsx(f,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Jt.displayName=Ht.displayName;const DialogHeader=({className:e,...n})=>a.jsx("div",{className:o("flex flex-col space-y-1.5 text-center sm:text-left",e),...n});DialogHeader.displayName="DialogHeader";const Qt=e.forwardRef(({className:e,...n},i)=>a.jsx(Ut,{ref:i,className:o("text-lg font-semibold leading-none tracking-tight",e),...n}));Qt.displayName=Ut.displayName;const en=e.forwardRef(({className:e,...n},i)=>a.jsx(Kt,{ref:i,className:o("text-sm text-muted-foreground",e),...n}));function PrivacyNotice(){const[n,i]=e.useState(!1),[r,o]=e.useState(null),{clearAllData:c,getDataUsage:l}={clearAllData:async()=>{try{const e=x.getCurrentSession();return await g.clearSession(e.id),x.clearSession(),document.querySelectorAll('img[src^="blob:"]').forEach(e=>{const n=e.src;n.startsWith("blob:")&&URL.revokeObjectURL(n)}),!0}catch(e){return console.error("Failed to clear all data:",e),!1}},getDataUsage:async()=>{try{const e=x.getCurrentSession(),n=await g.getSessionImages(e.id),a=n.reduce((e,n)=>e+n.blob.size,0);return{totalSize:a,totalImages:n.length,sizeMB:(a/1048576).toFixed(2)}}catch(e){return console.error("Failed to get data usage:",e),null}}};return a.jsxs(Yt,{open:n,onOpenChange:i,children:[a.jsx(Vt,{asChild:!0,children:a.jsxs(p,{variant:"ghost",size:"sm",className:"text-graphite-60 hover:text-graphite-90",onClick:async()=>{const e=await l();o(e)},children:[a.jsx(s,{className:"h-4 w-4 mr-1"}),"Privacy"]})}),a.jsxs(Jt,{className:"max-w-md",children:[a.jsxs(DialogHeader,{children:[a.jsxs(Qt,{className:"flex items-center gap-2",children:[a.jsx(s,{className:"h-5 w-5 text-success-green"}),"Privacy & Data Protection"]}),a.jsx(en,{children:"Your privacy is our priority. Here's how we handle your images."})]}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:"space-y-3",children:[a.jsxs("div",{className:"flex items-start gap-3",children:[a.jsx(d,{className:"h-5 w-5 text-blue-500 mt-0.5"}),a.jsxs("div",{children:[a.jsx("h4",{className:"font-medium text-sm",children:"Local Storage Only"}),a.jsx("p",{className:"text-xs text-graphite-60",children:"Images are stored temporarily in your browser's IndexedDB, never on our servers"})]})]}),a.jsxs("div",{className:"flex items-start gap-3",children:[a.jsx(s,{className:"h-5 w-5 text-success-green mt-0.5"}),a.jsxs("div",{children:[a.jsx("h4",{className:"font-medium text-sm",children:"No Server Upload"}),a.jsx("p",{className:"text-xs text-graphite-60",children:"AI analysis happens client-side, images never leave your device"})]})]}),a.jsxs("div",{className:"flex items-start gap-3",children:[a.jsx(u,{className:"h-5 w-5 text-orange-500 mt-0.5"}),a.jsxs("div",{children:[a.jsx("h4",{className:"font-medium text-sm",children:"Auto-Cleanup"}),a.jsx("p",{className:"text-xs text-graphite-60",children:"Images are automatically deleted after 24 hours or when you close the tab"})]})]})]}),r&&a.jsxs("div",{className:"border-t pt-4",children:[a.jsx("h4",{className:"font-medium text-sm mb-2",children:"Current Usage"}),a.jsxs("div",{className:"flex gap-2",children:[a.jsxs(v,{variant:"outline",children:[r.totalImages," images"]}),a.jsxs(v,{variant:"outline",children:[r.sizeMB," MB"]})]})]}),a.jsxs("div",{className:"border-t pt-4 space-y-2",children:[a.jsx("h4",{className:"font-medium text-sm",children:"Manual Controls"}),a.jsxs(p,{variant:"secondary",size:"sm",onClick:async()=>{await c()&&o({totalImages:0,sizeMB:"0.00"})},className:"w-full",children:[a.jsx(u,{className:"h-4 w-4 mr-2"}),"Clear All Data Now"]}),a.jsx("p",{className:"text-xs text-graphite-60",children:"This will remove all images and analysis results from your browser"})]}),a.jsx("div",{className:"bg-blue-50 p-3 rounded-md",children:a.jsxs("div",{className:"flex gap-2",children:[a.jsx(m,{className:"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"}),a.jsx("p",{className:"text-xs text-blue-700",children:"For maximum privacy, use incognito/private browsing mode. All data will be automatically deleted when you close the window."})]})})]})]})]})}en.displayName=Kt.displayName;var tn={exports:{}};function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction;tn.exports=function(){function shim(e,n,a,i,r,o){if("SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"!==o){var c=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}();const nn=c(tn.exports);var an={},rn={},on={};!function(e){function setObjProp(e,n,a){Object.defineProperty(e,n,{value:a,writable:!1,configurable:!1,enumerable:!0})}Object.defineProperty(e,"__esModule",{value:!0}),e.COMMON_MIME_TYPES=void 0,e.toFileWithPath=function(n,a,i){var r=function(n){var a=n.name;if(a&&-1!==a.lastIndexOf(".")&&!n.type){var i=a.split(".").pop().toLowerCase(),r=e.COMMON_MIME_TYPES.get(i);r&&Object.defineProperty(n,"type",{value:r,writable:!1,configurable:!1,enumerable:!0})}return n}(n),o=n.webkitRelativePath,c="string"==typeof a?a:"string"==typeof o&&o.length>0?o:"./".concat(n.name);"string"!=typeof r.path&&setObjProp(r,"path",c);void 0!==i&&Object.defineProperty(r,"handle",{value:i,writable:!1,configurable:!1,enumerable:!0});return setObjProp(r,"relativePath",c),r},e.COMMON_MIME_TYPES=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]])}(on);var cn=rn&&rn.__awaiter||function(e,n,a,i){return new(a||(a=Promise))(function(r,o){function fulfilled(e){try{step(i.next(e))}catch(e){o(e)}}function rejected(e){try{step(i.throw(e))}catch(e){o(e)}}function step(e){var n;e.done?r(e.value):(n=e.value,n instanceof a?n:new a(function(e){e(n)})).then(fulfilled,rejected)}step((i=i.apply(e,n||[])).next())})},ln=rn&&rn.__generator||function(e,n){var a,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},c=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return c.next=verb(0),c.throw=verb(1),c.return=verb(2),"function"==typeof Symbol&&(c[Symbol.iterator]=function(){return this}),c;function verb(l){return function(p){return function(l){if(a)throw new TypeError("Generator is already executing.");for(;c&&(c=0,l[0]&&(o=0)),o;)try{if(a=1,i&&(r=2&l[0]?i.return:l[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,l[1])).done)return r;switch(i=0,r&&(l=[2&l[0],r.value]),l[0]){case 0:case 1:r=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,i=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!r||l[1]>r[0]&&l[1]<r[3])){o.label=l[1];break}if(6===l[0]&&o.label<r[1]){o.label=r[1],r=l;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(l);break}r[2]&&o.ops.pop(),o.trys.pop();continue}l=n.call(e,o)}catch(e){l=[6,e],i=0}finally{a=r=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,p])}}},pn=rn&&rn.__read||function(e,n){var a="function"==typeof Symbol&&e[Symbol.iterator];if(!a)return e;var i,r,o=a.call(e),c=[];try{for(;(void 0===n||n-- >0)&&!(i=o.next()).done;)c.push(i.value)}catch(e){r={error:e}}finally{try{i&&!i.done&&(a=o.return)&&a.call(o)}finally{if(r)throw r.error}}return c},sn=rn&&rn.__spreadArray||function(e,n,a){if(a||2===arguments.length)for(var i,r=0,o=n.length;r<o;r++)!i&&r in n||(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))};Object.defineProperty(rn,"__esModule",{value:!0}),rn.fromEvent=function(e){return cn(this,void 0,void 0,function(){return ln(this,function(n){return isObject(e)&&isObject(e.dataTransfer)?[2,getDataTransferFiles(e.dataTransfer,e.type)]:function(e){return isObject(e)&&isObject(e.target)}(e)?[2,getInputFiles(e)]:Array.isArray(e)&&e.every(function(e){return"getFile"in e&&"function"==typeof e.getFile})?[2,getFsHandleFiles(e)]:[2,[]]})})};var dn=on,un=[".DS_Store","Thumbs.db"];function isObject(e){return"object"==typeof e&&null!==e}function getInputFiles(e){return fromList(e.target.files).map(function(e){return(0,dn.toFileWithPath)(e)})}function getFsHandleFiles(e){return cn(this,void 0,void 0,function(){return ln(this,function(n){switch(n.label){case 0:return[4,Promise.all(e.map(function(e){return e.getFile()}))];case 1:return[2,n.sent().map(function(e){return(0,dn.toFileWithPath)(e)})]}})})}function getDataTransferFiles(e,n){return cn(this,void 0,void 0,function(){var a;return ln(this,function(i){switch(i.label){case 0:return e.items?(a=fromList(e.items).filter(function(e){return"file"===e.kind}),"drop"!==n?[2,a]:[4,Promise.all(a.map(toFilePromises))]):[3,2];case 1:return[2,noIgnoredFiles(flatten(i.sent()))];case 2:return[2,noIgnoredFiles(fromList(e.files).map(function(e){return(0,dn.toFileWithPath)(e)}))]}})})}function noIgnoredFiles(e){return e.filter(function(e){return-1===un.indexOf(e.name)})}function fromList(e){if(null===e)return[];for(var n=[],a=0;a<e.length;a++){var i=e[a];n.push(i)}return n}function toFilePromises(e){if("function"!=typeof e.webkitGetAsEntry)return fromDataTransferItem(e);var n=e.webkitGetAsEntry();return n&&n.isDirectory?fromDirEntry(n):fromDataTransferItem(e,n)}function flatten(e){return e.reduce(function(e,n){return sn(sn([],pn(e),!1),pn(Array.isArray(n)?flatten(n):[n]),!1)},[])}function fromDataTransferItem(e,n){return cn(this,void 0,void 0,function(){var a,i,r,o;return ln(this,function(c){switch(c.label){case 0:return globalThis.isSecureContext&&"function"==typeof e.getAsFileSystemHandle?[4,e.getAsFileSystemHandle()]:[3,3];case 1:if(null===(a=c.sent()))throw new Error("".concat(e," is not a File"));return void 0===a?[3,3]:[4,a.getFile()];case 2:return(i=c.sent()).handle=a,[2,(0,dn.toFileWithPath)(i)];case 3:if(!(r=e.getAsFile()))throw new Error("".concat(e," is not a File"));return[2,(0,dn.toFileWithPath)(r,null!==(o=null==n?void 0:n.fullPath)&&void 0!==o?o:void 0)]}})})}function fromEntry(e){return cn(this,void 0,void 0,function(){return ln(this,function(n){return[2,e.isDirectory?fromDirEntry(e):fromFileEntry(e)]})})}function fromDirEntry(e){var n=e.createReader();return new Promise(function(e,a){var i=[];!function readEntries(){var r=this;n.readEntries(function(n){return cn(r,void 0,void 0,function(){var r,o,c;return ln(this,function(l){switch(l.label){case 0:if(n.length)return[3,5];l.label=1;case 1:return l.trys.push([1,3,,4]),[4,Promise.all(i)];case 2:return r=l.sent(),e(r),[3,4];case 3:return o=l.sent(),a(o),[3,4];case 4:return[3,6];case 5:c=Promise.all(n.map(fromEntry)),i.push(c),readEntries(),l.label=6;case 6:return[2]}})})},function(e){a(e)})}()})}function fromFileEntry(e){return cn(this,void 0,void 0,function(){return ln(this,function(n){return[2,new Promise(function(n,a){e.file(function(a){var i=(0,dn.toFileWithPath)(a,e.fullPath);n(i)},function(e){a(e)})})]})})}!function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.fromEvent=void 0;var n=rn;Object.defineProperty(e,"fromEvent",{enumerable:!0,get:function(){return n.fromEvent}})}(an);const mn=c(function(e){var n={};function t(a){if(n[a])return n[a].exports;var i=n[a]={i:a,l:!1,exports:{}};return e[a].call(i.exports,i,i.exports,t),i.l=!0,i.exports}return t.m=e,t.c=n,t.d=function(e,n,a){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:a})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var a=Object.create(null);if(t.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var i in e)t.d(a,i,function(n){return e[n]}.bind(null,i));return a},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p="",t(t.s=0)}([function(e,n,a){n.__esModule=!0,n.default=function(e,n){if(e&&n){var a=Array.isArray(n)?n:n.split(",");if(0===a.length)return!0;var i=e.name||"",r=(e.type||"").toLowerCase(),o=r.replace(/\/.*$/,"");return a.some(function(e){var n=e.trim().toLowerCase();return"."===n.charAt(0)?i.toLowerCase().endsWith(n):n.endsWith("/*")?o===n.replace(/\/.*$/,""):r===n})}return!0}}]));function _toConsumableArray$1(e){return function(e){if(Array.isArray(e))return _arrayLikeToArray$1(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||_unsupportedIterableToArray$1(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ownKeys$1(e,n){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);n&&(i=i.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),a.push.apply(a,i)}return a}function _objectSpread$1(e){for(var n=1;n<arguments.length;n++){var a=null!=arguments[n]?arguments[n]:{};n%2?ownKeys$1(Object(a),!0).forEach(function(n){_defineProperty$1(e,n,a[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):ownKeys$1(Object(a)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(a,n))})}return e}function _defineProperty$1(e,n,a){return n in e?Object.defineProperty(e,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[n]=a,e}function _slicedToArray$1(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==a)return;var i,r,o=[],c=!0,l=!1;try{for(a=a.call(e);!(c=(i=a.next()).done)&&(o.push(i.value),!n||o.length!==n);c=!0);}catch(e){l=!0,r=e}finally{try{c||null==a.return||a.return()}finally{if(l)throw r}}return o}(e,n)||_unsupportedIterableToArray$1(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _unsupportedIterableToArray$1(e,n){if(e){if("string"==typeof e)return _arrayLikeToArray$1(e,n);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?_arrayLikeToArray$1(e,n):void 0}}function _arrayLikeToArray$1(e,n){(null==n||n>e.length)&&(n=e.length);for(var a=0,i=new Array(n);a<n;a++)i[a]=e[a];return i}var fn="function"==typeof mn?mn:mn.default,getInvalidTypeRejectionErr=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").split(","),n=e.length>1?"one of ".concat(e.join(", ")):e[0];return{code:"file-invalid-type",message:"File type must be ".concat(n)}},getTooLargeRejectionErr=function(e){return{code:"file-too-large",message:"File is larger than ".concat(e," ").concat(1===e?"byte":"bytes")}},getTooSmallRejectionErr=function(e){return{code:"file-too-small",message:"File is smaller than ".concat(e," ").concat(1===e?"byte":"bytes")}},vn={code:"too-many-files",message:"Too many files"};function fileAccepted(e,n){var a="application/x-moz-file"===e.type||fn(e,n);return[a,a?null:getInvalidTypeRejectionErr(n)]}function fileMatchSize(e,n,a){if(isDefined(e.size))if(isDefined(n)&&isDefined(a)){if(e.size>a)return[!1,getTooLargeRejectionErr(a)];if(e.size<n)return[!1,getTooSmallRejectionErr(n)]}else{if(isDefined(n)&&e.size<n)return[!1,getTooSmallRejectionErr(n)];if(isDefined(a)&&e.size>a)return[!1,getTooLargeRejectionErr(a)]}return[!0,null]}function isDefined(e){return null!=e}function isPropagationStopped(e){return"function"==typeof e.isPropagationStopped?e.isPropagationStopped():void 0!==e.cancelBubble&&e.cancelBubble}function isEvtWithFiles(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(e){return"Files"===e||"application/x-moz-file"===e}):!!e.target&&!!e.target.files}function onDocumentDragOver(e){e.preventDefault()}function composeEventHandlers(){for(var e=arguments.length,n=new Array(e),a=0;a<e;a++)n[a]=arguments[a];return function(e){for(var a=arguments.length,i=new Array(a>1?a-1:0),r=1;r<a;r++)i[r-1]=arguments[r];return n.some(function(n){return!isPropagationStopped(e)&&n&&n.apply(void 0,[e].concat(i)),isPropagationStopped(e)})}}function isMIMEType(e){return"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||"application/*"===e||/\w+\/[-+.\w]+/g.test(e)}function isExt(e){return/^.*\.[\w]+$/.test(e)}var xn=["children"],gn=["open"],bn=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],hn=["refKey","onChange","onClick"];function _toConsumableArray(e){return function(e){if(Array.isArray(e))return _arrayLikeToArray(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||_unsupportedIterableToArray(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _slicedToArray(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==a)return;var i,r,o=[],c=!0,l=!1;try{for(a=a.call(e);!(c=(i=a.next()).done)&&(o.push(i.value),!n||o.length!==n);c=!0);}catch(e){l=!0,r=e}finally{try{c||null==a.return||a.return()}finally{if(l)throw r}}return o}(e,n)||_unsupportedIterableToArray(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _unsupportedIterableToArray(e,n){if(e){if("string"==typeof e)return _arrayLikeToArray(e,n);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?_arrayLikeToArray(e,n):void 0}}function _arrayLikeToArray(e,n){(null==n||n>e.length)&&(n=e.length);for(var a=0,i=new Array(n);a<n;a++)i[a]=e[a];return i}function ownKeys(e,n){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);n&&(i=i.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),a.push.apply(a,i)}return a}function _objectSpread(e){for(var n=1;n<arguments.length;n++){var a=null!=arguments[n]?arguments[n]:{};n%2?ownKeys(Object(a),!0).forEach(function(n){_defineProperty(e,n,a[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(a,n))})}return e}function _defineProperty(e,n,a){return n in e?Object.defineProperty(e,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[n]=a,e}function _objectWithoutProperties(e,n){if(null==e)return{};var a,i,r=function(e,n){if(null==e)return{};var a,i,r={},o=Object.keys(e);for(i=0;i<o.length;i++)a=o[i],n.indexOf(a)>=0||(r[a]=e[a]);return r}(e,n);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)a=o[i],n.indexOf(a)>=0||Object.prototype.propertyIsEnumerable.call(e,a)&&(r[a]=e[a])}return r}var yn=e.forwardRef(function(n,a){var i=n.children,r=useDropzone(_objectWithoutProperties(n,xn)),o=r.open,c=_objectWithoutProperties(r,gn);return e.useImperativeHandle(a,function(){return{open:o}},[o]),l.createElement(e.Fragment,null,i(_objectSpread(_objectSpread({},c),{},{open:o})))});yn.displayName="Dropzone";var _n={disabled:!1,getFilesFromEvent:an.fromEvent,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};yn.defaultProps=_n,yn.propTypes={children:nn.func,accept:nn.objectOf(nn.arrayOf(nn.string)),multiple:nn.bool,preventDropOnDocument:nn.bool,noClick:nn.bool,noKeyboard:nn.bool,noDrag:nn.bool,noDragEventsBubbling:nn.bool,minSize:nn.number,maxSize:nn.number,maxFiles:nn.number,disabled:nn.bool,getFilesFromEvent:nn.func,onFileDialogCancel:nn.func,onFileDialogOpen:nn.func,useFsAccessApi:nn.bool,autoFocus:nn.bool,onDragEnter:nn.func,onDragLeave:nn.func,onDragOver:nn.func,onDrop:nn.func,onDropAccepted:nn.func,onDropRejected:nn.func,onError:nn.func,validator:nn.func};var wn={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function useDropzone(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=_objectSpread(_objectSpread({},_n),n),i=a.accept,r=a.disabled,o=a.getFilesFromEvent,c=a.maxSize,l=a.minSize,p=a.multiple,s=a.maxFiles,d=a.onDragEnter,u=a.onDragLeave,m=a.onDragOver,f=a.onDrop,v=a.onDropAccepted,x=a.onDropRejected,g=a.onFileDialogCancel,b=a.onFileDialogOpen,h=a.useFsAccessApi,y=a.autoFocus,_=a.preventDropOnDocument,w=a.noClick,j=a.noKeyboard,k=a.noDrag,S=a.noDragEventsBubbling,E=a.onError,O=a.validator,P=e.useMemo(function(){return function(e){if(isDefined(e))return Object.entries(e).reduce(function(e,n){var a=_slicedToArray$1(n,2),i=a[0],r=a[1];return[].concat(_toConsumableArray$1(e),[i],_toConsumableArray$1(r))},[]).filter(function(e){return isMIMEType(e)||isExt(e)}).join(",")}(i)},[i]),C=e.useMemo(function(){return function(e){return isDefined(e)?[{description:"Files",accept:Object.entries(e).filter(function(e){var n=_slicedToArray$1(e,2),a=n[0],i=n[1],r=!0;return isMIMEType(a)||(console.warn('Skipped "'.concat(a,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),r=!1),Array.isArray(i)&&i.every(isExt)||(console.warn('Skipped "'.concat(a,'" because an invalid file extension was provided.')),r=!1),r}).reduce(function(e,n){var a=_slicedToArray$1(n,2),i=a[0],r=a[1];return _objectSpread$1(_objectSpread$1({},e),{},_defineProperty$1({},i,r))},{})}]:e}(i)},[i]),R=e.useMemo(function(){return"function"==typeof b?b:noop},[b]),D=e.useMemo(function(){return"function"==typeof g?g:noop},[g]),A=e.useRef(null),T=e.useRef(null),N=_slicedToArray(e.useReducer(reducer,wn),2),z=N[0],M=N[1],F=z.isFocused,I=z.isFileDialogActive,q=e.useRef(!1),onWindowFocus=function(){!q.current&&I&&setTimeout(function(){T.current&&(T.current.files.length||(M({type:"closeDialog"}),D()))},300)};e.useEffect(function(){return window.addEventListener("focus",onWindowFocus,!1),function(){window.removeEventListener("focus",onWindowFocus,!1)}},[T,I,D,q]);var L=e.useRef([]),onDocumentDrop=function(e){A.current&&A.current.contains(e.target)||(e.preventDefault(),L.current=[])};e.useEffect(function(){return _&&(document.addEventListener("dragover",onDocumentDragOver,!1),document.addEventListener("drop",onDocumentDrop,!1)),function(){_&&(document.removeEventListener("dragover",onDocumentDragOver),document.removeEventListener("drop",onDocumentDrop))}},[A,_]),e.useEffect(function(){return!r&&y&&A.current&&A.current.focus(),function(){}},[A,y,r]);var W=e.useCallback(function(e){E?E(e):console.error(e)},[E]),$=e.useCallback(function(e){e.preventDefault(),e.persist(),stopPropagation(e),L.current=[].concat(_toConsumableArray(L.current),[e.target]),isEvtWithFiles(e)&&Promise.resolve(o(e)).then(function(n){if(!isPropagationStopped(e)||S){var a=n.length,i=a>0&&function(e){var n=e.files,a=e.accept,i=e.minSize,r=e.maxSize,o=e.multiple,c=e.maxFiles,l=e.validator;return!(!o&&n.length>1||o&&c>=1&&n.length>c)&&n.every(function(e){var n=_slicedToArray$1(fileAccepted(e,a),1)[0],o=_slicedToArray$1(fileMatchSize(e,i,r),1)[0],c=l?l(e):null;return n&&o&&!c})}({files:n,accept:P,minSize:l,maxSize:c,multiple:p,maxFiles:s,validator:O});M({isDragAccept:i,isDragReject:a>0&&!i,isDragActive:!0,type:"setDraggedFiles"}),d&&d(e)}}).catch(function(e){return W(e)})},[o,d,W,S,P,l,c,p,s,O]),B=e.useCallback(function(e){e.preventDefault(),e.persist(),stopPropagation(e);var n=isEvtWithFiles(e);if(n&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch(e){}return n&&m&&m(e),!1},[m,S]),H=e.useCallback(function(e){e.preventDefault(),e.persist(),stopPropagation(e);var n=L.current.filter(function(e){return A.current&&A.current.contains(e)}),a=n.indexOf(e.target);-1!==a&&n.splice(a,1),L.current=n,n.length>0||(M({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),isEvtWithFiles(e)&&u&&u(e))},[A,u,S]),U=e.useCallback(function(e,n){var a=[],i=[];e.forEach(function(e){var n=_slicedToArray(fileAccepted(e,P),2),r=n[0],o=n[1],p=_slicedToArray(fileMatchSize(e,l,c),2),s=p[0],d=p[1],u=O?O(e):null;if(r&&s&&!u)a.push(e);else{var m=[o,d];u&&(m=m.concat(u)),i.push({file:e,errors:m.filter(function(e){return e})})}}),(!p&&a.length>1||p&&s>=1&&a.length>s)&&(a.forEach(function(e){i.push({file:e,errors:[vn]})}),a.splice(0)),M({acceptedFiles:a,fileRejections:i,isDragReject:i.length>0,type:"setFiles"}),f&&f(a,i,n),i.length>0&&x&&x(i,n),a.length>0&&v&&v(a,n)},[M,p,P,l,c,s,f,v,x,O]),K=e.useCallback(function(e){e.preventDefault(),e.persist(),stopPropagation(e),L.current=[],isEvtWithFiles(e)&&Promise.resolve(o(e)).then(function(n){isPropagationStopped(e)&&!S||U(n,e)}).catch(function(e){return W(e)}),M({type:"reset"})},[o,U,W,S]),G=e.useCallback(function(){if(q.current){M({type:"openDialog"}),R();var e={multiple:p,types:C};window.showOpenFilePicker(e).then(function(e){return o(e)}).then(function(e){U(e,null),M({type:"closeDialog"})}).catch(function(e){var n;(n=e)instanceof DOMException&&("AbortError"===n.name||n.code===n.ABORT_ERR)?(D(e),M({type:"closeDialog"})):!function(e){return e instanceof DOMException&&("SecurityError"===e.name||e.code===e.SECURITY_ERR)}(e)?W(e):(q.current=!1,T.current?(T.current.value=null,T.current.click()):W(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided.")))})}else T.current&&(M({type:"openDialog"}),R(),T.current.value=null,T.current.click())},[M,R,D,h,U,W,C,p]),Y=e.useCallback(function(e){A.current&&A.current.isEqualNode(e.target)&&(" "!==e.key&&"Enter"!==e.key&&32!==e.keyCode&&13!==e.keyCode||(e.preventDefault(),G()))},[A,G]),V=e.useCallback(function(){M({type:"focus"})},[]),X=e.useCallback(function(){M({type:"blur"})},[]),Z=e.useCallback(function(){w||(!function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return function(e){return-1!==e.indexOf("MSIE")||-1!==e.indexOf("Trident/")}(e)||function(e){return-1!==e.indexOf("Edge/")}(e)}()?G():setTimeout(G,0))},[w,G]),composeHandler=function(e){return r?null:e},composeKeyboardHandler=function(e){return j?null:composeHandler(e)},composeDragHandler=function(e){return k?null:composeHandler(e)},stopPropagation=function(e){S&&e.stopPropagation()},J=e.useMemo(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.refKey,a=void 0===n?"ref":n,i=e.role,o=e.onKeyDown,c=e.onFocus,l=e.onBlur,p=e.onClick,s=e.onDragEnter,d=e.onDragOver,u=e.onDragLeave,m=e.onDrop,f=_objectWithoutProperties(e,bn);return _objectSpread(_objectSpread(_defineProperty({onKeyDown:composeKeyboardHandler(composeEventHandlers(o,Y)),onFocus:composeKeyboardHandler(composeEventHandlers(c,V)),onBlur:composeKeyboardHandler(composeEventHandlers(l,X)),onClick:composeHandler(composeEventHandlers(p,Z)),onDragEnter:composeDragHandler(composeEventHandlers(s,$)),onDragOver:composeDragHandler(composeEventHandlers(d,B)),onDragLeave:composeDragHandler(composeEventHandlers(u,H)),onDrop:composeDragHandler(composeEventHandlers(m,K)),role:"string"==typeof i&&""!==i?i:"presentation"},a,A),r||j?{}:{tabIndex:0}),f)}},[A,Y,V,X,Z,$,B,H,K,j,k,r]),Q=e.useCallback(function(e){e.stopPropagation()},[]),ee=e.useMemo(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.refKey,a=void 0===n?"ref":n,i=e.onChange,r=e.onClick,o=_objectWithoutProperties(e,hn);return _objectSpread(_objectSpread({},_defineProperty({accept:P,multiple:p,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:composeHandler(composeEventHandlers(i,K)),onClick:composeHandler(composeEventHandlers(r,Q)),tabIndex:-1},a,T)),o)}},[T,i,p,K,r]);return _objectSpread(_objectSpread({},z),{},{isFocused:F&&!r,getRootProps:J,getInputProps:ee,rootRef:A,inputRef:T,open:composeHandler(G)})}function reducer(e,n){switch(n.type){case"focus":return _objectSpread(_objectSpread({},e),{},{isFocused:!0});case"blur":return _objectSpread(_objectSpread({},e),{},{isFocused:!1});case"openDialog":return _objectSpread(_objectSpread({},wn),{},{isFileDialogActive:!0});case"closeDialog":return _objectSpread(_objectSpread({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return _objectSpread(_objectSpread({},e),{},{isDragActive:n.isDragActive,isDragAccept:n.isDragAccept,isDragReject:n.isDragReject});case"setFiles":return _objectSpread(_objectSpread({},e),{},{acceptedFiles:n.acceptedFiles,fileRejections:n.fileRejections,isDragReject:n.isDragReject});case"reset":return _objectSpread({},wn);default:return e}}function noop(){}export{PrivacyNotice as P,useDropzone as u};
//# sourceMappingURL=index.mjs.map
