{"version": 3, "file": "uiComponents.mjs", "sources": ["../../../../node_modules/@clerk/clerk-react/dist/internal.mjs", "../../../../node_modules/@clerk/tanstack-react-start/dist/client/uiComponents.js"], "sourcesContent": null, "names": ["useRoutingProps", "componentName", "props", "routingOptions", "path", "routing", "errorThrower", "throw", "noPathProvidedError", "incompatibleRoutingWithPathProvidedError", "usePathnameWithoutSplatRouteParams", "_splat", "useParams", "strict", "pathname", "useLocation", "splatRouteParam", "replace", "trim", "UserProfile", "Object", "assign", "jsx", "UserProfile$1", "OrganizationProfile$1", "OrganizationList$1", "SignIn", "SignIn$1", "SignUp", "SignUp$1"], "mappings": "", "x_google_ignoreList": [0, 1]}