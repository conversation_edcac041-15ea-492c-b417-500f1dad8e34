import{m as e,g as t}from"../nitro/nitro.mjs";import{<PERSON>uffer as r}from"node:buffer";import{q as a,r as s,j as n,c as o}from"./routeTree.gen-BFK54byf.mjs";import{q as i}from"./lucide-react.mjs";var u,l="vercel.ai.error",c=Symbol.for(l),d=class _AISDKError extends Error{constructor({name:e,message:t,cause:r}){super(t),this[u]=!0,this.name=e,this.cause=r}static isInstance(e){return _AISDKError.hasMarker(e,l)}static hasMarker(e,t){const r=Symbol.for(t);return null!=e&&"object"==typeof e&&r in e&&"boolean"==typeof e[r]&&!0===e[r]}};u=c;var p,m=d,f="AI_APICallError",h=`vercel.ai.error.${f}`,g=Symbol.for(h),y=class extends m{constructor({message:e,url:t,requestBodyValues:r,statusCode:a,responseHeaders:s,responseBody:n,cause:o,isRetryable:i=null!=a&&(408===a||409===a||429===a||a>=500),data:u}){super({name:f,message:e,cause:o}),this[p]=!0,this.url=t,this.requestBodyValues=r,this.statusCode=a,this.responseHeaders=s,this.responseBody=n,this.isRetryable=i,this.data=u}static isInstance(e){return m.hasMarker(e,h)}};p=g;var v,_="AI_EmptyResponseBodyError",x=`vercel.ai.error.${_}`,b=Symbol.for(x),T=class extends m{constructor({message:e="Empty response body"}={}){super({name:_,message:e}),this[v]=!0}static isInstance(e){return m.hasMarker(e,x)}};function getErrorMessage$1(e){return null==e?"unknown error":"string"==typeof e?e:e instanceof Error?e.message:JSON.stringify(e)}v=b;var w,I="AI_InvalidArgumentError",S=`vercel.ai.error.${I}`,A=Symbol.for(S),k=class extends m{constructor({message:e,cause:t,argument:r}){super({name:I,message:e,cause:t}),this[w]=!0,this.argument=r}static isInstance(e){return m.hasMarker(e,S)}};w=A;var C,E="AI_InvalidPromptError",P=`vercel.ai.error.${E}`,Z=Symbol.for(P),N=class extends m{constructor({prompt:e,message:t,cause:r}){super({name:E,message:`Invalid prompt: ${t}`,cause:r}),this[C]=!0,this.prompt=e}static isInstance(e){return m.hasMarker(e,P)}};C=Z;var R,O="AI_InvalidResponseDataError",j=`vercel.ai.error.${O}`,D=Symbol.for(j),M=class extends m{constructor({data:e,message:t=`Invalid response data: ${JSON.stringify(e)}.`}){super({name:O,message:t}),this[R]=!0,this.data=e}static isInstance(e){return m.hasMarker(e,j)}};R=D;var $,L="AI_JSONParseError",V=`vercel.ai.error.${L}`,F=Symbol.for(V),U=class extends m{constructor({text:e,cause:t}){super({name:L,message:`JSON parsing failed: Text: ${e}.\nError message: ${getErrorMessage$1(t)}`,cause:t}),this[$]=!0,this.text=e}static isInstance(e){return m.hasMarker(e,V)}};$=F;var B,z="AI_LoadAPIKeyError",q=`vercel.ai.error.${z}`,J=Symbol.for(q),K=class extends m{constructor({message:e}){super({name:z,message:e}),this[B]=!0}static isInstance(e){return m.hasMarker(e,q)}};B=J;var W,H="AI_TooManyEmbeddingValuesForCallError",G=`vercel.ai.error.${H}`,Y=Symbol.for(G),X=class extends m{constructor(e){super({name:H,message:`Too many values for a single embedding call. The ${e.provider} model "${e.modelId}" can only embed up to ${e.maxEmbeddingsPerCall} values per call, but ${e.values.length} values were provided.`}),this[W]=!0,this.provider=e.provider,this.modelId=e.modelId,this.maxEmbeddingsPerCall=e.maxEmbeddingsPerCall,this.values=e.values}static isInstance(e){return m.hasMarker(e,G)}};W=Y;var Q,ee="AI_TypeValidationError",te=`vercel.ai.error.${ee}`,re=Symbol.for(te);Q=re;var ae,se=class _TypeValidationError extends m{constructor({value:e,cause:t}){super({name:ee,message:`Type validation failed: Value: ${JSON.stringify(e)}.\nError message: ${getErrorMessage$1(t)}`,cause:t}),this[Q]=!0,this.value=e}static isInstance(e){return m.hasMarker(e,te)}static wrap({value:e,cause:t}){return _TypeValidationError.isInstance(t)&&t.value===e?t:new _TypeValidationError({value:e,cause:t})}},ne="AI_UnsupportedFunctionalityError",oe=`vercel.ai.error.${ne}`,ie=Symbol.for(oe),ue=class extends m{constructor({functionality:e,message:t=`'${e}' functionality not supported.`}){super({name:ne,message:t}),this[ae]=!0,this.functionality=e}static isInstance(e){return m.hasMarker(e,oe)}};ae=ie;var le={exports:{}};const ce=void 0!==r,de=/"(?:_|\\u005[Ff])(?:_|\\u005[Ff])(?:p|\\u0070)(?:r|\\u0072)(?:o|\\u006[Ff])(?:t|\\u0074)(?:o|\\u006[Ff])(?:_|\\u005[Ff])(?:_|\\u005[Ff])"\s*:/,pe=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/;function _parse(e,t,a){null==a&&null!==t&&"object"==typeof t&&(a=t,t=void 0),ce&&r.isBuffer(e)&&(e=e.toString()),e&&65279===e.charCodeAt(0)&&(e=e.slice(1));const s=JSON.parse(e,t);if(null===s||"object"!=typeof s)return s;const n=a&&a.protoAction||"error",o=a&&a.constructorAction||"error";if("ignore"===n&&"ignore"===o)return s;if("ignore"!==n&&"ignore"!==o){if(!1===de.test(e)&&!1===pe.test(e))return s}else if("ignore"!==n&&"ignore"===o){if(!1===de.test(e))return s}else if(!1===pe.test(e))return s;return filter(s,{protoAction:n,constructorAction:o,safe:a&&a.safe})}function filter(e,{protoAction:t="error",constructorAction:r="error",safe:a}={}){let s=[e];for(;s.length;){const e=s;s=[];for(const n of e){if("ignore"!==t&&Object.prototype.hasOwnProperty.call(n,"__proto__")){if(!0===a)return null;if("error"===t)throw new SyntaxError("Object contains forbidden prototype property");delete n.__proto__}if("ignore"!==r&&Object.prototype.hasOwnProperty.call(n,"constructor")&&Object.prototype.hasOwnProperty.call(n.constructor,"prototype")){if(!0===a)return null;if("error"===r)throw new SyntaxError("Object contains forbidden prototype property");delete n.constructor}for(const e in n){const t=n[e];t&&"object"==typeof t&&s.push(t)}}}return e}function parse(e,t,r){const a=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return _parse(e,t,r)}finally{Error.stackTraceLimit=a}}le.exports=parse,le.exports.default=parse,le.exports.parse=parse,le.exports.safeParse=function(e,t){const r=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return _parse(e,t,{safe:!0})}catch(e){return null}finally{Error.stackTraceLimit=r}},le.exports.scan=filter;const me=a(le.exports);function combineHeaders(...e){return e.reduce((e,t)=>({...e,...null!=t?t:{}}),{})}function createEventSourceParserStream(){let e,t,r,a="",s=[];function parseLine(e,t){if(""===e)return void dispatchEvent(t);if(e.startsWith(":"))return;const r=e.indexOf(":");if(-1===r)return void handleField(e,"");const a=r+1;handleField(e.slice(0,r),a<e.length&&" "===e[a]?e.slice(a+1):e.slice(a))}function dispatchEvent(a){s.length>0&&(a.enqueue({event:e,data:s.join("\n"),id:t,retry:r}),s=[],e=void 0,r=void 0)}function handleField(a,n){switch(a){case"event":e=n;break;case"data":s.push(n);break;case"id":t=n;break;case"retry":const a=parseInt(n,10);isNaN(a)||(r=a)}}return new TransformStream({transform(e,t){const{lines:r,incompleteLine:s}=function(e,t){const r=[];let a=e;for(let e=0;e<t.length;){const s=t[e++];"\n"===s?(r.push(a),a=""):"\r"===s?(r.push(a),a="","\n"===t[e]&&e++):a+=s}return{lines:r,incompleteLine:a}}(a,e);a=s;for(let e=0;e<r.length;e++)parseLine(r[e],t)},flush(e){parseLine(a,e),dispatchEvent(e)}})}function extractResponseHeaders(e){const t={};return e.headers.forEach((e,r)=>{t[r]=e}),t}var createIdGenerator=({prefix:e,size:t=16,alphabet:r="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",separator:a="-"}={})=>{const s=((e,t=21)=>(r=t)=>{let a="",s=0|r;for(;s--;)a+=e[Math.random()*e.length|0];return a})(r,t);if(null==e)return s;if(r.includes(a))throw new k({argument:"separator",message:`The separator "${a}" must not be part of the alphabet "${r}".`});return t=>`${e}${a}${s(t)}`},fe=createIdGenerator();function isAbortError(e){return e instanceof Error&&("AbortError"===e.name||"TimeoutError"===e.name)}function loadApiKey({apiKey:t,environmentVariableName:r,apiKeyParameterName:a="apiKey",description:s}){if("string"==typeof t)return t;if(null!=t)throw new K({message:`${s} API key must be a string.`});if(void 0===e)throw new K({message:`${s} API key is missing. Pass it using the '${a}' parameter. Environment variables is not supported in this environment.`});if(null==(t=e.env[r]))throw new K({message:`${s} API key is missing. Pass it using the '${a}' parameter or the ${r} environment variable.`});if("string"!=typeof t)throw new K({message:`${s} API key must be a string. The value of the ${r} environment variable is not a string.`});return t}var he=Symbol.for("vercel.ai.validator");function asValidator(e){return function(e){return"object"==typeof e&&null!==e&&he in e&&!0===e[he]&&"validate"in e}(e)?e:function(e){return t=t=>{const r=e.safeParse(t);return r.success?{success:!0,value:r.data}:{success:!1,error:r.error}},{[he]:!0,validate:t};var t}(e)}function safeValidateTypes({value:e,schema:t}){const r=asValidator(t);try{if(null==r.validate)return{success:!0,value:e};const t=r.validate(e);return t.success?t:{success:!1,error:se.wrap({value:e,cause:t.error})}}catch(t){return{success:!1,error:se.wrap({value:e,cause:t})}}}function parseJSON({text:e,schema:t}){try{const r=me.parse(e);return null==t?r:function({value:e,schema:t}){const r=safeValidateTypes({value:e,schema:t});if(!r.success)throw se.wrap({value:e,cause:r.error});return r.value}({value:r,schema:t})}catch(t){if(U.isInstance(t)||se.isInstance(t))throw t;throw new U({text:e,cause:t})}}function safeParseJSON({text:e,schema:t}){try{const r=me.parse(e);if(null==t)return{success:!0,value:r,rawValue:r};const a=safeValidateTypes({value:r,schema:t});return a.success?{...a,rawValue:r}:a}catch(t){return{success:!1,error:U.isInstance(t)?t:new U({text:e,cause:t})}}}function isParsableJson(e){try{return me.parse(e),!0}catch(e){return!1}}function parseProviderOptions({provider:e,providerOptions:t,schema:r}){if(null==(null==t?void 0:t[e]))return;const a=safeValidateTypes({value:t[e],schema:r});if(!a.success)throw new k({argument:"providerOptions",message:`invalid ${e} provider options`,cause:a.error});return a.value}var ge,ye,getOriginalFetch2=()=>globalThis.fetch,postJsonToApi=async({url:e,headers:t,body:r,failedResponseHandler:a,successfulResponseHandler:s,abortSignal:n,fetch:o})=>postToApi({url:e,headers:{"Content-Type":"application/json",...t},body:{content:JSON.stringify(r),values:r},failedResponseHandler:a,successfulResponseHandler:s,abortSignal:n,fetch:o}),postFormDataToApi=async({url:e,headers:t,formData:r,failedResponseHandler:a,successfulResponseHandler:s,abortSignal:n,fetch:o})=>postToApi({url:e,headers:t,body:{content:r,values:Object.fromEntries(r.entries())},failedResponseHandler:a,successfulResponseHandler:s,abortSignal:n,fetch:o}),postToApi=async({url:e,headers:t={},body:r,successfulResponseHandler:a,failedResponseHandler:s,abortSignal:n,fetch:o=getOriginalFetch2()})=>{try{const u=await o(e,{method:"POST",headers:(i=t,Object.fromEntries(Object.entries(i).filter(([e,t])=>null!=t))),body:r.content,signal:n}),l=extractResponseHeaders(u);if(!u.ok){let t;try{t=await s({response:u,url:e,requestBodyValues:r.values})}catch(t){if(isAbortError(t)||y.isInstance(t))throw t;throw new y({message:"Failed to process error response",cause:t,statusCode:u.status,url:e,responseHeaders:l,requestBodyValues:r.values})}throw t.value}try{return await a({response:u,url:e,requestBodyValues:r.values})}catch(t){if(t instanceof Error&&(isAbortError(t)||y.isInstance(t)))throw t;throw new y({message:"Failed to process successful response",cause:t,statusCode:u.status,url:e,responseHeaders:l,requestBodyValues:r.values})}}catch(t){if(isAbortError(t))throw t;if(t instanceof TypeError&&"fetch failed"===t.message){const a=t.cause;if(null!=a)throw new y({message:`Cannot connect to API: ${a.message}`,cause:a,url:e,requestBodyValues:r.values,isRetryable:!0})}throw t}var i},createJsonErrorResponseHandler=({errorSchema:e,errorToMessage:t,isRetryable:r})=>async({response:a,url:s,requestBodyValues:n})=>{const o=await a.text(),i=extractResponseHeaders(a);if(""===o.trim())return{responseHeaders:i,value:new y({message:a.statusText,url:s,requestBodyValues:n,statusCode:a.status,responseHeaders:i,responseBody:o,isRetryable:null==r?void 0:r(a)})};try{const u=parseJSON({text:o,schema:e});return{responseHeaders:i,value:new y({message:t(u),url:s,requestBodyValues:n,statusCode:a.status,responseHeaders:i,responseBody:o,data:u,isRetryable:null==r?void 0:r(a,u)})}}catch(e){return{responseHeaders:i,value:new y({message:a.statusText,url:s,requestBodyValues:n,statusCode:a.status,responseHeaders:i,responseBody:o,isRetryable:null==r?void 0:r(a)})}}},createEventSourceResponseHandler=e=>async({response:t})=>{const r=extractResponseHeaders(t);if(null==t.body)throw new T({});return{responseHeaders:r,value:t.body.pipeThrough(new TextDecoderStream).pipeThrough(createEventSourceParserStream()).pipeThrough(new TransformStream({transform({data:t},r){"[DONE]"!==t&&r.enqueue(safeParseJSON({text:t,schema:e}))}}))}},createJsonResponseHandler=e=>async({response:t,url:r,requestBodyValues:a})=>{const s=await t.text(),n=safeParseJSON({text:s,schema:e}),o=extractResponseHeaders(t);if(!n.success)throw new y({message:"Invalid JSON response",cause:n.error,statusCode:t.status,responseHeaders:o,responseBody:s,url:r,requestBodyValues:a});return{responseHeaders:o,value:n.value,rawValue:n.rawValue}},createBinaryResponseHandler=()=>async({response:e,url:t,requestBodyValues:r})=>{const a=extractResponseHeaders(e);if(!e.body)throw new y({message:"Response body is empty",url:t,requestBodyValues:r,statusCode:e.status,responseHeaders:a,responseBody:void 0});try{const t=await e.arrayBuffer();return{responseHeaders:a,value:new Uint8Array(t)}}catch(s){throw new y({message:"Failed to read response as array buffer",url:t,requestBodyValues:r,statusCode:e.status,responseHeaders:a,responseBody:void 0,cause:s})}},{btoa:ve,atob:_e}=globalThis;function convertBase64ToUint8Array(e){const t=e.replace(/-/g,"+").replace(/_/g,"/"),r=_e(t);return Uint8Array.from(r,e=>e.codePointAt(0))}function convertUint8ArrayToBase64(e){let t="";for(let r=0;r<e.length;r++)t+=String.fromCodePoint(e[r]);return ve(t)}function withoutTrailingSlash(e){return null==e?void 0:e.replace(/\/$/,"")}!function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw new Error},e.arrayToEnum=e=>{const t={};for(const r of e)t[r]=r;return t},e.getValidEnumValues=t=>{const r=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),a={};for(const e of r)a[e]=t[e];return e.objectValues(a)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{const t=[];for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t},e.find=(e,t)=>{for(const r of e)if(t(r))return r},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(ge||(ge={})),function(e){e.mergeShapes=(e,t)=>({...e,...t})}(ye||(ye={}));const xe=ge.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),getParsedType=e=>{switch(typeof e){case"undefined":return xe.undefined;case"string":return xe.string;case"number":return Number.isNaN(e)?xe.nan:xe.number;case"boolean":return xe.boolean;case"function":return xe.function;case"bigint":return xe.bigint;case"symbol":return xe.symbol;case"object":return Array.isArray(e)?xe.array:null===e?xe.null:e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch?xe.promise:"undefined"!=typeof Map&&e instanceof Map?xe.map:"undefined"!=typeof Set&&e instanceof Set?xe.set:"undefined"!=typeof Date&&e instanceof Date?xe.date:xe.object;default:return xe.unknown}},be=ge.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class ZodError extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(e){return e.message},r={_errors:[]},processError=e=>{for(const a of e.issues)if("invalid_union"===a.code)a.unionErrors.map(processError);else if("invalid_return_type"===a.code)processError(a.returnTypeError);else if("invalid_arguments"===a.code)processError(a.argumentsError);else if(0===a.path.length)r._errors.push(t(a));else{let e=r,s=0;for(;s<a.path.length;){const r=a.path[s];s===a.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(t(a))):e[r]=e[r]||{_errors:[]},e=e[r],s++}}};return processError(this),r}static assert(e){if(!(e instanceof ZodError))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,ge.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){const t={},r=[];for(const a of this.issues)a.path.length>0?(t[a.path[0]]=t[a.path[0]]||[],t[a.path[0]].push(e(a))):r.push(e(a));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}ZodError.create=e=>new ZodError(e);const errorMap=(e,t)=>{let r;switch(e.code){case be.invalid_type:r=e.received===xe.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case be.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,ge.jsonStringifyReplacer)}`;break;case be.unrecognized_keys:r=`Unrecognized key(s) in object: ${ge.joinValues(e.keys,", ")}`;break;case be.invalid_union:r="Invalid input";break;case be.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${ge.joinValues(e.options)}`;break;case be.invalid_enum_value:r=`Invalid enum value. Expected ${ge.joinValues(e.options)}, received '${e.received}'`;break;case be.invalid_arguments:r="Invalid function arguments";break;case be.invalid_return_type:r="Invalid function return type";break;case be.invalid_date:r="Invalid date";break;case be.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:ge.assertNever(e.validation):r="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case be.too_small:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case be.too_big:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case be.custom:r="Invalid input";break;case be.invalid_intersection_types:r="Intersection results could not be merged";break;case be.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case be.not_finite:r="Number must be finite";break;default:r=t.defaultError,ge.assertNever(e)}return{message:r}};let Te=errorMap;function addIssueToContext(e,t){const r=Te,a=(e=>{const{data:t,path:r,errorMaps:a,issueData:s}=e,n=[...r,...s.path||[]],o={...s,path:n};if(void 0!==s.message)return{...s,path:n,message:s.message};let i="";const u=a.filter(e=>!!e).slice().reverse();for(const e of u)i=e(o,{data:t,defaultError:i}).message;return{...s,path:n,message:i}})({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,r,r===errorMap?void 0:errorMap].filter(e=>!!e)});e.common.issues.push(a)}class ParseStatus{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){const r=[];for(const a of t){if("aborted"===a.status)return we;"dirty"===a.status&&e.dirty(),r.push(a.value)}return{status:e.value,value:r}}static async mergeObjectAsync(e,t){const r=[];for(const e of t){const t=await e.key,a=await e.value;r.push({key:t,value:a})}return ParseStatus.mergeObjectSync(e,r)}static mergeObjectSync(e,t){const r={};for(const a of t){const{key:t,value:s}=a;if("aborted"===t.status)return we;if("aborted"===s.status)return we;"dirty"===t.status&&e.dirty(),"dirty"===s.status&&e.dirty(),"__proto__"===t.value||void 0===s.value&&!a.alwaysSet||(r[t.value]=s.value)}return{status:e.value,value:r}}}const we=Object.freeze({status:"aborted"}),DIRTY=e=>({status:"dirty",value:e}),OK=e=>({status:"valid",value:e}),isAborted=e=>"aborted"===e.status,isDirty=e=>"dirty"===e.status,isValid=e=>"valid"===e.status,isAsync=e=>"undefined"!=typeof Promise&&e instanceof Promise;var Ie;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(Ie||(Ie={}));class ParseInputLazyPath{constructor(e,t,r,a){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=a}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const handleResult=(e,t)=>{if(isValid(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new ZodError(e.common.issues);return this._error=t,this._error}}};function processCreateParams(e){if(!e)return{};const{errorMap:t,invalid_type_error:r,required_error:a,description:s}=e;if(t&&(r||a))throw new Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');if(t)return{errorMap:t,description:s};return{errorMap:(t,s)=>{const{message:n}=e;return"invalid_enum_value"===t.code?{message:n??s.defaultError}:void 0===s.data?{message:n??a??s.defaultError}:"invalid_type"!==t.code?{message:s.defaultError}:{message:n??r??s.defaultError}},description:s}}class ZodType{get description(){return this._def.description}_getType(e){return getParsedType(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:getParsedType(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new ParseStatus,ctx:{common:e.parent.common,data:e.data,parsedType:getParsedType(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(isAsync(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){const r={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:getParsedType(e)},a=this._parseSync({data:e,path:r.path,parent:r});return handleResult(r,a)}"~validate"(e){const t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:getParsedType(e)};if(!this["~standard"].async)try{const r=this._parseSync({data:e,path:[],parent:t});return isValid(r)?{value:r.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>isValid(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){const r=await this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error}async safeParseAsync(e,t){const r={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:getParsedType(e)},a=this._parse({data:e,path:r.path,parent:r}),s=await(isAsync(a)?a:Promise.resolve(a));return handleResult(r,s)}refine(e,t){const getIssueProperties=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,r)=>{const a=e(t),setError=()=>r.addIssue({code:be.custom,...getIssueProperties(t)});return"undefined"!=typeof Promise&&a instanceof Promise?a.then(e=>!!e||(setError(),!1)):!!a||(setError(),!1)})}refinement(e,t){return this._refinement((r,a)=>!!e(r)||(a.addIssue("function"==typeof t?t(r,a):t),!1))}_refinement(e){return new ZodEffects({schema:this,typeName:Ue.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return ZodOptional.create(this,this._def)}nullable(){return ZodNullable.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return ZodArray.create(this)}promise(){return ZodPromise.create(this,this._def)}or(e){return ZodUnion.create([this,e],this._def)}and(e){return ZodIntersection.create(this,e,this._def)}transform(e){return new ZodEffects({...processCreateParams(this._def),schema:this,typeName:Ue.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t="function"==typeof e?e:()=>e;return new ZodDefault({...processCreateParams(this._def),innerType:this,defaultValue:t,typeName:Ue.ZodDefault})}brand(){return new ZodBranded({typeName:Ue.ZodBranded,type:this,...processCreateParams(this._def)})}catch(e){const t="function"==typeof e?e:()=>e;return new ZodCatch({...processCreateParams(this._def),innerType:this,catchValue:t,typeName:Ue.ZodCatch})}describe(e){return new(0,this.constructor)({...this._def,description:e})}pipe(e){return ZodPipeline.create(this,e)}readonly(){return ZodReadonly.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Se=/^c[^\s-]{8,}$/i,Ae=/^[0-9a-z]+$/,ke=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Ce=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Ee=/^[a-z0-9_-]{21}$/i,Pe=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Ze=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Ne=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let Re;const Oe=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,je=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,De=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Me=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,$e=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Le=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Ve="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Fe=new RegExp(`^${Ve}$`);function timeRegexSource(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${e.precision?"+":"?"}`}function timeRegex(e){return new RegExp(`^${timeRegexSource(e)}$`)}function datetimeRegex(e){let t=`${Ve}T${timeRegexSource(e)}`;const r=[];return r.push(e.local?"Z?":"Z"),e.offset&&r.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${r.join("|")})`,new RegExp(`^${t}$`)}function isValidIP(e,t){return!("v4"!==t&&t||!Oe.test(e))||!("v6"!==t&&t||!De.test(e))}function isValidJWT(e,t){if(!Pe.test(e))return!1;try{const[r]=e.split("."),a=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),s=JSON.parse(atob(a));return"object"==typeof s&&null!==s&&((!("typ"in s)||"JWT"===s?.typ)&&(!!s.alg&&(!t||s.alg===t)))}catch{return!1}}function isValidCidr(e,t){return!("v4"!==t&&t||!je.test(e))||!("v6"!==t&&t||!Me.test(e))}class ZodString extends ZodType{_parse(e){this._def.coerce&&(e.data=String(e.data));if(this._getType(e)!==xe.string){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:be.invalid_type,expected:xe.string,received:t.parsedType}),we}const t=new ParseStatus;let r;for(const a of this._def.checks)if("min"===a.kind)e.data.length<a.value&&(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:be.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),t.dirty());else if("max"===a.kind)e.data.length>a.value&&(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:be.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),t.dirty());else if("length"===a.kind){const s=e.data.length>a.value,n=e.data.length<a.value;(s||n)&&(r=this._getOrReturnCtx(e,r),s?addIssueToContext(r,{code:be.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}):n&&addIssueToContext(r,{code:be.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}),t.dirty())}else if("email"===a.kind)Ne.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"email",code:be.invalid_string,message:a.message}),t.dirty());else if("emoji"===a.kind)Re||(Re=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),Re.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"emoji",code:be.invalid_string,message:a.message}),t.dirty());else if("uuid"===a.kind)Ce.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"uuid",code:be.invalid_string,message:a.message}),t.dirty());else if("nanoid"===a.kind)Ee.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"nanoid",code:be.invalid_string,message:a.message}),t.dirty());else if("cuid"===a.kind)Se.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"cuid",code:be.invalid_string,message:a.message}),t.dirty());else if("cuid2"===a.kind)Ae.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"cuid2",code:be.invalid_string,message:a.message}),t.dirty());else if("ulid"===a.kind)ke.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"ulid",code:be.invalid_string,message:a.message}),t.dirty());else if("url"===a.kind)try{new URL(e.data)}catch{r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"url",code:be.invalid_string,message:a.message}),t.dirty()}else if("regex"===a.kind){a.regex.lastIndex=0;a.regex.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"regex",code:be.invalid_string,message:a.message}),t.dirty())}else if("trim"===a.kind)e.data=e.data.trim();else if("includes"===a.kind)e.data.includes(a.value,a.position)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:be.invalid_string,validation:{includes:a.value,position:a.position},message:a.message}),t.dirty());else if("toLowerCase"===a.kind)e.data=e.data.toLowerCase();else if("toUpperCase"===a.kind)e.data=e.data.toUpperCase();else if("startsWith"===a.kind)e.data.startsWith(a.value)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:be.invalid_string,validation:{startsWith:a.value},message:a.message}),t.dirty());else if("endsWith"===a.kind)e.data.endsWith(a.value)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:be.invalid_string,validation:{endsWith:a.value},message:a.message}),t.dirty());else if("datetime"===a.kind){datetimeRegex(a).test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:be.invalid_string,validation:"datetime",message:a.message}),t.dirty())}else if("date"===a.kind){Fe.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:be.invalid_string,validation:"date",message:a.message}),t.dirty())}else if("time"===a.kind){timeRegex(a).test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:be.invalid_string,validation:"time",message:a.message}),t.dirty())}else"duration"===a.kind?Ze.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"duration",code:be.invalid_string,message:a.message}),t.dirty()):"ip"===a.kind?isValidIP(e.data,a.version)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"ip",code:be.invalid_string,message:a.message}),t.dirty()):"jwt"===a.kind?isValidJWT(e.data,a.alg)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"jwt",code:be.invalid_string,message:a.message}),t.dirty()):"cidr"===a.kind?isValidCidr(e.data,a.version)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"cidr",code:be.invalid_string,message:a.message}),t.dirty()):"base64"===a.kind?$e.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"base64",code:be.invalid_string,message:a.message}),t.dirty()):"base64url"===a.kind?Le.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"base64url",code:be.invalid_string,message:a.message}),t.dirty()):ge.assertNever(a);return{status:t.value,value:e.data}}_regex(e,t,r){return this.refinement(t=>e.test(t),{validation:t,code:be.invalid_string,...Ie.errToObj(r)})}_addCheck(e){return new ZodString({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...Ie.errToObj(e)})}url(e){return this._addCheck({kind:"url",...Ie.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...Ie.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...Ie.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...Ie.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...Ie.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...Ie.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...Ie.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...Ie.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...Ie.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...Ie.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...Ie.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...Ie.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...Ie.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...Ie.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...Ie.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...Ie.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...Ie.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...Ie.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...Ie.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...Ie.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...Ie.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...Ie.errToObj(t)})}nonempty(e){return this.min(1,Ie.errToObj(e))}trim(){return new ZodString({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new ZodString({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new ZodString({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}function floatSafeRemainder(e,t){const r=(e.toString().split(".")[1]||"").length,a=(t.toString().split(".")[1]||"").length,s=r>a?r:a;return Number.parseInt(e.toFixed(s).replace(".",""))%Number.parseInt(t.toFixed(s).replace(".",""))/10**s}ZodString.create=e=>new ZodString({checks:[],typeName:Ue.ZodString,coerce:e?.coerce??!1,...processCreateParams(e)});class ZodNumber extends ZodType{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){this._def.coerce&&(e.data=Number(e.data));if(this._getType(e)!==xe.number){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:be.invalid_type,expected:xe.number,received:t.parsedType}),we}let t;const r=new ParseStatus;for(const a of this._def.checks)if("int"===a.kind)ge.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),addIssueToContext(t,{code:be.invalid_type,expected:"integer",received:"float",message:a.message}),r.dirty());else if("min"===a.kind){(a.inclusive?e.data<a.value:e.data<=a.value)&&(t=this._getOrReturnCtx(e,t),addIssueToContext(t,{code:be.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty())}else if("max"===a.kind){(a.inclusive?e.data>a.value:e.data>=a.value)&&(t=this._getOrReturnCtx(e,t),addIssueToContext(t,{code:be.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty())}else"multipleOf"===a.kind?0!==floatSafeRemainder(e.data,a.value)&&(t=this._getOrReturnCtx(e,t),addIssueToContext(t,{code:be.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):"finite"===a.kind?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),addIssueToContext(t,{code:be.not_finite,message:a.message}),r.dirty()):ge.assertNever(a);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,Ie.toString(t))}gt(e,t){return this.setLimit("min",e,!1,Ie.toString(t))}lte(e,t){return this.setLimit("max",e,!0,Ie.toString(t))}lt(e,t){return this.setLimit("max",e,!1,Ie.toString(t))}setLimit(e,t,r,a){return new ZodNumber({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:Ie.toString(a)}]})}_addCheck(e){return new ZodNumber({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:Ie.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:Ie.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:Ie.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:Ie.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:Ie.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:Ie.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:Ie.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:Ie.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:Ie.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&ge.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const r of this._def.checks){if("finite"===r.kind||"int"===r.kind||"multipleOf"===r.kind)return!0;"min"===r.kind?(null===t||r.value>t)&&(t=r.value):"max"===r.kind&&(null===e||r.value<e)&&(e=r.value)}return Number.isFinite(t)&&Number.isFinite(e)}}ZodNumber.create=e=>new ZodNumber({checks:[],typeName:Ue.ZodNumber,coerce:e?.coerce||!1,...processCreateParams(e)});class ZodBigInt extends ZodType{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==xe.bigint)return this._getInvalidInput(e);let t;const r=new ParseStatus;for(const a of this._def.checks)if("min"===a.kind){(a.inclusive?e.data<a.value:e.data<=a.value)&&(t=this._getOrReturnCtx(e,t),addIssueToContext(t,{code:be.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty())}else if("max"===a.kind){(a.inclusive?e.data>a.value:e.data>=a.value)&&(t=this._getOrReturnCtx(e,t),addIssueToContext(t,{code:be.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty())}else"multipleOf"===a.kind?e.data%a.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),addIssueToContext(t,{code:be.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):ge.assertNever(a);return{status:r.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:be.invalid_type,expected:xe.bigint,received:t.parsedType}),we}gte(e,t){return this.setLimit("min",e,!0,Ie.toString(t))}gt(e,t){return this.setLimit("min",e,!1,Ie.toString(t))}lte(e,t){return this.setLimit("max",e,!0,Ie.toString(t))}lt(e,t){return this.setLimit("max",e,!1,Ie.toString(t))}setLimit(e,t,r,a){return new ZodBigInt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:Ie.toString(a)}]})}_addCheck(e){return new ZodBigInt({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:Ie.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:Ie.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:Ie.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:Ie.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:Ie.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}ZodBigInt.create=e=>new ZodBigInt({checks:[],typeName:Ue.ZodBigInt,coerce:e?.coerce??!1,...processCreateParams(e)});class ZodBoolean extends ZodType{_parse(e){this._def.coerce&&(e.data=Boolean(e.data));if(this._getType(e)!==xe.boolean){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:be.invalid_type,expected:xe.boolean,received:t.parsedType}),we}return OK(e.data)}}ZodBoolean.create=e=>new ZodBoolean({typeName:Ue.ZodBoolean,coerce:e?.coerce||!1,...processCreateParams(e)});class ZodDate extends ZodType{_parse(e){this._def.coerce&&(e.data=new Date(e.data));if(this._getType(e)!==xe.date){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:be.invalid_type,expected:xe.date,received:t.parsedType}),we}if(Number.isNaN(e.data.getTime())){return addIssueToContext(this._getOrReturnCtx(e),{code:be.invalid_date}),we}const t=new ParseStatus;let r;for(const a of this._def.checks)"min"===a.kind?e.data.getTime()<a.value&&(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:be.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),t.dirty()):"max"===a.kind?e.data.getTime()>a.value&&(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:be.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),t.dirty()):ge.assertNever(a);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new ZodDate({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:Ie.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:Ie.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}ZodDate.create=e=>new ZodDate({checks:[],coerce:e?.coerce||!1,typeName:Ue.ZodDate,...processCreateParams(e)});class ZodSymbol extends ZodType{_parse(e){if(this._getType(e)!==xe.symbol){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:be.invalid_type,expected:xe.symbol,received:t.parsedType}),we}return OK(e.data)}}ZodSymbol.create=e=>new ZodSymbol({typeName:Ue.ZodSymbol,...processCreateParams(e)});class ZodUndefined extends ZodType{_parse(e){if(this._getType(e)!==xe.undefined){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:be.invalid_type,expected:xe.undefined,received:t.parsedType}),we}return OK(e.data)}}ZodUndefined.create=e=>new ZodUndefined({typeName:Ue.ZodUndefined,...processCreateParams(e)});class ZodNull extends ZodType{_parse(e){if(this._getType(e)!==xe.null){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:be.invalid_type,expected:xe.null,received:t.parsedType}),we}return OK(e.data)}}ZodNull.create=e=>new ZodNull({typeName:Ue.ZodNull,...processCreateParams(e)});class ZodAny extends ZodType{constructor(){super(...arguments),this._any=!0}_parse(e){return OK(e.data)}}ZodAny.create=e=>new ZodAny({typeName:Ue.ZodAny,...processCreateParams(e)});class ZodUnknown extends ZodType{constructor(){super(...arguments),this._unknown=!0}_parse(e){return OK(e.data)}}ZodUnknown.create=e=>new ZodUnknown({typeName:Ue.ZodUnknown,...processCreateParams(e)});class ZodNever extends ZodType{_parse(e){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:be.invalid_type,expected:xe.never,received:t.parsedType}),we}}ZodNever.create=e=>new ZodNever({typeName:Ue.ZodNever,...processCreateParams(e)});class ZodVoid extends ZodType{_parse(e){if(this._getType(e)!==xe.undefined){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:be.invalid_type,expected:xe.void,received:t.parsedType}),we}return OK(e.data)}}ZodVoid.create=e=>new ZodVoid({typeName:Ue.ZodVoid,...processCreateParams(e)});class ZodArray extends ZodType{_parse(e){const{ctx:t,status:r}=this._processInputParams(e),a=this._def;if(t.parsedType!==xe.array)return addIssueToContext(t,{code:be.invalid_type,expected:xe.array,received:t.parsedType}),we;if(null!==a.exactLength){const e=t.data.length>a.exactLength.value,s=t.data.length<a.exactLength.value;(e||s)&&(addIssueToContext(t,{code:e?be.too_big:be.too_small,minimum:s?a.exactLength.value:void 0,maximum:e?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),r.dirty())}if(null!==a.minLength&&t.data.length<a.minLength.value&&(addIssueToContext(t,{code:be.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),r.dirty()),null!==a.maxLength&&t.data.length>a.maxLength.value&&(addIssueToContext(t,{code:be.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map((e,r)=>a.type._parseAsync(new ParseInputLazyPath(t,e,t.path,r)))).then(e=>ParseStatus.mergeArray(r,e));const s=[...t.data].map((e,r)=>a.type._parseSync(new ParseInputLazyPath(t,e,t.path,r)));return ParseStatus.mergeArray(r,s)}get element(){return this._def.type}min(e,t){return new ZodArray({...this._def,minLength:{value:e,message:Ie.toString(t)}})}max(e,t){return new ZodArray({...this._def,maxLength:{value:e,message:Ie.toString(t)}})}length(e,t){return new ZodArray({...this._def,exactLength:{value:e,message:Ie.toString(t)}})}nonempty(e){return this.min(1,e)}}function deepPartialify(e){if(e instanceof ZodObject){const t={};for(const r in e.shape){const a=e.shape[r];t[r]=ZodOptional.create(deepPartialify(a))}return new ZodObject({...e._def,shape:()=>t})}return e instanceof ZodArray?new ZodArray({...e._def,type:deepPartialify(e.element)}):e instanceof ZodOptional?ZodOptional.create(deepPartialify(e.unwrap())):e instanceof ZodNullable?ZodNullable.create(deepPartialify(e.unwrap())):e instanceof ZodTuple?ZodTuple.create(e.items.map(e=>deepPartialify(e))):e}ZodArray.create=(e,t)=>new ZodArray({type:e,minLength:null,maxLength:null,exactLength:null,typeName:Ue.ZodArray,...processCreateParams(t)});class ZodObject extends ZodType{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;const e=this._def.shape(),t=ge.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==xe.object){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:be.invalid_type,expected:xe.object,received:t.parsedType}),we}const{status:t,ctx:r}=this._processInputParams(e),{shape:a,keys:s}=this._getCached(),n=[];if(!(this._def.catchall instanceof ZodNever&&"strip"===this._def.unknownKeys))for(const e in r.data)s.includes(e)||n.push(e);const o=[];for(const e of s){const t=a[e],s=r.data[e];o.push({key:{status:"valid",value:e},value:t._parse(new ParseInputLazyPath(r,s,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof ZodNever){const e=this._def.unknownKeys;if("passthrough"===e)for(const e of n)o.push({key:{status:"valid",value:e},value:{status:"valid",value:r.data[e]}});else if("strict"===e)n.length>0&&(addIssueToContext(r,{code:be.unrecognized_keys,keys:n}),t.dirty());else if("strip"!==e)throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const e=this._def.catchall;for(const t of n){const a=r.data[t];o.push({key:{status:"valid",value:t},value:e._parse(new ParseInputLazyPath(r,a,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then(async()=>{const e=[];for(const t of o){const r=await t.key,a=await t.value;e.push({key:r,value:a,alwaysSet:t.alwaysSet})}return e}).then(e=>ParseStatus.mergeObjectSync(t,e)):ParseStatus.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return Ie.errToObj,new ZodObject({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,r)=>{const a=this._def.errorMap?.(t,r).message??r.defaultError;return"unrecognized_keys"===t.code?{message:Ie.errToObj(e).message??a}:{message:a}}}:{}})}strip(){return new ZodObject({...this._def,unknownKeys:"strip"})}passthrough(){return new ZodObject({...this._def,unknownKeys:"passthrough"})}extend(e){return new ZodObject({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new ZodObject({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:Ue.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new ZodObject({...this._def,catchall:e})}pick(e){const t={};for(const r of ge.objectKeys(e))e[r]&&this.shape[r]&&(t[r]=this.shape[r]);return new ZodObject({...this._def,shape:()=>t})}omit(e){const t={};for(const r of ge.objectKeys(this.shape))e[r]||(t[r]=this.shape[r]);return new ZodObject({...this._def,shape:()=>t})}deepPartial(){return deepPartialify(this)}partial(e){const t={};for(const r of ge.objectKeys(this.shape)){const a=this.shape[r];e&&!e[r]?t[r]=a:t[r]=a.optional()}return new ZodObject({...this._def,shape:()=>t})}required(e){const t={};for(const r of ge.objectKeys(this.shape))if(e&&!e[r])t[r]=this.shape[r];else{let e=this.shape[r];for(;e instanceof ZodOptional;)e=e._def.innerType;t[r]=e}return new ZodObject({...this._def,shape:()=>t})}keyof(){return createZodEnum(ge.objectKeys(this.shape))}}ZodObject.create=(e,t)=>new ZodObject({shape:()=>e,unknownKeys:"strip",catchall:ZodNever.create(),typeName:Ue.ZodObject,...processCreateParams(t)}),ZodObject.strictCreate=(e,t)=>new ZodObject({shape:()=>e,unknownKeys:"strict",catchall:ZodNever.create(),typeName:Ue.ZodObject,...processCreateParams(t)}),ZodObject.lazycreate=(e,t)=>new ZodObject({shape:e,unknownKeys:"strip",catchall:ZodNever.create(),typeName:Ue.ZodObject,...processCreateParams(t)});class ZodUnion extends ZodType{_parse(e){const{ctx:t}=this._processInputParams(e),r=this._def.options;if(t.common.async)return Promise.all(r.map(async e=>{const r={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:r}),ctx:r}})).then(function(e){for(const t of e)if("valid"===t.result.status)return t.result;for(const r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;const r=e.map(e=>new ZodError(e.ctx.common.issues));return addIssueToContext(t,{code:be.invalid_union,unionErrors:r}),we});{let e;const a=[];for(const s of r){const r={...t,common:{...t.common,issues:[]},parent:null},n=s._parseSync({data:t.data,path:t.path,parent:r});if("valid"===n.status)return n;"dirty"!==n.status||e||(e={result:n,ctx:r}),r.common.issues.length&&a.push(r.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;const s=a.map(e=>new ZodError(e));return addIssueToContext(t,{code:be.invalid_union,unionErrors:s}),we}}get options(){return this._def.options}}ZodUnion.create=(e,t)=>new ZodUnion({options:e,typeName:Ue.ZodUnion,...processCreateParams(t)});const getDiscriminator=e=>e instanceof ZodLazy?getDiscriminator(e.schema):e instanceof ZodEffects?getDiscriminator(e.innerType()):e instanceof ZodLiteral?[e.value]:e instanceof ZodEnum?e.options:e instanceof ZodNativeEnum?ge.objectValues(e.enum):e instanceof ZodDefault?getDiscriminator(e._def.innerType):e instanceof ZodUndefined?[void 0]:e instanceof ZodNull?[null]:e instanceof ZodOptional?[void 0,...getDiscriminator(e.unwrap())]:e instanceof ZodNullable?[null,...getDiscriminator(e.unwrap())]:e instanceof ZodBranded||e instanceof ZodReadonly?getDiscriminator(e.unwrap()):e instanceof ZodCatch?getDiscriminator(e._def.innerType):[];class ZodDiscriminatedUnion extends ZodType{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==xe.object)return addIssueToContext(t,{code:be.invalid_type,expected:xe.object,received:t.parsedType}),we;const r=this.discriminator,a=t.data[r],s=this.optionsMap.get(a);return s?t.common.async?s._parseAsync({data:t.data,path:t.path,parent:t}):s._parseSync({data:t.data,path:t.path,parent:t}):(addIssueToContext(t,{code:be.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),we)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,r){const a=new Map;for(const r of t){const t=getDiscriminator(r.shape[e]);if(!t.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const s of t){if(a.has(s))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(s)}`);a.set(s,r)}}return new ZodDiscriminatedUnion({typeName:Ue.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:a,...processCreateParams(r)})}}function mergeValues(e,t){const r=getParsedType(e),a=getParsedType(t);if(e===t)return{valid:!0,data:e};if(r===xe.object&&a===xe.object){const r=ge.objectKeys(t),a=ge.objectKeys(e).filter(e=>-1!==r.indexOf(e)),s={...e,...t};for(const r of a){const a=mergeValues(e[r],t[r]);if(!a.valid)return{valid:!1};s[r]=a.data}return{valid:!0,data:s}}if(r===xe.array&&a===xe.array){if(e.length!==t.length)return{valid:!1};const r=[];for(let a=0;a<e.length;a++){const s=mergeValues(e[a],t[a]);if(!s.valid)return{valid:!1};r.push(s.data)}return{valid:!0,data:r}}return r===xe.date&&a===xe.date&&+e===+t?{valid:!0,data:e}:{valid:!1}}class ZodIntersection extends ZodType{_parse(e){const{status:t,ctx:r}=this._processInputParams(e),handleParsed=(e,a)=>{if(isAborted(e)||isAborted(a))return we;const s=mergeValues(e.value,a.value);return s.valid?((isDirty(e)||isDirty(a))&&t.dirty(),{status:t.value,value:s.data}):(addIssueToContext(r,{code:be.invalid_intersection_types}),we)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([e,t])=>handleParsed(e,t)):handleParsed(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}ZodIntersection.create=(e,t,r)=>new ZodIntersection({left:e,right:t,typeName:Ue.ZodIntersection,...processCreateParams(r)});class ZodTuple extends ZodType{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==xe.array)return addIssueToContext(r,{code:be.invalid_type,expected:xe.array,received:r.parsedType}),we;if(r.data.length<this._def.items.length)return addIssueToContext(r,{code:be.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),we;!this._def.rest&&r.data.length>this._def.items.length&&(addIssueToContext(r,{code:be.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const a=[...r.data].map((e,t)=>{const a=this._def.items[t]||this._def.rest;return a?a._parse(new ParseInputLazyPath(r,e,r.path,t)):null}).filter(e=>!!e);return r.common.async?Promise.all(a).then(e=>ParseStatus.mergeArray(t,e)):ParseStatus.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new ZodTuple({...this._def,rest:e})}}ZodTuple.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new ZodTuple({items:e,typeName:Ue.ZodTuple,rest:null,...processCreateParams(t)})};class ZodRecord extends ZodType{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==xe.object)return addIssueToContext(r,{code:be.invalid_type,expected:xe.object,received:r.parsedType}),we;const a=[],s=this._def.keyType,n=this._def.valueType;for(const e in r.data)a.push({key:s._parse(new ParseInputLazyPath(r,e,r.path,e)),value:n._parse(new ParseInputLazyPath(r,r.data[e],r.path,e)),alwaysSet:e in r.data});return r.common.async?ParseStatus.mergeObjectAsync(t,a):ParseStatus.mergeObjectSync(t,a)}get element(){return this._def.valueType}static create(e,t,r){return new ZodRecord(t instanceof ZodType?{keyType:e,valueType:t,typeName:Ue.ZodRecord,...processCreateParams(r)}:{keyType:ZodString.create(),valueType:e,typeName:Ue.ZodRecord,...processCreateParams(t)})}}class ZodMap extends ZodType{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==xe.map)return addIssueToContext(r,{code:be.invalid_type,expected:xe.map,received:r.parsedType}),we;const a=this._def.keyType,s=this._def.valueType,n=[...r.data.entries()].map(([e,t],n)=>({key:a._parse(new ParseInputLazyPath(r,e,r.path,[n,"key"])),value:s._parse(new ParseInputLazyPath(r,t,r.path,[n,"value"]))}));if(r.common.async){const e=new Map;return Promise.resolve().then(async()=>{for(const r of n){const a=await r.key,s=await r.value;if("aborted"===a.status||"aborted"===s.status)return we;"dirty"!==a.status&&"dirty"!==s.status||t.dirty(),e.set(a.value,s.value)}return{status:t.value,value:e}})}{const e=new Map;for(const r of n){const a=r.key,s=r.value;if("aborted"===a.status||"aborted"===s.status)return we;"dirty"!==a.status&&"dirty"!==s.status||t.dirty(),e.set(a.value,s.value)}return{status:t.value,value:e}}}}ZodMap.create=(e,t,r)=>new ZodMap({valueType:t,keyType:e,typeName:Ue.ZodMap,...processCreateParams(r)});class ZodSet extends ZodType{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==xe.set)return addIssueToContext(r,{code:be.invalid_type,expected:xe.set,received:r.parsedType}),we;const a=this._def;null!==a.minSize&&r.data.size<a.minSize.value&&(addIssueToContext(r,{code:be.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),t.dirty()),null!==a.maxSize&&r.data.size>a.maxSize.value&&(addIssueToContext(r,{code:be.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),t.dirty());const s=this._def.valueType;function finalizeSet(e){const r=new Set;for(const a of e){if("aborted"===a.status)return we;"dirty"===a.status&&t.dirty(),r.add(a.value)}return{status:t.value,value:r}}const n=[...r.data.values()].map((e,t)=>s._parse(new ParseInputLazyPath(r,e,r.path,t)));return r.common.async?Promise.all(n).then(e=>finalizeSet(e)):finalizeSet(n)}min(e,t){return new ZodSet({...this._def,minSize:{value:e,message:Ie.toString(t)}})}max(e,t){return new ZodSet({...this._def,maxSize:{value:e,message:Ie.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}ZodSet.create=(e,t)=>new ZodSet({valueType:e,minSize:null,maxSize:null,typeName:Ue.ZodSet,...processCreateParams(t)});class ZodLazy extends ZodType{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}ZodLazy.create=(e,t)=>new ZodLazy({getter:e,typeName:Ue.ZodLazy,...processCreateParams(t)});class ZodLiteral extends ZodType{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{received:t.data,code:be.invalid_literal,expected:this._def.value}),we}return{status:"valid",value:e.data}}get value(){return this._def.value}}function createZodEnum(e,t){return new ZodEnum({values:e,typeName:Ue.ZodEnum,...processCreateParams(t)})}ZodLiteral.create=(e,t)=>new ZodLiteral({value:e,typeName:Ue.ZodLiteral,...processCreateParams(t)});class ZodEnum extends ZodType{_parse(e){if("string"!=typeof e.data){const t=this._getOrReturnCtx(e),r=this._def.values;return addIssueToContext(t,{expected:ge.joinValues(r),received:t.parsedType,code:be.invalid_type}),we}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){const t=this._getOrReturnCtx(e),r=this._def.values;return addIssueToContext(t,{received:t.data,code:be.invalid_enum_value,options:r}),we}return OK(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return ZodEnum.create(e,{...this._def,...t})}exclude(e,t=this._def){return ZodEnum.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}ZodEnum.create=createZodEnum;class ZodNativeEnum extends ZodType{_parse(e){const t=ge.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==xe.string&&r.parsedType!==xe.number){const e=ge.objectValues(t);return addIssueToContext(r,{expected:ge.joinValues(e),received:r.parsedType,code:be.invalid_type}),we}if(this._cache||(this._cache=new Set(ge.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){const e=ge.objectValues(t);return addIssueToContext(r,{received:r.data,code:be.invalid_enum_value,options:e}),we}return OK(e.data)}get enum(){return this._def.values}}ZodNativeEnum.create=(e,t)=>new ZodNativeEnum({values:e,typeName:Ue.ZodNativeEnum,...processCreateParams(t)});class ZodPromise extends ZodType{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==xe.promise&&!1===t.common.async)return addIssueToContext(t,{code:be.invalid_type,expected:xe.promise,received:t.parsedType}),we;const r=t.parsedType===xe.promise?t.data:Promise.resolve(t.data);return OK(r.then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}ZodPromise.create=(e,t)=>new ZodPromise({type:e,typeName:Ue.ZodPromise,...processCreateParams(t)});class ZodEffects extends ZodType{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===Ue.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:r}=this._processInputParams(e),a=this._def.effect||null,s={addIssue:e=>{addIssueToContext(r,e),e.fatal?t.abort():t.dirty()},get path(){return r.path}};if(s.addIssue=s.addIssue.bind(s),"preprocess"===a.type){const e=a.transform(r.data,s);if(r.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return we;const a=await this._def.schema._parseAsync({data:e,path:r.path,parent:r});return"aborted"===a.status?we:"dirty"===a.status||"dirty"===t.value?DIRTY(a.value):a});{if("aborted"===t.value)return we;const a=this._def.schema._parseSync({data:e,path:r.path,parent:r});return"aborted"===a.status?we:"dirty"===a.status||"dirty"===t.value?DIRTY(a.value):a}}if("refinement"===a.type){const executeRefinement=e=>{const t=a.refinement(e,s);if(r.common.async)return Promise.resolve(t);if(t instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1===r.common.async){const e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?we:("dirty"===e.status&&t.dirty(),executeRefinement(e.value),{status:t.value,value:e.value})}return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(e=>"aborted"===e.status?we:("dirty"===e.status&&t.dirty(),executeRefinement(e.value).then(()=>({status:t.value,value:e.value}))))}if("transform"===a.type){if(!1===r.common.async){const e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!isValid(e))return we;const n=a.transform(e.value,s);if(n instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:n}}return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(e=>isValid(e)?Promise.resolve(a.transform(e.value,s)).then(e=>({status:t.value,value:e})):we)}ge.assertNever(a)}}ZodEffects.create=(e,t,r)=>new ZodEffects({schema:e,typeName:Ue.ZodEffects,effect:t,...processCreateParams(r)}),ZodEffects.createWithPreprocess=(e,t,r)=>new ZodEffects({schema:t,effect:{type:"preprocess",transform:e},typeName:Ue.ZodEffects,...processCreateParams(r)});class ZodOptional extends ZodType{_parse(e){return this._getType(e)===xe.undefined?OK(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ZodOptional.create=(e,t)=>new ZodOptional({innerType:e,typeName:Ue.ZodOptional,...processCreateParams(t)});class ZodNullable extends ZodType{_parse(e){return this._getType(e)===xe.null?OK(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ZodNullable.create=(e,t)=>new ZodNullable({innerType:e,typeName:Ue.ZodNullable,...processCreateParams(t)});class ZodDefault extends ZodType{_parse(e){const{ctx:t}=this._processInputParams(e);let r=t.data;return t.parsedType===xe.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}ZodDefault.create=(e,t)=>new ZodDefault({innerType:e,typeName:Ue.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...processCreateParams(t)});class ZodCatch extends ZodType{_parse(e){const{ctx:t}=this._processInputParams(e),r={...t,common:{...t.common,issues:[]}},a=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return isAsync(a)?a.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new ZodError(r.common.issues)},input:r.data})})):{status:"valid",value:"valid"===a.status?a.value:this._def.catchValue({get error(){return new ZodError(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}ZodCatch.create=(e,t)=>new ZodCatch({innerType:e,typeName:Ue.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...processCreateParams(t)});class ZodNaN extends ZodType{_parse(e){if(this._getType(e)!==xe.nan){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:be.invalid_type,expected:xe.nan,received:t.parsedType}),we}return{status:"valid",value:e.data}}}ZodNaN.create=e=>new ZodNaN({typeName:Ue.ZodNaN,...processCreateParams(e)});class ZodBranded extends ZodType{_parse(e){const{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}class ZodPipeline extends ZodType{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.common.async){return(async()=>{const e=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?we:"dirty"===e.status?(t.dirty(),DIRTY(e.value)):this._def.out._parseAsync({data:e.value,path:r.path,parent:r})})()}{const e=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?we:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:r.path,parent:r})}}static create(e,t){return new ZodPipeline({in:e,out:t,typeName:Ue.ZodPipeline})}}class ZodReadonly extends ZodType{_parse(e){const t=this._def.innerType._parse(e),freeze=e=>(isValid(e)&&(e.value=Object.freeze(e.value)),e);return isAsync(t)?t.then(e=>freeze(e)):freeze(t)}unwrap(){return this._def.innerType}}function cleanParams(e,t){const r="function"==typeof e?e(t):"string"==typeof e?{message:e}:e;return"string"==typeof r?{message:r}:r}function custom(e,t={},r){return e?ZodAny.create().superRefine((a,s)=>{const n=e(a);if(n instanceof Promise)return n.then(e=>{if(!e){const e=cleanParams(t,a),n=e.fatal??r??!0;s.addIssue({code:"custom",...e,fatal:n})}});if(!n){const e=cleanParams(t,a),n=e.fatal??r??!0;s.addIssue({code:"custom",...e,fatal:n})}}):ZodAny.create()}var Ue;ZodReadonly.create=(e,t)=>new ZodReadonly({innerType:e,typeName:Ue.ZodReadonly,...processCreateParams(t)}),function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(Ue||(Ue={}));const instanceOfType=(e,t={message:`Input not instance of ${e.name}`})=>custom(t=>t instanceof e,t),Be=ZodString.create,ze=ZodNumber.create,qe=ZodBoolean.create,Je=ZodNull.create,Ke=ZodAny.create,We=ZodUnknown.create;ZodNever.create;const He=ZodArray.create,Ge=ZodObject.create,Ye=ZodUnion.create,Xe=ZodDiscriminatedUnion.create;ZodIntersection.create,ZodTuple.create;const Qe=ZodRecord.create,et=ZodLazy.create,tt=ZodLiteral.create,rt=ZodEnum.create;ZodPromise.create;const at=ZodOptional.create;ZodNullable.create;const st=Symbol("Let zodToJsonSchema decide on which parser to use"),nt={name:void 0,$refStrategy:"root",basePath:["#"],effectStrategy:"input",pipeStrategy:"all",dateStrategy:"format:date-time",mapStrategy:"entries",removeAdditionalStrategy:"passthrough",allowedAdditionalProperties:!0,rejectedAdditionalProperties:!1,definitionPath:"definitions",target:"jsonSchema7",strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:"escape",applyRegexFlags:!1,emailStrategy:"format:email",base64Strategy:"contentEncoding:base64",nameStrategy:"ref",openAiAnyTypeName:"OpenAiAnyType"},getRefs=e=>{const t=(e=>"string"==typeof e?{...nt,name:e}:{...nt,...e})(e),r=void 0!==t.name?[...t.basePath,t.definitionPath,t.name]:t.basePath;return{...t,flags:{hasReferencedOpenAiAnyType:!1},currentPath:r,propertyPath:void 0,seen:new Map(Object.entries(t.definitions).map(([e,r])=>[r._def,{def:r._def,path:[...t.basePath,t.definitionPath,e],jsonSchema:void 0}]))}};function addErrorMessage(e,t,r,a){a?.errorMessages&&r&&(e.errorMessage={...e.errorMessage,[t]:r})}function setResponseValueAndErrors(e,t,r,a,s){e[t]=r,addErrorMessage(e,t,a,s)}const getRelativePath=(e,t)=>{let r=0;for(;r<e.length&&r<t.length&&e[r]===t[r];r++);return[(e.length-r).toString(),...t.slice(r)].join("/")};function parseAnyDef(e){if("openAi"!==e.target)return{};const t=[...e.basePath,e.definitionPath,e.openAiAnyTypeName];return e.flags.hasReferencedOpenAiAnyType=!0,{$ref:"relative"===e.$refStrategy?getRelativePath(t,e.currentPath):t.join("/")}}function parseBrandedDef(e,t){return parseDef(e.type._def,t)}function parseDateDef(e,t,r){const a=r??t.dateStrategy;if(Array.isArray(a))return{anyOf:a.map((r,a)=>parseDateDef(e,t,r))};switch(a){case"string":case"format:date-time":return{type:"string",format:"date-time"};case"format:date":return{type:"string",format:"date"};case"integer":return integerDateParser(e,t)}}const integerDateParser=(e,t)=>{const r={type:"integer",format:"unix-time"};if("openApi3"===t.target)return r;for(const a of e.checks)switch(a.kind){case"min":setResponseValueAndErrors(r,"minimum",a.value,a.message,t);break;case"max":setResponseValueAndErrors(r,"maximum",a.value,a.message,t)}return r};let ot;const it=/^[cC][^\s-]{8,}$/,ut=/^[0-9a-z]+$/,lt=/^[0-9A-HJKMNP-TV-Z]{26}$/,ct=/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,zodPatterns_emoji=()=>(void 0===ot&&(ot=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),ot),dt=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,pt=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,mt=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,ft=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,ht=/^[a-zA-Z0-9_-]{21}$/,gt=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;function parseStringDef(e,t){const r={type:"string"};if(e.checks)for(const a of e.checks)switch(a.kind){case"min":setResponseValueAndErrors(r,"minLength","number"==typeof r.minLength?Math.max(r.minLength,a.value):a.value,a.message,t);break;case"max":setResponseValueAndErrors(r,"maxLength","number"==typeof r.maxLength?Math.min(r.maxLength,a.value):a.value,a.message,t);break;case"email":switch(t.emailStrategy){case"format:email":addFormat(r,"email",a.message,t);break;case"format:idn-email":addFormat(r,"idn-email",a.message,t);break;case"pattern:zod":addPattern(r,ct,a.message,t)}break;case"url":addFormat(r,"uri",a.message,t);break;case"uuid":addFormat(r,"uuid",a.message,t);break;case"regex":addPattern(r,a.regex,a.message,t);break;case"cuid":addPattern(r,it,a.message,t);break;case"cuid2":addPattern(r,ut,a.message,t);break;case"startsWith":addPattern(r,RegExp(`^${escapeLiteralCheckValue(a.value,t)}`),a.message,t);break;case"endsWith":addPattern(r,RegExp(`${escapeLiteralCheckValue(a.value,t)}$`),a.message,t);break;case"datetime":addFormat(r,"date-time",a.message,t);break;case"date":addFormat(r,"date",a.message,t);break;case"time":addFormat(r,"time",a.message,t);break;case"duration":addFormat(r,"duration",a.message,t);break;case"length":setResponseValueAndErrors(r,"minLength","number"==typeof r.minLength?Math.max(r.minLength,a.value):a.value,a.message,t),setResponseValueAndErrors(r,"maxLength","number"==typeof r.maxLength?Math.min(r.maxLength,a.value):a.value,a.message,t);break;case"includes":addPattern(r,RegExp(escapeLiteralCheckValue(a.value,t)),a.message,t);break;case"ip":"v6"!==a.version&&addFormat(r,"ipv4",a.message,t),"v4"!==a.version&&addFormat(r,"ipv6",a.message,t);break;case"base64url":addPattern(r,ft,a.message,t);break;case"jwt":addPattern(r,gt,a.message,t);break;case"cidr":"v6"!==a.version&&addPattern(r,dt,a.message,t),"v4"!==a.version&&addPattern(r,pt,a.message,t);break;case"emoji":addPattern(r,zodPatterns_emoji(),a.message,t);break;case"ulid":addPattern(r,lt,a.message,t);break;case"base64":switch(t.base64Strategy){case"format:binary":addFormat(r,"binary",a.message,t);break;case"contentEncoding:base64":setResponseValueAndErrors(r,"contentEncoding","base64",a.message,t);break;case"pattern:zod":addPattern(r,mt,a.message,t)}break;case"nanoid":addPattern(r,ht,a.message,t)}return r}function escapeLiteralCheckValue(e,t){return"escape"===t.patternStrategy?function(e){let t="";for(let r=0;r<e.length;r++)yt.has(e[r])||(t+="\\"),t+=e[r];return t}(e):e}const yt=new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");function addFormat(e,t,r,a){e.format||e.anyOf?.some(e=>e.format)?(e.anyOf||(e.anyOf=[]),e.format&&(e.anyOf.push({format:e.format,...e.errorMessage&&a.errorMessages&&{errorMessage:{format:e.errorMessage.format}}}),delete e.format,e.errorMessage&&(delete e.errorMessage.format,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.anyOf.push({format:t,...r&&a.errorMessages&&{errorMessage:{format:r}}})):setResponseValueAndErrors(e,"format",t,r,a)}function addPattern(e,t,r,a){e.pattern||e.allOf?.some(e=>e.pattern)?(e.allOf||(e.allOf=[]),e.pattern&&(e.allOf.push({pattern:e.pattern,...e.errorMessage&&a.errorMessages&&{errorMessage:{pattern:e.errorMessage.pattern}}}),delete e.pattern,e.errorMessage&&(delete e.errorMessage.pattern,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.allOf.push({pattern:stringifyRegExpWithFlags(t,a),...r&&a.errorMessages&&{errorMessage:{pattern:r}}})):setResponseValueAndErrors(e,"pattern",stringifyRegExpWithFlags(t,a),r,a)}function stringifyRegExpWithFlags(e,t){if(!t.applyRegexFlags||!e.flags)return e.source;const r=e.flags.includes("i"),a=e.flags.includes("m"),s=e.flags.includes("s"),n=r?e.source.toLowerCase():e.source;let o="",i=!1,u=!1,l=!1;for(let e=0;e<n.length;e++)if(i)o+=n[e],i=!1;else{if(r)if(u){if(n[e].match(/[a-z]/)){l?(o+=n[e],o+=`${n[e-2]}-${n[e]}`.toUpperCase(),l=!1):"-"===n[e+1]&&n[e+2]?.match(/[a-z]/)?(o+=n[e],l=!0):o+=`${n[e]}${n[e].toUpperCase()}`;continue}}else if(n[e].match(/[a-z]/)){o+=`[${n[e]}${n[e].toUpperCase()}]`;continue}if(a){if("^"===n[e]){o+="(^|(?<=[\r\n]))";continue}if("$"===n[e]){o+="($|(?=[\r\n]))";continue}}s&&"."===n[e]?o+=u?`${n[e]}\r\n`:`[${n[e]}\r\n]`:(o+=n[e],"\\"===n[e]?i=!0:u&&"]"===n[e]?u=!1:u||"["!==n[e]||(u=!0))}try{new RegExp(o)}catch{return console.warn(`Could not convert regex pattern at ${t.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`),e.source}return o}function parseRecordDef(e,t){if("openAi"===t.target&&console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead."),"openApi3"===t.target&&e.keyType?._def.typeName===Ue.ZodEnum)return{type:"object",required:e.keyType._def.values,properties:e.keyType._def.values.reduce((r,a)=>({...r,[a]:parseDef(e.valueType._def,{...t,currentPath:[...t.currentPath,"properties",a]})??parseAnyDef(t)}),{}),additionalProperties:t.rejectedAdditionalProperties};const r={type:"object",additionalProperties:parseDef(e.valueType._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]})??t.allowedAdditionalProperties};if("openApi3"===t.target)return r;if(e.keyType?._def.typeName===Ue.ZodString&&e.keyType._def.checks?.length){const{type:a,...s}=parseStringDef(e.keyType._def,t);return{...r,propertyNames:s}}if(e.keyType?._def.typeName===Ue.ZodEnum)return{...r,propertyNames:{enum:e.keyType._def.values}};if(e.keyType?._def.typeName===Ue.ZodBranded&&e.keyType._def.type._def.typeName===Ue.ZodString&&e.keyType._def.type._def.checks?.length){const{type:a,...s}=parseBrandedDef(e.keyType._def,t);return{...r,propertyNames:s}}return r}const vt={ZodString:"string",ZodNumber:"number",ZodBigInt:"integer",ZodBoolean:"boolean",ZodNull:"null"};const asAnyOf=(e,t)=>{const r=(e.options instanceof Map?Array.from(e.options.values()):e.options).map((e,r)=>parseDef(e._def,{...t,currentPath:[...t.currentPath,"anyOf",`${r}`]})).filter(e=>!!e&&(!t.strictUnions||"object"==typeof e&&Object.keys(e).length>0));return r.length?{anyOf:r}:void 0};function parseObjectDef(e,t){const r="openAi"===t.target,a={type:"object",properties:{}},s=[],n=e.shape();for(const e in n){let o=n[e];if(void 0===o||void 0===o._def)continue;let i=safeIsOptional(o);i&&r&&("ZodOptional"===o._def.typeName&&(o=o._def.innerType),o.isNullable()||(o=o.nullable()),i=!1);const u=parseDef(o._def,{...t,currentPath:[...t.currentPath,"properties",e],propertyPath:[...t.currentPath,"properties",e]});void 0!==u&&(a.properties[e]=u,i||s.push(e))}s.length&&(a.required=s);const o=function(e,t){if("ZodNever"!==e.catchall._def.typeName)return parseDef(e.catchall._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]});switch(e.unknownKeys){case"passthrough":return t.allowedAdditionalProperties;case"strict":return t.rejectedAdditionalProperties;case"strip":return"strict"===t.removeAdditionalStrategy?t.allowedAdditionalProperties:t.rejectedAdditionalProperties}}(e,t);return void 0!==o&&(a.additionalProperties=o),a}function safeIsOptional(e){try{return e.isOptional()}catch{return!0}}const selectParser=(e,t,r)=>{switch(t){case Ue.ZodString:return parseStringDef(e,r);case Ue.ZodNumber:return function(e,t){const r={type:"number"};if(!e.checks)return r;for(const a of e.checks)switch(a.kind){case"int":r.type="integer",addErrorMessage(r,"type",a.message,t);break;case"min":"jsonSchema7"===t.target?a.inclusive?setResponseValueAndErrors(r,"minimum",a.value,a.message,t):setResponseValueAndErrors(r,"exclusiveMinimum",a.value,a.message,t):(a.inclusive||(r.exclusiveMinimum=!0),setResponseValueAndErrors(r,"minimum",a.value,a.message,t));break;case"max":"jsonSchema7"===t.target?a.inclusive?setResponseValueAndErrors(r,"maximum",a.value,a.message,t):setResponseValueAndErrors(r,"exclusiveMaximum",a.value,a.message,t):(a.inclusive||(r.exclusiveMaximum=!0),setResponseValueAndErrors(r,"maximum",a.value,a.message,t));break;case"multipleOf":setResponseValueAndErrors(r,"multipleOf",a.value,a.message,t)}return r}(e,r);case Ue.ZodObject:return parseObjectDef(e,r);case Ue.ZodBigInt:return function(e,t){const r={type:"integer",format:"int64"};if(!e.checks)return r;for(const a of e.checks)switch(a.kind){case"min":"jsonSchema7"===t.target?a.inclusive?setResponseValueAndErrors(r,"minimum",a.value,a.message,t):setResponseValueAndErrors(r,"exclusiveMinimum",a.value,a.message,t):(a.inclusive||(r.exclusiveMinimum=!0),setResponseValueAndErrors(r,"minimum",a.value,a.message,t));break;case"max":"jsonSchema7"===t.target?a.inclusive?setResponseValueAndErrors(r,"maximum",a.value,a.message,t):setResponseValueAndErrors(r,"exclusiveMaximum",a.value,a.message,t):(a.inclusive||(r.exclusiveMaximum=!0),setResponseValueAndErrors(r,"maximum",a.value,a.message,t));break;case"multipleOf":setResponseValueAndErrors(r,"multipleOf",a.value,a.message,t)}return r}(e,r);case Ue.ZodBoolean:return{type:"boolean"};case Ue.ZodDate:return parseDateDef(e,r);case Ue.ZodUndefined:return function(e){return{not:parseAnyDef(e)}}(r);case Ue.ZodNull:return function(e){return"openApi3"===e.target?{enum:["null"],nullable:!0}:{type:"null"}}(r);case Ue.ZodArray:return function(e,t){const r={type:"array"};return e.type?._def&&e.type?._def?.typeName!==Ue.ZodAny&&(r.items=parseDef(e.type._def,{...t,currentPath:[...t.currentPath,"items"]})),e.minLength&&setResponseValueAndErrors(r,"minItems",e.minLength.value,e.minLength.message,t),e.maxLength&&setResponseValueAndErrors(r,"maxItems",e.maxLength.value,e.maxLength.message,t),e.exactLength&&(setResponseValueAndErrors(r,"minItems",e.exactLength.value,e.exactLength.message,t),setResponseValueAndErrors(r,"maxItems",e.exactLength.value,e.exactLength.message,t)),r}(e,r);case Ue.ZodUnion:case Ue.ZodDiscriminatedUnion:return function(e,t){if("openApi3"===t.target)return asAnyOf(e,t);const r=e.options instanceof Map?Array.from(e.options.values()):e.options;if(r.every(e=>e._def.typeName in vt&&(!e._def.checks||!e._def.checks.length))){const e=r.reduce((e,t)=>{const r=vt[t._def.typeName];return r&&!e.includes(r)?[...e,r]:e},[]);return{type:e.length>1?e:e[0]}}if(r.every(e=>"ZodLiteral"===e._def.typeName&&!e.description)){const e=r.reduce((e,t)=>{const r=typeof t._def.value;switch(r){case"string":case"number":case"boolean":return[...e,r];case"bigint":return[...e,"integer"];case"object":if(null===t._def.value)return[...e,"null"];default:return e}},[]);if(e.length===r.length){const t=e.filter((e,t,r)=>r.indexOf(e)===t);return{type:t.length>1?t:t[0],enum:r.reduce((e,t)=>e.includes(t._def.value)?e:[...e,t._def.value],[])}}}else if(r.every(e=>"ZodEnum"===e._def.typeName))return{type:"string",enum:r.reduce((e,t)=>[...e,...t._def.values.filter(t=>!e.includes(t))],[])};return asAnyOf(e,t)}(e,r);case Ue.ZodIntersection:return function(e,t){const r=[parseDef(e.left._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),parseDef(e.right._def,{...t,currentPath:[...t.currentPath,"allOf","1"]})].filter(e=>!!e);let a="jsonSchema2019-09"===t.target?{unevaluatedProperties:!1}:void 0;const s=[];return r.forEach(e=>{if("type"in(t=e)&&"string"===t.type||!("allOf"in t)){let t=e;if("additionalProperties"in e&&!1===e.additionalProperties){const{additionalProperties:r,...a}=e;t=a}else a=void 0;s.push(t)}else s.push(...e.allOf),void 0===e.unevaluatedProperties&&(a=void 0);var t}),s.length?{allOf:s,...a}:void 0}(e,r);case Ue.ZodTuple:return function(e,t){return e.rest?{type:"array",minItems:e.items.length,items:e.items.map((e,r)=>parseDef(e._def,{...t,currentPath:[...t.currentPath,"items",`${r}`]})).reduce((e,t)=>void 0===t?e:[...e,t],[]),additionalItems:parseDef(e.rest._def,{...t,currentPath:[...t.currentPath,"additionalItems"]})}:{type:"array",minItems:e.items.length,maxItems:e.items.length,items:e.items.map((e,r)=>parseDef(e._def,{...t,currentPath:[...t.currentPath,"items",`${r}`]})).reduce((e,t)=>void 0===t?e:[...e,t],[])}}(e,r);case Ue.ZodRecord:return parseRecordDef(e,r);case Ue.ZodLiteral:return function(e,t){const r=typeof e.value;return"bigint"!==r&&"number"!==r&&"boolean"!==r&&"string"!==r?{type:Array.isArray(e.value)?"array":"object"}:"openApi3"===t.target?{type:"bigint"===r?"integer":r,enum:[e.value]}:{type:"bigint"===r?"integer":r,const:e.value}}(e,r);case Ue.ZodEnum:return function(e){return{type:"string",enum:Array.from(e.values)}}(e);case Ue.ZodNativeEnum:return function(e){const t=e.values,r=Object.keys(e.values).filter(e=>"number"!=typeof t[t[e]]).map(e=>t[e]),a=Array.from(new Set(r.map(e=>typeof e)));return{type:1===a.length?"string"===a[0]?"string":"number":["string","number"],enum:r}}(e);case Ue.ZodNullable:return function(e,t){if(["ZodString","ZodNumber","ZodBigInt","ZodBoolean","ZodNull"].includes(e.innerType._def.typeName)&&(!e.innerType._def.checks||!e.innerType._def.checks.length))return"openApi3"===t.target?{type:vt[e.innerType._def.typeName],nullable:!0}:{type:[vt[e.innerType._def.typeName],"null"]};if("openApi3"===t.target){const r=parseDef(e.innerType._def,{...t,currentPath:[...t.currentPath]});return r&&"$ref"in r?{allOf:[r],nullable:!0}:r&&{...r,nullable:!0}}const r=parseDef(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","0"]});return r&&{anyOf:[r,{type:"null"}]}}(e,r);case Ue.ZodOptional:return((e,t)=>{if(t.currentPath.toString()===t.propertyPath?.toString())return parseDef(e.innerType._def,t);const r=parseDef(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","1"]});return r?{anyOf:[{not:parseAnyDef(t)},r]}:parseAnyDef(t)})(e,r);case Ue.ZodMap:return function(e,t){return"record"===t.mapStrategy?parseRecordDef(e,t):{type:"array",maxItems:125,items:{type:"array",items:[parseDef(e.keyType._def,{...t,currentPath:[...t.currentPath,"items","items","0"]})||parseAnyDef(t),parseDef(e.valueType._def,{...t,currentPath:[...t.currentPath,"items","items","1"]})||parseAnyDef(t)],minItems:2,maxItems:2}}}(e,r);case Ue.ZodSet:return function(e,t){const r={type:"array",uniqueItems:!0,items:parseDef(e.valueType._def,{...t,currentPath:[...t.currentPath,"items"]})};return e.minSize&&setResponseValueAndErrors(r,"minItems",e.minSize.value,e.minSize.message,t),e.maxSize&&setResponseValueAndErrors(r,"maxItems",e.maxSize.value,e.maxSize.message,t),r}(e,r);case Ue.ZodLazy:return()=>e.getter()._def;case Ue.ZodPromise:return function(e,t){return parseDef(e.type._def,t)}(e,r);case Ue.ZodNaN:case Ue.ZodNever:return function(e){return"openAi"===e.target?void 0:{not:parseAnyDef({...e,currentPath:[...e.currentPath,"not"]})}}(r);case Ue.ZodEffects:return function(e,t){return"input"===t.effectStrategy?parseDef(e.schema._def,t):parseAnyDef(t)}(e,r);case Ue.ZodAny:return parseAnyDef(r);case Ue.ZodUnknown:return function(e){return parseAnyDef(e)}(r);case Ue.ZodDefault:return function(e,t){return{...parseDef(e.innerType._def,t),default:e.defaultValue()}}(e,r);case Ue.ZodBranded:return parseBrandedDef(e,r);case Ue.ZodReadonly:case Ue.ZodCatch:return((e,t)=>parseDef(e.innerType._def,t))(e,r);case Ue.ZodPipeline:return((e,t)=>{if("input"===t.pipeStrategy)return parseDef(e.in._def,t);if("output"===t.pipeStrategy)return parseDef(e.out._def,t);const r=parseDef(e.in._def,{...t,currentPath:[...t.currentPath,"allOf","0"]});return{allOf:[r,parseDef(e.out._def,{...t,currentPath:[...t.currentPath,"allOf",r?"1":"0"]})].filter(e=>void 0!==e)}})(e,r);case Ue.ZodFunction:case Ue.ZodVoid:case Ue.ZodSymbol:default:return}};function parseDef(e,t,r=!1){const a=t.seen.get(e);if(t.override){const s=t.override?.(e,t,a,r);if(s!==st)return s}if(a&&!r){const e=get$ref(a,t);if(void 0!==e)return e}const s={def:e,path:t.currentPath,jsonSchema:void 0};t.seen.set(e,s);const n=selectParser(e,e.typeName,t),o="function"==typeof n?parseDef(n(),t):n;if(o&&addMeta(e,t,o),t.postProcess){const r=t.postProcess(o,e,t);return s.jsonSchema=o,r}return s.jsonSchema=o,o}const get$ref=(e,t)=>{switch(t.$refStrategy){case"root":return{$ref:e.path.join("/")};case"relative":return{$ref:getRelativePath(t.currentPath,e.path)};case"none":case"seen":return e.path.length<t.currentPath.length&&e.path.every((e,r)=>t.currentPath[r]===e)?(console.warn(`Recursive reference detected at ${t.currentPath.join("/")}! Defaulting to any`),parseAnyDef(t)):"seen"===t.$refStrategy?parseAnyDef(t):void 0}},addMeta=(e,t,r)=>(e.description&&(r.description=e.description,t.markdownDescription&&(r.markdownDescription=e.description)),r);var _t={code:"0",name:"text",parse:e=>{if("string"!=typeof e)throw new Error('"text" parts expect a string value.');return{type:"text",value:e}}},xt={code:"3",name:"error",parse:e=>{if("string"!=typeof e)throw new Error('"error" parts expect a string value.');return{type:"error",value:e}}},bt={code:"4",name:"assistant_message",parse:e=>{if(!(null!=e&&"object"==typeof e&&"id"in e&&"role"in e&&"content"in e&&"string"==typeof e.id&&"string"==typeof e.role&&"assistant"===e.role&&Array.isArray(e.content)&&e.content.every(e=>null!=e&&"object"==typeof e&&"type"in e&&"text"===e.type&&"text"in e&&null!=e.text&&"object"==typeof e.text&&"value"in e.text&&"string"==typeof e.text.value)))throw new Error('"assistant_message" parts expect an object with an "id", "role", and "content" property.');return{type:"assistant_message",value:e}}},Tt={code:"5",name:"assistant_control_data",parse:e=>{if(null==e||"object"!=typeof e||!("threadId"in e)||!("messageId"in e)||"string"!=typeof e.threadId||"string"!=typeof e.messageId)throw new Error('"assistant_control_data" parts expect an object with a "threadId" and "messageId" property.');return{type:"assistant_control_data",value:{threadId:e.threadId,messageId:e.messageId}}}},wt={code:"6",name:"data_message",parse:e=>{if(null==e||"object"!=typeof e||!("role"in e)||!("data"in e)||"string"!=typeof e.role||"data"!==e.role)throw new Error('"data_message" parts expect an object with a "role" and "data" property.');return{type:"data_message",value:e}}};function fixJson(e){const t=["ROOT"];let r=-1,a=null;function processValueStart(e,s,n){switch(e){case'"':r=s,t.pop(),t.push(n),t.push("INSIDE_STRING");break;case"f":case"t":case"n":r=s,a=s,t.pop(),t.push(n),t.push("INSIDE_LITERAL");break;case"-":t.pop(),t.push(n),t.push("INSIDE_NUMBER");break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":r=s,t.pop(),t.push(n),t.push("INSIDE_NUMBER");break;case"{":r=s,t.pop(),t.push(n),t.push("INSIDE_OBJECT_START");break;case"[":r=s,t.pop(),t.push(n),t.push("INSIDE_ARRAY_START")}}function processAfterObjectValue(e,a){switch(e){case",":t.pop(),t.push("INSIDE_OBJECT_AFTER_COMMA");break;case"}":r=a,t.pop()}}function processAfterArrayValue(e,a){switch(e){case",":t.pop(),t.push("INSIDE_ARRAY_AFTER_COMMA");break;case"]":r=a,t.pop()}}for(let s=0;s<e.length;s++){const n=e[s];switch(t[t.length-1]){case"ROOT":processValueStart(n,s,"FINISH");break;case"INSIDE_OBJECT_START":switch(n){case'"':t.pop(),t.push("INSIDE_OBJECT_KEY");break;case"}":r=s,t.pop()}break;case"INSIDE_OBJECT_AFTER_COMMA":if('"'===n)t.pop(),t.push("INSIDE_OBJECT_KEY");break;case"INSIDE_OBJECT_KEY":if('"'===n)t.pop(),t.push("INSIDE_OBJECT_AFTER_KEY");break;case"INSIDE_OBJECT_AFTER_KEY":if(":"===n)t.pop(),t.push("INSIDE_OBJECT_BEFORE_VALUE");break;case"INSIDE_OBJECT_BEFORE_VALUE":processValueStart(n,s,"INSIDE_OBJECT_AFTER_VALUE");break;case"INSIDE_OBJECT_AFTER_VALUE":processAfterObjectValue(n,s);break;case"INSIDE_STRING":switch(n){case'"':t.pop(),r=s;break;case"\\":t.push("INSIDE_STRING_ESCAPE");break;default:r=s}break;case"INSIDE_ARRAY_START":if("]"===n)r=s,t.pop();else r=s,processValueStart(n,s,"INSIDE_ARRAY_AFTER_VALUE");break;case"INSIDE_ARRAY_AFTER_VALUE":switch(n){case",":t.pop(),t.push("INSIDE_ARRAY_AFTER_COMMA");break;case"]":r=s,t.pop();break;default:r=s}break;case"INSIDE_ARRAY_AFTER_COMMA":processValueStart(n,s,"INSIDE_ARRAY_AFTER_VALUE");break;case"INSIDE_STRING_ESCAPE":t.pop(),r=s;break;case"INSIDE_NUMBER":switch(n){case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":r=s;break;case"e":case"E":case"-":case".":break;case",":t.pop(),"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&processAfterArrayValue(n,s),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]&&processAfterObjectValue(n,s);break;case"}":t.pop(),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]&&processAfterObjectValue(n,s);break;case"]":t.pop(),"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&processAfterArrayValue(n,s);break;default:t.pop()}break;case"INSIDE_LITERAL":{const o=e.substring(a,s+1);"false".startsWith(o)||"true".startsWith(o)||"null".startsWith(o)?r=s:(t.pop(),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]?processAfterObjectValue(n,s):"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&processAfterArrayValue(n,s));break}}}let s=e.slice(0,r+1);for(let r=t.length-1;r>=0;r--){switch(t[r]){case"INSIDE_STRING":s+='"';break;case"INSIDE_OBJECT_KEY":case"INSIDE_OBJECT_AFTER_KEY":case"INSIDE_OBJECT_AFTER_COMMA":case"INSIDE_OBJECT_START":case"INSIDE_OBJECT_BEFORE_VALUE":case"INSIDE_OBJECT_AFTER_VALUE":s+="}";break;case"INSIDE_ARRAY_START":case"INSIDE_ARRAY_AFTER_COMMA":case"INSIDE_ARRAY_AFTER_VALUE":s+="]";break;case"INSIDE_LITERAL":{const t=e.substring(a,e.length);"true".startsWith(t)?s+="true".slice(t.length):"false".startsWith(t)?s+="false".slice(t.length):"null".startsWith(t)&&(s+="null".slice(t.length))}}}return s}[_t,xt,bt,Tt,wt].map(e=>e.code);var It=[{code:"0",name:"text",parse:e=>{if("string"!=typeof e)throw new Error('"text" parts expect a string value.');return{type:"text",value:e}}},{code:"2",name:"data",parse:e=>{if(!Array.isArray(e))throw new Error('"data" parts expect an array value.');return{type:"data",value:e}}},{code:"3",name:"error",parse:e=>{if("string"!=typeof e)throw new Error('"error" parts expect a string value.');return{type:"error",value:e}}},{code:"8",name:"message_annotations",parse:e=>{if(!Array.isArray(e))throw new Error('"message_annotations" parts expect an array value.');return{type:"message_annotations",value:e}}},{code:"9",name:"tool_call",parse:e=>{if(null==e||"object"!=typeof e||!("toolCallId"in e)||"string"!=typeof e.toolCallId||!("toolName"in e)||"string"!=typeof e.toolName||!("args"in e)||"object"!=typeof e.args)throw new Error('"tool_call" parts expect an object with a "toolCallId", "toolName", and "args" property.');return{type:"tool_call",value:e}}},{code:"a",name:"tool_result",parse:e=>{if(null==e||"object"!=typeof e||!("toolCallId"in e)||"string"!=typeof e.toolCallId||!("result"in e))throw new Error('"tool_result" parts expect an object with a "toolCallId" and a "result" property.');return{type:"tool_result",value:e}}},{code:"b",name:"tool_call_streaming_start",parse:e=>{if(null==e||"object"!=typeof e||!("toolCallId"in e)||"string"!=typeof e.toolCallId||!("toolName"in e)||"string"!=typeof e.toolName)throw new Error('"tool_call_streaming_start" parts expect an object with a "toolCallId" and "toolName" property.');return{type:"tool_call_streaming_start",value:e}}},{code:"c",name:"tool_call_delta",parse:e=>{if(null==e||"object"!=typeof e||!("toolCallId"in e)||"string"!=typeof e.toolCallId||!("argsTextDelta"in e)||"string"!=typeof e.argsTextDelta)throw new Error('"tool_call_delta" parts expect an object with a "toolCallId" and "argsTextDelta" property.');return{type:"tool_call_delta",value:e}}},{code:"d",name:"finish_message",parse:e=>{if(null==e||"object"!=typeof e||!("finishReason"in e)||"string"!=typeof e.finishReason)throw new Error('"finish_message" parts expect an object with a "finishReason" property.');const t={finishReason:e.finishReason};return"usage"in e&&null!=e.usage&&"object"==typeof e.usage&&"promptTokens"in e.usage&&"completionTokens"in e.usage&&(t.usage={promptTokens:"number"==typeof e.usage.promptTokens?e.usage.promptTokens:Number.NaN,completionTokens:"number"==typeof e.usage.completionTokens?e.usage.completionTokens:Number.NaN}),{type:"finish_message",value:t}}},{code:"e",name:"finish_step",parse:e=>{if(null==e||"object"!=typeof e||!("finishReason"in e)||"string"!=typeof e.finishReason)throw new Error('"finish_step" parts expect an object with a "finishReason" property.');const t={finishReason:e.finishReason,isContinued:!1};return"usage"in e&&null!=e.usage&&"object"==typeof e.usage&&"promptTokens"in e.usage&&"completionTokens"in e.usage&&(t.usage={promptTokens:"number"==typeof e.usage.promptTokens?e.usage.promptTokens:Number.NaN,completionTokens:"number"==typeof e.usage.completionTokens?e.usage.completionTokens:Number.NaN}),"isContinued"in e&&"boolean"==typeof e.isContinued&&(t.isContinued=e.isContinued),{type:"finish_step",value:t}}},{code:"f",name:"start_step",parse:e=>{if(null==e||"object"!=typeof e||!("messageId"in e)||"string"!=typeof e.messageId)throw new Error('"start_step" parts expect an object with an "id" property.');return{type:"start_step",value:{messageId:e.messageId}}}},{code:"g",name:"reasoning",parse:e=>{if("string"!=typeof e)throw new Error('"reasoning" parts expect a string value.');return{type:"reasoning",value:e}}},{code:"h",name:"source",parse:e=>{if(null==e||"object"!=typeof e)throw new Error('"source" parts expect a Source object.');return{type:"source",value:e}}},{code:"i",name:"redacted_reasoning",parse:e=>{if(null==e||"object"!=typeof e||!("data"in e)||"string"!=typeof e.data)throw new Error('"redacted_reasoning" parts expect an object with a "data" property.');return{type:"redacted_reasoning",value:{data:e.data}}}},{code:"j",name:"reasoning_signature",parse:e=>{if(null==e||"object"!=typeof e||!("signature"in e)||"string"!=typeof e.signature)throw new Error('"reasoning_signature" parts expect an object with a "signature" property.');return{type:"reasoning_signature",value:{signature:e.signature}}}},{code:"k",name:"file",parse:e=>{if(null==e||"object"!=typeof e||!("data"in e)||"string"!=typeof e.data||!("mimeType"in e)||"string"!=typeof e.mimeType)throw new Error('"file" parts expect an object with a "data" and "mimeType" property.');return{type:"file",value:e}}}];function formatDataStreamPart(e,t){const r=It.find(t=>t.name===e);if(!r)throw new Error(`Invalid stream part type: ${e}`);return`${r.code}:${JSON.stringify(t)}\n`}function zodSchema(e,t){const r=null!=void 0&&undefined;return function(e,{validate:t}={}){return{[St]:!0,_type:void 0,[he]:!0,jsonSchema:e,validate:t}}(((e,t)=>{const r=getRefs(t);let a="object"==typeof t&&t.definitions?Object.entries(t.definitions).reduce((e,[t,a])=>({...e,[t]:parseDef(a._def,{...r,currentPath:[...r.basePath,r.definitionPath,t]},!0)??parseAnyDef(r)}),{}):void 0;const s="string"==typeof t?t:"title"===t?.nameStrategy?void 0:t?.name,n=parseDef(e._def,void 0===s?r:{...r,currentPath:[...r.basePath,r.definitionPath,s]},!1)??parseAnyDef(r),o="object"==typeof t&&void 0!==t.name&&"title"===t.nameStrategy?t.name:void 0;void 0!==o&&(n.title=o),r.flags.hasReferencedOpenAiAnyType&&(a||(a={}),a[r.openAiAnyTypeName]||(a[r.openAiAnyTypeName]={type:["string","number","integer","boolean","array","null"],items:{$ref:"relative"===r.$refStrategy?"1":[...r.basePath,r.definitionPath,r.openAiAnyTypeName].join("/")}}));const i=void 0===s?a?{...n,[r.definitionPath]:a}:n:{$ref:[..."relative"===r.$refStrategy?[]:r.basePath,r.definitionPath,s].join("/"),[r.definitionPath]:{...a,[s]:n}};return"jsonSchema7"===r.target?i.$schema="http://json-schema.org/draft-07/schema#":"jsonSchema2019-09"!==r.target&&"openAi"!==r.target||(i.$schema="https://json-schema.org/draft/2019-09/schema#"),"openAi"===r.target&&("anyOf"in i||"oneOf"in i||"allOf"in i||"type"in i&&Array.isArray(i.type))&&console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property."),i})(e,{$refStrategy:r?"root":"none",target:"jsonSchema7"}),{validate:t=>{const r=e.safeParse(t);return r.success?{success:!0,value:r.data}:{success:!1,error:r.error}}})}Object.fromEntries(It.map(e=>[e.code,e])),Object.fromEntries(It.map(e=>[e.name,e.code])),It.map(e=>e.code);var St=Symbol.for("vercel.ai.schema");function asSchema(e){return"object"==typeof(t=e)&&null!==t&&St in t&&!0===t[St]&&"jsonSchema"in t&&"validate"in t?e:zodSchema(e);var t}var At="object"==typeof globalThis?globalThis:t,kt="1.9.0",Ct=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;var Et=function(e){var t=new Set([e]),r=new Set,a=e.match(Ct);if(!a)return function(){return!1};var s=+a[1],n=+a[2],o=+a[3];if(null!=a[4])return function(t){return t===e};function _reject(e){return r.add(e),!1}function _accept(e){return t.add(e),!0}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;var a=e.match(Ct);if(!a)return _reject(e);var i=+a[1],u=+a[2],l=+a[3];return null!=a[4]||s!==i?_reject(e):0===s?n===u&&o<=l?_accept(e):_reject(e):n<=u?_accept(e):_reject(e)}}(kt),Pt=kt.split(".")[0],Zt=Symbol.for("opentelemetry.js.api."+Pt),Nt=At;function registerGlobal(e,t,r,a){var s;void 0===a&&(a=!1);var n=Nt[Zt]=null!==(s=Nt[Zt])&&void 0!==s?s:{version:kt};if(!a&&n[e]){var o=new Error("@opentelemetry/api: Attempted duplicate registration of API: "+e);return r.error(o.stack||o.message),!1}if(n.version!==kt){o=new Error("@opentelemetry/api: Registration of version v"+n.version+" for "+e+" does not match previously registered API v"+kt);return r.error(o.stack||o.message),!1}return n[e]=t,r.debug("@opentelemetry/api: Registered a global for "+e+" v"+kt+"."),!0}function getGlobal(e){var t,r,a=null===(t=Nt[Zt])||void 0===t?void 0:t.version;if(a&&Et(a))return null===(r=Nt[Zt])||void 0===r?void 0:r[e]}function unregisterGlobal(e,t){t.debug("@opentelemetry/api: Unregistering a global for "+e+" v"+kt+".");var r=Nt[Zt];r&&delete r[e]}var Rt,__read$3=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var a,s,n=r.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(a=n.next()).done;)o.push(a.value)}catch(e){s={error:e}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return o},__spreadArray$3=function(e,t,r){if(r||2===arguments.length)for(var a,s=0,n=t.length;s<n;s++)!a&&s in t||(a||(a=Array.prototype.slice.call(t,0,s)),a[s]=t[s]);return e.concat(a||Array.prototype.slice.call(t))},Ot=function(){function DiagComponentLogger(e){this._namespace=e.namespace||"DiagComponentLogger"}return DiagComponentLogger.prototype.debug=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return logProxy("debug",this._namespace,e)},DiagComponentLogger.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return logProxy("error",this._namespace,e)},DiagComponentLogger.prototype.info=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return logProxy("info",this._namespace,e)},DiagComponentLogger.prototype.warn=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return logProxy("warn",this._namespace,e)},DiagComponentLogger.prototype.verbose=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return logProxy("verbose",this._namespace,e)},DiagComponentLogger}();function logProxy(e,t,r){var a=getGlobal("diag");if(a)return r.unshift(t),a[e].apply(a,__spreadArray$3([],__read$3(r),!1))}!function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(Rt||(Rt={}));var __read$2=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var a,s,n=r.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(a=n.next()).done;)o.push(a.value)}catch(e){s={error:e}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return o},__spreadArray$2=function(e,t,r){if(r||2===arguments.length)for(var a,s=0,n=t.length;s<n;s++)!a&&s in t||(a||(a=Array.prototype.slice.call(t,0,s)),a[s]=t[s]);return e.concat(a||Array.prototype.slice.call(t))},jt=function(){function DiagAPI(){function _logProxy(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var a=getGlobal("diag");if(a)return a[e].apply(a,__spreadArray$2([],__read$2(t),!1))}}var e=this;e.setLogger=function(t,r){var a,s,n;if(void 0===r&&(r={logLevel:Rt.INFO}),t===e){var o=new Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return e.error(null!==(a=o.stack)&&void 0!==a?a:o.message),!1}"number"==typeof r&&(r={logLevel:r});var i=getGlobal("diag"),u=function(e,t){function _filterFunc(r,a){var s=t[r];return"function"==typeof s&&e>=a?s.bind(t):function(){}}return e<Rt.NONE?e=Rt.NONE:e>Rt.ALL&&(e=Rt.ALL),t=t||{},{error:_filterFunc("error",Rt.ERROR),warn:_filterFunc("warn",Rt.WARN),info:_filterFunc("info",Rt.INFO),debug:_filterFunc("debug",Rt.DEBUG),verbose:_filterFunc("verbose",Rt.VERBOSE)}}(null!==(s=r.logLevel)&&void 0!==s?s:Rt.INFO,t);if(i&&!r.suppressOverrideMessage){var l=null!==(n=(new Error).stack)&&void 0!==n?n:"<failed to generate stacktrace>";i.warn("Current logger will be overwritten from "+l),u.warn("Current logger will overwrite one already registered from "+l)}return registerGlobal("diag",u,e,!0)},e.disable=function(){unregisterGlobal("diag",e)},e.createComponentLogger=function(e){return new Ot(e)},e.verbose=_logProxy("verbose"),e.debug=_logProxy("debug"),e.info=_logProxy("info"),e.warn=_logProxy("warn"),e.error=_logProxy("error")}return DiagAPI.instance=function(){return this._instance||(this._instance=new DiagAPI),this._instance},DiagAPI}();var Dt,Mt=new function BaseContext(e){var t=this;t._currentContext=e?new Map(e):new Map,t.getValue=function(e){return t._currentContext.get(e)},t.setValue=function(e,r){var a=new BaseContext(t._currentContext);return a._currentContext.set(e,r),a},t.deleteValue=function(e){var r=new BaseContext(t._currentContext);return r._currentContext.delete(e),r}},__read$1=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var a,s,n=r.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(a=n.next()).done;)o.push(a.value)}catch(e){s={error:e}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return o},__spreadArray$1=function(e,t,r){if(r||2===arguments.length)for(var a,s=0,n=t.length;s<n;s++)!a&&s in t||(a||(a=Array.prototype.slice.call(t,0,s)),a[s]=t[s]);return e.concat(a||Array.prototype.slice.call(t))},$t=function(){function NoopContextManager(){}return NoopContextManager.prototype.active=function(){return Mt},NoopContextManager.prototype.with=function(e,t,r){for(var a=[],s=3;s<arguments.length;s++)a[s-3]=arguments[s];return t.call.apply(t,__spreadArray$1([r],__read$1(a),!1))},NoopContextManager.prototype.bind=function(e,t){return t},NoopContextManager.prototype.enable=function(){return this},NoopContextManager.prototype.disable=function(){return this},NoopContextManager}(),__read=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var a,s,n=r.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(a=n.next()).done;)o.push(a.value)}catch(e){s={error:e}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return o},__spreadArray=function(e,t,r){if(r||2===arguments.length)for(var a,s=0,n=t.length;s<n;s++)!a&&s in t||(a||(a=Array.prototype.slice.call(t,0,s)),a[s]=t[s]);return e.concat(a||Array.prototype.slice.call(t))},Lt="context",Vt=new $t,Ft=function(){function ContextAPI(){}return ContextAPI.getInstance=function(){return this._instance||(this._instance=new ContextAPI),this._instance},ContextAPI.prototype.setGlobalContextManager=function(e){return registerGlobal(Lt,e,jt.instance())},ContextAPI.prototype.active=function(){return this._getContextManager().active()},ContextAPI.prototype.with=function(e,t,r){for(var a,s=[],n=3;n<arguments.length;n++)s[n-3]=arguments[n];return(a=this._getContextManager()).with.apply(a,__spreadArray([e,t,r],__read(s),!1))},ContextAPI.prototype.bind=function(e,t){return this._getContextManager().bind(e,t)},ContextAPI.prototype._getContextManager=function(){return getGlobal(Lt)||Vt},ContextAPI.prototype.disable=function(){this._getContextManager().disable(),unregisterGlobal(Lt,jt.instance())},ContextAPI}();!function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(Dt||(Dt={}));var Ut,Bt="0000000000000000",zt="00000000000000000000000000000000",qt={traceId:zt,spanId:Bt,traceFlags:Dt.NONE},Jt=function(){function NonRecordingSpan(e){void 0===e&&(e=qt),this._spanContext=e}return NonRecordingSpan.prototype.spanContext=function(){return this._spanContext},NonRecordingSpan.prototype.setAttribute=function(e,t){return this},NonRecordingSpan.prototype.setAttributes=function(e){return this},NonRecordingSpan.prototype.addEvent=function(e,t){return this},NonRecordingSpan.prototype.addLink=function(e){return this},NonRecordingSpan.prototype.addLinks=function(e){return this},NonRecordingSpan.prototype.setStatus=function(e){return this},NonRecordingSpan.prototype.updateName=function(e){return this},NonRecordingSpan.prototype.end=function(e){},NonRecordingSpan.prototype.isRecording=function(){return!1},NonRecordingSpan.prototype.recordException=function(e,t){},NonRecordingSpan}(),Kt=(Ut="OpenTelemetry Context Key SPAN",Symbol.for(Ut));function getSpan(e){return e.getValue(Kt)||void 0}function getActiveSpan(){return getSpan(Ft.getInstance().active())}function setSpan(e,t){return e.setValue(Kt,t)}function deleteSpan(e){return e.deleteValue(Kt)}function setSpanContext(e,t){return setSpan(e,new Jt(t))}function getSpanContext(e){var t;return null===(t=getSpan(e))||void 0===t?void 0:t.spanContext()}var Wt=/^([0-9a-f]{32})$/i,Ht=/^[0-9a-f]{16}$/i;function isSpanContextValid(e){return r=e.traceId,Wt.test(r)&&r!==zt&&(t=e.spanId,Ht.test(t)&&t!==Bt);var t,r}function wrapSpanContext(e){return new Jt(e)}var Gt=Ft.getInstance(),Yt=function(){function NoopTracer(){}return NoopTracer.prototype.startSpan=function(e,t,r){if(void 0===r&&(r=Gt.active()),Boolean(null==t?void 0:t.root))return new Jt;var a,s=r&&getSpanContext(r);return"object"==typeof(a=s)&&"string"==typeof a.spanId&&"string"==typeof a.traceId&&"number"==typeof a.traceFlags&&isSpanContextValid(s)?new Jt(s):new Jt},NoopTracer.prototype.startActiveSpan=function(e,t,r,a){var s,n,o;if(!(arguments.length<2)){2===arguments.length?o=t:3===arguments.length?(s=t,o=r):(s=t,n=r,o=a);var i=null!=n?n:Gt.active(),u=this.startSpan(e,s,i),l=setSpan(i,u);return Gt.with(l,o,void 0,u)}},NoopTracer}();var Xt,Qt=new Yt,er=function(){function ProxyTracer(e,t,r,a){this._provider=e,this.name=t,this.version=r,this.options=a}return ProxyTracer.prototype.startSpan=function(e,t,r){return this._getTracer().startSpan(e,t,r)},ProxyTracer.prototype.startActiveSpan=function(e,t,r,a){var s=this._getTracer();return Reflect.apply(s.startActiveSpan,s,arguments)},ProxyTracer.prototype._getTracer=function(){if(this._delegate)return this._delegate;var e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):Qt},ProxyTracer}(),tr=new(function(){function NoopTracerProvider(){}return NoopTracerProvider.prototype.getTracer=function(e,t,r){return new Yt},NoopTracerProvider}()),rr=function(){function ProxyTracerProvider(){}return ProxyTracerProvider.prototype.getTracer=function(e,t,r){var a;return null!==(a=this.getDelegateTracer(e,t,r))&&void 0!==a?a:new er(this,e,t,r)},ProxyTracerProvider.prototype.getDelegate=function(){var e;return null!==(e=this._delegate)&&void 0!==e?e:tr},ProxyTracerProvider.prototype.setDelegate=function(e){this._delegate=e},ProxyTracerProvider.prototype.getDelegateTracer=function(e,t,r){var a;return null===(a=this._delegate)||void 0===a?void 0:a.getTracer(e,t,r)},ProxyTracerProvider}();!function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(Xt||(Xt={}));var ar="trace",sr=function(){function TraceAPI(){this._proxyTracerProvider=new rr,this.wrapSpanContext=wrapSpanContext,this.isSpanContextValid=isSpanContextValid,this.deleteSpan=deleteSpan,this.getSpan=getSpan,this.getActiveSpan=getActiveSpan,this.getSpanContext=getSpanContext,this.setSpan=setSpan,this.setSpanContext=setSpanContext}return TraceAPI.getInstance=function(){return this._instance||(this._instance=new TraceAPI),this._instance},TraceAPI.prototype.setGlobalTracerProvider=function(e){var t=registerGlobal(ar,this._proxyTracerProvider,jt.instance());return t&&this._proxyTracerProvider.setDelegate(e),t},TraceAPI.prototype.getTracerProvider=function(){return getGlobal(ar)||this._proxyTracerProvider},TraceAPI.prototype.getTracer=function(e,t){return this.getTracerProvider().getTracer(e,t)},TraceAPI.prototype.disable=function(){unregisterGlobal(ar,jt.instance()),this._proxyTracerProvider=new rr},TraceAPI}(),nr=sr.getInstance(),or=Object.defineProperty,__export=(e,t)=>{for(var r in t)or(e,r,{get:t[r],enumerable:!0})};function prepareResponseHeaders(e,{contentType:t,dataStreamVersion:r}){const a=new Headers(null!=e?e:{});return a.has("Content-Type")||a.set("Content-Type",t),a.set("X-Vercel-AI-Data-Stream",r),a}var ir,ur="AI_InvalidArgumentError",lr=`vercel.ai.error.${ur}`,cr=Symbol.for(lr),dr=class extends m{constructor({parameter:e,value:t,message:r}){super({name:ur,message:`Invalid argument for parameter ${e}: ${r}`}),this[ir]=!0,this.parameter=e,this.value=t}static isInstance(e){return m.hasMarker(e,lr)}};ir=cr;var pr,mr="AI_RetryError",fr=`vercel.ai.error.${mr}`,hr=Symbol.for(fr),gr=class extends m{constructor({message:e,reason:t,errors:r}){super({name:mr,message:e}),this[pr]=!0,this.reason=t,this.errors=r,this.lastError=r[r.length-1]}static isInstance(e){return m.hasMarker(e,fr)}};pr=hr;var retryWithExponentialBackoff=({maxRetries:e=2,initialDelayInMs:t=2e3,backoffFactor:r=2}={})=>async a=>_retryWithExponentialBackoff(a,{maxRetries:e,delayInMs:t,backoffFactor:r});async function _retryWithExponentialBackoff(e,{maxRetries:t,delayInMs:r,backoffFactor:a},s=[]){try{return await e()}catch(n){if(isAbortError(n))throw n;if(0===t)throw n;const o=function(e){return null==e?"unknown error":"string"==typeof e?e:e instanceof Error?e.message:JSON.stringify(e)}(n),i=[...s,n],u=i.length;if(u>t)throw new gr({message:`Failed after ${u} attempts. Last error: ${o}`,reason:"maxRetriesExceeded",errors:i});if(n instanceof Error&&y.isInstance(n)&&!0===n.isRetryable&&u<=t)return await async function(e){return null==e?Promise.resolve():new Promise(t=>setTimeout(t,e))}(r),_retryWithExponentialBackoff(e,{maxRetries:t,delayInMs:a*r,backoffFactor:a},i);if(1===u)throw n;throw new gr({message:`Failed after ${u} attempts with non-retryable error: '${o}'`,reason:"errorNotRetryable",errors:i})}}function assembleOperationName({operationId:e,telemetry:t}){return{"operation.name":`${e}${null!=(null==t?void 0:t.functionId)?` ${t.functionId}`:""}`,"resource.name":null==t?void 0:t.functionId,"ai.operationId":e,"ai.telemetry.functionId":null==t?void 0:t.functionId}}var yr={startSpan:()=>vr,startActiveSpan:(e,t,r,a)=>"function"==typeof t?t(vr):"function"==typeof r?r(vr):"function"==typeof a?a(vr):void 0},vr={spanContext:()=>_r,setAttribute(){return this},setAttributes(){return this},addEvent(){return this},addLink(){return this},addLinks(){return this},setStatus(){return this},updateName(){return this},end(){return this},isRecording:()=>!1,recordException(){return this}},_r={traceId:"",spanId:"",traceFlags:0};function recordSpan({name:e,tracer:t,attributes:r,fn:a,endWhenDone:s=!0}){return t.startActiveSpan(e,{attributes:r},async e=>{try{const t=await a(e);return s&&e.end(),t}catch(t){try{t instanceof Error?(e.recordException({name:t.name,message:t.message,stack:t.stack}),e.setStatus({code:Xt.ERROR,message:t.message})):e.setStatus({code:Xt.ERROR})}finally{e.end()}throw t}})}function selectTelemetryAttributes({telemetry:e,attributes:t}){return!0!==(null==e?void 0:e.isEnabled)?{}:Object.entries(t).reduce((t,[r,a])=>{if(void 0===a)return t;if("object"==typeof a&&"input"in a&&"function"==typeof a.input){if(!1===(null==e?void 0:e.recordInputs))return t;const s=a.input();return void 0===s?t:{...t,[r]:s}}if("object"==typeof a&&"output"in a&&"function"==typeof a.output){if(!1===(null==e?void 0:e.recordOutputs))return t;const s=a.output();return void 0===s?t:{...t,[r]:s}}return{...t,[r]:a}},{})}var xr=class{constructor({data:e,mimeType:t}){const r=e instanceof Uint8Array;this.base64Data=r?void 0:e,this.uint8ArrayData=r?e:void 0,this.mimeType=t}get base64(){return null==this.base64Data&&(this.base64Data=convertUint8ArrayToBase64(this.uint8ArrayData)),this.base64Data}get uint8Array(){return null==this.uint8ArrayData&&(this.uint8ArrayData=convertBase64ToUint8Array(this.base64Data)),this.uint8ArrayData}},br=[{mimeType:"image/gif",bytesPrefix:[71,73,70],base64Prefix:"R0lG"},{mimeType:"image/png",bytesPrefix:[137,80,78,71],base64Prefix:"iVBORw"},{mimeType:"image/jpeg",bytesPrefix:[255,216],base64Prefix:"/9j/"},{mimeType:"image/webp",bytesPrefix:[82,73,70,70],base64Prefix:"UklGRg"},{mimeType:"image/bmp",bytesPrefix:[66,77],base64Prefix:"Qk"},{mimeType:"image/tiff",bytesPrefix:[73,73,42,0],base64Prefix:"SUkqAA"},{mimeType:"image/tiff",bytesPrefix:[77,77,0,42],base64Prefix:"TU0AKg"},{mimeType:"image/avif",bytesPrefix:[0,0,0,32,102,116,121,112,97,118,105,102],base64Prefix:"AAAAIGZ0eXBhdmlm"},{mimeType:"image/heic",bytesPrefix:[0,0,0,32,102,116,121,112,104,101,105,99],base64Prefix:"AAAAIGZ0eXBoZWlj"}];function stripID3TagsIfPresent(e){return"string"==typeof e&&e.startsWith("SUQz")||"string"!=typeof e&&e.length>10&&73===e[0]&&68===e[1]&&51===e[2]?(e=>{const t="string"==typeof e?convertBase64ToUint8Array(e):e,r=(127&t[6])<<21|(127&t[7])<<14|(127&t[8])<<7|127&t[9];return t.slice(r+10)})(e):e}var Tr,wr="AI_NoObjectGeneratedError",Ir=`vercel.ai.error.${wr}`,Sr=Symbol.for(Ir),Ar=class extends m{constructor({message:e="No object generated.",cause:t,text:r,response:a,usage:s,finishReason:n}){super({name:wr,message:e,cause:t}),this[Tr]=!0,this.text=r,this.response=a,this.usage=s,this.finishReason=n}static isInstance(e){return m.hasMarker(e,Ir)}};Tr=Sr;var kr,Cr="AI_DownloadError",Er=`vercel.ai.error.${Cr}`,Pr=Symbol.for(Er),Zr=class extends m{constructor({url:e,statusCode:t,statusText:r,cause:a,message:s=(null==a?`Failed to download ${e}: ${t} ${r}`:`Failed to download ${e}: ${a}`)}){super({name:Cr,message:s,cause:a}),this[kr]=!0,this.url=e,this.statusCode=t,this.statusText=r}static isInstance(e){return m.hasMarker(e,Er)}};async function download({url:e}){var t;const r=e.toString();try{const e=await fetch(r);if(!e.ok)throw new Zr({url:r,statusCode:e.status,statusText:e.statusText});return{data:new Uint8Array(await e.arrayBuffer()),mimeType:null!=(t=e.headers.get("content-type"))?t:void 0}}catch(e){if(Zr.isInstance(e))throw e;throw new Zr({url:r,cause:e})}}kr=Pr;var Nr,Rr="AI_InvalidDataContentError",Or=`vercel.ai.error.${Rr}`,jr=Symbol.for(Or),Dr=class extends m{constructor({content:e,cause:t,message:r=`Invalid data content. Expected a base64 string, Uint8Array, ArrayBuffer, or Buffer, but got ${typeof e}.`}){super({name:Rr,message:r,cause:t}),this[Nr]=!0,this.content=e}static isInstance(e){return m.hasMarker(e,Or)}};Nr=jr;var Mr=Ye([Be(),instanceOfType(Uint8Array),instanceOfType(ArrayBuffer),custom(e=>{var t,r;return null!=(r=null==(t=globalThis.Buffer)?void 0:t.isBuffer(e))&&r},{message:"Must be a Buffer"})]);function convertDataContentToBase64String(e){return"string"==typeof e?e:e instanceof ArrayBuffer?convertUint8ArrayToBase64(new Uint8Array(e)):convertUint8ArrayToBase64(e)}function convertDataContentToUint8Array(e){if(e instanceof Uint8Array)return e;if("string"==typeof e)try{return convertBase64ToUint8Array(e)}catch(t){throw new Dr({message:"Invalid data content. Content string is not a base64-encoded media.",content:e,cause:t})}if(e instanceof ArrayBuffer)return new Uint8Array(e);throw new Dr({content:e})}function convertUint8ArrayToText(e){try{return(new TextDecoder).decode(e)}catch(e){throw new Error("Error decoding Uint8Array to text")}}var $r,Lr="AI_InvalidMessageRoleError",Vr=`vercel.ai.error.${Lr}`,Fr=Symbol.for(Vr),Ur=class extends m{constructor({role:e,message:t=`Invalid message role: '${e}'. Must be one of: "system", "user", "assistant", "tool".`}){super({name:Lr,message:t}),this[$r]=!0,this.role=e}static isInstance(e){return m.hasMarker(e,Vr)}};async function convertToLanguageModelPrompt({prompt:e,modelSupportsImageUrls:t=!0,modelSupportsUrl:r=()=>!1,downloadImplementation:a=download}){const s=await async function(e,t,r,a){const s=e.filter(e=>"user"===e.role).map(e=>e.content).filter(e=>Array.isArray(e)).flat().filter(e=>"image"===e.type||"file"===e.type).filter(e=>!("image"===e.type&&!0===r)).map(e=>"image"===e.type?e.image:e.data).map(e=>"string"==typeof e&&(e.startsWith("http:")||e.startsWith("https:"))?new URL(e):e).filter(e=>e instanceof URL).filter(e=>!a(e)),n=await Promise.all(s.map(async e=>({url:e,data:await t({url:e})})));return Object.fromEntries(n.map(({url:e,data:t})=>[e.toString(),t]))}(e.messages,a,t,r);return[...null!=e.system?[{role:"system",content:e.system}]:[],...e.messages.map(e=>function(e,t){var r,a,s,n,o,i;const u=e.role;switch(u){case"system":return{role:"system",content:e.content,providerMetadata:null!=(r=e.providerOptions)?r:e.experimental_providerMetadata};case"user":return"string"==typeof e.content?{role:"user",content:[{type:"text",text:e.content}],providerMetadata:null!=(a=e.providerOptions)?a:e.experimental_providerMetadata}:{role:"user",content:e.content.map(e=>function(e,t){var r,a,s,n;if("text"===e.type)return{type:"text",text:e.text,providerMetadata:null!=(r=e.providerOptions)?r:e.experimental_providerMetadata};let o,i,u,l=e.mimeType;const c=e.type;switch(c){case"image":o=e.image;break;case"file":o=e.data;break;default:throw new Error(`Unsupported part type: ${c}`)}try{i="string"==typeof o?new URL(o):o}catch(e){i=o}if(i instanceof URL)if("data:"===i.protocol){const{mimeType:e,base64Content:t}=function(e){try{const[t,r]=e.split(",");return{mimeType:t.split(";")[0].split(":")[1],base64Content:r}}catch(e){return{mimeType:void 0,base64Content:void 0}}}(i.toString());if(null==e||null==t)throw new Error(`Invalid data URL format in part ${c}`);l=e,u=convertDataContentToUint8Array(t)}else{const e=t[i.toString()];e?(u=e.data,null!=l||(l=e.mimeType)):u=i}else u=convertDataContentToUint8Array(i);switch(c){case"image":return u instanceof Uint8Array&&(l=null!=(a=function({data:e,signatures:t}){const r=stripID3TagsIfPresent(e);for(const e of t)if("string"==typeof r?r.startsWith(e.base64Prefix):r.length>=e.bytesPrefix.length&&e.bytesPrefix.every((e,t)=>r[t]===e))return e.mimeType}({data:u,signatures:br}))?a:l),{type:"image",image:u,mimeType:l,providerMetadata:null!=(s=e.providerOptions)?s:e.experimental_providerMetadata};case"file":if(null==l)throw new Error("Mime type is missing for file part");return{type:"file",data:u instanceof Uint8Array?convertDataContentToBase64String(u):u,filename:e.filename,mimeType:l,providerMetadata:null!=(n=e.providerOptions)?n:e.experimental_providerMetadata}}}(e,t)).filter(e=>"text"!==e.type||""!==e.text),providerMetadata:null!=(s=e.providerOptions)?s:e.experimental_providerMetadata};case"assistant":return"string"==typeof e.content?{role:"assistant",content:[{type:"text",text:e.content}],providerMetadata:null!=(n=e.providerOptions)?n:e.experimental_providerMetadata}:{role:"assistant",content:e.content.filter(e=>"text"!==e.type||""!==e.text).map(e=>{var t;const r=null!=(t=e.providerOptions)?t:e.experimental_providerMetadata;switch(e.type){case"file":return{type:"file",data:e.data instanceof URL?e.data:convertDataContentToBase64String(e.data),filename:e.filename,mimeType:e.mimeType,providerMetadata:r};case"reasoning":return{type:"reasoning",text:e.text,signature:e.signature,providerMetadata:r};case"redacted-reasoning":return{type:"redacted-reasoning",data:e.data,providerMetadata:r};case"text":return{type:"text",text:e.text,providerMetadata:r};case"tool-call":return{type:"tool-call",toolCallId:e.toolCallId,toolName:e.toolName,args:e.args,providerMetadata:r}}}),providerMetadata:null!=(o=e.providerOptions)?o:e.experimental_providerMetadata};case"tool":return{role:"tool",content:e.content.map(e=>{var t;return{type:"tool-result",toolCallId:e.toolCallId,toolName:e.toolName,result:e.result,content:e.experimental_content,isError:e.isError,providerMetadata:null!=(t=e.providerOptions)?t:e.experimental_providerMetadata}}),providerMetadata:null!=(i=e.providerOptions)?i:e.experimental_providerMetadata};default:throw new Ur({role:u})}}(e,s))]}function attachmentsToParts(e){var t,r,a;const s=[];for(const n of e){let e;try{e=new URL(n.url)}catch(e){throw new Error(`Invalid URL: ${n.url}`)}switch(e.protocol){case"http:":case"https:":if(null==(t=n.contentType)?void 0:t.startsWith("image/"))s.push({type:"image",image:e});else{if(!n.contentType)throw new Error("If the attachment is not an image, it must specify a content type");s.push({type:"file",data:e,mimeType:n.contentType})}break;case"data:":{let e,t,o;try{[e,t]=n.url.split(","),o=e.split(";")[0].split(":")[1]}catch(e){throw new Error(`Error processing data URL: ${n.url}`)}if(null==o||null==t)throw new Error(`Invalid data URL format: ${n.url}`);if(null==(r=n.contentType)?void 0:r.startsWith("image/"))s.push({type:"image",image:convertDataContentToUint8Array(t)});else if(null==(a=n.contentType)?void 0:a.startsWith("text/"))s.push({type:"text",text:convertUint8ArrayToText(convertDataContentToUint8Array(t))});else{if(!n.contentType)throw new Error("If the attachment is not an image or text, it must specify a content type");s.push({type:"file",data:t,mimeType:n.contentType})}break}default:throw new Error(`Unsupported URL protocol: ${e.protocol}`)}}return s}$r=Fr;var Br,zr="AI_MessageConversionError",qr=`vercel.ai.error.${zr}`,Jr=Symbol.for(qr),Kr=class extends m{constructor({originalMessage:e,message:t}){super({name:zr,message:t}),this[Br]=!0,this.originalMessage=e}static isInstance(e){return m.hasMarker(e,qr)}};Br=Jr;var Wr=et(()=>Ye([Je(),Be(),ze(),qe(),Qe(Be(),Wr),He(Wr)])),Hr=Qe(Be(),Qe(Be(),Wr)),Gr=He(Ye([Ge({type:tt("text"),text:Be()}),Ge({type:tt("image"),data:Be(),mimeType:Be().optional()})])),Yr=Ge({type:tt("text"),text:Be(),providerOptions:Hr.optional(),experimental_providerMetadata:Hr.optional()}),Xr=Ge({type:tt("image"),image:Ye([Mr,instanceOfType(URL)]),mimeType:Be().optional(),providerOptions:Hr.optional(),experimental_providerMetadata:Hr.optional()}),Qr=Ge({type:tt("file"),data:Ye([Mr,instanceOfType(URL)]),filename:Be().optional(),mimeType:Be(),providerOptions:Hr.optional(),experimental_providerMetadata:Hr.optional()}),ea=Ge({type:tt("reasoning"),text:Be(),providerOptions:Hr.optional(),experimental_providerMetadata:Hr.optional()}),ta=Ge({type:tt("redacted-reasoning"),data:Be(),providerOptions:Hr.optional(),experimental_providerMetadata:Hr.optional()}),ra=Ge({type:tt("tool-call"),toolCallId:Be(),toolName:Be(),args:We(),providerOptions:Hr.optional(),experimental_providerMetadata:Hr.optional()}),aa=Ge({type:tt("tool-result"),toolCallId:Be(),toolName:Be(),result:We(),content:Gr.optional(),isError:qe().optional(),providerOptions:Hr.optional(),experimental_providerMetadata:Hr.optional()}),sa=Ge({role:tt("system"),content:Be(),providerOptions:Hr.optional(),experimental_providerMetadata:Hr.optional()}),na=Ge({role:tt("user"),content:Ye([Be(),He(Ye([Yr,Xr,Qr]))]),providerOptions:Hr.optional(),experimental_providerMetadata:Hr.optional()}),oa=Ge({role:tt("assistant"),content:Ye([Be(),He(Ye([Yr,Qr,ea,ta,ra]))]),providerOptions:Hr.optional(),experimental_providerMetadata:Hr.optional()}),ia=Ge({role:tt("tool"),content:He(aa),providerOptions:Hr.optional(),experimental_providerMetadata:Hr.optional()}),ua=Ye([sa,na,oa,ia]);function standardizePrompt({prompt:e,tools:t}){if(null==e.prompt&&null==e.messages)throw new N({prompt:e,message:"prompt or messages must be defined"});if(null!=e.prompt&&null!=e.messages)throw new N({prompt:e,message:"prompt and messages cannot be defined at the same time"});if(null!=e.system&&"string"!=typeof e.system)throw new N({prompt:e,message:"system must be a string"});if(null!=e.prompt){if("string"!=typeof e.prompt)throw new N({prompt:e,message:"prompt must be a string"});return{type:"prompt",system:e.system,messages:[{role:"user",content:e.prompt}]}}if(null!=e.messages){const r=function(e){if(!Array.isArray(e))throw new N({prompt:e,message:["messages must be an array of CoreMessage or UIMessage",`Received non-array value: ${JSON.stringify(e)}`].join("\n"),cause:e});if(0===e.length)return"messages";const t=e.map(detectSingleMessageCharacteristics);if(t.some(e=>"has-ui-specific-parts"===e))return"ui-messages";const r=t.findIndex(e=>"has-core-specific-parts"!==e&&"message"!==e);if(-1===r)return"messages";throw new N({prompt:e,message:["messages must be an array of CoreMessage or UIMessage",`Received message of type: "${t[r]}" at index ${r}`,`messages[${r}]: ${JSON.stringify(e[r])}`].join("\n"),cause:e})}(e.messages),a="ui-messages"===r?function(e,t){var r,a;const s=null!=(r=null==t?void 0:t.tools)?r:{},n=[];for(let t=0;t<e.length;t++){const r=e[t],o=t===e.length-1,{role:i,content:u,experimental_attachments:l}=r;switch(i){case"system":n.push({role:"system",content:u});break;case"user":if(null==r.parts)n.push({role:"user",content:l?[{type:"text",text:u},...attachmentsToParts(l)]:u});else{const e=r.parts.filter(e=>"text"===e.type).map(e=>({type:"text",text:e.text}));n.push({role:"user",content:l?[...e,...attachmentsToParts(l)]:e})}break;case"assistant":{if(null!=r.parts){let processBlock2=function(){const a=[];for(const e of o)switch(e.type){case"file":case"text":a.push(e);break;case"reasoning":for(const t of e.details)switch(t.type){case"text":a.push({type:"reasoning",text:t.text,signature:t.signature});break;case"redacted":a.push({type:"redacted-reasoning",data:t.data})}break;case"tool-invocation":a.push({type:"tool-call",toolCallId:e.toolInvocation.toolCallId,toolName:e.toolInvocation.toolName,args:e.toolInvocation.args});break;default:throw new Error(`Unsupported part: ${e}`)}n.push({role:"assistant",content:a});const i=o.filter(e=>"tool-invocation"===e.type).map(e=>e.toolInvocation);i.length>0&&n.push({role:"tool",content:i.map(e=>{if(!("result"in e))throw new Kr({originalMessage:r,message:"ToolInvocation must have a result: "+JSON.stringify(e)});const{toolCallId:t,toolName:a,result:n}=e,o=s[a];return null!=(null==o?void 0:o.experimental_toToolResultContent)?{type:"tool-result",toolCallId:t,toolName:a,result:o.experimental_toToolResultContent(n),experimental_content:o.experimental_toToolResultContent(n)}:{type:"tool-result",toolCallId:t,toolName:a,result:n}})}),o=[],t=!1,e++},e=0,t=!1,o=[];for(const s of r.parts)switch(s.type){case"text":t&&processBlock2(),o.push(s);break;case"file":case"reasoning":o.push(s);break;case"tool-invocation":(null!=(a=s.toolInvocation.step)?a:0)!==e&&processBlock2(),o.push(s),t=!0}processBlock2();break}const e=r.toolInvocations;if(null==e||0===e.length){n.push({role:"assistant",content:u});break}const t=e.reduce((e,t)=>{var r;return Math.max(e,null!=(r=t.step)?r:0)},0);for(let a=0;a<=t;a++){const t=e.filter(e=>{var t;return(null!=(t=e.step)?t:0)===a});0!==t.length&&(n.push({role:"assistant",content:[...o&&u&&0===a?[{type:"text",text:u}]:[],...t.map(({toolCallId:e,toolName:t,args:r})=>({type:"tool-call",toolCallId:e,toolName:t,args:r}))]}),n.push({role:"tool",content:t.map(e=>{if(!("result"in e))throw new Kr({originalMessage:r,message:"ToolInvocation must have a result: "+JSON.stringify(e)});const{toolCallId:t,toolName:a,result:n}=e,o=s[a];return null!=(null==o?void 0:o.experimental_toToolResultContent)?{type:"tool-result",toolCallId:t,toolName:a,result:o.experimental_toToolResultContent(n),experimental_content:o.experimental_toToolResultContent(n)}:{type:"tool-result",toolCallId:t,toolName:a,result:n}})}))}u&&!o&&n.push({role:"assistant",content:u});break}case"data":break;default:throw new Kr({originalMessage:r,message:`Unsupported role: ${i}`})}}return n}(e.messages,{tools:t}):e.messages;if(0===a.length)throw new N({prompt:e,message:"messages must not be empty"});const s=safeValidateTypes({value:a,schema:He(ua)});if(!s.success)throw new N({prompt:e,message:["message must be a CoreMessage or a UI message",`Validation error: ${s.error.message}`].join("\n"),cause:s.error});return{type:"messages",messages:a,system:e.system}}throw new Error("unreachable")}function detectSingleMessageCharacteristics(e){return"object"==typeof e&&null!==e&&("function"===e.role||"data"===e.role||"toolInvocations"in e||"parts"in e||"experimental_attachments"in e)?"has-ui-specific-parts":"object"==typeof e&&null!==e&&"content"in e&&(Array.isArray(e.content)||"experimental_providerMetadata"in e||"providerOptions"in e)?"has-core-specific-parts":"object"==typeof e&&null!==e&&"role"in e&&"content"in e&&"string"==typeof e.content&&["system","user","assistant","tool"].includes(e.role)?"message":"other"}function calculateLanguageModelUsage({promptTokens:e,completionTokens:t}){return{promptTokens:e,completionTokens:t,totalTokens:e+t}}function addLanguageModelUsage(e,t){return{promptTokens:e.promptTokens+t.promptTokens,completionTokens:e.completionTokens+t.completionTokens,totalTokens:e.totalTokens+t.totalTokens}}function stringifyForTelemetry(e){const t=e.map(e=>({...e,content:"string"==typeof e.content?e.content:e.content.map(processPart)}));return JSON.stringify(t)}function processPart(e){return"image"===e.type?{...e,image:e.image instanceof Uint8Array?convertDataContentToBase64String(e.image):e.image}:e}createIdGenerator({prefix:"aiobj",size:24}),createIdGenerator({prefix:"aiobj",size:24});var la,ca="AI_NoOutputSpecifiedError",da=`vercel.ai.error.${ca}`,pa=Symbol.for(da),ma=class extends m{constructor({message:e="No output specified."}={}){super({name:ca,message:e}),this[la]=!0}static isInstance(e){return m.hasMarker(e,da)}};la=pa;var fa,ha="AI_ToolExecutionError",ga=`vercel.ai.error.${ha}`,ya=Symbol.for(ga),va=class extends m{constructor({toolArgs:e,toolName:t,toolCallId:r,cause:a,message:s=`Error executing tool ${t}: ${getErrorMessage$1(a)}`}){super({name:ha,message:s,cause:a}),this[fa]=!0,this.toolArgs=e,this.toolName=t,this.toolCallId=r}static isInstance(e){return m.hasMarker(e,ga)}};function prepareToolsAndToolChoice({tools:e,toolChoice:t,activeTools:r}){if(!(null!=(a=e)&&Object.keys(a).length>0))return{tools:void 0,toolChoice:void 0};var a;return{tools:(null!=r?Object.entries(e).filter(([e])=>r.includes(e)):Object.entries(e)).map(([e,t])=>{const r=t.type;switch(r){case void 0:case"function":return{type:"function",name:e,description:t.description,parameters:asSchema(t.parameters).jsonSchema};case"provider-defined":return{type:"provider-defined",name:e,id:t.id,args:t.args};default:throw new Error(`Unsupported tool type: ${r}`)}}),toolChoice:null==t?{type:"auto"}:"string"==typeof t?{type:t}:{type:"tool",toolName:t.toolName}}}fa=ya;var _a=/^([\s\S]*?)(\s+)(\S*)$/;function removeTextAfterLastWhitespace(e){const t=function(e){const t=e.match(_a);return t?{prefix:t[1],whitespace:t[2],suffix:t[3]}:void 0}(e);return t?t.prefix+t.whitespace:e}var xa,ba="AI_InvalidToolArgumentsError",Ta=`vercel.ai.error.${ba}`,wa=Symbol.for(Ta),Ia=class extends m{constructor({toolArgs:e,toolName:t,cause:r,message:a=`Invalid arguments for tool ${t}: ${getErrorMessage$1(r)}`}){super({name:ba,message:a,cause:r}),this[xa]=!0,this.toolArgs=e,this.toolName=t}static isInstance(e){return m.hasMarker(e,Ta)}};xa=wa;var Sa,Aa="AI_NoSuchToolError",ka=`vercel.ai.error.${Aa}`,Ca=Symbol.for(ka),Ea=class extends m{constructor({toolName:e,availableTools:t,message:r=`Model tried to call unavailable tool '${e}'. ${void 0===t?"No tools are available.":`Available tools: ${t.join(", ")}.`}`}){super({name:Aa,message:r}),this[Sa]=!0,this.toolName=e,this.availableTools=t}static isInstance(e){return m.hasMarker(e,ka)}};Sa=Ca;var Pa,Za="AI_ToolCallRepairError",Na=`vercel.ai.error.${Za}`,Ra=Symbol.for(Na),Oa=class extends m{constructor({cause:e,originalError:t,message:r=`Error repairing tool call: ${getErrorMessage$1(e)}`}){super({name:Za,message:r,cause:e}),this[Pa]=!0,this.originalError=t}static isInstance(e){return m.hasMarker(e,Na)}};async function parseToolCall({toolCall:e,tools:t,repairToolCall:r,system:a,messages:s}){if(null==t)throw new Ea({toolName:e.toolName});try{return await doParseToolCall({toolCall:e,tools:t})}catch(n){if(null==r||!Ea.isInstance(n)&&!Ia.isInstance(n))throw n;let o=null;try{o=await r({toolCall:e,tools:t,parameterSchema:({toolName:e})=>asSchema(t[e].parameters).jsonSchema,system:a,messages:s,error:n})}catch(e){throw new Oa({cause:e,originalError:n})}if(null==o)throw n;return await doParseToolCall({toolCall:o,tools:t})}}async function doParseToolCall({toolCall:e,tools:t}){const r=e.toolName,a=t[r];if(null==a)throw new Ea({toolName:e.toolName,availableTools:Object.keys(t)});const s=asSchema(a.parameters),n=""===e.args.trim()?safeValidateTypes({value:{},schema:s}):safeParseJSON({text:e.args,schema:s});if(!1===n.success)throw new Ia({toolName:r,toolArgs:e.args,cause:n.error});return{type:"tool-call",toolCallId:e.toolCallId,toolName:r,args:n.value}}function asReasoningText(e){const t=e.filter(e=>"text"===e.type).map(e=>e.text).join("");return t.length>0?t:void 0}function toResponseMessages({text:e="",files:t,reasoning:r,tools:a,toolCalls:s,toolResults:n,messageId:o,generateMessageId:i}){const u=[],l=[];return r.length>0&&l.push(...r.map(e=>"text"===e.type?{...e,type:"reasoning"}:{...e,type:"redacted-reasoning"})),t.length>0&&l.push(...t.map(e=>({type:"file",data:e.base64,mimeType:e.mimeType}))),e.length>0&&l.push({type:"text",text:e}),s.length>0&&l.push(...s),l.length>0&&u.push({role:"assistant",content:l,id:o}),n.length>0&&u.push({role:"tool",id:i(),content:n.map(e=>{const t=a[e.toolName];return null!=(null==t?void 0:t.experimental_toToolResultContent)?{type:"tool-result",toolCallId:e.toolCallId,toolName:e.toolName,result:t.experimental_toToolResultContent(e.result),experimental_content:t.experimental_toToolResultContent(e.result)}:{type:"tool-result",toolCallId:e.toolCallId,toolName:e.toolName,result:e.result}})}),u}Pa=Ra;var ja=createIdGenerator({prefix:"aitxt",size:24}),Da=createIdGenerator({prefix:"msg",size:24});async function generateText({model:e,tools:t,toolChoice:r,system:a,prompt:s,messages:n,maxRetries:o,abortSignal:i,headers:u,maxSteps:l=1,experimental_generateMessageId:c=Da,experimental_output:d,experimental_continueSteps:p=!1,experimental_telemetry:m,experimental_providerMetadata:f,providerOptions:h=f,experimental_activeTools:g,experimental_prepareStep:y,experimental_repairToolCall:v,_internal:{generateId:_=ja,currentDate:x=()=>new Date}={},onStepFinish:b,...T}){var w;if(l<1)throw new dr({parameter:"maxSteps",value:l,message:"maxSteps must be at least 1"});const{maxRetries:I,retry:S}=function({maxRetries:e}){if(null!=e){if(!Number.isInteger(e))throw new dr({parameter:"maxRetries",value:e,message:"maxRetries must be an integer"});if(e<0)throw new dr({parameter:"maxRetries",value:e,message:"maxRetries must be >= 0"})}const t=null!=e?e:2;return{maxRetries:t,retry:retryWithExponentialBackoff({maxRetries:t})}}({maxRetries:o}),A=function({model:e,settings:t,telemetry:r,headers:a}){var s;return{"ai.model.provider":e.provider,"ai.model.id":e.modelId,...Object.entries(t).reduce((e,[t,r])=>(e[`ai.settings.${t}`]=r,e),{}),...Object.entries(null!=(s=null==r?void 0:r.metadata)?s:{}).reduce((e,[t,r])=>(e[`ai.telemetry.metadata.${t}`]=r,e),{}),...Object.entries(null!=a?a:{}).reduce((e,[t,r])=>(void 0!==r&&(e[`ai.request.headers.${t}`]=r),e),{})}}({model:e,telemetry:m,headers:u,settings:{...T,maxRetries:I}}),k=standardizePrompt({prompt:{system:null!=(w=null==d?void 0:d.injectIntoSystemPrompt({system:a,model:e}))?w:a,prompt:s,messages:n},tools:t}),C=function({isEnabled:e=!1,tracer:t}={}){return e?t||nr.getTracer("ai"):yr}(m);return recordSpan({name:"ai.generateText",attributes:selectTelemetryAttributes({telemetry:m,attributes:{...assembleOperationName({operationId:"ai.generateText",telemetry:m}),...A,"ai.model.provider":e.provider,"ai.model.id":e.modelId,"ai.prompt":{input:()=>JSON.stringify({system:a,prompt:s,messages:n})},"ai.settings.maxSteps":l}}),tracer:C,fn:async s=>{var n,o,f,w,I,E,P,Z,N,R,O,j,D,M;const $=function({maxTokens:e,temperature:t,topP:r,topK:a,presencePenalty:s,frequencyPenalty:n,stopSequences:o,seed:i}){if(null!=e){if(!Number.isInteger(e))throw new dr({parameter:"maxTokens",value:e,message:"maxTokens must be an integer"});if(e<1)throw new dr({parameter:"maxTokens",value:e,message:"maxTokens must be >= 1"})}if(null!=t&&"number"!=typeof t)throw new dr({parameter:"temperature",value:t,message:"temperature must be a number"});if(null!=r&&"number"!=typeof r)throw new dr({parameter:"topP",value:r,message:"topP must be a number"});if(null!=a&&"number"!=typeof a)throw new dr({parameter:"topK",value:a,message:"topK must be a number"});if(null!=s&&"number"!=typeof s)throw new dr({parameter:"presencePenalty",value:s,message:"presencePenalty must be a number"});if(null!=n&&"number"!=typeof n)throw new dr({parameter:"frequencyPenalty",value:n,message:"frequencyPenalty must be a number"});if(null!=i&&!Number.isInteger(i))throw new dr({parameter:"seed",value:i,message:"seed must be an integer"});return{maxTokens:e,temperature:null!=t?t:0,topP:r,topK:a,presencePenalty:s,frequencyPenalty:n,stopSequences:null!=o&&o.length>0?o:void 0,seed:i}}(T);let L,V=[],F=[],U=[],B=0;const z=[];let q="";const J=[],K=[];let W={completionTokens:0,promptTokens:0,totalTokens:0},H="initial";do{const s=0===B?k.type:"messages",j=[...k.messages,...z],D=await(null==y?void 0:y({model:e,steps:K,maxSteps:l,stepNumber:B})),M=null!=(n=null==D?void 0:D.toolChoice)?n:r,G=null!=(o=null==D?void 0:D.experimental_activeTools)?o:g,Y=null!=(f=null==D?void 0:D.model)?f:e,X=await convertToLanguageModelPrompt({prompt:{type:s,system:k.system,messages:j},modelSupportsImageUrls:Y.supportsImageUrls,modelSupportsUrl:null==(w=Y.supportsUrl)?void 0:w.bind(Y)}),Q={type:"regular",...prepareToolsAndToolChoice({tools:t,toolChoice:M,activeTools:G})};L=await S(()=>recordSpan({name:"ai.generateText.doGenerate",attributes:selectTelemetryAttributes({telemetry:m,attributes:{...assembleOperationName({operationId:"ai.generateText.doGenerate",telemetry:m}),...A,"ai.model.provider":Y.provider,"ai.model.id":Y.modelId,"ai.prompt.format":{input:()=>s},"ai.prompt.messages":{input:()=>stringifyForTelemetry(X)},"ai.prompt.tools":{input:()=>{var e;return null==(e=Q.tools)?void 0:e.map(e=>JSON.stringify(e))}},"ai.prompt.toolChoice":{input:()=>null!=Q.toolChoice?JSON.stringify(Q.toolChoice):void 0},"gen_ai.system":Y.provider,"gen_ai.request.model":Y.modelId,"gen_ai.request.frequency_penalty":T.frequencyPenalty,"gen_ai.request.max_tokens":T.maxTokens,"gen_ai.request.presence_penalty":T.presencePenalty,"gen_ai.request.stop_sequences":T.stopSequences,"gen_ai.request.temperature":T.temperature,"gen_ai.request.top_k":T.topK,"gen_ai.request.top_p":T.topP}}),tracer:C,fn:async t=>{var r,a,n,o,l,c;const p=await Y.doGenerate({mode:Q,...$,inputFormat:s,responseFormat:null==d?void 0:d.responseFormat({model:e}),prompt:X,providerMetadata:h,abortSignal:i,headers:u}),f={id:null!=(a=null==(r=p.response)?void 0:r.id)?a:_(),timestamp:null!=(o=null==(n=p.response)?void 0:n.timestamp)?o:x(),modelId:null!=(c=null==(l=p.response)?void 0:l.modelId)?c:Y.modelId};return t.setAttributes(selectTelemetryAttributes({telemetry:m,attributes:{"ai.response.finishReason":p.finishReason,"ai.response.text":{output:()=>p.text},"ai.response.toolCalls":{output:()=>JSON.stringify(p.toolCalls)},"ai.response.id":f.id,"ai.response.model":f.modelId,"ai.response.timestamp":f.timestamp.toISOString(),"ai.usage.promptTokens":p.usage.promptTokens,"ai.usage.completionTokens":p.usage.completionTokens,"gen_ai.response.finish_reasons":[p.finishReason],"gen_ai.response.id":f.id,"gen_ai.response.model":f.modelId,"gen_ai.usage.input_tokens":p.usage.promptTokens,"gen_ai.usage.output_tokens":p.usage.completionTokens}})),{...p,response:f}}})),V=await Promise.all((null!=(I=L.toolCalls)?I:[]).map(e=>parseToolCall({toolCall:e,tools:t,repairToolCall:v,system:a,messages:j}))),F=null==t?[]:await executeTools({toolCalls:V,tools:t,tracer:C,telemetry:m,messages:j,abortSignal:i});const ee=calculateLanguageModelUsage(L.usage);W=addLanguageModelUsage(W,ee);let te="done";++B<l&&(p&&"length"===L.finishReason&&0===V.length?te="continue":V.length>0&&F.length===V.length&&(te="tool-result"));const re=null!=(E=L.text)?E:"",ae="continue"===H&&q.trimEnd()!==q?re.trimStart():re,se="continue"===te?removeTextAfterLastWhitespace(ae):ae;if(q="continue"===te||"continue"===H?q+se:se,U=asReasoningDetails(L.reasoning),J.push(...null!=(P=L.sources)?P:[]),"continue"===H){const e=z[z.length-1];"string"==typeof e.content?e.content+=se:e.content.push({text:se,type:"text"})}else z.push(...toResponseMessages({text:q,files:asFiles(L.files),reasoning:asReasoningDetails(L.reasoning),tools:null!=t?t:{},toolCalls:V,toolResults:F,messageId:c(),generateMessageId:c}));const ne={stepType:H,text:se,reasoning:asReasoningText(U),reasoningDetails:U,files:asFiles(L.files),sources:null!=(Z=L.sources)?Z:[],toolCalls:V,toolResults:F,finishReason:L.finishReason,usage:ee,warnings:L.warnings,logprobs:L.logprobs,request:null!=(N=L.request)?N:{},response:{...L.response,headers:null==(R=L.rawResponse)?void 0:R.headers,body:null==(O=L.rawResponse)?void 0:O.body,messages:structuredClone(z)},providerMetadata:L.providerMetadata,experimental_providerMetadata:L.providerMetadata,isContinued:"continue"===te};K.push(ne),await(null==b?void 0:b(ne)),H=te}while("done"!==H);return s.setAttributes(selectTelemetryAttributes({telemetry:m,attributes:{"ai.response.finishReason":L.finishReason,"ai.response.text":{output:()=>L.text},"ai.response.toolCalls":{output:()=>JSON.stringify(L.toolCalls)},"ai.usage.promptTokens":L.usage.promptTokens,"ai.usage.completionTokens":L.usage.completionTokens}})),new Ma({text:q,files:asFiles(L.files),reasoning:asReasoningText(U),reasoningDetails:U,sources:J,outputResolver:()=>{if(null==d)throw new ma;return d.parseOutput({text:q},{response:L.response,usage:W,finishReason:L.finishReason})},toolCalls:V,toolResults:F,finishReason:L.finishReason,usage:W,warnings:L.warnings,request:null!=(j=L.request)?j:{},response:{...L.response,headers:null==(D=L.rawResponse)?void 0:D.headers,body:null==(M=L.rawResponse)?void 0:M.body,messages:z},logprobs:L.logprobs,steps:K,providerMetadata:L.providerMetadata})}})}async function executeTools({toolCalls:e,tools:t,tracer:r,telemetry:a,messages:s,abortSignal:n}){return(await Promise.all(e.map(async({toolCallId:e,toolName:o,args:i})=>{const u=t[o];if(null==(null==u?void 0:u.execute))return;const l=await recordSpan({name:"ai.toolCall",attributes:selectTelemetryAttributes({telemetry:a,attributes:{...assembleOperationName({operationId:"ai.toolCall",telemetry:a}),"ai.toolCall.name":o,"ai.toolCall.id":e,"ai.toolCall.args":{output:()=>JSON.stringify(i)}}}),tracer:r,fn:async t=>{try{const r=await u.execute(i,{toolCallId:e,messages:s,abortSignal:n});try{t.setAttributes(selectTelemetryAttributes({telemetry:a,attributes:{"ai.toolCall.result":{output:()=>JSON.stringify(r)}}}))}catch(e){}return r}catch(t){throw new va({toolCallId:e,toolName:o,toolArgs:i,cause:t})}}});return{type:"tool-result",toolCallId:e,toolName:o,args:i,result:l}}))).filter(e=>null!=e)}var Ma=class{constructor(e){this.text=e.text,this.files=e.files,this.reasoning=e.reasoning,this.reasoningDetails=e.reasoningDetails,this.toolCalls=e.toolCalls,this.toolResults=e.toolResults,this.finishReason=e.finishReason,this.usage=e.usage,this.warnings=e.warnings,this.request=e.request,this.response=e.response,this.steps=e.steps,this.experimental_providerMetadata=e.providerMetadata,this.providerMetadata=e.providerMetadata,this.logprobs=e.logprobs,this.outputResolver=e.outputResolver,this.sources=e.sources}get experimental_output(){return this.outputResolver()}};function asReasoningDetails(e){return null==e?[]:"string"==typeof e?[{type:"text",text:e}]:e}function asFiles(e){var t;return null!=(t=null==e?void 0:e.map(e=>new xr(e)))?t:[]}__export({},{object:()=>object,text:()=>text});var text=()=>({type:"text",responseFormat:()=>({type:"text"}),injectIntoSystemPrompt:({system:e})=>e,parsePartial:({text:e})=>({partial:e}),parseOutput:({text:e})=>e}),object=({schema:e})=>{const t=asSchema(e);return{type:"object",responseFormat:({model:e})=>({type:"json",schema:e.supportsStructuredOutputs?t.jsonSchema:void 0}),injectIntoSystemPrompt:({system:e,model:r})=>r.supportsStructuredOutputs?e:function({prompt:e,schema:t,schemaPrefix:r=(null!=t?"JSON schema:":void 0),schemaSuffix:a=(null!=t?"You MUST answer with a JSON object that matches the JSON schema above.":"You MUST answer with JSON.")}){return[null!=e&&e.length>0?e:void 0,null!=e&&e.length>0?"":void 0,r,null!=t?JSON.stringify(t):void 0,a].filter(e=>null!=e).join("\n")}({prompt:e,schema:t.jsonSchema}),parsePartial({text:e}){const t=function(e){if(void 0===e)return{value:void 0,state:"undefined-input"};let t=safeParseJSON({text:e});return t.success?{value:t.value,state:"successful-parse"}:(t=safeParseJSON({text:fixJson(e)}),t.success?{value:t.value,state:"repaired-parse"}:{value:void 0,state:"failed-parse"})}(e);switch(t.state){case"failed-parse":case"undefined-input":return;case"repaired-parse":case"successful-parse":return{partial:t.value};default:{const e=t.state;throw new Error(`Unsupported parse state: ${e}`)}}},parseOutput({text:e},r){const a=safeParseJSON({text:e});if(!a.success)throw new Ar({message:"No object generated: could not parse the response.",cause:a.error,text:e,response:r.response,usage:r.usage,finishReason:r.finishReason});const s=safeValidateTypes({value:a.value,schema:t});if(!s.success)throw new Ar({message:"No object generated: response did not match schema.",cause:s.error,text:e,response:r.response,usage:r.usage,finishReason:r.finishReason});return s.value}}};function mergeStreams(e,t){const r=e.getReader(),a=t.getReader();let s,n,o=!1,i=!1;async function readStream1(e){try{null==s&&(s=r.read());const t=await s;s=void 0,t.done?e.close():e.enqueue(t.value)}catch(t){e.error(t)}}async function readStream2(e){try{null==n&&(n=a.read());const t=await n;n=void 0,t.done?e.close():e.enqueue(t.value)}catch(t){e.error(t)}}return new ReadableStream({async pull(e){try{if(o)return void await readStream2(e);if(i)return void await readStream1(e);null==s&&(s=r.read()),null==n&&(n=a.read());const{result:t,reader:u}=await Promise.race([s.then(e=>({result:e,reader:r})),n.then(e=>({result:e,reader:a}))]);t.done||e.enqueue(t.value),u===r?(s=void 0,t.done&&(await readStream2(e),o=!0)):(n=void 0,t.done&&(i=!0,await readStream1(e)))}catch(t){e.error(t)}},cancel(){r.cancel(),a.cancel()}})}createIdGenerator({prefix:"aitxt",size:24}),createIdGenerator({prefix:"msg",size:24});var $a=Ge({name:Be(),version:Be()}).passthrough(),La=Ge({_meta:at(Ge({}).passthrough())}).passthrough(),Va=La,Fa=Ge({method:Be(),params:at(La)}),Ua=Ge({experimental:at(Ge({}).passthrough()),logging:at(Ge({}).passthrough()),prompts:at(Ge({listChanged:at(qe())}).passthrough()),resources:at(Ge({subscribe:at(qe()),listChanged:at(qe())}).passthrough()),tools:at(Ge({listChanged:at(qe())}).passthrough())}).passthrough();Va.extend({protocolVersion:Be(),capabilities:Ua,serverInfo:$a,instructions:at(Be())});var Ba=Va.extend({nextCursor:at(Be())}),za=Ge({name:Be(),description:at(Be()),inputSchema:Ge({type:tt("object"),properties:at(Ge({}).passthrough())}).passthrough()}).passthrough();Ba.extend({tools:He(za)});var qa=Ge({type:tt("text"),text:Be()}).passthrough(),Ja=Ge({type:tt("image"),data:Be().base64(),mimeType:Be()}).passthrough(),Ka=Ge({uri:Be(),mimeType:at(Be())}).passthrough(),Wa=Ka.extend({text:Be()}),Ha=Ka.extend({blob:Be().base64()}),Ga=Ge({type:tt("resource"),resource:Ye([Wa,Ha])}).passthrough();Va.extend({content:He(Ye([qa,Ja,Ga])),isError:qe().default(!1).optional()}).or(Va.extend({toolResult:We()}));var Ya="2.0",Xa=Ge({jsonrpc:tt(Ya),id:Ye([Be(),ze().int()])}).merge(Fa).strict(),Qa=Ge({jsonrpc:tt(Ya),id:Ye([Be(),ze().int()]),result:Va}).strict(),es=Ge({jsonrpc:tt(Ya),id:Ye([Be(),ze().int()]),error:Ge({code:ze().int(),message:Be(),data:at(We())})}).strict(),ts=Ge({jsonrpc:tt(Ya)}).merge(Ge({method:Be(),params:at(La)})).strict();Ye([Xa,ts,Qa,es]);function createCallbacksTransformer(e={}){const t=new TextEncoder;let r="";return new TransformStream({async start(){e.onStart&&await e.onStart()},async transform(a,s){s.enqueue(t.encode(a)),r+=a,e.onToken&&await e.onToken(a),e.onText&&"string"==typeof a&&await e.onText(a)},async flush(){e.onCompletion&&await e.onCompletion(r),e.onFinal&&await e.onFinal(r)}})}function toDataStreamInternal(e,t){return e.pipeThrough(new TransformStream({transform:async(e,t)=>{var r;"string"!=typeof e?"event"in e?"on_chat_model_stream"===e.event&&forwardAIMessageChunk(null==(r=e.data)?void 0:r.chunk,t):forwardAIMessageChunk(e,t):t.enqueue(e)}})).pipeThrough(createCallbacksTransformer(t)).pipeThrough(new TextDecoderStream).pipeThrough(new TransformStream({transform:async(e,t)=>{t.enqueue(formatDataStreamPart("text",e))}}))}function toDataStream(e,t){return toDataStreamInternal(e,t).pipeThrough(new TextEncoderStream)}function toDataStreamResponse(e,t){var r;const a=toDataStreamInternal(e,null==t?void 0:t.callbacks).pipeThrough(new TextEncoderStream),s=null==t?void 0:t.data,n=null==t?void 0:t.init,o=s?mergeStreams(s.stream,a):a;return new Response(o,{status:null!=(r=null==n?void 0:n.status)?r:200,statusText:null==n?void 0:n.statusText,headers:prepareResponseHeaders(null==n?void 0:n.headers,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"})})}function mergeIntoDataStream(e,t){t.dataStream.merge(toDataStreamInternal(e,t.callbacks))}function forwardAIMessageChunk(e,t){if("string"==typeof e.content)t.enqueue(e.content);else{const r=e.content;for(const e of r)"text"===e.type&&t.enqueue(e.text)}}__export({},{mergeIntoDataStream:()=>mergeIntoDataStream,toDataStream:()=>toDataStream,toDataStreamResponse:()=>toDataStreamResponse});function toDataStreamInternal2(e,t){const r=function(){let e=!0;return t=>(e&&(t=t.trimStart())&&(e=!1),t)}();return(a=e[Symbol.asyncIterator](),new ReadableStream({async pull(e){try{const{value:t,done:r}=await a.next();r?e.close():e.enqueue(t)}catch(t){e.error(t)}},cancel(){}})).pipeThrough(new TransformStream({async transform(e,t){t.enqueue(r(e.delta))}})).pipeThrough(createCallbacksTransformer(t)).pipeThrough(new TextDecoderStream).pipeThrough(new TransformStream({transform:async(e,t)=>{t.enqueue(formatDataStreamPart("text",e))}}));var a}function toDataStream2(e,t){return toDataStreamInternal2(e,t).pipeThrough(new TextEncoderStream)}function toDataStreamResponse2(e,t={}){var r;const{init:a,data:s,callbacks:n}=t,o=toDataStreamInternal2(e,n).pipeThrough(new TextEncoderStream),i=s?mergeStreams(s.stream,o):o;return new Response(i,{status:null!=(r=null==a?void 0:a.status)?r:200,statusText:null==a?void 0:a.statusText,headers:prepareResponseHeaders(null==a?void 0:a.headers,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"})})}function mergeIntoDataStream2(e,t){t.dataStream.merge(toDataStreamInternal2(e,t.callbacks))}function composeContextScopes(...e){const t=e[0];if(1===e.length)return t;const createScope=()=>{const r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){const a=r.reduce((t,{useScope:r,scopeName:a})=>({...t,...r(e)[`__scope${a}`]}),{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return createScope.scopeName=t.scopeName,createScope}__export({},{mergeIntoDataStream:()=>mergeIntoDataStream2,toDataStream:()=>toDataStream2,toDataStreamResponse:()=>toDataStreamResponse2});var rs=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{const r=i(`Primitive.${t}`),a=s.forwardRef((e,a)=>{const{asChild:s,...o}=e,i=s?r:t;return n.jsx(i,{...o,ref:a})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{}),as="Progress",ss=100,[ns,os]=function(e,t=[]){let r=[];const createScope=()=>{const t=r.map(e=>s.createContext(e));return function(r){const a=r?.[e]||t;return s.useMemo(()=>({[`__scope${e}`]:{...r,[e]:a}}),[r,a])}};return createScope.scopeName=e,[function(t,a){const o=s.createContext(a),i=r.length;r=[...r,a];const Provider=t=>{const{scope:r,children:a,...u}=t,l=r?.[e]?.[i]||o,c=s.useMemo(()=>u,Object.values(u));return n.jsx(l.Provider,{value:c,children:a})};return Provider.displayName=t+"Provider",[Provider,function(r,n){const u=n?.[e]?.[i]||o,l=s.useContext(u);if(l)return l;if(void 0!==a)return a;throw new Error(`\`${r}\` must be used within \`${t}\``)}]},composeContextScopes(createScope,...t)]}(as),[is,us]=ns(as),ls=s.forwardRef((e,t)=>{const{__scopeProgress:r,value:a=null,max:s,getValueLabel:o=defaultGetValueLabel,...i}=e;!s&&0!==s||isValidMaxNumber(s)||console.error(`Invalid prop \`max\` of value \`${`${s}`}\` supplied to \`${"Progress"}\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`);const u=isValidMaxNumber(s)?s:ss;null===a||isValidValueNumber(a,u)||console.error(function(e,t){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${t}\`. The \`value\` prop must be:\n  - a positive number\n  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)\n  - \`null\` or \`undefined\` if the progress is indeterminate.\n\nDefaulting to \`null\`.`}(`${a}`,"Progress"));const l=isValidValueNumber(a,u)?a:null,c=isNumber(l)?o(l,u):void 0;return n.jsx(is,{scope:r,value:l,max:u,children:n.jsx(rs.div,{"aria-valuemax":u,"aria-valuemin":0,"aria-valuenow":isNumber(l)?l:void 0,"aria-valuetext":c,role:"progressbar","data-state":getProgressState(l,u),"data-value":l??void 0,"data-max":u,...i,ref:t})})});ls.displayName=as;var cs="ProgressIndicator",ds=s.forwardRef((e,t)=>{const{__scopeProgress:r,...a}=e,s=us(cs,r);return n.jsx(rs.div,{"data-state":getProgressState(s.value,s.max),"data-value":s.value??void 0,"data-max":s.max,...a,ref:t})});function defaultGetValueLabel(e,t){return`${Math.round(e/t*100)}%`}function getProgressState(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function isNumber(e){return"number"==typeof e}function isValidMaxNumber(e){return isNumber(e)&&!isNaN(e)&&e>0}function isValidValueNumber(e,t){return isNumber(e)&&!isNaN(e)&&e<=t&&e>=0}ds.displayName=cs;var ps=ls,ms=ds;const fs=s.forwardRef(({className:e,value:t,...r},a)=>n.jsx(ps,{ref:a,className:o("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:n.jsx(ms,{className:"h-full w-full flex-1 bg-gradient-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));fs.displayName=ps.displayName;export{postFormDataToApi as A,createBinaryResponseHandler as B,M as I,fs as P,X as T,ue as U,fe as a,N as b,combineHeaders as c,createJsonResponseHandler as d,createEventSourceResponseHandler as e,convertUint8ArrayToBase64 as f,generateText as g,createJsonErrorResponseHandler as h,isParsableJson as i,tt as j,We as k,loadApiKey as l,He as m,Ke as n,Ge as o,postJsonToApi as p,ze as q,rt as r,Be as s,Qe as t,Ye as u,Xe as v,withoutTrailingSlash as w,qe as x,parseProviderOptions as y,convertBase64ToUint8Array as z};
//# sourceMappingURL=progress-WcEdq6Og.mjs.map
