import{w as e,U as t,p as o,c as n,a as s,I as r,i as a,l,b as i,d as u,e as c,f as p,h as g,o as d,s as m,j as h,u as f,k as y,m as b,n as _,q as v,r as w,t as k}from"./progress-WcEdq6Og.mjs";var x=Object.defineProperty,T=Object.defineProperties,C=Object.getOwnPropertyDescriptors,R=Object.getOwnPropertySymbols,O=Object.prototype.hasOwnProperty,q=Object.prototype.propertyIsEnumerable,__defNormalProp=(e,t,o)=>t in e?x(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,__spreadValues=(e,t)=>{for(var o in t||(t={}))O.call(t,o)&&__defNormalProp(e,o,t[o]);if(R)for(var o of R(t))q.call(t,o)&&__defNormalProp(e,o,t[o]);return e},__spreadProps=(e,t)=>T(e,C(t)),__objRest=(e,t)=>{var o={};for(var n in e)O.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&R)for(var n of R(e))t.indexOf(n)<0&&q.call(e,n)&&(o[n]=e[n]);return o},$=f([d({type:h("reasoning.summary"),summary:m()}),d({type:h("reasoning.encrypted"),data:m()}),d({type:h("reasoning.text"),text:m().nullish(),signature:m().nullish()})]),j=b(f([$,y().transform(()=>null)])).transform(e=>e.filter(e=>!!e));function getCacheControl(e){var t,o,n;const s=null==e?void 0:e.anthropic,r=null==e?void 0:e.openrouter;return null!=(n=null!=(o=null!=(t=null==r?void 0:r.cacheControl)?t:null==r?void 0:r.cache_control)?o:null==s?void 0:s.cacheControl)?n:null==s?void 0:s.cache_control}function convertToOpenRouterChatMessages(e){var t,o,n;const s=[];for(const{role:r,content:a,providerMetadata:l}of e)switch(r){case"system":s.push({role:"system",content:a,cache_control:getCacheControl(l)});break;case"user":{if(1===a.length&&"text"===(null==(t=a[0])?void 0:t.type)){s.push({role:"user",content:a[0].text,cache_control:null!=(o=getCacheControl(l))?o:getCacheControl(a[0].providerMetadata)});break}const e=getCacheControl(l),n=a.map(t=>{var o,n,s,r;const a=null!=(o=getCacheControl(t.providerMetadata))?o:e;switch(t.type){case"text":return{type:"text",text:t.text,cache_control:a};case"image":return{type:"image_url",image_url:{url:t.image instanceof URL?t.image.toString():`data:${null!=(n=t.mimeType)?n:"image/jpeg"};base64,${p(t.image)}`},cache_control:a};case"file":return{type:"file",file:{filename:String(null==(r=null==(s=t.providerMetadata)?void 0:s.openrouter)?void 0:r.filename),file_data:t.data instanceof Uint8Array?`data:${t.mimeType};base64,${p(t.data)}`:`data:${t.mimeType};base64,${t.data}`},cache_control:a};default:throw new Error(`Unsupported content part type: ${t}`)}});s.push({role:"user",content:n});break}case"assistant":{let e="",t="";const o=[],n=[];for(const s of a)switch(s.type){case"text":e+=s.text;break;case"tool-call":n.push({id:s.toolCallId,type:"function",function:{name:s.toolName,arguments:JSON.stringify(s.args)}});break;case"reasoning":t+=s.text,o.push({type:"reasoning.text",text:s.text,signature:s.signature});break;case"redacted-reasoning":o.push({type:"reasoning.encrypted",data:s.data});break;case"file":break;default:throw new Error(`Unsupported part: ${s}`)}s.push({role:"assistant",content:e,tool_calls:n.length>0?n:void 0,reasoning:t||void 0,reasoning_details:o.length>0?o:void 0,cache_control:getCacheControl(l)});break}case"tool":for(const e of a)s.push({role:"tool",tool_call_id:e.toolCallId,content:JSON.stringify(e.result),cache_control:null!=(n=getCacheControl(l))?n:getCacheControl(e.providerMetadata)});break;default:throw new Error(`Unsupported role: ${r}`)}return s}function mapOpenRouterChatLogProbsOutput(e){var t,o;return null!=(o=null==(t=null==e?void 0:e.content)?void 0:t.map(({token:e,logprob:t,top_logprobs:o})=>({token:e,logprob:t,topLogprobs:o?o.map(({token:e,logprob:t})=>({token:e,logprob:t})):[]})))?o:void 0}function mapOpenRouterFinishReason(e){switch(e){case"stop":return"stop";case"length":return"length";case"content_filter":return"content-filter";case"function_call":case"tool_calls":return"tool-calls";default:return"unknown"}}var I=d({error:d({code:f([m(),v()]).nullable(),message:m(),type:m().nullable(),param:_().nullable()})}),N=g({errorSchema:I,errorToMessage:e=>e.error.message});var S=class{constructor(e,t,o){this.specificationVersion="v1",this.defaultObjectGenerationMode="tool",this.modelId=e,this.settings=t,this.config=o}get provider(){return this.config.provider}getArgs({mode:e,prompt:o,maxTokens:n,temperature:s,topP:r,frequencyPenalty:a,presencePenalty:l,seed:i,stopSequences:u,responseFormat:c,topK:p,providerMetadata:g}){var d;const m=e.type,h=null!=(d=null==g?void 0:g.openrouter)?d:{},f=__spreadValues(__spreadValues(__spreadValues({model:this.modelId,models:this.settings.models,logit_bias:this.settings.logitBias,logprobs:!0===this.settings.logprobs||"number"==typeof this.settings.logprobs||void 0,top_logprobs:"number"==typeof this.settings.logprobs?this.settings.logprobs:"boolean"==typeof this.settings.logprobs&&this.settings.logprobs?0:void 0,user:this.settings.user,parallel_tool_calls:this.settings.parallelToolCalls,max_tokens:n,temperature:s,top_p:r,frequency_penalty:a,presence_penalty:l,seed:i,stop:u,response_format:c,top_k:p,messages:convertToOpenRouterChatMessages(o),include_reasoning:this.settings.includeReasoning,reasoning:this.settings.reasoning,usage:this.settings.usage},this.config.extraBody),this.settings.extraBody),h);switch(m){case"regular":return __spreadValues(__spreadValues({},f),function(e){var t;const o=(null==(t=e.tools)?void 0:t.length)?e.tools:void 0;if(null==o)return{tools:void 0,tool_choice:void 0};const n=o.map(e=>function(e){return"parameters"in e}(e)?{type:"function",function:{name:e.name,description:e.description,parameters:e.parameters}}:{type:"function",function:{name:e.name}}),s=e.toolChoice;if(null==s)return{tools:n,tool_choice:void 0};const r=s.type;switch(r){case"auto":case"none":case"required":return{tools:n,tool_choice:r};case"tool":return{tools:n,tool_choice:{type:"function",function:{name:s.toolName}}};default:throw new Error(`Unsupported tool choice type: ${r}`)}}(e));case"object-json":return __spreadProps(__spreadValues({},f),{response_format:{type:"json_object"}});case"object-tool":return __spreadProps(__spreadValues({},f),{tool_choice:{type:"function",function:{name:e.tool.name}},tools:[{type:"function",function:{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters}}]});default:throw new t({functionality:`${m} mode`})}}async doGenerate(e){var t,r,a,l,i,c,p,g,d;const m=this.getArgs(e),{responseHeaders:h,value:f}=await o({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:n(this.config.headers(),e.headers),body:m,failedResponseHandler:N,successfulResponseHandler:u(E),abortSignal:e.abortSignal,fetch:this.config.fetch}),y=m,{messages:b}=y,_=__objRest(y,["messages"]),v=f.choices[0];if(!v)throw new Error("No choice in response");const w=f.usage?{promptTokens:null!=(t=f.usage.prompt_tokens)?t:0,completionTokens:null!=(r=f.usage.completion_tokens)?r:0}:{promptTokens:0,completionTokens:0},k={};f.usage&&(null==(a=this.settings.usage)?void 0:a.include)&&(k.openrouter={usage:{promptTokens:f.usage.prompt_tokens,promptTokensDetails:f.usage.prompt_tokens_details?{cachedTokens:null!=(l=f.usage.prompt_tokens_details.cached_tokens)?l:0}:void 0,completionTokens:f.usage.completion_tokens,completionTokensDetails:f.usage.completion_tokens_details?{reasoningTokens:null!=(i=f.usage.completion_tokens_details.reasoning_tokens)?i:0}:void 0,cost:f.usage.cost,totalTokens:null!=(c=f.usage.total_tokens)?c:0}});const x=Object.keys(k).length>0,T=null!=(p=v.message.reasoning_details)?p:[],C=T.length>0?T.map(e=>{var t;switch(e.type){case"reasoning.text":if(e.text)return{type:"text",text:e.text,signature:null!=(t=e.signature)?t:void 0};break;case"reasoning.summary":if(e.summary)return{type:"text",text:e.summary};break;case"reasoning.encrypted":if(e.data)return{type:"redacted",data:e.data}}return null}).filter(e=>null!==e):v.message.reasoning?[{type:"text",text:v.message.reasoning}]:[];return __spreadValues({response:{id:f.id,modelId:f.model},text:null!=(g=v.message.content)?g:void 0,reasoning:C,toolCalls:null==(d=v.message.tool_calls)?void 0:d.map(e=>{var t;return{toolCallType:"function",toolCallId:null!=(t=e.id)?t:s(),toolName:e.function.name,args:e.function.arguments}}),finishReason:mapOpenRouterFinishReason(v.finish_reason),usage:w,rawCall:{rawPrompt:b,rawSettings:_},rawResponse:{headers:h},warnings:[],logprobs:mapOpenRouterChatLogProbsOutput(v.logprobs)},x?{providerMetadata:k}:{})}async doStream(e){var t,l;const i=this.getArgs(e),{responseHeaders:u,value:p}=await o({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:n(this.config.headers(),e.headers),body:__spreadProps(__spreadValues({},i),{stream:!0,stream_options:"strict"===this.config.compatibility?__spreadValues({include_usage:!0},(null==(t=this.settings.usage)?void 0:t.include)?{include_usage:!0}:{}):void 0}),failedResponseHandler:N,successfulResponseHandler:c(M),abortSignal:e.abortSignal,fetch:this.config.fetch}),g=i,{messages:d}=g,m=__objRest(g,["messages"]),h=[];let f,y="other",b={promptTokens:Number.NaN,completionTokens:Number.NaN};const _={},v=!!(null==(l=this.settings.usage)?void 0:l.include);return{stream:p.pipeThrough(new TransformStream({transform(e,t){var o,n,l,i,u,c,p,g,d,m,v,w,k,x;if(!e.success)return y="error",void t.enqueue({type:"error",error:e.error});const T=e.value;if("error"in T)return y="error",void t.enqueue({type:"error",error:T.error});T.id&&t.enqueue({type:"response-metadata",id:T.id}),T.model&&t.enqueue({type:"response-metadata",modelId:T.model}),null!=T.usage&&(b={promptTokens:T.usage.prompt_tokens,completionTokens:T.usage.completion_tokens},_.promptTokens=T.usage.prompt_tokens,T.usage.prompt_tokens_details&&(_.promptTokensDetails={cachedTokens:null!=(o=T.usage.prompt_tokens_details.cached_tokens)?o:0}),_.completionTokens=T.usage.completion_tokens,T.usage.completion_tokens_details&&(_.completionTokensDetails={reasoningTokens:null!=(n=T.usage.completion_tokens_details.reasoning_tokens)?n:0}),_.cost=T.usage.cost,_.totalTokens=T.usage.total_tokens);const C=T.choices[0];if(null!=(null==C?void 0:C.finish_reason)&&(y=mapOpenRouterFinishReason(C.finish_reason)),null==(null==C?void 0:C.delta))return;const R=C.delta;if(null!=R.content&&t.enqueue({type:"text-delta",textDelta:R.content}),null!=R.reasoning&&t.enqueue({type:"reasoning",textDelta:R.reasoning}),R.reasoning_details&&R.reasoning_details.length>0)for(const e of R.reasoning_details)switch(e.type){case"reasoning.text":e.text&&t.enqueue({type:"reasoning",textDelta:e.text}),e.signature&&t.enqueue({type:"reasoning-signature",signature:e.signature});break;case"reasoning.encrypted":e.data&&t.enqueue({type:"redacted-reasoning",data:e.data});break;case"reasoning.summary":e.summary&&t.enqueue({type:"reasoning",textDelta:e.summary})}const O=mapOpenRouterChatLogProbsOutput(null==C?void 0:C.logprobs);if((null==O?void 0:O.length)&&(void 0===f&&(f=[]),f.push(...O)),null!=R.tool_calls)for(const e of R.tool_calls){const o=e.index;if(null==h[o]){if("function"!==e.type)throw new r({data:e,message:"Expected 'function' type."});if(null==e.id)throw new r({data:e,message:"Expected 'id' to be a string."});if(null==(null==(l=e.function)?void 0:l.name))throw new r({data:e,message:"Expected 'function.name' to be a string."});h[o]={id:e.id,type:"function",function:{name:e.function.name,arguments:null!=(i=e.function.arguments)?i:""},sent:!1};const n=h[o];if(null==n)throw new Error("Tool call is missing");null!=(null==(u=n.function)?void 0:u.name)&&null!=(null==(c=n.function)?void 0:c.arguments)&&a(n.function.arguments)&&(t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:n.id,toolName:n.function.name,argsTextDelta:n.function.arguments}),t.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(p=n.id)?p:s(),toolName:n.function.name,args:n.function.arguments}),n.sent=!0);continue}const n=h[o];if(null==n)throw new Error("Tool call is missing");null!=(null==(g=e.function)?void 0:g.arguments)&&(n.function.arguments+=null!=(m=null==(d=e.function)?void 0:d.arguments)?m:""),t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:n.id,toolName:n.function.name,argsTextDelta:null!=(v=e.function.arguments)?v:""}),null!=(null==(w=n.function)?void 0:w.name)&&null!=(null==(k=n.function)?void 0:k.arguments)&&a(n.function.arguments)&&(t.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(x=n.id)?x:s(),toolName:n.function.name,args:n.function.arguments}),n.sent=!0)}},flush(e){var t;if("tool-calls"===y)for(const o of h)o.sent||(e.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(t=o.id)?t:s(),toolName:o.function.name,args:a(o.function.arguments)?o.function.arguments:"{}"}),o.sent=!0);const o={};!v||void 0===_.totalTokens&&void 0===_.cost&&void 0===_.promptTokensDetails&&void 0===_.completionTokensDetails||(o.openrouter={usage:_});const n=Object.keys(o).length>0&&v;e.enqueue(__spreadValues({type:"finish",finishReason:y,logprobs:f,usage:b},n?{providerMetadata:o}:{}))}})),rawCall:{rawPrompt:d,rawSettings:m},rawResponse:{headers:u},warnings:[]}}},P=d({id:m().optional(),model:m().optional(),usage:d({prompt_tokens:v(),prompt_tokens_details:d({cached_tokens:v()}).nullish(),completion_tokens:v(),completion_tokens_details:d({reasoning_tokens:v()}).nullish(),total_tokens:v(),cost:v().optional()}).nullish()}),E=P.extend({choices:b(d({message:d({role:h("assistant"),content:m().nullable().optional(),reasoning:m().nullable().optional(),reasoning_details:j.nullish(),tool_calls:b(d({id:m().optional().nullable(),type:h("function"),function:d({name:m(),arguments:m()})})).optional()}),index:v(),logprobs:d({content:b(d({token:m(),logprob:v(),top_logprobs:b(d({token:m(),logprob:v()}))})).nullable()}).nullable().optional(),finish_reason:m().optional().nullable()}))}),M=f([P.extend({choices:b(d({delta:d({role:w(["assistant"]).optional(),content:m().nullish(),reasoning:m().nullish().optional(),reasoning_details:j.nullish(),tool_calls:b(d({index:v(),id:m().nullish(),type:h("function").optional(),function:d({name:m().nullish(),arguments:m().nullish()})})).nullish()}).nullish(),logprobs:d({content:b(d({token:m(),logprob:v(),top_logprobs:b(d({token:m(),logprob:v()}))})).nullable()}).nullish(),finish_reason:m().nullable().optional(),index:v()}))}),I]);function mapOpenRouterCompletionLogProbs(e){return null==e?void 0:e.tokens.map((t,o)=>{var n,s;return{token:t,logprob:null!=(n=e.token_logprobs[o])?n:0,topLogprobs:e.top_logprobs?Object.entries(null!=(s=e.top_logprobs[o])?s:{}).map(([e,t])=>({token:e,logprob:t})):[]}})}var D=class{constructor(e,t,o){this.specificationVersion="v1",this.defaultObjectGenerationMode=void 0,this.modelId=e,this.settings=t,this.config=o}get provider(){return this.config.provider}getArgs({mode:e,inputFormat:o,prompt:n,maxTokens:s,temperature:r,topP:a,frequencyPenalty:l,presencePenalty:u,seed:c,responseFormat:p,topK:g,stopSequences:d,providerMetadata:m}){var h,f;const y=e.type,b=null!=(h=null==m?void 0:m.openrouter)?h:{},{prompt:_}=function({prompt:e,inputFormat:o,user:n="user",assistant:s="assistant"}){if("prompt"===o&&1===e.length&&e[0]&&"user"===e[0].role&&1===e[0].content.length&&e[0].content[0]&&"text"===e[0].content[0].type)return{prompt:e[0].content[0].text};let r="";e[0]&&"system"===e[0].role&&(r+=`${e[0].content}\n\n`,e=e.slice(1));for(const{role:o,content:a}of e)switch(o){case"system":throw new i({message:"Unexpected system message in prompt: ${content}",prompt:e});case"user":r+=`${n}:\n${a.map(e=>{switch(e.type){case"text":return e.text;case"image":throw new t({functionality:"images"});case"file":throw new t({functionality:"file attachments"});default:throw new Error(`Unsupported content type: ${e}`)}}).join("")}\n\n`;break;case"assistant":r+=`${s}:\n${a.map(e=>{switch(e.type){case"text":return e.text;case"tool-call":throw new t({functionality:"tool-call messages"});case"reasoning":throw new t({functionality:"reasoning messages"});case"redacted-reasoning":throw new t({functionality:"redacted reasoning messages"});case"file":throw new t({functionality:"file attachments"});default:throw new Error(`Unsupported content type: ${e}`)}}).join("")}\n\n`;break;case"tool":throw new t({functionality:"tool messages"});default:throw new Error(`Unsupported role: ${o}`)}return r+=`${s}:\n`,{prompt:r}}({prompt:n,inputFormat:o}),v=__spreadValues(__spreadValues(__spreadValues({model:this.modelId,models:this.settings.models,logit_bias:this.settings.logitBias,logprobs:"number"==typeof this.settings.logprobs?this.settings.logprobs:"boolean"==typeof this.settings.logprobs&&this.settings.logprobs?0:void 0,suffix:this.settings.suffix,user:this.settings.user,max_tokens:s,temperature:r,top_p:a,frequency_penalty:l,presence_penalty:u,seed:c,stop:d,response_format:p,top_k:g,prompt:_,include_reasoning:this.settings.includeReasoning,reasoning:this.settings.reasoning},this.config.extraBody),this.settings.extraBody),b);switch(y){case"regular":if(null==(f=e.tools)?void 0:f.length)throw new t({functionality:"tools"});if(e.toolChoice)throw new t({functionality:"toolChoice"});return v;case"object-json":throw new t({functionality:"object-json mode"});case"object-tool":throw new t({functionality:"object-tool mode"});default:throw new t({functionality:`${y} mode`})}}async doGenerate(e){var t,s,r,a,l;const i=this.getArgs(e),{responseHeaders:c,value:p}=await o({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:n(this.config.headers(),e.headers),body:i,failedResponseHandler:N,successfulResponseHandler:u(U),abortSignal:e.abortSignal,fetch:this.config.fetch}),g=i,{prompt:d}=g,m=__objRest(g,["prompt"]);if("error"in p)throw new Error(`${p.error.message}`);const h=p.choices[0];if(!h)throw new Error("No choice in OpenRouter completion response");return{response:{id:p.id,modelId:p.model},text:null!=(t=h.text)?t:"",reasoning:h.reasoning||void 0,usage:{promptTokens:null!=(r=null==(s=p.usage)?void 0:s.prompt_tokens)?r:0,completionTokens:null!=(l=null==(a=p.usage)?void 0:a.completion_tokens)?l:0},finishReason:mapOpenRouterFinishReason(h.finish_reason),logprobs:mapOpenRouterCompletionLogProbs(h.logprobs),rawCall:{rawPrompt:d,rawSettings:m},rawResponse:{headers:c},warnings:[]}}async doStream(e){const t=this.getArgs(e),{responseHeaders:s,value:r}=await o({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:n(this.config.headers(),e.headers),body:__spreadProps(__spreadValues({},this.getArgs(e)),{stream:!0,stream_options:"strict"===this.config.compatibility?{include_usage:!0}:void 0}),failedResponseHandler:N,successfulResponseHandler:c(U),abortSignal:e.abortSignal,fetch:this.config.fetch}),a=t,{prompt:l}=a,i=__objRest(a,["prompt"]);let u,p="other",g={promptTokens:Number.NaN,completionTokens:Number.NaN};return{stream:r.pipeThrough(new TransformStream({transform(e,t){if(!e.success)return p="error",void t.enqueue({type:"error",error:e.error});const o=e.value;if("error"in o)return p="error",void t.enqueue({type:"error",error:o.error});null!=o.usage&&(g={promptTokens:o.usage.prompt_tokens,completionTokens:o.usage.completion_tokens});const n=o.choices[0];null!=(null==n?void 0:n.finish_reason)&&(p=mapOpenRouterFinishReason(n.finish_reason)),null!=(null==n?void 0:n.text)&&t.enqueue({type:"text-delta",textDelta:n.text});const s=mapOpenRouterCompletionLogProbs(null==n?void 0:n.logprobs);(null==s?void 0:s.length)&&(void 0===u&&(u=[]),u.push(...s))},flush(e){e.enqueue({type:"finish",finishReason:p,logprobs:u,usage:g})}})),rawCall:{rawPrompt:l,rawSettings:i},rawResponse:{headers:s},warnings:[]}}},U=f([d({id:m().optional(),model:m().optional(),choices:b(d({text:m(),reasoning:m().nullish().optional(),reasoning_details:j.nullish(),finish_reason:m().nullish(),index:v(),logprobs:d({tokens:b(m()),token_logprobs:b(v()),top_logprobs:b(k(m(),v())).nullable()}).nullable().optional()})),usage:d({prompt_tokens:v(),completion_tokens:v()}).optional().nullable()}),I]);var H=function(t={}){var o,n,s;const r=null!=(n=e(null!=(o=t.baseURL)?o:t.baseUrl))?n:"https://openrouter.ai/api/v1",a=null!=(s=t.compatibility)?s:"compatible",getHeaders=()=>__spreadValues({Authorization:`Bearer ${l({apiKey:t.apiKey,environmentVariableName:"OPENROUTER_API_KEY",description:"OpenRouter"})}`},t.headers),createChatModel=(e,o={})=>new S(e,o,{provider:"openrouter.chat",url:({path:e})=>`${r}${e}`,headers:getHeaders,compatibility:a,fetch:t.fetch,extraBody:t.extraBody}),createCompletionModel=(e,o={})=>new D(e,o,{provider:"openrouter.completion",url:({path:e})=>`${r}${e}`,headers:getHeaders,compatibility:a,fetch:t.fetch,extraBody:t.extraBody}),createLanguageModel=(e,t)=>{if(new.target)throw new Error("The OpenRouter model function cannot be called with the new keyword.");return"openai/gpt-3.5-turbo-instruct"===e?createCompletionModel(e,t):createChatModel(e,t)},provider=(e,t)=>createLanguageModel(e,t);return provider.languageModel=createLanguageModel,provider.chat=createChatModel,provider.completion=createCompletionModel,provider}({compatibility:"strict"});export{H as o};
//# sourceMappingURL=index2.mjs.map
