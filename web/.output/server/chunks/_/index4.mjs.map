{"version": 3, "file": "index4.mjs", "sources": ["../../../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "../../../../node_modules/@radix-ui/react-primitive/node_modules/@radix-ui/react-slot/dist/index.mjs", "../../../../node_modules/@radix-ui/react-primitive/dist/index.mjs"], "sourcesContent": null, "names": ["setRef", "ref", "value", "current", "composeRefs", "refs", "node", "hasCleanup", "cleanups", "map", "cleanup", "i", "length", "useComposedRefs", "React.useCallback", "Slot", "React.forwardRef", "props", "forwardedRef", "children", "slotProps", "childrenA<PERSON>y", "React.Children", "toArray", "slottable", "find", "isSlottable", "newElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "child", "count", "only", "React.isValidElement", "jsx", "SlotClone", "React.cloneElement", "displayName", "childrenRef", "element", "getter", "Object", "getOwnPropertyDescriptor", "get", "<PERSON><PERSON><PERSON><PERSON>", "isReactWarning", "getElementRef", "mergeProps", "Slottable", "Fragment", "type", "childProps", "overrideProps", "propName", "slotPropValue", "childPropV<PERSON>ue", "test", "args", "filter", "Boolean", "join", "Primitive", "reduce", "primitive", "Node", "<PERSON><PERSON><PERSON><PERSON>", "primitiveProps", "Comp", "dispatchDiscreteCustomEvent", "target", "event", "ReactDOM.flushSync", "dispatchEvent"], "mappings": "", "x_google_ignoreList": [0, 1, 2]}