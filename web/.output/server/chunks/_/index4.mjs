import{r as e,j as n,l as r}from"./routeTree.gen-BFK54byf.mjs";function setRef(e,n){if("function"==typeof e)return e(n);null!=e&&(e.current=n)}function composeRefs(...e){return n=>{let r=!1;const t=e.map(e=>{const t=setRef(e,n);return r||"function"!=typeof t||(r=!0),t});if(r)return()=>{for(let n=0;n<t.length;n++){const r=t[n];"function"==typeof r?r():setRef(e[n],null)}}}}function useComposedRefs(...n){return e.useCallback(composeRefs(...n),n)}var t=e.forwardRef((r,t)=>{const{children:s,...i}=r,l=e.Children.toArray(s),c=l.find(isSlottable);if(c){const r=c.props.children,s=l.map(n=>n===c?e.Children.count(r)>1?e.Children.only(null):e.isValidElement(r)?r.props.children:null:n);return n.jsx(o,{...i,ref:t,children:e.isValidElement(r)?e.cloneElement(r,void 0,s):null})}return n.jsx(o,{...i,ref:t,children:s})});t.displayName="Slot";var o=e.forwardRef((n,r)=>{const{children:t,...o}=n;if(e.isValidElement(t)){const n=function(e){let n=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=n&&"isReactWarning"in n&&n.isReactWarning;if(r)return e.ref;if(n=Object.getOwnPropertyDescriptor(e,"ref")?.get,r=n&&"isReactWarning"in n&&n.isReactWarning,r)return e.props.ref;return e.props.ref||e.ref}(t);return e.cloneElement(t,{...mergeProps(o,t.props),ref:r?composeRefs(r,n):n})}return e.Children.count(t)>1?e.Children.only(null):null});o.displayName="SlotClone";var Slottable=({children:e})=>n.jsx(n.Fragment,{children:e});function isSlottable(n){return e.isValidElement(n)&&n.type===Slottable}function mergeProps(e,n){const r={...n};for(const t in n){const o=e[t],s=n[t];/^on[A-Z]/.test(t)?o&&s?r[t]=(...e)=>{s(...e),o(...e)}:o&&(r[t]=o):"style"===t?r[t]={...o,...s}:"className"===t&&(r[t]=[o,s].filter(Boolean).join(" "))}return{...e,...r}}var s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((r,o)=>{const s=e.forwardRef((e,r)=>{const{asChild:s,...i}=e,l=s?t:o;return n.jsx(l,{...i,ref:r})});return s.displayName=`Primitive.${o}`,{...r,[o]:s}},{});function dispatchDiscreteCustomEvent(e,n){e&&r.flushSync(()=>e.dispatchEvent(n))}export{s as P,composeRefs as c,dispatchDiscreteCustomEvent as d,useComposedRefs as u};
//# sourceMappingURL=index4.mjs.map
