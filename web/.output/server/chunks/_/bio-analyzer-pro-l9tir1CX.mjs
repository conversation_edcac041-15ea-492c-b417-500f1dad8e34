import{m as e}from"../nitro/nitro.mjs";import{C as s,a as i,b as n,d as a,c as t}from"./card-zxSsGLJg.mjs";import{B as r,A as o,C as l,b as c,T as m,S as d,U as p,L as h,h as x}from"./lucide-react.mjs";import{B as u}from"./badge-Cwk-ZwYz.mjs";import{P as g,g as y}from"./progress-WcEdq6Og.mjs";import{T as v,a as f,b as j,c as b}from"./tabs-D9-6MReb.mjs";import{T as N}from"./textarea-Ca6uXUkL.mjs";import{A,B as w,c as S,g as k,a as _,b as E}from"./advanced-scoring-Cd6PgUsS.mjs";import{r as T,j as I,L as P}from"./routeTree.gen-BFK54byf.mjs";import{o as O}from"./index2.mjs";import"node:process";import"cloudflare:workers";import"node:events";import"node:buffer";import"node:timers";import"node:stream";import"node:stream/web";import"node:async_hooks";var R=Object.defineProperty,__publicField=(e,s,i)=>((e,s,i)=>s in e?R(e,s,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[s]=i)(e,"symbol"!=typeof s?s+"":s,i),C=class{generateExpertPrompt(e,s,i){const n=E(e);return{systemPrompt:this.buildSystemPrompt(n),userPrompt:this.buildUserPrompt(n,s,i),chainOfThoughtStructure:this.buildChainOfThoughtStructure(n),examples:this.getExamples(e)}}buildSystemPrompt(e){return`You are ${e.name}, ${e.credentials}.\n\nBACKGROUND & EXPERTISE:\n${e.background}\n\nYour core expertise includes:\n${e.expertise.map(e=>`• ${e}`).join("\n")}\n\nANALYSIS APPROACH:\n${e.analysisApproach}\n\nCRITICAL ANALYSIS REQUIREMENTS:\nYou are a BRUTALLY HONEST expert who provides OBJECTIVE, UNFORGIVING analysis. Your reputation depends on accuracy, not kindness.\n\nSCORING PHILOSOPHY:\n- 90-100: EXCEPTIONAL - Top 5% of all dating profiles, near-perfect execution\n- 80-89: EXCELLENT - Top 15% of profiles, very strong with minor flaws\n- 70-79: GOOD - Above average, solid but with notable improvement areas\n- 60-69: AVERAGE - Typical profile, significant room for improvement\n- 50-59: BELOW AVERAGE - Multiple issues, needs major work\n- 40-49: POOR - Serious problems, likely to perform badly\n- 30-39: VERY POOR - Major red flags, would repel most matches\n- 20-29: TERRIBLE - Fundamentally broken, needs complete rewrite\n- 10-19: AWFUL - Actively harmful to dating prospects\n- 0-9: CATASTROPHIC - Should not be used under any circumstances\n\nANALYSIS REQUIREMENTS:\n1. BE RUTHLESSLY CRITICAL - Most bios are mediocre and deserve low scores\n2. IDENTIFY EVERY FLAW - No matter how small, call out problems\n3. DEMAND EXCELLENCE - Only exceptional bios deserve high scores\n4. PROVIDE HARSH TRUTH - Your job is accuracy, not making people feel good\n5. USE REAL-WORLD STANDARDS - Compare to actual successful dating profiles\n\nRESPONSE REQUIREMENTS:\n- Be uncompromisingly honest about weaknesses\n- Support every criticism with specific evidence\n- Score based on real dating market performance\n- Prioritize brutal honesty over politeness\n- Remember: Average bios get average results (poor performance)\n\nYour analysis will be synthesized with other expert perspectives for comprehensive feedback.`}buildUserPrompt(e,s,i){return`${i?this.buildContextInfo(i):""}\n\nPlease analyze this dating profile bio using your expertise as a ${e.type} expert.\n\nBIO TO ANALYZE:\n"${s}"\n\nSYSTEMATIC ANALYSIS FRAMEWORK:\n\n1. INITIAL ASSESSMENT PHASE:\n   - What is my immediate impression of this bio?\n   - What key elements stand out from my expert perspective?\n   - What does this bio communicate about the person?\n\n2. DETAILED EXPERT EVALUATION:\n   - How does this bio perform in my area of expertise?\n   - What specific strengths and weaknesses do I identify?\n   - How does this compare to successful bios I've analyzed?\n\n3. PSYCHOLOGICAL/LINGUISTIC/MARKET ANALYSIS:\n   - What personality traits or characteristics are evident?\n   - How effective is the communication style?\n   - What market positioning does this create?\n\n4. CRITICAL SCORING METHODOLOGY:\n   - What score (0-100) does this bio ACTUALLY deserve? (Be harsh - most bios are 40-60)\n   - What SPECIFIC FLAWS and weaknesses lower this score?\n   - Why would this bio FAIL in the competitive dating market?\n   - What evidence PROVES this score is accurate?\n\n5. CONFIDENCE EVALUATION:\n   - How confident am I in this analysis (0-100)?\n   - What factors support or limit my confidence?\n   - What additional context would enhance my assessment?\n\n6. STRATEGIC RECOMMENDATIONS:\n   - What are my top 3-5 specific improvement recommendations?\n   - Which changes would have the highest impact?\n   - What is the implementation difficulty for each suggestion?\n\nRESPONSE FORMAT:\nProvide your analysis in this exact JSON structure:\n\n{\n  "initial_assessment": {\n    "immediate_impression": "Your first expert impression",\n    "key_elements": ["element1", "element2", "element3"],\n    "overall_communication": "What this bio communicates about the person"\n  },\n  "expert_evaluation": {\n    "strengths": ["strength1", "strength2"],\n    "weaknesses": ["weakness1", "weakness2"],\n    "expert_specific_analysis": "Analysis from your specific expertise",\n    "comparative_assessment": "How this compares to successful bios"\n  },\n  "specialized_analysis": {\n    "personality_indicators": ["trait1", "trait2"],\n    "communication_effectiveness": "Assessment of communication style",\n    "market_positioning": "How this positions the person in the dating market",\n    "target_audience_appeal": "Who this would appeal to"\n  },\n  "scoring_methodology": {\n    "score": 45,\n    "harsh_reality_check": "Why this bio would struggle in the real dating market",\n    "critical_flaws": ["major_flaw1", "major_flaw2", "major_flaw3"],\n    "evidence_for_low_score": ["specific_evidence1", "specific_evidence2"],\n    "market_performance_prediction": "How this would actually perform (be realistic)",\n    "score_components": {\n      "writing_quality": 40,\n      "personality_appeal": 50,\n      "market_competitiveness": 45\n    }\n  },\n  "confidence_evaluation": {\n    "confidence": 88,\n    "supporting_factors": ["factor1", "factor2"],\n    "limiting_factors": ["limitation1", "limitation2"],\n    "context_needed": "What additional context would help"\n  },\n  "strategic_recommendations": [\n    {\n      "recommendation": "Specific actionable improvement",\n      "impact_level": "high|medium|low",\n      "implementation_difficulty": "easy|moderate|challenging",\n      "reasoning": "Why this recommendation is important",\n      "priority": 1,\n      "expected_outcome": "What improvement this would create"\n    }\n  ],\n  "key_insights": ["insight1", "insight2", "insight3"]\n}\n\nApply your specialized expertise while maintaining a constructive and helpful tone.`}buildChainOfThoughtStructure(e){return`\nCHAIN-OF-THOUGHT REASONING STRUCTURE FOR BIO ANALYSIS:\n\nStep 1: Expert First Impression\n- "As a ${e.type} expert, my immediate reaction to this bio is..."\n- "The key elements that catch my attention are..."\n- "From my professional perspective, this bio suggests..."\n\nStep 2: Specialized Analysis Application\n- "Applying my expertise in ${e.expertise[0]}, I observe..."\n- "The linguistic/psychological/market factors indicate..."\n- "Based on my experience with ${e.specializations[0]}, this demonstrates..."\n\nStep 3: Comparative Market Assessment\n- "Compared to successful bios I've analyzed..."\n- "This bio would rank in the [percentile] because..."\n- "The competitive positioning appears to be..."\n\nStep 4: Brutal Honest Scoring\n- "I'm scoring this [X]/100 because it has these CRITICAL FLAWS..."\n- "This bio would FAIL in the dating market because..."\n- "The harsh reality is that this bio..."\n- "Compared to successful profiles, this lacks..."\n\nStep 5: Critical Improvement Requirements\n- "This bio MUST change these fundamental issues..."\n- "Without these improvements, this profile will continue to fail..."\n- "The brutal truth is that this person needs to..."\n`}buildContextInfo(e){let s="ANALYSIS CONTEXT:\n";return e.targetDemographic&&(s+=`• Target Demographic: ${e.targetDemographic}\n`),e.platform&&(s+=`• Platform: ${e.platform}\n`),e.relationshipGoals&&(s+=`• Relationship Goals: ${e.relationshipGoals}\n`),e.tone&&(s+=`• Desired Tone: ${e.tone}\n`),s+"\n"}getExamples(e){return{psychology:["Example Bio: \"Love hiking, good food, and deep conversations. Looking for someone who can make me laugh and isn't afraid to be vulnerable.\"\n        Analysis: \"While this attempts emotional depth, it's actually a collection of dating clichés. 'Deep conversations' and 'vulnerability' are overused buzzwords that signal virtue signaling rather than genuine emotional intelligence. The bio lacks specificity and personality. Score: 52/100 because it's generic and forgettable despite good intentions.\"","Example Bio: \"Just looking for fun, nothing serious. Hit me up if you're down for whatever.\"\n        Analysis: \"This bio screams emotional unavailability and poor communication skills. The casual dismissal of serious connections and vague 'whatever' language suggests someone who can't articulate their needs or commit to anything meaningful. Score: 18/100 because this actively repels quality matches and attracts only hookup culture.\""],dating_coach:["Example Bio: \"Entrepreneur who loves weekend adventures and trying new restaurants. Seeking a partner in crime for life's next chapter. Dog lover and terrible cook - you've been warned!\"\n        Analysis: \"This bio uses tired dating clichés like 'partner in crime' and 'life's next chapter' that appear on thousands of profiles. While the self-deprecating humor about cooking is decent, the overall message is generic and doesn't differentiate from the competition. Score: 61/100 - mediocre execution of overused formulas.\"","Example Bio: \"I'm perfect, looking for my soulmate. Must be 6'+ and love fitness. No drama or games.\"\n        Analysis: \"This bio is a dating disaster. The narcissistic 'perfect' claim combined with shallow physical requirements and negative 'no drama' language creates an entitled, demanding tone that repels quality matches. Score: 12/100 because this actively sabotages dating success.\""],data_science:['Example Bio: "Software engineer who rock climbs on weekends. Love craft beer and board games. Looking for someone to explore the city with."\n        Analysis: "While this hits common interest points, it\'s a stereotypical tech bro profile that blends into the crowd. The activities are predictable for the demographic and lack personality. Data shows this type performs average at best due to oversaturation. Score: 48/100 based on competitive analysis."','Example Bio: "Just ask me anything you want to know."\n        Analysis: "This lazy bio shows 67% lower engagement rates and 78% fewer quality conversations. Zero conversation starters, no personality indicators, and puts all effort on the match. Algorithmic death sentence. Score: 8/100 based on platform performance data."'],fashion:["Example Bio: \"Vintage vinyl collector with a weakness for Sunday farmers markets. Equally comfortable in hiking boots or dress shoes. Seeking someone who appreciates both adventure and elegance.\"\n        Analysis: \"This tries too hard to appear sophisticated and comes across as pretentious. The 'hiking boots or dress shoes' line is forced and the 'adventure and elegance' phrase is clichéd. Reads like someone trying to impress rather than being authentic. Score: 54/100 because it feels manufactured rather than genuine.\"",'Example Bio: "Love partying and getting wasted every weekend. Looking for someone who can keep up with my lifestyle."\n        Analysis: "This bio screams poor life choices and potential substance abuse issues. The focus on \'getting wasted\' suggests immaturity and lack of self-control. Completely unappealing to quality matches. Score: 9/100 because this actively destroys dating prospects."']}[e]||[]}},L=class{constructor(){if(__publicField(this,"apiKey"),__publicField(this,"promptGenerator"),__publicField(this,"scoringEngine"),this.apiKey="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",!this.apiKey)throw console.error("🔑 VITE_OPENROUTER_API_KEY not found in environment variables"),new Error("OpenRouter API key is required. Please set VITE_OPENROUTER_API_KEY in your .env file");console.log("🔑 OpenRouter API key loaded successfully for advanced bio analysis"),console.log(`🔑 API Key preview: ${this.apiKey.substring(0,10)}...`),"undefined"!=typeof globalThis&&(globalThis.process=globalThis.process||{},e.env=e.env||{},e.env.OPENROUTER_API_KEY=this.apiKey),this.promptGenerator=new C,this.scoringEngine=new A}async analyzeBio(e,s={},i){const n=Date.now(),a=`bio_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;console.log("🚀 Starting advanced bio analysis with o3"),console.log(`📝 Bio length: ${e.length} characters`);try{null==i||i({phase:"pre_analysis",progress:10,message:"Performing linguistic analysis..."});const t=await this.performLinguisticAnalysis(e);null==i||i({phase:"expert_analysis",progress:20,message:"Conducting multi-expert analysis..."});const r=await this.conductExpertAnalyses(e,s,i);null==i||i({phase:"scoring",progress:70,message:"Generating psychological profile..."});const o=await this.generatePsychologicalProfile(e,r);null==i||i({phase:"insights",progress:80,message:"Performing market analysis..."});const l=await this.performMarketAnalysis(e,r);null==i||i({phase:"comparison",progress:90,message:"Calculating advanced scores..."});const c=this.scoringEngine.calculateDetailedScoring(r,w,S);null==i||i({phase:"finalization",progress:95,message:"Generating improvements and insights..."});const m=await this.generateActionableInsights(r,c),{quickWins:d,longTermImprovements:p}=this.categorizeRecommendations(m),h=await this.generateImprovedVersions(e,r),x=this.scoringEngine.generateComparativeAnalysis(c.overallScore,c,r),u=this.calculateConfidenceMetrics(r,t),g=Date.now()-n;console.log("📊 FINAL BIO ANALYSIS RESULTS:",{bio:e.substring(0,100)+"...",overallScore:c.overallScore,percentileRank:c.percentileRank,expertScores:r.map(e=>({expert:e.expertType,score:e.score})),processingTime:`${g}ms`});const y={id:a,originalBio:e,timestamp:Date.now(),overallScore:c.overallScore,percentileRank:c.percentileRank,improvementPotential:c.improvementPotential,marketCompetitiveness:c.marketCompetitiveness,expertAnalyses:r,detailedScoring:c,linguisticAnalysis:t,psychologicalProfile:o,marketAnalysis:l,actionableInsights:m,quickWins:d,longTermImprovements:p,comparativeAnalysis:x,confidenceMetrics:u,improvedVersions:h,processingTime:g,modelUsed:"openai/o3",analysisVersion:"1.0.0"};return console.log(`✅ Advanced bio analysis completed in ${g}ms - Overall Score: ${y.overallScore}/100`),y}catch(e){throw console.error("❌ Advanced bio analysis failed:",e),new Error(`Advanced analysis failed: ${e instanceof Error?e.message:"Unknown error"}`)}}async performLinguisticAnalysis(e){console.log("📊 Performing linguistic analysis...");const{text:s}=await y({model:O("openai/o3"),messages:[{role:"system",content:"You are a computational linguistics expert. Analyze the following bio for linguistic characteristics and provide a JSON response with readability, sentiment, tone, vocabulary level, and grammar scores."},{role:"user",content:`Analyze this bio linguistically:\n\n"${e}"\n\nProvide analysis in this JSON format:\n{\n  "readabilityScore": 85,\n  "sentimentScore": 72,\n  "toneAnalysis": ["confident", "friendly", "authentic"],\n  "vocabularyLevel": "intermediate",\n  "grammarScore": 90\n}`}],maxTokens:500,temperature:.1});return this.parseSimpleJSON(s,{targetAudienceAlignment:75,competitivePositioning:"above average",conversionPotential:70,engagementProbability:75,nicheAppeal:["professionals","general audience"]})}async conductExpertAnalyses(e,s,i){const n=k(),a=[];for(let t=0;t<n.length;t++){const r=n[t],o=20+t/n.length*45;null==i||i({phase:"expert_analysis",currentExpert:_(r),progress:o,message:`Analyzing with ${r} expert...`});try{const i=await this.conductSingleExpertAnalysis(r,e,s);a.push(i),console.log(`✅ ${r} analysis completed - Score: ${i.score}/100`),t<n.length-1&&await new Promise(e=>setTimeout(e,800))}catch(e){console.error(`❌ ${r} analysis failed:`,e)}}return a}async conductSingleExpertAnalysis(e,s,i){var n,a,t,r,o,l,c;const m=this.promptGenerator.generateExpertPrompt(e,s,i);console.log(`🤖 Calling OpenRouter with o3 for ${e} expert bio analysis`);const{text:d}=await y({model:O("openai/o3"),messages:[{role:"system",content:m.systemPrompt},{role:"user",content:m.userPrompt}],maxTokens:1500,temperature:.1}),p=this.parseAdvancedAnalysisResult(d),h=(null==(n=p.scoring_methodology)?void 0:n.score)||75;return console.log(`🎯 Advanced Bio Analysis - ${e} Expert:`,{bio:s.substring(0,100)+"...",score:h,confidence:(null==(a=p.confidence_evaluation)?void 0:a.confidence)||85,criticalFlaws:(null==(t=p.scoring_methodology)?void 0:t.critical_flaws)||[],harshReality:(null==(r=p.scoring_methodology)?void 0:r.harsh_reality_check)||"No harsh reality check provided"}),{expertType:e,expertName:_(e),credentials:_(e),analysis:(null==(o=p.expert_evaluation)?void 0:o.expert_specific_analysis)||"Analysis completed",score:h,confidence:(null==(l=p.confidence_evaluation)?void 0:l.confidence)||85,keyObservations:(null==(c=p.initial_assessment)?void 0:c.key_elements)||[],recommendations:this.parseRecommendations(p.strategic_recommendations||[])}}parseAdvancedAnalysisResult(e){var s,i,n,a,t,r,o,l,c,m,d,p;try{console.log("🔍 Parsing advanced bio analysis response...");const h=e.match(/\{[\s\S]*\}/);if(!h)return console.warn("⚠️ No JSON found in response, using fallback"),this.getFallbackBioAnalysisData();const x=JSON.parse(h[0]);return console.log("✅ Successfully parsed advanced bio analysis JSON"),{initial_assessment:{key_elements:Array.isArray(null==(s=x.initial_assessment)?void 0:s.key_elements)?x.initial_assessment.key_elements:["Bio analysis completed"],immediate_impression:(null==(i=x.initial_assessment)?void 0:i.immediate_impression)||"Professional analysis completed",overall_communication:(null==(n=x.initial_assessment)?void 0:n.overall_communication)||"Communication style assessed"},expert_evaluation:{expert_specific_analysis:(null==(a=x.expert_evaluation)?void 0:a.expert_specific_analysis)||"Expert analysis completed successfully",strengths:Array.isArray(null==(t=x.expert_evaluation)?void 0:t.strengths)?x.expert_evaluation.strengths:["Analysis completed"],weaknesses:Array.isArray(null==(r=x.expert_evaluation)?void 0:r.weaknesses)?x.expert_evaluation.weaknesses:["Areas for improvement identified"]},scoring_methodology:{score:Math.max(0,Math.min(100,parseInt(null==(o=x.scoring_methodology)?void 0:o.score)||75)),evidence:Array.isArray(null==(l=x.scoring_methodology)?void 0:l.evidence)?x.scoring_methodology.evidence:["Evidence-based scoring completed"],key_factors:Array.isArray(null==(c=x.scoring_methodology)?void 0:c.key_factors)?x.scoring_methodology.key_factors:["Multiple factors considered"]},confidence_evaluation:{confidence:Math.max(0,Math.min(100,parseInt(null==(m=x.confidence_evaluation)?void 0:m.confidence)||85)),supporting_factors:Array.isArray(null==(d=x.confidence_evaluation)?void 0:d.supporting_factors)?x.confidence_evaluation.supporting_factors:["Professional analysis methodology"],limiting_factors:Array.isArray(null==(p=x.confidence_evaluation)?void 0:p.limiting_factors)?x.confidence_evaluation.limiting_factors:[]},strategic_recommendations:Array.isArray(x.strategic_recommendations)?x.strategic_recommendations:[]}}catch(s){return console.error("❌ Failed to parse advanced bio analysis result:",s),console.log("📝 Raw response:",e),this.getFallbackBioAnalysisData()}}getFallbackBioAnalysisData(){return{initial_assessment:{key_elements:["Bio analysis completed","Professional assessment performed"],immediate_impression:"Analysis completed successfully",overall_communication:"Communication style assessed"},expert_evaluation:{expert_specific_analysis:"Professional analysis completed. Please try again for more detailed insights.",strengths:["Bio submitted successfully","Analysis framework applied"],weaknesses:["Detailed analysis temporarily unavailable"]},scoring_methodology:{score:75,evidence:["Analysis methodology applied","Professional standards used"],key_factors:["Multiple assessment criteria","Expert evaluation framework"]},confidence_evaluation:{confidence:85,supporting_factors:["Professional analysis system","Established methodology"],limiting_factors:["Response parsing issue - please retry"]},strategic_recommendations:[{recommendation:"Try the analysis again for more detailed insights",impact_level:"medium",implementation_difficulty:"easy",reasoning:"System temporarily unable to provide detailed analysis"}]}}parseSimpleJSON(e,s){try{console.log("🔍 Parsing simple JSON response...");const i=e.match(/\{[\s\S]*\}/);if(!i)return console.warn("⚠️ No JSON found in response, using fallback"),s;const n=JSON.parse(i[0]);return console.log("✅ Successfully parsed simple JSON"),n}catch(i){return console.error("❌ Failed to parse simple JSON:",i),console.log("📝 Raw response:",e),s}}parseRecommendations(e){return e.map(e=>({recommendation:e.recommendation||"No recommendation provided",priority:"high"===e.impact_level?"high":"medium"===e.impact_level?"medium":"low",impactScore:this.mapImpactToScore(e.impact_level),effortRequired:"easy"===e.implementation_difficulty?"low":"moderate"===e.implementation_difficulty?"medium":"high",category:"general",reasoning:e.reasoning||"Expert recommendation"}))}mapImpactToScore(e){switch(e){case"high":return 85;case"medium":return 65;case"low":return 40;default:return 50}}async generatePsychologicalProfile(e,s){console.log("🧠 Generating psychological profile...");const{text:i}=await y({model:O("openai/o3"),messages:[{role:"system",content:"You are a clinical psychologist specializing in personality assessment. Analyze the bio for Big 5 personality traits, attachment style, confidence level, and emotional intelligence indicators."},{role:"user",content:`Analyze this bio for psychological indicators:\n\n"${e}"\n\nProvide analysis in this JSON format:\n{\n  "personalityTraits": {\n    "openness": 75,\n    "conscientiousness": 68,\n    "extraversion": 82,\n    "agreeableness": 71,\n    "neuroticism": 25\n  },\n  "attachmentStyle": "secure",\n  "confidenceLevel": 78,\n  "emotionalIntelligence": 85\n}`}],maxTokens:600,temperature:.1});return this.parseSimpleJSON(i,{readabilityScore:75,sentimentScore:70,toneAnalysis:["professional","friendly"],vocabularyLevel:"intermediate",grammarScore:80})}async performMarketAnalysis(e,s){console.log("📈 Performing market analysis...");const{text:i}=await y({model:O("openai/o3"),messages:[{role:"system",content:"You are a dating market analyst. Assess target audience alignment, competitive positioning, conversion potential, and niche appeal."},{role:"user",content:`Analyze this bio for market positioning:\n\n"${e}"\n\nProvide analysis in this JSON format:\n{\n  "targetAudienceAlignment": 82,\n  "competitivePositioning": "above average",\n  "conversionPotential": 75,\n  "engagementProbability": 78,\n  "nicheAppeal": ["professionals", "active lifestyle"]\n}`}],maxTokens:500,temperature:.1});return this.parseSimpleJSON(i,{personalityTraits:{openness:70,conscientiousness:75,extraversion:65,agreeableness:70,neuroticism:30},attachmentStyle:"secure",confidenceLevel:75,emotionalIntelligence:80})}async generateActionableInsights(e,s){const i=[];for(const s of e)i.push(...s.recommendations);return i.sort((e,s)=>s.impactScore-e.impactScore).slice(0,8)}categorizeRecommendations(e){return{quickWins:e.filter(e=>"low"===e.effortRequired&&e.impactScore>=60),longTermImprovements:e.filter(e=>"high"===e.effortRequired&&e.impactScore>=70)}}async generateImprovedVersions(e,s){console.log("✨ Generating improved bio versions...");const i=s.flatMap(e=>e.recommendations).slice(0,5).map(e=>e.recommendation).join("; "),{text:n}=await y({model:O("openai/o3"),messages:[{role:"system",content:"You are an expert dating profile writer. Create three improved versions of the bio in different tones: witty, sincere, and adventurous. Each should address the identified improvements while maintaining authenticity."},{role:"user",content:`Original bio: "${e}"\n\nKey improvements needed: ${i}\n\nCreate three improved versions in this JSON format:\n{\n  "witty": "Improved witty version...",\n  "sincere": "Improved sincere version...",\n  "adventurous": "Improved adventurous version..."\n}`}],maxTokens:800,temperature:.3});return this.parseSimpleJSON(n,{witty:"Improved witty version of your bio...",sincere:"Improved sincere version of your bio...",adventurous:"Improved adventurous version of your bio..."})}calculateConfidenceMetrics(e,s){const i=e.map(e=>e.confidence),n=i.reduce((e,s)=>e+s,0)/i.length;return{overallConfidence:Math.round(n),confidenceFactors:{analysis_complexity:85,expert_consensus:this.calculateExpertConsensus(e),data_availability:90},uncertaintyAreas:this.identifyUncertaintyAreas(e),confidenceReasons:["Multiple expert validation","Comprehensive linguistic analysis","Advanced psychological profiling"]}}calculateExpertConsensus(e){const s=e.map(e=>e.score),i=s.reduce((e,s)=>e+s,0)/s.length,n=s.reduce((e,s)=>e+Math.pow(s-i,2),0)/s.length,a=Math.sqrt(n);return Math.max(0,Math.round(100-2*a))}identifyUncertaintyAreas(e){const s=[],i=e.reduce((e,s)=>(e[s.expertType]=s.score,e),{});return Math.abs(i.psychology-i.dating_coach)>20&&s.push("Psychological appeal vs. practical dating advice alignment"),s}};const SplitComponent=function(){const[e,y]=T.useState(""),[A,w]=T.useState("idle"),[S,k]=T.useState(null),[_,E]=T.useState(null),[O,R]=T.useState(null),[C]=T.useState(()=>new L),handleCopy=e=>{navigator.clipboard.writeText(e)};return I.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50",children:[I.jsx("header",{className:"border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50",children:I.jsx("div",{className:"container mx-auto px-4 py-4",children:I.jsx("div",{className:"flex items-center justify-between",children:I.jsxs("div",{className:"flex items-center space-x-4",children:[I.jsx(r,{variant:"ghost",size:"sm",asChild:!0,children:I.jsxs(P,{to:"/bio-analyzer",children:[I.jsx(o,{className:"h-4 w-4 mr-2"}),"Back to Basic"]})}),I.jsxs("div",{className:"flex items-center space-x-2",children:[I.jsx(l,{className:"h-6 w-6 text-purple-600"}),I.jsx("h1",{className:"text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent",children:"Advanced Bio Analyzer"}),I.jsx(u,{variant:"secondary",className:"bg-purple-100 text-purple-700",children:"Powered by OpenRouter o3"})]})]})})})}),I.jsxs("main",{className:"container mx-auto px-4 py-8",children:["idle"===A&&I.jsxs("div",{className:"max-w-4xl mx-auto",children:[I.jsxs("div",{className:"text-center mb-8",children:[I.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Professional Bio Analysis & Optimization"}),I.jsx("p",{className:"text-lg text-gray-600 mb-6",children:"Get expert-level insights and improvements powered by OpenRouter's o3 model"}),I.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[I.jsxs(s,{children:[I.jsxs(i,{className:"text-center",children:[I.jsx(c,{className:"h-8 w-8 text-purple-600 mx-auto mb-2"}),I.jsx(n,{className:"text-lg",children:"Psychological Profiling"})]}),I.jsx(a,{children:I.jsx("p",{className:"text-sm text-gray-600",children:"Big 5 personality traits, attachment style, and emotional intelligence analysis"})})]}),I.jsxs(s,{children:[I.jsxs(i,{className:"text-center",children:[I.jsx(m,{className:"h-8 w-8 text-blue-600 mx-auto mb-2"}),I.jsx(n,{className:"text-lg",children:"Market Analysis"})]}),I.jsx(a,{children:I.jsx("p",{className:"text-sm text-gray-600",children:"Target audience alignment, competitive positioning, and conversion potential"})})]}),I.jsxs(s,{children:[I.jsxs(i,{className:"text-center",children:[I.jsx(d,{className:"h-8 w-8 text-green-600 mx-auto mb-2"}),I.jsx(n,{className:"text-lg",children:"AI Improvements"})]}),I.jsx(a,{children:I.jsx("p",{className:"text-sm text-gray-600",children:"Three optimized versions in different tones with expert recommendations"})})]})]})]}),I.jsxs(s,{className:"max-w-2xl mx-auto",children:[I.jsxs(i,{children:[I.jsxs(n,{className:"flex items-center space-x-2",children:[I.jsx(p,{className:"h-5 w-5"}),I.jsx("span",{children:"Enter Your Bio"})]}),I.jsx(t,{children:"Paste your current dating bio for comprehensive analysis"})]}),I.jsx(a,{children:I.jsxs("div",{className:"space-y-4",children:[I.jsxs("div",{children:[I.jsx(N,{placeholder:"Enter your dating bio here...",value:e,onChange:e=>y(e.target.value),className:"min-h-[120px] resize-none",maxLength:500}),I.jsxs("div",{className:"flex justify-between text-sm text-gray-500 mt-2",children:[I.jsx("span",{children:"Minimum 20 characters"}),I.jsxs("span",{children:[e.length,"/500"]})]})]}),I.jsxs(r,{onClick:async()=>{if(e.length<20)R("Bio must be at least 20 characters.");else if(e.length>500)R("Bio must be 500 characters or less.");else{w("processing"),R(null),E(null);try{const s=await C.analyzeBio(e,{analysisDepth:"comprehensive",includeComparative:!0,generateImprovements:!0},e=>{E(e)});k(s),w("done")}catch(e){console.error("Advanced bio analysis failed:",e),R("Analysis failed. Please try again."),w("idle")}}},size:"lg",disabled:e.length<20,className:"w-full bg-gradient-to-r from-purple-600 to-blue-600",children:[I.jsx(c,{className:"mr-2 h-5 w-5"}),"Analyze with Advanced AI"]}),O&&I.jsx("p",{className:"text-center text-red-600 text-sm",children:O})]})})]})]}),"processing"===A&&I.jsxs("div",{className:"max-w-2xl mx-auto text-center",children:[I.jsxs("div",{className:"mb-8",children:[I.jsx(l,{className:"h-16 w-16 text-purple-600 mx-auto mb-4 animate-pulse"}),I.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Advanced Analysis in Progress"}),I.jsx("p",{className:"text-gray-600",children:"Our expert AI system is analyzing your bio with professional-grade precision"})]}),_&&I.jsxs("div",{className:"space-y-4",children:[I.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border",children:[I.jsxs("div",{className:"flex items-center justify-between mb-2",children:[I.jsx("span",{className:"font-medium text-gray-900",children:_.message}),I.jsxs("span",{className:"text-sm text-gray-500",children:[Math.round(_.progress),"%"]})]}),I.jsx(g,{value:_.progress,className:"h-2"}),_.currentExpert&&I.jsxs("p",{className:"text-sm text-gray-600 mt-2",children:["Current Expert: ",_.currentExpert]})]}),I.jsxs("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-500",children:[I.jsx(h,{className:"h-4 w-4 animate-spin"}),I.jsx("span",{children:"Processing with OpenRouter o3 model..."})]})]})]}),"done"===A&&S&&I.jsxs("div",{className:"max-w-6xl mx-auto",children:[I.jsxs("div",{className:"text-center mb-8",children:[I.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Advanced Analysis Complete"}),I.jsx("p",{className:"text-gray-600",children:"Professional insights and optimized versions of your bio"})]}),I.jsxs(s,{className:"mb-8",children:[I.jsx(i,{children:I.jsxs("div",{className:"flex items-center justify-between",children:[I.jsx(n,{children:"Analysis Results"}),I.jsxs("div",{className:"flex items-center space-x-4",children:[I.jsxs(u,{variant:"outline",className:"text-lg font-bold",children:[S.overallScore,"/100"]}),I.jsxs(u,{variant:"secondary",children:[S.percentileRank,"th percentile"]})]})]})}),I.jsx(a,{children:I.jsxs(v,{defaultValue:"overview",className:"w-full",children:[I.jsxs(f,{className:"grid w-full grid-cols-6",children:[I.jsx(j,{value:"overview",children:"Overview"}),I.jsx(j,{value:"experts",children:"Expert Analysis"}),I.jsx(j,{value:"psychology",children:"Psychology"}),I.jsx(j,{value:"market",children:"Market"}),I.jsx(j,{value:"improvements",children:"Improvements"}),I.jsx(j,{value:"comparison",children:"Comparison"})]}),I.jsxs(b,{value:"overview",className:"space-y-6",children:[I.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[I.jsxs(s,{children:[I.jsx(i,{className:"pb-2",children:I.jsx(n,{className:"text-sm",children:"Overall Score"})}),I.jsx(a,{children:I.jsxs("div",{className:"text-2xl font-bold text-purple-600",children:[S.overallScore,"/100"]})})]}),I.jsxs(s,{children:[I.jsx(i,{className:"pb-2",children:I.jsx(n,{className:"text-sm",children:"Percentile Rank"})}),I.jsx(a,{children:I.jsxs("div",{className:"text-2xl font-bold text-blue-600",children:[S.percentileRank,"th"]})})]}),I.jsxs(s,{children:[I.jsx(i,{className:"pb-2",children:I.jsx(n,{className:"text-sm",children:"Improvement Potential"})}),I.jsx(a,{children:I.jsxs("div",{className:"text-2xl font-bold text-green-600",children:[S.improvementPotential,"%"]})})]}),I.jsxs(s,{children:[I.jsx(i,{className:"pb-2",children:I.jsx(n,{className:"text-sm",children:"Market Competitiveness"})}),I.jsx(a,{children:I.jsxs("div",{className:"text-2xl font-bold text-orange-600",children:[S.marketCompetitiveness,"/100"]})})]})]}),I.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[I.jsxs(s,{children:[I.jsxs(i,{children:[I.jsx(n,{className:"text-lg",children:"Quick Wins"}),I.jsx(t,{children:"Easy improvements with high impact"})]}),I.jsx(a,{children:I.jsx("div",{className:"space-y-3",children:S.quickWins.slice(0,3).map((e,s)=>I.jsxs("div",{className:"flex items-start space-x-3",children:[I.jsx(u,{variant:"outline",className:"text-xs",children:e.impactScore}),I.jsx("p",{className:"text-sm",children:e.recommendation})]},s))})})]}),I.jsxs(s,{children:[I.jsxs(i,{children:[I.jsx(n,{className:"text-lg",children:"Long-term Improvements"}),I.jsx(t,{children:"Strategic changes for maximum impact"})]}),I.jsx(a,{children:I.jsx("div",{className:"space-y-3",children:S.longTermImprovements.slice(0,3).map((e,s)=>I.jsxs("div",{className:"flex items-start space-x-3",children:[I.jsx(u,{variant:"outline",className:"text-xs",children:e.impactScore}),I.jsx("p",{className:"text-sm",children:e.recommendation})]},s))})})]})]})]}),I.jsx(b,{value:"experts",className:"space-y-4",children:S.expertAnalyses.map((e,r)=>I.jsxs(s,{children:[I.jsxs(i,{children:[I.jsxs("div",{className:"flex items-center justify-between",children:[I.jsxs(n,{className:"text-lg capitalize",children:[e.expertType.replace("_"," ")," Expert"]}),I.jsxs("div",{className:"flex items-center space-x-2",children:[I.jsxs(u,{variant:"outline",children:[e.score,"/100"]}),I.jsxs(u,{variant:"secondary",children:[e.confidence,"% confident"]})]})]}),I.jsx(t,{children:e.credentials})]}),I.jsxs(a,{children:[I.jsx("p",{className:"text-sm text-gray-700 mb-4",children:e.analysis}),I.jsxs("div",{className:"space-y-2",children:[I.jsx("h4",{className:"font-medium text-sm",children:"Key Observations:"}),I.jsx("ul",{className:"text-sm text-gray-600 space-y-1",children:e.keyObservations.map((e,s)=>I.jsxs("li",{className:"flex items-start space-x-2",children:[I.jsx("span",{className:"text-purple-500",children:"•"}),I.jsx("span",{children:e})]},s))})]})]})]},r))}),I.jsxs(b,{value:"psychology",className:"space-y-4",children:[I.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[I.jsxs(s,{children:[I.jsxs(i,{children:[I.jsx(n,{children:"Personality Profile (Big 5)"}),I.jsx(t,{children:"Psychological trait analysis"})]}),I.jsx(a,{children:I.jsx("div",{className:"space-y-4",children:Object.entries(S.psychologicalProfile.personalityTraits).map(([e,s])=>I.jsxs("div",{children:[I.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[I.jsx("span",{className:"capitalize font-medium",children:e}),I.jsxs("span",{children:[s,"/100"]})]}),I.jsx(g,{value:s,className:"h-2"})]},e))})})]}),I.jsxs(s,{children:[I.jsxs(i,{children:[I.jsx(n,{children:"Psychological Indicators"}),I.jsx(t,{children:"Key psychological metrics"})]}),I.jsx(a,{children:I.jsxs("div",{className:"space-y-4",children:[I.jsx("div",{children:I.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[I.jsx("span",{className:"font-medium",children:"Attachment Style"}),I.jsx("span",{className:"capitalize",children:S.psychologicalProfile.attachmentStyle})]})}),I.jsxs("div",{children:[I.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[I.jsx("span",{className:"font-medium",children:"Confidence Level"}),I.jsxs("span",{children:[S.psychologicalProfile.confidenceLevel,"/100"]})]}),I.jsx(g,{value:S.psychologicalProfile.confidenceLevel,className:"h-2"})]}),I.jsxs("div",{children:[I.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[I.jsx("span",{className:"font-medium",children:"Emotional Intelligence"}),I.jsxs("span",{children:[S.psychologicalProfile.emotionalIntelligence,"/100"]})]}),I.jsx(g,{value:S.psychologicalProfile.emotionalIntelligence,className:"h-2"})]})]})})]})]}),I.jsxs(s,{children:[I.jsxs(i,{children:[I.jsx(n,{children:"Linguistic Analysis"}),I.jsx(t,{children:"Communication style assessment"})]}),I.jsxs(a,{children:[I.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[I.jsxs("div",{children:[I.jsx("h4",{className:"font-medium text-sm mb-2",children:"Readability"}),I.jsxs("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:[S.linguisticAnalysis.readabilityScore,"/100"]}),I.jsx(g,{value:S.linguisticAnalysis.readabilityScore,className:"h-2"})]}),I.jsxs("div",{children:[I.jsx("h4",{className:"font-medium text-sm mb-2",children:"Sentiment"}),I.jsxs("div",{className:"text-2xl font-bold text-green-600 mb-1",children:[S.linguisticAnalysis.sentimentScore,"/100"]}),I.jsx(g,{value:S.linguisticAnalysis.sentimentScore,className:"h-2"})]}),I.jsxs("div",{children:[I.jsx("h4",{className:"font-medium text-sm mb-2",children:"Grammar"}),I.jsxs("div",{className:"text-2xl font-bold text-purple-600 mb-1",children:[S.linguisticAnalysis.grammarScore,"/100"]}),I.jsx(g,{value:S.linguisticAnalysis.grammarScore,className:"h-2"})]})]}),I.jsxs("div",{className:"mt-6",children:[I.jsx("h4",{className:"font-medium text-sm mb-2",children:"Tone Analysis:"}),I.jsx("div",{className:"flex flex-wrap gap-2",children:S.linguisticAnalysis.toneAnalysis.map((e,s)=>I.jsx(u,{variant:"outline",className:"text-xs",children:e},s))}),I.jsxs("div",{className:"mt-4",children:[I.jsx("span",{className:"font-medium text-sm",children:"Vocabulary Level: "}),I.jsx(u,{variant:"secondary",className:"capitalize",children:S.linguisticAnalysis.vocabularyLevel})]})]})]})]})]}),I.jsx(b,{value:"market",className:"space-y-4",children:I.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[I.jsxs(s,{children:[I.jsxs(i,{children:[I.jsx(n,{children:"Market Performance"}),I.jsx(t,{children:"Dating market analysis"})]}),I.jsx(a,{children:I.jsxs("div",{className:"space-y-4",children:[I.jsxs("div",{children:[I.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[I.jsx("span",{className:"font-medium",children:"Target Audience Alignment"}),I.jsxs("span",{children:[S.marketAnalysis.targetAudienceAlignment,"/100"]})]}),I.jsx(g,{value:S.marketAnalysis.targetAudienceAlignment,className:"h-2"})]}),I.jsxs("div",{children:[I.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[I.jsx("span",{className:"font-medium",children:"Conversion Potential"}),I.jsxs("span",{children:[S.marketAnalysis.conversionPotential,"/100"]})]}),I.jsx(g,{value:S.marketAnalysis.conversionPotential,className:"h-2"})]}),I.jsxs("div",{children:[I.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[I.jsx("span",{className:"font-medium",children:"Engagement Probability"}),I.jsxs("span",{children:[S.marketAnalysis.engagementProbability,"/100"]})]}),I.jsx(g,{value:S.marketAnalysis.engagementProbability,className:"h-2"})]})]})})]}),I.jsxs(s,{children:[I.jsxs(i,{children:[I.jsx(n,{children:"Market Positioning"}),I.jsx(t,{children:"Competitive analysis"})]}),I.jsx(a,{children:I.jsxs("div",{className:"space-y-4",children:[I.jsxs("div",{children:[I.jsx("h4",{className:"font-medium text-sm mb-2",children:"Competitive Position:"}),I.jsx(u,{variant:"outline",className:"text-lg capitalize",children:S.marketAnalysis.competitivePositioning})]}),I.jsxs("div",{children:[I.jsx("h4",{className:"font-medium text-sm mb-2",children:"Niche Appeal:"}),I.jsx("div",{className:"flex flex-wrap gap-2",children:S.marketAnalysis.nicheAppeal.map((e,s)=>I.jsx(u,{variant:"secondary",className:"text-xs",children:e},s))})]})]})})]})]})}),I.jsx(b,{value:"improvements",className:"space-y-6",children:I.jsxs("div",{className:"grid grid-cols-1 gap-6",children:[I.jsxs(s,{children:[I.jsxs(i,{children:[I.jsx(n,{children:"Original Bio"}),I.jsx(t,{children:"Your current bio for reference"})]}),I.jsxs(a,{children:[I.jsx("div",{className:"bg-gray-50 p-4 rounded-lg mb-4",children:I.jsx("p",{className:"text-sm whitespace-pre-wrap",children:S.originalBio})}),I.jsxs(r,{onClick:()=>handleCopy(S.originalBio),size:"sm",variant:"outline",children:[I.jsx(x,{className:"mr-2 h-4 w-4"})," Copy Original"]})]})]}),I.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:Object.entries(S.improvedVersions).map(([e,o])=>I.jsxs(s,{children:[I.jsxs(i,{children:[I.jsxs(n,{className:"capitalize",children:[e," Version"]}),I.jsxs(t,{children:["AI-optimized bio in ",e," tone"]})]}),I.jsxs(a,{children:[I.jsx("div",{className:"bg-gray-50 p-4 rounded-lg mb-4 min-h-[120px]",children:I.jsx("p",{className:"text-sm whitespace-pre-wrap",children:o})}),I.jsxs(r,{onClick:()=>handleCopy(o),size:"sm",variant:"outline",className:"w-full",children:[I.jsx(x,{className:"mr-2 h-4 w-4"})," Copy ",e]})]})]},e))})]})}),I.jsx(b,{value:"comparison",className:"space-y-4",children:I.jsxs(s,{children:[I.jsxs(i,{children:[I.jsx(n,{children:"Market Comparison"}),I.jsx(t,{children:"How your bio compares to others"})]}),I.jsx(a,{children:I.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[I.jsxs("div",{children:[I.jsx("h4",{className:"font-medium text-sm mb-4",children:"Market Position"}),I.jsxs("div",{className:"text-center p-6 bg-gray-50 rounded-lg",children:[I.jsx("div",{className:"text-3xl font-bold text-purple-600 mb-2",children:S.comparativeAnalysis.marketPosition.replace("_"," ").toUpperCase()}),I.jsxs("p",{className:"text-sm text-gray-600",children:["Top ",S.comparativeAnalysis.topPercentile,"% of bios"]})]})]}),I.jsxs("div",{children:[I.jsx("h4",{className:"font-medium text-sm mb-4",children:"Competitive Advantages"}),I.jsx("div",{className:"space-y-2",children:S.comparativeAnalysis.competitiveAdvantages.map((e,s)=>I.jsxs("div",{className:"flex items-center space-x-2",children:[I.jsx("span",{className:"text-green-500",children:"✓"}),I.jsx("span",{className:"text-sm",children:e})]},s))}),I.jsx("h4",{className:"font-medium text-sm mb-2 mt-4",children:"Areas for Improvement"}),I.jsx("div",{className:"space-y-2",children:S.comparativeAnalysis.areasForImprovement.map((e,s)=>I.jsxs("div",{className:"flex items-center space-x-2",children:[I.jsx("span",{className:"text-orange-500",children:"→"}),I.jsx("span",{className:"text-sm",children:e})]},s))})]})]})})]})})]})})]}),I.jsxs("div",{className:"text-center",children:[I.jsx(r,{variant:"outline",onClick:()=>{w("idle"),k(null),y("")},className:"mr-4",children:"Analyze Another Bio"}),I.jsx(r,{asChild:!0,size:"lg",children:I.jsxs(P,{to:"/image-analyzer-pro",children:["Analyze Photos with Advanced AI ",I.jsx(d,{className:"ml-2 h-5 w-5"})]})})]})]})]})]})};export{SplitComponent as component};
//# sourceMappingURL=bio-analyzer-pro-l9tir1CX.mjs.map
