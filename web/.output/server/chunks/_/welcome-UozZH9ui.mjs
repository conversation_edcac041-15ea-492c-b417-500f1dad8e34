import{C as e,a as s,b as a,c as t,d as r}from"./card-zxSsGLJg.mjs";import{H as i,S as l,a as n,B as c,A as d}from"./lucide-react.mjs";import{r as o,j as m,S as x,a as h,L as p}from"./routeTree.gen-BFK54byf.mjs";import{S as j,a as g}from"./uiComponents.mjs";import"../nitro/nitro.mjs";import"node:process";import"cloudflare:workers";import"node:events";import"node:buffer";import"node:timers";import"node:stream";import"node:stream/web";import"node:async_hooks";function WelcomeCard({onSignIn:d,onSignUp:o}){return m.jsxs(e,{className:"shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm",children:[m.jsxs(s,{className:"text-center pb-6",children:[m.jsx("div",{className:"flex justify-center mb-4",children:m.jsxs("div",{className:"relative",children:[m.jsx(i,{className:"w-12 h-12 text-flame-red fill-current"}),m.jsx(l,{className:"w-4 h-4 text-sparks-pink absolute -top-1 -right-1"})]})}),m.jsx(a,{className:"text-h2-mobile md:text-h2 text-graphite-90",children:"Welcome to TinderOP"}),m.jsx(t,{className:"text-body-md text-graphite-60",children:"The AI-powered dating profile optimizer that helps you get more matches"})]}),m.jsxs(r,{className:"space-y-6",children:[m.jsxs("div",{className:"flex flex-col space-y-4",children:[m.jsxs("div",{className:"flex items-center space-x-3 text-sm text-graphite-60",children:[m.jsx(n,{className:"w-4 h-4 text-sparks-pink"}),m.jsx("span",{children:"AI-powered photo analysis"})]}),m.jsxs("div",{className:"flex items-center space-x-3 text-sm text-graphite-60",children:[m.jsx(l,{className:"w-4 h-4 text-sparks-pink"}),m.jsx("span",{children:"Bio optimization suggestions"})]}),m.jsxs("div",{className:"flex items-center space-x-3 text-sm text-graphite-60",children:[m.jsx(i,{className:"w-4 h-4 text-sparks-pink"}),m.jsx("span",{children:"Increase your match rate"})]})]}),m.jsxs("div",{className:"space-y-4",children:[m.jsxs("div",{className:"relative",children:[m.jsx("div",{className:"absolute inset-0 flex items-center",children:m.jsx("div",{className:"w-full border-t border-graphite-20"})}),m.jsx("div",{className:"relative flex justify-center text-sm",children:m.jsx("span",{className:"px-2 bg-cloud-white text-graphite-60",children:"Get started"})})]}),m.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[m.jsx(c,{variant:"primary",className:"w-full",onClick:o,children:"Sign Up"}),m.jsx(c,{variant:"secondary",className:"w-full",onClick:d,children:"Sign In"})]})]})]})]})}function SignInCard({onBack:l}){return m.jsxs(e,{className:"shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm",children:[m.jsxs(s,{className:"text-center pb-6",children:[m.jsxs("div",{className:"flex items-center justify-between mb-4",children:[m.jsx(c,{variant:"ghost",size:"icon",onClick:l,className:"text-graphite-60 hover:text-graphite-90",children:m.jsx(d,{className:"w-4 h-4"})}),m.jsx("div",{className:"flex-1 flex justify-center",children:m.jsx(i,{className:"w-8 h-8 text-flame-red fill-current"})}),m.jsx("div",{className:"w-10"})]}),m.jsx(a,{className:"text-h2-mobile md:text-h2 text-graphite-90",children:"Sign In"}),m.jsx(t,{className:"text-body-md text-graphite-60",children:"Welcome back! Sign in to continue optimizing your profile"})]}),m.jsx(r,{children:m.jsx("div",{className:"flex justify-center",children:m.jsx(j,{routing:"hash",signUpUrl:"/welcome",afterSignInUrl:"/dashboard",appearance:{elements:{rootBox:"w-full",card:"shadow-none border-0 bg-transparent",headerTitle:"hidden",headerSubtitle:"hidden",socialButtonsBlockButton:"bg-gradient-primary text-cloud-white hover:bg-gradient-primary/90",formButtonPrimary:"bg-gradient-primary text-cloud-white hover:bg-gradient-primary/90",footerActionLink:"text-flame-red hover:text-flame-red/80"}}})})})]})}function SignUpCard({onBack:l}){return m.jsxs(e,{className:"shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm",children:[m.jsxs(s,{className:"text-center pb-6",children:[m.jsxs("div",{className:"flex items-center justify-between mb-4",children:[m.jsx(c,{variant:"ghost",size:"icon",onClick:l,className:"text-graphite-60 hover:text-graphite-90",children:m.jsx(d,{className:"w-4 h-4"})}),m.jsx("div",{className:"flex-1 flex justify-center",children:m.jsx(i,{className:"w-8 h-8 text-flame-red fill-current"})}),m.jsx("div",{className:"w-10"})]}),m.jsx(a,{className:"text-h2-mobile md:text-h2 text-graphite-90",children:"Sign Up"}),m.jsx(t,{className:"text-body-md text-graphite-60",children:"Create your account and start getting better matches today"})]}),m.jsx(r,{children:m.jsx("div",{className:"flex justify-center",children:m.jsx(g,{routing:"hash",signInUrl:"/welcome",afterSignUpUrl:"/dashboard",appearance:{elements:{rootBox:"w-full",card:"shadow-none border-0 bg-transparent",headerTitle:"hidden",headerSubtitle:"hidden",socialButtonsBlockButton:"bg-gradient-primary text-cloud-white hover:bg-gradient-primary/90",formButtonPrimary:"bg-gradient-primary text-cloud-white hover:bg-gradient-primary/90",footerActionLink:"text-flame-red hover:text-flame-red/80"}}})})})]})}function WelcomeBack(){return m.jsxs(e,{className:"shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm",children:[m.jsxs(s,{className:"text-center pb-6",children:[m.jsx("div",{className:"flex justify-center mb-4",children:m.jsxs("div",{className:"relative",children:[m.jsx(i,{className:"w-12 h-12 text-flame-red fill-current"}),m.jsx(l,{className:"w-4 h-4 text-sparks-pink absolute -top-1 -right-1"})]})}),m.jsx(a,{className:"text-h2-mobile md:text-h2 text-graphite-90",children:"Welcome back!"}),m.jsx(t,{className:"text-body-md text-graphite-60",children:"Ready to optimize your dating profile?"})]}),m.jsxs(r,{className:"space-y-4",children:[m.jsxs("div",{className:"flex flex-col space-y-3",children:[m.jsx(c,{asChild:!0,variant:"primary",className:"w-full",children:m.jsx(p,{to:"/dashboard",children:"Go to Dashboard"})}),m.jsx(c,{asChild:!0,variant:"secondary",className:"w-full",children:m.jsx(p,{to:"/image-analyzer",children:"Analyze Photos"})}),m.jsx(c,{asChild:!0,variant:"secondary",className:"w-full",children:m.jsx(p,{to:"/bio-analyzer",children:"Optimize Bio"})})]}),m.jsx("div",{className:"text-center pt-4",children:m.jsx(c,{asChild:!0,variant:"tertiary",className:"text-sm",children:m.jsx(p,{to:"/",children:"Back to Home"})})})]})]})}const SplitComponent=function(){const[e,s]=o.useState(!1),[a,t]=o.useState(!1);return m.jsx("div",{className:"min-h-screen bg-gradient-hero flex items-center justify-center p-4",children:m.jsxs("div",{className:"w-full max-w-md",children:[m.jsxs(x,{children:[!e&&!a&&m.jsx(WelcomeCard,{onSignIn:()=>s(!0),onSignUp:()=>t(!0)}),e&&m.jsx(SignInCard,{onBack:()=>s(!1)}),a&&m.jsx(SignUpCard,{onBack:()=>t(!1)})]}),m.jsx(h,{children:m.jsx(WelcomeBack,{})})]})})};export{SplitComponent as component};
//# sourceMappingURL=welcome-UozZH9ui.mjs.map
