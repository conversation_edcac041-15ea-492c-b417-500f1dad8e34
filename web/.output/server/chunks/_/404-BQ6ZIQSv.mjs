import{j as e}from"./routeTree.gen-BFK54byf.mjs";import"../nitro/nitro.mjs";import"node:process";import"cloudflare:workers";import"node:events";import"node:buffer";import"node:timers";import"node:stream";import"node:stream/web";import"node:async_hooks";const SplitComponent=function(){return e.jsxs("div",{className:"flex flex-col items-center justify-center h-screen",children:[e.jsx("img",{src:"/Selection_405.png",alt:"Not Found",className:"w-1/2"}),e.jsx("h1",{className:"text-4xl font-bold",children:"Page Not Found"}),e.jsx("p",{className:"text-lg",children:"The page you are looking for does not exist."})]})};export{SplitComponent as component};
//# sourceMappingURL=404-BQ6ZIQSv.mjs.map
