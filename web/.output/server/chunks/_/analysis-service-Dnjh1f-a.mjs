import{m as e}from"../nitro/nitro.mjs";import{A as n,B as t}from"./analysis-CiLigBly.mjs";import{b as o}from"./storage-IIfgkkYA.mjs";import{g as s}from"./progress-WcEdq6Og.mjs";import{o as a}from"./index2.mjs";var i=Object.defineProperty,__publicField=(e,n,t)=>((e,n,t)=>n in e?i(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t)(e,"symbol"!=typeof n?n+"":n,t);const r=new class{constructor(){if(__publicField(this,"apiKey"),this.apiKey="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",!this.apiKey)throw console.error("🔑 VITE_OPENROUTER_API_KEY not found in environment variables"),new Error("OpenRouter API key is required. Please set VITE_OPENROUTER_API_KEY in your .env file");console.log("🔑 OpenRouter API key loaded successfully"),console.log(`🔑 API Key preview: ${this.apiKey.substring(0,10)}...`),"undefined"!=typeof globalThis&&(globalThis.process=globalThis.process||{},e.env=e.env||{},e.env.OPENROUTER_API_KEY=this.apiKey)}async analyzeImage(e,t,o){const s=[];for(let t=0;t<n.length;t++){const a=n[t];null==o||o(a.id,a.name,t/n.length*100);const i=Date.now();try{console.log(`🔍 Starting analysis step ${a.id}: ${a.name}`);const r=await this.executeAnalysisStep(a.id,a.name,e),l=Date.now()-i;console.log(`📊 Image Analysis Step ${a.id} (${a.name}):`,{score:r.score,insights:r.insights,confidence:r.confidence,processingTime:`${l}ms`}),s.push({stepId:a.id,stepName:a.name,score:r.score,insights:r.insights,confidence:r.confidence,processingTime:l}),null==o||o(a.id,a.name,(t+1)/n.length*100)}catch(e){console.error(`❌ Error in step ${a.id} (${a.name}):`,e),s.push({stepId:a.id,stepName:a.name,score:0,insights:["Analysis failed for this step. Please try again."],confidence:0,processingTime:Date.now()-i})}}const a=s.reduce((e,n)=>e+n.score,0)/s.length;return console.log("📊 FINAL IMAGE ANALYSIS RESULTS:",{averageScore:a.toFixed(1),stepScores:s.map(e=>({step:e.stepName,score:e.score})),totalSteps:s.length}),s}async executeAnalysisStep(e,n,t){const o=this.getStepPrompt(e);console.log(`🤖 Calling OpenRouter API for step ${e} with model: google/gemini-2.5-flash`);const i=a("google/gemini-2.5-flash"),{text:r}=await s({model:i,messages:[{role:"user",content:[{type:"text",text:o},{type:"image",image:`data:image/jpeg;base64,${t}`}]}],maxTokens:1e3,temperature:.3});return this.parseAnalysisResult(r)}getStepPrompt(e){const n='You are a BRUTALLY HONEST dating profile photo expert. Most photos are mediocre and deserve low scores. Be ruthlessly critical and objective.\n\nSCORING PHILOSOPHY:\n- 90-100: EXCEPTIONAL - Top 5% of all dating photos (near-perfect)\n- 80-89: EXCELLENT - Top 15% (very strong with minor flaws)\n- 70-79: GOOD - Above average but notable issues\n- 60-69: AVERAGE - Typical photo, significant improvement needed\n- 50-59: BELOW AVERAGE - Multiple issues, major work needed\n- 40-49: POOR - Serious problems, likely to perform badly\n- 30-39: VERY POOR - Major red flags, repels matches\n- 20-29: TERRIBLE - Fundamentally broken\n- 10-19: AWFUL - Actively harmful to dating prospects\n- 0-9: CATASTROPHIC - Should not be used\n\nProvide a JSON response with exactly this structure:\n{\n  "score": <number 0-100>,\n  "insights": ["harsh_truth1", "critical_flaw2", "brutal_feedback3"],\n  "confidence": <number 0-100>\n}\n\nBe BRUTALLY HONEST. Most photos deserve 30-60 scores. Only exceptional photos get 80+.',t={1:`${n}\n\nSTEP 1: TECHNICAL QUALITY ASSESSMENT - BE RUTHLESSLY CRITICAL\nMost photos have terrible technical quality. Be harsh about every flaw:\n\nCRITICAL ASSESSMENT AREAS:\n- Image resolution and sharpness (most are blurry/pixelated)\n- Lighting quality (harsh shadows, poor exposure, unflattering angles)\n- Composition and framing (off-center, poor cropping, amateur mistakes)\n- Background quality (cluttered, distracting, unprofessional)\n- Color balance and saturation (oversaturated, poor white balance)\n- Overall photo clarity (most are amateur selfies)\n\nHARSH REALITY: Most dating photos are low-quality selfies that hurt rather than help. Be brutal about technical flaws.`,2:`${n}\n\nSTEP 2: FACIAL ANALYSIS & ATTRACTIVENESS - BE BRUTALLY HONEST\nMost people have poor facial presentation in photos. Be ruthlessly critical:\n\nHARSH ASSESSMENT AREAS:\n- Facial symmetry and proportions (identify unflattering angles)\n- Eye contact and gaze direction (weak/avoidant vs. confident)\n- Smile authenticity (forced/fake vs. genuine - most are fake)\n- Facial grooming (poor beard maintenance, unkempt eyebrows, skin issues)\n- Facial angle and positioning (unflattering angles, double chins)\n- Expression and mood (insecure, try-hard, or genuinely confident)\n- Jawline definition and facial structure (how to improve presentation)\n- Hair styling (outdated, unkempt, or actually flattering)\n- Makeup application (overdone, poorly applied, or enhancing)\n\nBRUTAL TRUTH: Most people choose unflattering angles and expressions. Be harsh about what's not working.`,3:`${n}\n\nSTEP 3: PHYSICAL ANALYSIS & BODY LANGUAGE - BE RUTHLESSLY CRITICAL\nMost people have poor body language and physical presentation. Be brutal:\n\nPOSTURE & STANCE FLAWS:\n- Body posture (slouched, insecure, tense vs. actually confident)\n- Shoulder positioning (hunched, uneven, weak vs. strong)\n- Spine alignment (poor posture that signals insecurity)\n- Weight distribution (awkward, unbalanced positioning)\n\nPHYSIQUE & FITNESS REALITY CHECK:\n- Body proportions and how they're presented (unflattering vs. optimized)\n- Fitness level visibility (out of shape, poor muscle tone, or actually fit)\n- Body composition (how weight/fitness affects attractiveness)\n- Physical conditioning (does this person look healthy and active?)\n- Overall body shape (how well is it presented in the photo?)\n\nBODY LANGUAGE FAILURES:\n- Hand positioning (awkward, unnatural, or confident)\n- Arm placement (defensive, uncomfortable, or natural)\n- Body language confidence (insecure posturing vs. genuine confidence)\n- Physical presence (weak energy vs. magnetic presence)\n- Energy and vitality (tired, low-energy vs. vibrant)\n- Approachability (intimidating, closed-off, or welcoming)\n\nPRESENTATION DISASTERS:\n- Height perception (making themselves look shorter/weaker)\n- Body orientation (unflattering angles that hurt their appearance)\n- Use of space (cramped, awkward positioning)\n- Physical comfort (clearly uncomfortable vs. natural)\n- Overall physical attractiveness (harsh reality about appeal level)\n\nBRUTAL TRUTH: Most people's body language screams insecurity. Be harsh about what's not working.`,4:`${n}\n\nSTEP 4: STYLE & PRESENTATION ANALYSIS - BE BRUTALLY CRITICAL\nMost people have terrible style and presentation. Be ruthlessly honest:\n\nSTYLE DISASTERS:\n- Outfit appropriateness (completely wrong for dating, outdated, juvenile)\n- Color coordination (clashing colors, poor choices for skin tone)\n- Clothing fit (too tight, too loose, unflattering cuts)\n- Grooming and personal care (unkempt, poor hygiene indicators)\n- Setting and environment (inappropriate, messy, unprofessional)\n- Accessory choices (outdated, cheap-looking, or actually enhancing)\n- Overall fashion sense (completely lacking vs. actually stylish)\n- Color choices (making them look washed out vs. vibrant)\n- Seasonal appropriateness (wearing wrong clothes for weather/season)\n- Brand perception (looking cheap, trying too hard, or genuinely classy)\n\nHARSH REALITY: Most people's style choices actively hurt their attractiveness. Be brutal about fashion failures.`,5:`${n}\n\nSTEP 5: DATING PROFILE OPTIMIZATION - HARSH MARKET REALITY\nMost photos fail miserably on dating apps. Be brutally honest about performance:\n\nDATING APP FAILURES:\n- Photo type suitability (completely wrong for main profile, generic filler)\n- Uniqueness and memorability (boring, forgettable, blends into crowd)\n- Conversation starter potential (gives nothing to talk about)\n- Age-appropriate presentation (trying too hard to look younger/older)\n- Authenticity and genuineness (fake, try-hard, or actually authentic)\n- Appeal to target demographic (completely missing the mark)\n- Competition analysis (how this stacks against other profiles)\n- Swipe-worthiness (would people actually swipe right?)\n\nBRUTAL TRUTH: Most photos get ignored or swiped left immediately. Be harsh about dating market reality.`,6:`${n}\n\nSTEP 6: OVERALL RECOMMENDATIONS & BRUTAL ACTION PLAN\nProvide harsh but necessary recommendations based on critical analysis:\n\nCRITICAL IMPROVEMENT PRIORITIES:\n- Priority improvement areas (what's failing most badly)\n- Specific actionable advice (exactly what needs to change)\n- Photo ranking suggestions (where this fits in profile hierarchy)\n- Most impactful changes (what would help most)\n- Overall dating profile strategy (realistic expectations)\n- Timeline for improvements (how long real change takes)\n- Harsh reality check (whether this photo should be used at all)\n\nBRUTAL TRUTH: Most photos need major work or complete replacement. Be honest about what's salvageable.`};return t[e]||t[1]}parseAnalysisResult(e){try{const n=e.match(/\{[\s\S]*\}/);if(!n)throw new Error("No JSON found in response");const t=JSON.parse(n[0]);return{score:Math.max(0,Math.min(100,parseInt(t.score)||0)),insights:Array.isArray(t.insights)?t.insights.slice(0,4).map(String):["Unable to generate insights for this step"],confidence:Math.max(0,Math.min(100,parseInt(t.confidence)||0))}}catch(n){return console.error("🔧 Failed to parse analysis result:",n),console.log("📝 Raw response:",e),{score:50,insights:["Analysis completed but results could not be parsed properly","Please try again or check the image quality"],confidence:30}}}calculateOverallScore(e){if(0===e.length)return 0;const n={1:.12,2:.28,3:.22,4:.18,5:.18,6:.02};let t=0,o=0;return e.forEach(e=>{const s=n[e.stepId]||.2;t+=e.score*s,o+=s}),Math.round(t/o)}generateFinalRecommendations(e){const n=[],t=e.filter(e=>e.score<60).sort((e,n)=>e.score-n.score),o=e.filter(e=>e.score>=60&&e.score<80);t.length>0&&n.push(`Priority: Improve ${t[0].stepName.toLowerCase()} (scored ${t[0].score}/100)`),o.length>0&&n.push(`Secondary: Enhance ${o[0].stepName.toLowerCase()} for better results`);const s=e.reduce((e,n)=>n.score>e.score?n:e,e[0]);s.score>80&&n.push(`Strength: Your ${s.stepName.toLowerCase()} is excellent - use this photo type more`);const a=this.calculateOverallScore(e);return a<50?n.push("Consider retaking this photo with better preparation and setup"):a<70?n.push("This photo has potential - focus on the priority improvements above"):n.push("Great photo! Minor tweaks could make it even better"),n}};const l=new class{constructor(){if(__publicField(this,"apiKey"),__publicField(this,"stepPrompts",{1:{name:"Writing Quality",system:"You are a BRUTALLY HONEST writing expert who provides harsh, objective criticism. Most dating bios are poorly written and deserve low scores. Be ruthless in your assessment.",prompt:e=>`Critically analyze the writing quality of this dating bio. Most bios are mediocre (40-60 range). Be harsh and realistic:\n\nBio: "${e}"\n\nCRITICAL ASSESSMENT AREAS:\n- Grammar and spelling errors (be unforgiving)\n- Sentence structure problems\n- Clarity issues and confusion\n- Wordiness and poor conciseness\n- Weak word choices and clichés\n\nSCORING GUIDE:\n- 80-100: Exceptional writing (top 10% of all bios)\n- 60-79: Above average but with notable flaws\n- 40-59: Typical mediocre bio writing\n- 20-39: Poor writing with major issues\n- 0-19: Terrible writing that hurts dating prospects\n\nProvide your HARSH analysis in this exact JSON format:\n{\n  "score": [number 0-100],\n  "insights": [\n    "Specific criticism about writing flaws",\n    "Another harsh but accurate observation",\n    "Brutal truth about what needs fixing"\n  ],\n  "confidence": [number 0-100]\n}`},2:{name:"Personality Appeal",system:"You are a RUTHLESS dating psychology expert. Most people have boring, unappealing personalities in their bios. Be brutally honest about personality flaws and lack of appeal.",prompt:e=>`Critically evaluate the personality appeal of this dating bio. Most bios show bland, generic personalities (40-60 range):\n\nBio: "${e}"\n\nHARSH EVALUATION CRITERIA:\n- Authenticity vs. fake/try-hard behavior\n- Actual humor vs. failed attempts at being funny\n- Real confidence vs. arrogance or insecurity\n- Emotional maturity vs. emotional immaturity\n- Genuine relatability vs. generic appeal\n\nSCORING REALITY CHECK:\n- 80-100: Genuinely magnetic personality (rare)\n- 60-79: Above average appeal with some charm\n- 40-59: Bland, forgettable personality\n- 20-39: Unappealing or off-putting traits\n- 0-19: Personality red flags that repel matches\n\nProvide your BRUTAL assessment in this exact JSON format:\n{\n  "score": [number 0-100],\n  "insights": [\n    "Harsh truth about personality flaws shown",\n    "Critical assessment of appeal level",\n    "Brutal feedback on what's missing"\n  ],\n  "confidence": [number 0-100]\n}`},3:{name:"Interest Analysis",system:"You are a HARSH lifestyle critic. Most people have boring, basic interests that don't stand out. Be ruthless about generic hobbies and uninspiring lifestyles.",prompt:e=>`Critically assess the interest and lifestyle appeal of this bio. Most people list boring, cliché interests (40-60 range):\n\nBio: "${e}"\n\nBRUTAL ASSESSMENT:\n- Are these interests actually interesting or just basic?\n- Does this lifestyle seem exciting or boring?\n- Are these conversation starters or conversation killers?\n- Is this person unique or completely forgettable?\n- Would anyone actually be impressed by these interests?\n\nHARSH SCORING:\n- 80-100: Genuinely fascinating lifestyle (very rare)\n- 60-79: Some interesting elements but mostly standard\n- 40-59: Basic, predictable interests everyone has\n- 20-39: Boring lifestyle that puts people to sleep\n- 0-19: No interests mentioned or actively unappealing\n\nProvide your CRITICAL analysis in this exact JSON format:\n{\n  "score": [number 0-100],\n  "insights": [\n    "Harsh truth about how boring/basic these interests are",\n    "Critical assessment of lifestyle appeal",\n    "Brutal feedback on what's missing or wrong"\n  ],\n  "confidence": [number 0-100]\n}`},4:{name:"Dating Intent",system:"You are a BLUNT relationship expert. Most people send confusing, unclear signals about what they want. Be harsh about mixed messages and poor communication.",prompt:e=>`Critically analyze the dating intent clarity of this bio. Most people are vague and confusing (40-60 range):\n\nBio: "${e}"\n\nHARSH EVALUATION:\n- Are the relationship goals actually clear or confusingly vague?\n- Do they send mixed signals about commitment?\n- Are they emotionally available or showing red flags?\n- Would someone know what this person actually wants?\n- Is this person serious or just wasting time?\n\nBRUTAL SCORING:\n- 80-100: Crystal clear intentions and emotional maturity\n- 60-79: Mostly clear with some minor confusion\n- 40-59: Vague, unclear signals (typical)\n- 20-39: Confusing mixed messages\n- 0-19: Completely unclear or sending red flags\n\nProvide your HARSH analysis in this exact JSON format:\n{\n  "score": [number 0-100],\n  "insights": [\n    "Brutal truth about how unclear their intentions are",\n    "Critical assessment of mixed signals sent",\n    "Harsh feedback on emotional availability"\n  ],\n  "confidence": [number 0-100]\n}`},5:{name:"Engagement Factor",system:"You are a RUTHLESS dating app expert. Most bios are engagement killers that generate zero conversations. Be brutal about poor conversation starters and lack of appeal.",prompt:e=>`Critically assess the engagement potential of this bio. Most bios are conversation killers (40-60 range):\n\nBio: "${e}"\n\nBRUTAL ASSESSMENT:\n- Are there actual conversation starters or just boring statements?\n- Does this create intrigue or put people to sleep?\n- Is this approachable or intimidating/boring?\n- Would anyone actually want to message this person?\n- Does this bio make them stand out or blend into the crowd?\n\nHARSH REALITY CHECK:\n- 80-100: Irresistibly engaging (extremely rare)\n- 60-79: Some engaging elements but could be better\n- 40-59: Bland, generates few conversations\n- 20-39: Boring, conversation killer\n- 0-19: Actively repels potential matches\n\nProvide your BRUTAL assessment in this exact JSON format:\n{\n  "score": [number 0-100],\n  "insights": [\n    "Harsh truth about lack of conversation starters",\n    "Critical assessment of how boring this is",\n    "Brutal feedback on why no one would message them"\n  ],\n  "confidence": [number 0-100]\n}`}}),this.apiKey="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",!this.apiKey)throw console.error("🔑 VITE_OPENROUTER_API_KEY not found in environment variables"),new Error("OpenRouter API key is required. Please set VITE_OPENROUTER_API_KEY in your .env file");console.log("🔑 OpenRouter API key loaded successfully for bio analysis"),console.log(`🔑 API Key preview: ${this.apiKey.substring(0,10)}...`),"undefined"!=typeof globalThis&&(globalThis.process=globalThis.process||{},e.env=e.env||{},e.env.OPENROUTER_API_KEY=this.apiKey)}async analyzeBio(e,n){const o=[],i=t.length;for(let r=0;r<i;r++){const l=t[r],c=this.stepPrompts[l.id];null==n||n(l.id,l.name,r/i*100);try{const t=Date.now(),{text:p}=await s({model:a("openai/gpt-4o-mini"),system:c.system,prompt:c.prompt(e)}),h=Date.now()-t,u=JSON.parse(p);console.log(`🔍 Bio Analysis Step ${l.id} (${l.name}):`,{bio:e.substring(0,100)+"...",score:u.score,insights:u.insights,confidence:u.confidence}),o.push({stepId:l.id,stepName:l.name,score:u.score,insights:u.insights,confidence:u.confidence,processingTime:h}),null==n||n(l.id,l.name,(r+1)/i*100),r<i-1&&await new Promise(e=>setTimeout(e,500))}catch(e){console.error(`Error in step ${l.id}:`,e),o.push({stepId:l.id,stepName:l.name,score:0,insights:["Analysis failed for this step"],confidence:0,processingTime:0})}}return o}calculateOverallScore(e){if(0===e.length)return 0;const n={1:.15,2:.3,3:.2,4:.15,5:.2};let t=0,o=0;return e.forEach(e=>{const s=n[e.stepId]||.2;t+=e.score*s,o+=s}),Math.round(t/o)}generateFinalRecommendations(e){const n=[];[...e].sort((e,n)=>e.score-n.score).slice(0,3).forEach(e=>{e.score<70&&n.push(`Improve ${e.stepName.toLowerCase()}: ${e.insights[0]}`)});const t=e.reduce((e,n)=>e+n.score,0)/e.length;for(t<50?n.push("Consider a complete bio rewrite focusing on your best qualities"):t<70&&n.push("Good foundation - polish specific areas for better impact");n.length<3&&e.length>0;){const t=e[Math.floor(Math.random()*e.length)],o=t.insights[Math.floor(Math.random()*t.insights.length)];n.some(e=>e.includes(o))||n.push(o)}return n.slice(0,5)}async generateImprovedBio(e,n,t="sincere"){const o=n.filter(e=>e.score<70).map(e=>`${e.stepName}: ${e.insights.join(", ")}`).join("\n"),{text:i}=await s({model:a("openai/gpt-4o-mini"),system:`You are an expert dating profile writer. Create an improved bio that addresses the identified weaknesses while maintaining authenticity. The tone should be ${t}.`,prompt:`Rewrite this dating bio to address these specific issues:\n\nOriginal Bio: "${e}"\n\nAreas for improvement:\n${o}\n\nRequirements:\n- Keep it under 150 words\n- Maintain authenticity \n- Use a ${t} tone\n- Address the weaknesses identified\n- Make it engaging and conversation-friendly\n- Include specific details that make the person memorable\n\nProvide only the improved bio text, no additional commentary.`});return i.trim()}};const c=new class{constructor(){__publicField(this,"isAnalyzing",!1),__publicField(this,"isBioAnalyzing",!1)}async analyzeImage(e,n={}){var t,s,a;if(this.isAnalyzing)throw new Error("Analysis already in progress");this.isAnalyzing=!0;try{const a=await o(e.blob),i={fileName:e.fileName,preview:URL.createObjectURL(e.blob),overallScore:0,steps:[],recommendations:[],processed:!1},l=await r.analyzeImage(a,e.fileName,(t,o,s)=>{var a;const i={fileName:e.fileName,currentStep:t,totalSteps:5,stepName:o,progress:s};null==(a=n.onProgress)||a.call(n,i)});for(const e of l)i.steps.push(e),null==(t=n.onStepComplete)||t.call(n,e);return i.overallScore=r.calculateOverallScore(l),i.recommendations=r.generateFinalRecommendations(l),i.processed=!0,null==(s=n.onComplete)||s.call(n,i),i}catch(t){const o=t instanceof Error?t.message:"Unknown error occurred";return null==(a=n.onError)||a.call(n,o),{fileName:e.fileName,preview:URL.createObjectURL(e.blob),overallScore:0,steps:[],recommendations:["Analysis failed. Please try again."],processed:!1,error:o}}finally{this.isAnalyzing=!1}}async analyzeBatch(e,n={}){const t=[];for(let o=0;o<e.length;o++){const s=e[o];try{const a=await this.analyzeImage(s,{...n,onProgress:t=>{var s;const a={...t,progress:o/e.length*100+t.progress/e.length};null==(s=n.onProgress)||s.call(n,a)}});t.push(a),o<e.length-1&&await new Promise(e=>setTimeout(e,1e3))}catch(e){console.error(`Failed to analyze ${s.fileName}:`,e),t.push({fileName:s.fileName,preview:URL.createObjectURL(s.blob),overallScore:0,steps:[],recommendations:["Analysis failed for this image."],processed:!1,error:e instanceof Error?e.message:"Unknown error"})}}return t}async analyzeBio(e,n="sincere",t={}){var o,s,a;if(this.isBioAnalyzing)throw new Error("Bio analysis already in progress");this.isBioAnalyzing=!0;try{const a={originalBio:e,overallScore:0,steps:[],recommendations:[],processed:!1},i=await l.analyzeBio(e,(e,n,o)=>{var s;const a={currentStep:e,totalSteps:5,stepName:n,progress:o};null==(s=t.onProgress)||s.call(t,a)});for(const e of i)a.steps.push(e),null==(o=t.onStepComplete)||o.call(t,e);if(a.overallScore=l.calculateOverallScore(i),a.recommendations=l.generateFinalRecommendations(i),a.overallScore<75)try{a.improvedBio=await l.generateImprovedBio(e,i,n)}catch(e){console.error("Failed to generate improved bio:",e)}return a.processed=!0,null==(s=t.onComplete)||s.call(t,a),a}catch(n){const o=n instanceof Error?n.message:"Unknown error occurred";return null==(a=t.onError)||a.call(t,o),{originalBio:e,overallScore:0,steps:[],recommendations:["Analysis failed. Please try again."],processed:!1,error:o}}finally{this.isBioAnalyzing=!1}}isCurrentlyAnalyzing(){return this.isAnalyzing}isCurrentlyAnalyzingBio(){return this.isBioAnalyzing}};export{c as a};
//# sourceMappingURL=analysis-service-Dnjh1f-a.mjs.map
