import{r as t,j as e,L as i,c as n}from"./routeTree.gen-BFK54byf.mjs";import{B as s,T as o,H as r,S as a,r as l,s as c,a as u}from"./lucide-react.mjs";import{p as h}from"./performance.mjs";import"../nitro/nitro.mjs";import"node:process";import"cloudflare:workers";import"node:events";import"node:buffer";import"node:timers";import"node:stream";import"node:stream/web";import"node:async_hooks";const d=t.createContext({});function useConstant(e){const i=t.useRef(null);return null===i.current&&(i.current=e()),i.current}const m=t.useEffect,p=t.createContext(null);function addUniqueItem(t,e){-1===t.indexOf(e)&&t.push(e)}function removeItem(t,e){const i=t.indexOf(e);i>-1&&t.splice(i,1)}const clamp=(t,e,i)=>i>e?e:i<t?t:i;const f={},isNumericalString=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function isObject(t){return"object"==typeof t&&null!==t}const isZeroValueString=t=>/^0[^.\s]+$/u.test(t);function memo(t){let e;return()=>(void 0===e&&(e=t()),e)}const noop=t=>t,combineFunctions=(t,e)=>i=>e(t(i)),pipe=(...t)=>t.reduce(combineFunctions),progress=(t,e,i)=>{const n=e-t;return 0===n?1:(i-t)/n};class SubscriptionManager{constructor(){this.subscriptions=[]}add(t){return addUniqueItem(this.subscriptions,t),()=>removeItem(this.subscriptions,t)}notify(t,e,i){const n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let s=0;s<n;s++){const n=this.subscriptions[s];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const secondsToMilliseconds=t=>1e3*t,millisecondsToSeconds=t=>t/1e3;function velocityPerSecond(t,e){return e?t*(1e3/e):0}const calcBezier=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function cubicBezier(t,e,i,n){if(t===e&&i===n)return noop;const getTForX=e=>function(t,e,i,n,s){let o,r,a=0;do{r=e+(i-e)/2,o=calcBezier(r,n,s)-t,o>0?i=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,i);return t=>0===t||1===t?t:calcBezier(getTForX(t),e,n)}const mirrorEasing=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,reverseEasing=t=>e=>1-t(1-e),g=cubicBezier(.33,1.53,.69,.99),y=reverseEasing(g),v=mirrorEasing(y),anticipate=t=>(t*=2)<1?.5*y(t):.5*(2-Math.pow(2,-10*(t-1))),circIn=t=>1-Math.sin(Math.acos(t)),x=reverseEasing(circIn),T=mirrorEasing(circIn),P=cubicBezier(.42,0,1,1),b=cubicBezier(0,0,.58,1),A=cubicBezier(.42,0,.58,1),isBezierDefinition=t=>Array.isArray(t)&&"number"==typeof t[0],S={linear:noop,easeIn:P,easeInOut:A,easeOut:b,circIn:circIn,circInOut:T,circOut:x,backIn:y,backInOut:v,backOut:g,anticipate:anticipate},easingDefinitionToFunction=t=>{if(isBezierDefinition(t)){t.length;const[e,i,n,s]=t;return cubicBezier(e,i,n,s)}return"string"==typeof t?S[t]:t},w=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];function createRenderBatcher(t,e){let i=!1,n=!0;const s={delta:0,timestamp:0,isProcessing:!1},flagRunNextFrame=()=>i=!0,o=w.reduce((t,e)=>(t[e]=function(t){let e=new Set,i=new Set,n=!1,s=!1;const o=new WeakSet;let r={delta:0,timestamp:0,isProcessing:!1};function triggerCallback(e){o.has(e)&&(a.schedule(e),t()),e(r)}const a={schedule:(t,s=!1,r=!1)=>{const a=r&&n?e:i;return s&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),o.delete(t)},process:t=>{r=t,n?s=!0:(n=!0,[e,i]=[i,e],e.forEach(triggerCallback),e.clear(),n=!1,s&&(s=!1,a.process(t)))}};return a}(flagRunNextFrame),t),{}),{setup:r,read:a,resolveKeyframes:l,preUpdate:c,update:u,preRender:d,render:m,postRender:p}=o,processBatch=()=>{const o=f.useManualTiming?s.timestamp:h.now();i=!1,f.useManualTiming||(s.delta=n?1e3/60:Math.max(Math.min(o-s.timestamp,40),1)),s.timestamp=o,s.isProcessing=!0,r.process(s),a.process(s),l.process(s),c.process(s),u.process(s),d.process(s),m.process(s),p.process(s),s.isProcessing=!1,i&&e&&(n=!1,t(processBatch))};return{schedule:w.reduce((e,r)=>{const a=o[r];return e[r]=(e,o=!1,r=!1)=>(i||(i=!0,n=!0,s.isProcessing||t(processBatch)),a.schedule(e,o,r)),e},{}),cancel:t=>{for(let e=0;e<w.length;e++)o[w[e]].cancel(t)},state:s,steps:o}}const{schedule:V,cancel:E,state:M,steps:D}=createRenderBatcher("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:noop,!0);let C;function clearTime(){C=void 0}const j={now:()=>(void 0===C&&j.set(M.isProcessing||f.useManualTiming?M.timestamp:h.now()),C),set:t=>{C=t,queueMicrotask(clearTime)}},checkStringStartsWith=t=>e=>"string"==typeof e&&e.startsWith(t),R=checkStringStartsWith("--"),k=checkStringStartsWith("var(--"),isCSSVariableToken=t=>!!k(t)&&L.test(t.split("/*")[0].trim()),L=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,B={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},F={...B,transform:t=>clamp(0,1,t)},I={...B,default:1},sanitize=t=>Math.round(1e5*t)/1e5,N=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const O=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,isColorString=(t,e)=>i=>Boolean("string"==typeof i&&O.test(i)&&i.startsWith(t)||e&&!function(t){return null==t}(i)&&Object.prototype.hasOwnProperty.call(i,e)),splitColor=(t,e,i)=>n=>{if("string"!=typeof n)return n;const[s,o,r,a]=n.match(N);return{[t]:parseFloat(s),[e]:parseFloat(o),[i]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},U={...B,transform:t=>Math.round((t=>clamp(0,255,t))(t))},W={test:isColorString("rgb","red"),parse:splitColor("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+U.transform(t)+", "+U.transform(e)+", "+U.transform(i)+", "+sanitize(F.transform(n))+")"};const $={test:isColorString("#"),parse:function(t){let e="",i="",n="",s="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),s=t.substring(4,5),e+=e,i+=i,n+=n,s+=s),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:s?parseInt(s,16)/255:1}},transform:W.transform},createUnitType=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),z=createUnitType("deg"),H=createUnitType("%"),K=createUnitType("px"),G=createUnitType("vh"),Y=createUnitType("vw"),X=(()=>({...H,parse:t=>H.parse(t)/100,transform:t=>H.transform(100*t)}))(),q={test:isColorString("hsl","hue"),parse:splitColor("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+H.transform(sanitize(e))+", "+H.transform(sanitize(i))+", "+sanitize(F.transform(n))+")"},Z={test:t=>W.test(t)||$.test(t)||q.test(t),parse:t=>W.test(t)?W.parse(t):q.test(t)?q.parse(t):$.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?W.transform(t):q.transform(t),getAnimatableNone:t=>{const e=Z.parse(t);return e.alpha=0,Z.transform(e)}},_=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const J="number",Q="color",tt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function analyseComplexValue(t){const e=t.toString(),i=[],n={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(tt,t=>(Z.test(t)?(n.color.push(o),s.push(Q),i.push(Z.parse(t))):t.startsWith("var(")?(n.var.push(o),s.push("var"),i.push(t)):(n.number.push(o),s.push(J),i.push(parseFloat(t))),++o,"${}")).split("${}");return{values:i,split:r,indexes:n,types:s}}function parseComplexValue(t){return analyseComplexValue(t).values}function createTransformer(t){const{split:e,types:i}=analyseComplexValue(t),n=e.length;return t=>{let s="";for(let o=0;o<n;o++)if(s+=e[o],void 0!==t[o]){const e=i[o];s+=e===J?sanitize(t[o]):e===Q?Z.transform(t[o]):t[o]}return s}}const convertNumbersToZero=t=>"number"==typeof t?0:Z.test(t)?Z.getAnimatableNone(t):t;const et={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(N)?.length||0)+(t.match(_)?.length||0)>0},parse:parseComplexValue,createTransformer:createTransformer,getAnimatableNone:function(t){const e=parseComplexValue(t);return createTransformer(t)(e.map(convertNumbersToZero))}};function hueToRgb(t,e,i){return i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function mixImmediate(t,e){return i=>i>0?e:t}const mixNumber$1=(t,e,i)=>t+(e-t)*i,mixLinearColor=(t,e,i)=>{const n=t*t,s=i*(e*e-n)+n;return s<0?0:Math.sqrt(s)},it=[$,W,q];function asRGBA(t){const e=(i=t,it.find(t=>t.test(i)));var i;if(!Boolean(e))return!1;let n=e.parse(t);return e===q&&(n=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let s=0,o=0,r=0;if(e/=100){const n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;s=hueToRgb(a,n,t+1/3),o=hueToRgb(a,n,t),r=hueToRgb(a,n,t-1/3)}else s=o=r=i;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:n}}(n)),n}const mixColor=(t,e)=>{const i=asRGBA(t),n=asRGBA(e);if(!i||!n)return mixImmediate(t,e);const s={...i};return t=>(s.red=mixLinearColor(i.red,n.red,t),s.green=mixLinearColor(i.green,n.green,t),s.blue=mixLinearColor(i.blue,n.blue,t),s.alpha=mixNumber$1(i.alpha,n.alpha,t),W.transform(s))},nt=new Set(["none","hidden"]);function mixNumber(t,e){return i=>mixNumber$1(t,e,i)}function getMixer(t){return"number"==typeof t?mixNumber:"string"==typeof t?isCSSVariableToken(t)?mixImmediate:Z.test(t)?mixColor:mixComplex:Array.isArray(t)?mixArray:"object"==typeof t?Z.test(t)?mixColor:mixObject:mixImmediate}function mixArray(t,e){const i=[...t],n=i.length,s=t.map((t,i)=>getMixer(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=s[e](t);return i}}function mixObject(t,e){const i={...t,...e},n={};for(const s in i)void 0!==t[s]&&void 0!==e[s]&&(n[s]=getMixer(t[s])(t[s],e[s]));return t=>{for(const e in n)i[e]=n[e](t);return i}}const mixComplex=(t,e)=>{const i=et.createTransformer(e),n=analyseComplexValue(t),s=analyseComplexValue(e);return n.indexes.var.length===s.indexes.var.length&&n.indexes.color.length===s.indexes.color.length&&n.indexes.number.length>=s.indexes.number.length?nt.has(t)&&!s.values.length||nt.has(e)&&!n.values.length?function(t,e){return nt.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):pipe(mixArray(function(t,e){const i=[],n={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){const o=e.types[s],r=t.indexes[o][n[o]],a=t.values[r]??0;i[s]=a,n[o]++}return i}(n,s),s.values),i):mixImmediate(t,e)};function mix(t,e,i){if("number"==typeof t&&"number"==typeof e&&"number"==typeof i)return mixNumber$1(t,e,i);return getMixer(t)(t,e)}const frameloopDriver=t=>{const passTimestamp=({timestamp:e})=>t(e);return{start:(t=!0)=>V.update(passTimestamp,t),stop:()=>E(passTimestamp),now:()=>M.isProcessing?M.timestamp:j.now()}},generateLinearEasing=(t,e,i=10)=>{let n="";const s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)n+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`},st=2e4;function calcGeneratorDuration(t){let e=0;let i=t.next(e);for(;!i.done&&e<st;)e+=50,i=t.next(e);return e>=st?1/0:e}function calcGeneratorVelocity(t,e,i){const n=Math.max(e-5,0);return velocityPerSecond(i-t(n),e-n)}const ot=100,rt=10,at=1,lt=0,ct=800,ut=.3,ht=.3,dt={granular:.01,default:2},mt={granular:.005,default:.5},pt=.01,ft=10,gt=.05,yt=1,vt=.001;function findSpring({duration:t=ct,bounce:e=ut,velocity:i=lt,mass:n=at}){let s,o,r=1-e;r=clamp(gt,yt,r),t=clamp(pt,ft,millisecondsToSeconds(t)),r<1?(s=e=>{const n=e*r,s=n*t,o=n-i,a=calcAngularFreq(e,r),l=Math.exp(-s);return vt-o/a*l},o=e=>{const n=e*r*t,o=n*i+i,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-n),c=calcAngularFreq(Math.pow(e,2),r);return(-s(e)+vt>0?-1:1)*((o-a)*l)/c}):(s=e=>Math.exp(-e*t)*((e-i)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(i-e)));const a=function(t,e,i){let n=i;for(let i=1;i<xt;i++)n-=t(n)/e(n);return n}(s,o,5/t);if(t=secondsToMilliseconds(t),isNaN(a))return{stiffness:ot,damping:rt,duration:t};{const e=Math.pow(a,2)*n;return{stiffness:e,damping:2*r*Math.sqrt(n*e),duration:t}}}const xt=12;function calcAngularFreq(t,e){return t*Math.sqrt(1-e*e)}const Tt=["duration","bounce"],Pt=["stiffness","damping","mass"];function isSpringType(t,e){return e.some(e=>void 0!==t[e])}function spring(t=ht,e=ut){const i="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:n,restDelta:s}=i;const o=i.keyframes[0],r=i.keyframes[i.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:c,mass:u,duration:h,velocity:d,isResolvedFromDuration:m}=function(t){let e={velocity:lt,stiffness:ot,damping:rt,mass:at,isResolvedFromDuration:!1,...t};if(!isSpringType(t,Pt)&&isSpringType(t,Tt))if(t.visualDuration){const i=t.visualDuration,n=2*Math.PI/(1.2*i),s=n*n,o=2*clamp(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:at,stiffness:s,damping:o}}else{const i=findSpring(t);e={...e,...i,mass:at},e.isResolvedFromDuration=!0}return e}({...i,velocity:-millisecondsToSeconds(i.velocity||0)}),p=d||0,f=c/(2*Math.sqrt(l*u)),g=r-o,y=millisecondsToSeconds(Math.sqrt(l/u)),v=Math.abs(g)<5;let x;if(n||(n=v?dt.granular:dt.default),s||(s=v?mt.granular:mt.default),f<1){const t=calcAngularFreq(y,f);x=e=>{const i=Math.exp(-f*y*e);return r-i*((p+f*y*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===f)x=t=>r-Math.exp(-y*t)*(g+(p+y*g)*t);else{const t=y*Math.sqrt(f*f-1);x=e=>{const i=Math.exp(-f*y*e),n=Math.min(t*e,300);return r-i*((p+f*y*g)*Math.sinh(n)+t*g*Math.cosh(n))/t}}const T={calculatedDuration:m&&h||null,next:t=>{const e=x(t);if(m)a.done=t>=h;else{let i=0===t?p:0;f<1&&(i=0===t?secondsToMilliseconds(p):calcGeneratorVelocity(x,t,e));const o=Math.abs(i)<=n,l=Math.abs(r-e)<=s;a.done=o&&l}return a.value=a.done?r:e,a},toString:()=>{const t=Math.min(calcGeneratorDuration(T),st),e=generateLinearEasing(e=>T.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return T}function inertia({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:c=.5,restSpeed:u}){const h=t[0],d={done:!1,value:h},nearestBoundary=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=i*e;const p=h+m,f=void 0===r?p:r(p);f!==p&&(m=f-h);const calcDelta=t=>-m*Math.exp(-t/n),calcLatest=t=>f+calcDelta(t),applyFriction=t=>{const e=calcDelta(t),i=calcLatest(t);d.done=Math.abs(e)<=c,d.value=d.done?f:i};let g,y;const checkCatchBoundary=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(g=t,y=spring({keyframes:[d.value,nearestBoundary(d.value)],velocity:calcGeneratorVelocity(calcLatest,t,d.value),damping:s,stiffness:o,restDelta:c,restSpeed:u}))};return checkCatchBoundary(0),{calculatedDuration:null,next:t=>{let e=!1;return y||void 0!==g||(e=!0,applyFriction(t),checkCatchBoundary(t)),void 0!==g&&t>=g?y.next(t-g):(!e&&applyFriction(t),d)}}}function interpolate(t,e,{clamp:i=!0,ease:n,mixer:s}={}){const o=t.length;if(e.length,1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,i){const n=[],s=i||f.mix||mix,o=t.length-1;for(let i=0;i<o;i++){let o=s(t[i],t[i+1]);if(e){const t=Array.isArray(e)?e[i]||noop:e;o=pipe(t,o)}n.push(o)}return n}(e,n,s),l=a.length,interpolator=i=>{if(r&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);const s=progress(t[n],t[n+1],i);return a[n](s)};return i?e=>interpolator(clamp(t[0],t[o-1],e)):interpolator}function defaultOffset(t){const e=[0];return function(t,e){const i=t[t.length-1];for(let n=1;n<=e;n++){const s=progress(0,e,n);t.push(mixNumber$1(i,1,s))}}(e,t.length-1),e}function keyframes({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){const s=(t=>Array.isArray(t)&&"number"!=typeof t[0])(n)?n.map(easingDefinitionToFunction):easingDefinitionToFunction(n),o={done:!1,value:e[0]},r=function(t,e){return t.map(t=>t*e)}(i&&i.length===e.length?i:defaultOffset(e),t),a=interpolate(r,e,{ease:Array.isArray(s)?s:(l=e,c=s,l.map(()=>c||A).splice(0,l.length-1))});var l,c;return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}spring.applyToOptions=t=>{const e=function(t,e=100,i){const n=i({...t,keyframes:[0,e]}),s=Math.min(calcGeneratorDuration(n),st);return{type:"keyframes",ease:t=>n.next(s*t).value/e,duration:millisecondsToSeconds(s)}}(t,100,spring);return t.ease=e.ease,t.duration=secondsToMilliseconds(e.duration),t.type="keyframes",t};const isNotNull$1=t=>null!==t;function getFinalKeyframe$1(t,{repeat:e,repeatType:i="loop"},n,s=1){const o=t.filter(isNotNull$1),r=s<0||e&&"loop"!==i&&e%2==1?0:o.length-1;return r&&void 0!==n?n:o[r]}const bt={decay:inertia,inertia:inertia,tween:keyframes,keyframes:keyframes,spring:spring};function replaceTransitionType(t){"string"==typeof t.type&&(t.type=bt[t.type])}class WithPromise{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const percentToProgress=t=>t/100;class JSAnimation extends WithPromise{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;t&&t.updatedAt!==j.now()&&this.tick(j.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;replaceTransitionType(t);const{type:e=keyframes,repeat:i=0,repeatDelay:n=0,repeatType:s,velocity:o=0}=t;let{keyframes:r}=t;const a=e||keyframes;a!==keyframes&&"number"!=typeof r[0]&&(this.mixKeyframes=pipe(percentToProgress,mix(r[0],r[1])),r=[0,100]);const l=a({...t,keyframes:r});"mirror"===s&&(this.mirroredGenerator=a({...t,keyframes:[...r].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=calcGeneratorDuration(l));const{calculatedDuration:c}=l;this.calculatedDuration=c,this.resolvedDuration=c+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:i,totalDuration:n,mixKeyframes:s,mirroredGenerator:o,resolvedDuration:r,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);const{delay:l=0,keyframes:c,repeat:u,repeatType:h,repeatDelay:d,type:m,onUpdate:p,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,x=i;if(u){const t=Math.min(this.currentTime,n)/r;let e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,e=Math.min(e,u+1);Boolean(e%2)&&("reverse"===h?(i=1-i,d&&(i-=d/r)):"mirror"===h&&(x=o)),v=clamp(0,1,i)*r}const T=y?{done:!1,value:c[0]}:x.next(v);s&&(T.value=s(T.value));let{done:P}=T;y||null===a||(P=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);const b=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return b&&m!==inertia&&(T.value=getFinalKeyframe$1(c,this.options,f,this.speed)),p&&p(T.value),b&&this.finish(),T}then(t,e){return this.finished.then(t,e)}get duration(){return millisecondsToSeconds(this.calculatedDuration)}get time(){return millisecondsToSeconds(this.currentTime)}set time(t){t=secondsToMilliseconds(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(j.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=millisecondsToSeconds(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=frameloopDriver,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();const i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(j.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}const radToDeg=t=>180*t/Math.PI,rotate=t=>{const e=radToDeg(Math.atan2(t[1],t[0]));return rebaseAngle(e)},At={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:rotate,rotateZ:rotate,skewX:t=>radToDeg(Math.atan(t[1])),skewY:t=>radToDeg(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},rebaseAngle=t=>((t%=360)<0&&(t+=360),t),scaleX=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),scaleY=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),St={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:scaleX,scaleY:scaleY,scale:t=>(scaleX(t)+scaleY(t))/2,rotateX:t=>rebaseAngle(radToDeg(Math.atan2(t[6],t[5]))),rotateY:t=>rebaseAngle(radToDeg(Math.atan2(-t[2],t[0]))),rotateZ:rotate,rotate:rotate,skewX:t=>radToDeg(Math.atan(t[4])),skewY:t=>radToDeg(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function defaultTransformValue(t){return t.includes("scale")?1:0}function parseValueFromTransform(t,e){if(!t||"none"===t)return defaultTransformValue(e);const i=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let n,s;if(i)n=St,s=i;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=At,s=e}if(!s)return defaultTransformValue(e);const o=n[e],r=s[1].split(",").map(convertTransformToNumber);return"function"==typeof o?o(r):r[o]}function convertTransformToNumber(t){return parseFloat(t.trim())}const wt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Vt=(()=>new Set(wt))(),isNumOrPxType=t=>t===B||t===K,Et=new Set(["x","y","z"]),Mt=wt.filter(t=>!Et.has(t));const Dt={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>parseValueFromTransform(e,"x"),y:(t,{transform:e})=>parseValueFromTransform(e,"y")};Dt.translateX=Dt.x,Dt.translateY=Dt.y;const Ct=new Set;let jt=!1,Rt=!1,kt=!1;function measureAllKeyframes(){if(Rt){const t=Array.from(Ct).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{const e=function(t){const e=[];return Mt.forEach(i=>{const n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(i.startsWith("scale")?1:0))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}Rt=!1,jt=!1,Ct.forEach(t=>t.complete(kt)),Ct.clear()}function readAllKeyframes(){Ct.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Rt=!0)})}class KeyframeResolver{constructor(t,e,i,n,s,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=s,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(Ct.add(this),jt||(jt=!0,V.read(readAllKeyframes),V.resolveKeyframes(measureAllKeyframes))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){const s=n?.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){const n=i.readValue(e,o);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=o),n&&void 0===s&&n.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),Ct.delete(this)}cancel(){"scheduled"===this.state&&(Ct.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const Lt=memo(()=>void 0!==window.ScrollTimeline),Bt={};function memoSupports(t,e){const i=memo(t);return()=>Bt[e]??i()}const Ft=memoSupports(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),cubicBezierAsString=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,It={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:cubicBezierAsString([0,.65,.55,1]),circOut:cubicBezierAsString([.55,0,1,.45]),backIn:cubicBezierAsString([.31,.01,.66,-.59]),backOut:cubicBezierAsString([.33,1.53,.69,.99])};function mapEasingToNativeEasing(t,e){return t?"function"==typeof t?Ft()?generateLinearEasing(t,e):"ease-out":isBezierDefinition(t)?cubicBezierAsString(t):Array.isArray(t)?t.map(t=>mapEasingToNativeEasing(t,e)||It.easeOut):It[t]:void 0}function startWaapiAnimation(t,e,i,{delay:n=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},c=void 0){const u={[e]:i};l&&(u.offset=l);const h=mapEasingToNativeEasing(a,s);Array.isArray(h)&&(u.easing=h);const d={delay:n,duration:s,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"};c&&(d.pseudoElement=c);return t.animate(u,d)}function isGenerator(t){return"function"==typeof t&&"applyToOptions"in t}class NativeAnimation extends WithPromise{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:i,keyframes:n,pseudoElement:s,allowFlatten:o=!1,finalKeyframe:r,onComplete:a}=t;this.isPseudoElement=Boolean(s),this.allowFlatten=o,this.options=t,t.type;const l=function({type:t,...e}){return isGenerator(t)&&Ft()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=startWaapiAnimation(e,i,n,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){const t=getFinalKeyframe$1(n,this.options,r,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){(t=>t.startsWith("--"))(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return millisecondsToSeconds(Number(t))}get time(){return millisecondsToSeconds(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=secondsToMilliseconds(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&Lt()?(this.animation.timeline=t,noop):e(this)}}const Nt={anticipate:anticipate,backInOut:v,circInOut:T};function replaceStringEasing(t){"string"==typeof t.ease&&t.ease in Nt&&(t.ease=Nt[t.ease])}class NativeAnimationExtended extends NativeAnimation{constructor(t){replaceStringEasing(t),replaceTransitionType(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:i,onComplete:n,element:s,...o}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const r=new JSAnimation({...o,autoplay:!1}),a=secondsToMilliseconds(this.finishedTime??this.time);e.setWithVelocity(r.sample(a-10).value,r.sample(a).value,10),r.stop()}}const isAnimatable=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!et.test(t)&&"0"!==t||t.startsWith("url(")));function isHTMLElement(t){return isObject(t)&&"offsetHeight"in t}const Ot=new Set(["opacity","clipPath","filter","transform"]),Ut=memo(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class AsyncMotionValueAnimation extends WithPromise{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:o="loop",keyframes:r,name:a,motionValue:l,element:c,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=j.now();const h={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:s,repeatType:o,name:a,motionValue:l,element:c,...u},d=c?.KeyframeResolver||KeyframeResolver;this.keyframeResolver=new d(r,(t,e,i)=>this.onKeyframesResolved(t,e,h,!i),a,l,c),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;const{name:s,type:o,velocity:r,delay:a,isHandoff:l,onUpdate:c}=i;this.resolvedAt=j.now(),function(t,e,i,n){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=isAnimatable(s,e),a=isAnimatable(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||isGenerator(i))&&n)}(t,s,o,r)||(!f.instantAnimations&&a||c?.(getFinalKeyframe$1(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);const u={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},h=!l&&function(t){const{motionValue:e,name:i,repeatDelay:n,repeatType:s,damping:o,type:r}=t;if(!isHTMLElement(e?.owner?.current))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return Ut()&&i&&Ot.has(i)&&("transform"!==i||!l)&&!a&&!n&&"mirror"!==s&&0!==o&&"inertia"!==r}(u)?new NativeAnimationExtended({...u,element:u.motionValue.owner.current}):new JSAnimation(u);h.finished.then(()=>this.notifyFinished()).catch(noop),this.pendingTimeline&&(this.stopTimeline=h.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=h}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),kt=!0,readAllKeyframes(),measureAllKeyframes(),kt=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const Wt=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function getVariableValue(t,e,i=1){const[n,s]=function(t){const e=Wt.exec(t);if(!e)return[,];const[,i,n,s]=e;return[`--${i??n}`,s]}(t);if(!n)return;const o=window.getComputedStyle(e).getPropertyValue(n);if(o){const t=o.trim();return isNumericalString(t)?parseFloat(t):t}return isCSSVariableToken(s)?getVariableValue(s,e,i+1):s}function getValueTransition(t,e){return t?.[e]??t?.default??t}const $t=new Set(["width","height","top","left","right","bottom",...wt]),testValueType=t=>e=>e.test(t),zt=[B,K,H,z,Y,G,{test:t=>"auto"===t,parse:t=>t}],findDimensionValueType=t=>zt.find(testValueType(t));function isNone(t){return"number"==typeof t?0===t:null===t||("none"===t||"0"===t||isZeroValueString(t))}const Ht=new Set(["brightness","contrast","saturate","opacity"]);function applyDefaultFilter(t){const[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[n]=i.match(N)||[];if(!n)return t;const s=i.replace(n,"");let o=Ht.has(e)?1:0;return n!==i&&(o*=100),e+"("+o+s+")"}const Kt=/\b([a-z-]*)\(.*?\)/gu,Gt={...et,getAnimatableNone:t=>{const e=t.match(Kt);return e?e.map(applyDefaultFilter).join(" "):t}},Yt={...B,transform:Math.round},Xt={borderWidth:K,borderTopWidth:K,borderRightWidth:K,borderBottomWidth:K,borderLeftWidth:K,borderRadius:K,radius:K,borderTopLeftRadius:K,borderTopRightRadius:K,borderBottomRightRadius:K,borderBottomLeftRadius:K,width:K,maxWidth:K,height:K,maxHeight:K,top:K,right:K,bottom:K,left:K,padding:K,paddingTop:K,paddingRight:K,paddingBottom:K,paddingLeft:K,margin:K,marginTop:K,marginRight:K,marginBottom:K,marginLeft:K,backgroundPositionX:K,backgroundPositionY:K,...{rotate:z,rotateX:z,rotateY:z,rotateZ:z,scale:I,scaleX:I,scaleY:I,scaleZ:I,skew:z,skewX:z,skewY:z,distance:K,translateX:K,translateY:K,translateZ:K,x:K,y:K,z:K,perspective:K,transformPerspective:K,opacity:F,originX:X,originY:X,originZ:K},zIndex:Yt,fillOpacity:F,strokeOpacity:F,numOctaves:Yt},qt={...Xt,color:Z,backgroundColor:Z,outlineColor:Z,fill:Z,stroke:Z,borderColor:Z,borderTopColor:Z,borderRightColor:Z,borderBottomColor:Z,borderLeftColor:Z,filter:Gt,WebkitFilter:Gt},getDefaultValueType=t=>qt[t];function getAnimatableNone(t,e){let i=getDefaultValueType(t);return i!==Gt&&(i=et),i.getAnimatableNone?i.getAnimatableNone(e):void 0}const Zt=new Set(["auto","none","0"]);class DOMKeyframesResolver extends KeyframeResolver{constructor(t,e,i,n,s){super(t,e,i,n,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&(n=n.trim(),isCSSVariableToken(n))){const s=getVariableValue(n,e.current);void 0!==s&&(t[i]=s),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!$t.has(i)||2!==t.length)return;const[n,s]=t,o=findDimensionValueType(n),r=findDimensionValueType(s);if(o!==r)if(isNumOrPxType(o)&&isNumOrPxType(r))for(let e=0;e<t.length;e++){const i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else Dt[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++)(null===t[e]||isNone(t[e]))&&i.push(e);i.length&&function(t,e,i){let n,s=0;for(;s<t.length&&!n;){const e=t[s];"string"==typeof e&&!Zt.has(e)&&analyseComplexValue(e).values.length&&(n=t[s]),s++}if(n&&i)for(const s of e)t[s]=getAnimatableNone(i,n)}(t,i,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Dt[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;const n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);const s=i.length-1,o=i[s];i[s]=Dt[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}const getValueAsType=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class MotionValue{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const i=j.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=j.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new SubscriptionManager);const i=this.events[t].add(e);return"change"===t?()=>{i(),V.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=j.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return velocityPerSecond(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function motionValue(t,e){return new MotionValue(t,e)}const{schedule:_t}=createRenderBatcher(queueMicrotask,!1),Jt={x:!1,y:!1};function isDragActive(){return Jt.x||Jt.y}function setupGesture(t,e){const i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document;const n=i?.[t]??e.querySelectorAll(t);return n?Array.from(n):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function isValidHover(t){return!("touch"===t.pointerType||isDragActive())}const isNodeOrChild=(t,e)=>!!e&&(t===e||isNodeOrChild(t,e.parentElement)),isPrimaryPointer=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,Qt=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const te=new WeakSet;function filterEvents(t){return e=>{"Enter"===e.key&&t(e)}}function firePointerEvent(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function isValidPressEvent(t){return isPrimaryPointer(t)&&!isDragActive()}function press(t,e,i={}){const[n,s,o]=setupGesture(t,i),startPress=t=>{const n=t.currentTarget;if(!isValidPressEvent(t))return;te.add(n);const o=e(n,t),onPointerEnd=(t,e)=>{window.removeEventListener("pointerup",onPointerUp),window.removeEventListener("pointercancel",onPointerCancel),te.has(n)&&te.delete(n),isValidPressEvent(t)&&"function"==typeof o&&o(t,{success:e})},onPointerUp=t=>{onPointerEnd(t,n===window||n===document||i.useGlobalTarget||isNodeOrChild(n,t.target))},onPointerCancel=t=>{onPointerEnd(t,!1)};window.addEventListener("pointerup",onPointerUp,s),window.addEventListener("pointercancel",onPointerCancel,s)};return n.forEach(t=>{var e;(i.useGlobalTarget?window:t).addEventListener("pointerdown",startPress,s),isHTMLElement(t)&&(t.addEventListener("focus",t=>((t,e)=>{const i=t.currentTarget;if(!i)return;const n=filterEvents(()=>{if(te.has(i))return;firePointerEvent(i,"down");const t=filterEvents(()=>{firePointerEvent(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>firePointerEvent(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)})(t,s)),e=t,Qt.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),o}function isSVGElement(t){return isObject(t)&&"ownerSVGElement"in t}const isMotionValue=t=>Boolean(t&&t.getVelocity),ee=[...zt,Z,et],ie=t.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class PopChildMeasure extends t.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=e.offsetParent,i=isHTMLElement(t)&&t.offsetWidth||0,n=this.props.sizeRef.current;n.height=e.offsetHeight||0,n.width=e.offsetWidth||0,n.top=e.offsetTop,n.left=e.offsetLeft,n.right=i-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function PopChild({children:i,isPresent:n,anchorX:s}){const o=t.useId(),r=t.useRef(null),a=t.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=t.useContext(ie);return t.useInsertionEffect(()=>{const{width:t,height:e,top:i,left:c,right:u}=a.current;if(n||!r.current||!t||!e)return;const h="left"===s?`left: ${c}`:`right: ${u}`;r.current.dataset.motionPopId=o;const d=document.createElement("style");return l&&(d.nonce=l),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`\n          [data-motion-pop-id="${o}"] {\n            position: absolute !important;\n            width: ${t}px !important;\n            height: ${e}px !important;\n            ${h}px !important;\n            top: ${i}px !important;\n          }\n        `),()=>{document.head.contains(d)&&document.head.removeChild(d)}},[n]),e.jsx(PopChildMeasure,{isPresent:n,childRef:r,sizeRef:a,children:t.cloneElement(i,{ref:r})})}const PresenceChild=({children:i,initial:n,isPresent:s,onExitComplete:o,custom:r,presenceAffectsLayout:a,mode:l,anchorX:c})=>{const u=useConstant(newChildrenMap),h=t.useId();let d=!0,m=t.useMemo(()=>(d=!1,{id:h,initial:n,isPresent:s,custom:r,onExitComplete:t=>{u.set(t,!0);for(const t of u.values())if(!t)return;o&&o()},register:t=>(u.set(t,!1),()=>u.delete(t))}),[s,u,o]);return a&&d&&(m={...m}),t.useMemo(()=>{u.forEach((t,e)=>u.set(e,!1))},[s]),t.useEffect(()=>{!s&&!u.size&&o&&o()},[s]),"popLayout"===l&&(i=e.jsx(PopChild,{isPresent:s,anchorX:c,children:i})),e.jsx(p.Provider,{value:m,children:i})};function newChildrenMap(){return new Map}function usePresence(e=!0){const i=t.useContext(p);if(null===i)return[!0,null];const{isPresent:n,onExitComplete:s,register:o}=i,r=t.useId();t.useEffect(()=>{if(e)return o(r)},[e]);const a=t.useCallback(()=>e&&s&&s(r),[r,s,e]);return!n&&s?[!1,a]:[!0]}const getChildKey=t=>t.key||"";function onlyElements(e){const i=[];return t.Children.forEach(e,e=>{t.isValidElement(e)&&i.push(e)}),i}const AnimatePresence=({children:i,custom:n,initial:s=!0,onExitComplete:o,presenceAffectsLayout:r=!0,mode:a="sync",propagate:l=!1,anchorX:c="left"})=>{const[u,h]=usePresence(l),p=t.useMemo(()=>onlyElements(i),[i]),f=l&&!u?[]:p.map(getChildKey),g=t.useRef(!0),y=t.useRef(p),v=useConstant(()=>new Map),[x,T]=t.useState(p),[P,b]=t.useState(p);m(()=>{g.current=!1,y.current=p;for(let t=0;t<P.length;t++){const e=getChildKey(P[t]);f.includes(e)?v.delete(e):!0!==v.get(e)&&v.set(e,!1)}},[P,f.length,f.join("-")]);const A=[];if(p!==x){let t=[...p];for(let e=0;e<P.length;e++){const i=P[e],n=getChildKey(i);f.includes(n)||(t.splice(e,0,i),A.push(i))}return"wait"===a&&A.length&&(t=A),b(onlyElements(t)),T(p),null}const{forceRender:S}=t.useContext(d);return e.jsx(e.Fragment,{children:P.map(t=>{const i=getChildKey(t),d=!(l&&!u)&&(p===P||f.includes(i));return e.jsx(PresenceChild,{isPresent:d,initial:!(g.current&&!s)&&void 0,custom:n,presenceAffectsLayout:r,mode:a,onExitComplete:d?void 0:()=>{if(!v.has(i))return;v.set(i,!0);let t=!0;v.forEach(e=>{e||(t=!1)}),t&&(S?.(),b(y.current),l&&h?.(),o&&o())},anchorX:c,children:t},i)})})},ne={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},se={};for(const t in ne)se[t]={isEnabled:e=>ne[t].some(t=>!!e[t])};const oe=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function isValidMotionProp(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||oe.has(t)}let shouldForward=t=>!isValidMotionProp(t);try{"function"==typeof(re=require("@emotion/is-prop-valid").default)&&(shouldForward=t=>t.startsWith("on")?!isValidMotionProp(t):re(t))}catch{}var re;function createDOMMotionComponentProxy(t){if("undefined"==typeof Proxy)return t;const e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}const ae=t.createContext({});function isAnimationControls(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function isVariantLabel(t){return"string"==typeof t||Array.isArray(t)}const le=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ce=["initial",...le];function isControllingVariants(t){return isAnimationControls(t.animate)||ce.some(e=>isVariantLabel(t[e]))}function isVariantNode(t){return Boolean(isControllingVariants(t)||t.variants)}function useCreateMotionContext(e){const{initial:i,animate:n}=function(t,e){if(isControllingVariants(t)){const{initial:e,animate:i}=t;return{initial:!1===e||isVariantLabel(e)?e:void 0,animate:isVariantLabel(i)?i:void 0}}return!1!==t.inherit?e:{}}(e,t.useContext(ae));return t.useMemo(()=>({initial:i,animate:n}),[variantLabelsAsDependency(i),variantLabelsAsDependency(n)])}function variantLabelsAsDependency(t){return Array.isArray(t)?t.join(" "):t}const ue=Symbol.for("motionComponentSymbol");function isRefObject(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function useMotionRef(e,i,n){return t.useCallback(t=>{t&&e.onMount&&e.onMount(t),i&&(t?i.mount(t):i.unmount()),n&&("function"==typeof n?n(t):isRefObject(n)&&(n.current=t))},[i])}const camelToDash=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),he="data-"+camelToDash("framerAppearId"),de=t.createContext({});function createRendererMotionComponent({preloadedFeatures:i,createVisualElement:n,useRender:s,useVisualState:o,Component:r}){function MotionComponent(i,n){const a={...t.useContext(ie),...i,layoutId:useLayoutId(i)},{isStatic:l}=a,c=useCreateMotionContext(i),u=o(i,l);return e.jsxs(ae.Provider,{value:c,children:[null,s(r,i,useMotionRef(u,c.visualElement,n),u,l,c.visualElement)]})}i&&function(t){for(const e in t)se[e]={...se[e],...t[e]}}(i),MotionComponent.displayName=`motion.${"string"==typeof r?r:`create(${r.displayName??r.name??""})`}`;const a=t.forwardRef(MotionComponent);return a[ue]=r,a}function useLayoutId({layoutId:e}){const i=t.useContext(d).id;return i&&void 0!==e?i+"-"+e:e}const me={};function isForcedMotionValue(t,{layout:e,layoutId:i}){return Vt.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!me[t]||"opacity"===t)}const pe={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},fe=wt.length;function buildHTMLStyles(t,e,i){const{style:n,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const t in e){const i=e[t];if(Vt.has(t))r=!0;else if(R(t))s[t]=i;else{const e=getValueAsType(i,Xt[t]);t.startsWith("origin")?(a=!0,o[t]=e):n[t]=e}}if(e.transform||(r||i?n.transform=function(t,e,i){let n="",s=!0;for(let o=0;o<fe;o++){const r=wt[o],a=t[r];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(r.startsWith("scale")?1:0):0===parseFloat(a),!l||i){const t=getValueAsType(a,Xt[r]);l||(s=!1,n+=`${pe[r]||r}(${t}) `),i&&(e[r]=t)}}return n=n.trim(),i?n=i(e,s?"":n):s&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:i=0}=o;n.transformOrigin=`${t} ${e} ${i}`}}const createHtmlRenderState=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function copyRawValuesOnly(t,e,i){for(const n in e)isMotionValue(e[n])||isForcedMotionValue(n,i)||(t[n]=e[n])}function useStyle(e,i){const n={};return copyRawValuesOnly(n,e.style||{},e),Object.assign(n,function({transformTemplate:e},i){return t.useMemo(()=>{const t={style:{},transform:{},transformOrigin:{},vars:{}};return buildHTMLStyles(t,i,e),Object.assign({},t.vars,t.style)},[i])}(e,i)),n}function useHTMLProps(t,e){const i={},n=useStyle(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i}const ge={offset:"stroke-dashoffset",array:"stroke-dasharray"},ye={offset:"strokeDashoffset",array:"strokeDasharray"};function buildSVGAttrs(t,{attrX:e,attrY:i,attrScale:n,pathLength:s,pathSpacing:o=1,pathOffset:r=0,...a},l,c,u){if(buildHTMLStyles(t,a,c),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:h,style:d}=t;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox=u?.transformBox??"fill-box",delete h.transformBox),void 0!==e&&(h.x=e),void 0!==i&&(h.y=i),void 0!==n&&(h.scale=n),void 0!==s&&function(t,e,i=1,n=0,s=!0){t.pathLength=1;const o=s?ge:ye;t[o.offset]=K.transform(-n);const r=K.transform(e),a=K.transform(i);t[o.array]=`${r} ${a}`}(h,s,o,r,!1)}const createSvgRenderState=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),isSVGTag=t=>"string"==typeof t&&"svg"===t.toLowerCase();function useSVGProps(e,i,n,s){const o=t.useMemo(()=>{const t={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return buildSVGAttrs(t,i,isSVGTag(s),e.transformTemplate,e.style),{...t.attrs,style:{...t.style}}},[i]);if(e.style){const t={};copyRawValuesOnly(t,e.style,e),o.style={...t,...o.style}}return o}const ve=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function isSVGComponent(t){return"string"==typeof t&&!t.includes("-")&&!!(ve.indexOf(t)>-1||/[A-Z]/u.test(t))}function createUseRender(e=!1){return(i,n,s,{latestValues:o},r)=>{const a=(isSVGComponent(i)?useSVGProps:useHTMLProps)(n,o,r,i),l=function(t,e,i){const n={};for(const s in t)"values"===s&&"object"==typeof t.values||(shouldForward(s)||!0===i&&isValidMotionProp(s)||!e&&!isValidMotionProp(s)||t.draggable&&s.startsWith("onDrag"))&&(n[s]=t[s]);return n}(n,"string"==typeof i,e),c=i!==t.Fragment?{...l,...a,ref:s}:{},{children:u}=n,h=t.useMemo(()=>isMotionValue(u)?u.get():u,[u]);return t.createElement(i,{...c,children:h})}}function getValueState(t){const e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function resolveVariantFromProps(t,e,i,n){if("function"==typeof e){const[s,o]=getValueState(n);e=e(void 0!==i?i:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=getValueState(n);e=e(void 0!==i?i:t.custom,s,o)}return e}function resolveMotionValue(t){return isMotionValue(t)?t.get():t}const makeUseVisualState=e=>(i,n)=>{const s=t.useContext(ae),o=t.useContext(p),make=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,s){return{latestValues:makeLatestValues(i,n,s,t),renderState:e()}}(e,i,s,o);return n?make():useConstant(make)};function makeLatestValues(t,e,i,n){const s={},o=n(t,{});for(const t in o)s[t]=resolveMotionValue(o[t]);let{initial:r,animate:a}=t;const l=isControllingVariants(t),c=isVariantNode(t);e&&c&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let u=!!i&&!1===i.initial;u=u||!1===r;const h=u?a:r;if(h&&"boolean"!=typeof h&&!isAnimationControls(h)){const e=Array.isArray(h)?h:[h];for(let i=0;i<e.length;i++){const n=resolveVariantFromProps(t,e[i]);if(n){const{transitionEnd:t,transition:e,...i}=n;for(const t in i){let e=i[t];if(Array.isArray(e)){e=e[u?e.length-1:0]}null!==e&&(s[t]=e)}for(const e in t)s[e]=t[e]}}}return s}function scrapeMotionValuesFromProps$1(t,e,i){const{style:n}=t,s={};for(const o in n)(isMotionValue(n[o])||e.style&&isMotionValue(e.style[o])||isForcedMotionValue(o,t)||void 0!==i?.getValue(o)?.liveStyle)&&(s[o]=n[o]);return s}const xe={useVisualState:makeUseVisualState({scrapeMotionValuesFromProps:scrapeMotionValuesFromProps$1,createRenderState:createHtmlRenderState})};function scrapeMotionValuesFromProps(t,e,i){const n=scrapeMotionValuesFromProps$1(t,e,i);for(const i in t)if(isMotionValue(t[i])||isMotionValue(e[i])){n[-1!==wt.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]}return n}const Te={useVisualState:makeUseVisualState({scrapeMotionValuesFromProps:scrapeMotionValuesFromProps,createRenderState:createSvgRenderState})};function createMotionComponentFactory(t,e){return function(i,{forwardMotionProps:n}={forwardMotionProps:!1}){return createRendererMotionComponent({...isSVGComponent(i)?Te:xe,preloadedFeatures:t,useRender:createUseRender(n),createVisualElement:e,Component:i})}}function resolveVariant(t,e,i){const n=t.getProps();return resolveVariantFromProps(n,e,void 0!==i?i:n.custom,t)}const isKeyframesTarget=t=>Array.isArray(t);function setMotionValue(t,e,i){t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,motionValue(i))}function resolveFinalValueInKeyframes(t){return isKeyframesTarget(t)?t[t.length-1]||0:t}function addValueToWillChange(t,e){const i=t.getValue("willChange");if(n=i,Boolean(isMotionValue(n)&&n.add))return i.add(e);if(!i&&f.WillChange){const i=new f.WillChange("auto");t.addValue("willChange",i),i.add(e)}var n}function getOptimisedAppearId(t){return t.props[he]}const isNotNull=t=>null!==t;const Pe={type:"spring",stiffness:500,damping:25,restSpeed:10},be={type:"keyframes",duration:.8},Ae={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},getDefaultTransition=(t,{keyframes:e})=>e.length>2?be:Vt.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:Pe:Ae;const animateMotionValue=(t,e,i,n={},s,o)=>r=>{const a=getValueTransition(n,t)||{},l=a.delay||n.delay||0;let{elapsed:c=0}=n;c-=secondsToMilliseconds(l);const u={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length})(a)||Object.assign(u,getDefaultTransition(t,u)),u.duration&&(u.duration=secondsToMilliseconds(u.duration)),u.repeatDelay&&(u.repeatDelay=secondsToMilliseconds(u.repeatDelay)),void 0!==u.from&&(u.keyframes[0]=u.from);let h=!1;if((!1===u.type||0===u.duration&&!u.repeatDelay)&&(u.duration=0,0===u.delay&&(h=!0)),(f.instantAnimations||f.skipAnimations)&&(h=!0,u.duration=0,u.delay=0),u.allowFlatten=!a.type&&!a.ease,h&&!o&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:i="loop"}){const n=t.filter(isNotNull);return n[e&&"loop"!==i&&e%2==1?0:n.length-1]}(u.keyframes,a);if(void 0!==t)return void V.update(()=>{u.onUpdate(t),u.onComplete()})}return a.isSync?new JSAnimation(u):new AsyncMotionValueAnimation(u)};function shouldBlockAnimation({protectedKeys:t,needsAnimating:e},i){const n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}function animateTarget(t,e,{delay:i=0,transitionOverride:n,type:s}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;n&&(o=n);const l=[],c=s&&t.animationState&&t.animationState.getState()[s];for(const e in a){const n=t.getValue(e,t.latestValues[e]??null),s=a[e];if(void 0===s||c&&shouldBlockAnimation(c,e))continue;const r={delay:i,...getValueTransition(o||{},e)},u=n.get();if(void 0!==u&&!n.isAnimating&&!Array.isArray(s)&&s===u&&!r.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){const i=getOptimisedAppearId(t);if(i){const t=window.MotionHandoffAnimation(i,e,V);null!==t&&(r.startTime=t,h=!0)}}addValueToWillChange(t,e),n.start(animateMotionValue(e,n,s,t.shouldReduceMotion&&$t.has(e)?{type:!1}:r,t,h));const d=n.animation;d&&l.push(d)}return r&&Promise.all(l).then(()=>{V.update(()=>{r&&function(t,e){const i=resolveVariant(t,e);let{transitionEnd:n={},transition:s={},...o}=i||{};o={...o,...n};for(const e in o)setMotionValue(t,e,resolveFinalValueInKeyframes(o[e]))}(t,r)})}),l}function animateVariant(t,e,i={}){const n=resolveVariant(t,e,"exit"===i.type?t.presenceContext?.custom:void 0);let{transition:s=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(s=i.transitionOverride);const o=n?()=>Promise.all(animateTarget(t,n,i)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(n=0)=>{const{delayChildren:o=0,staggerChildren:r,staggerDirection:a}=s;return function(t,e,i=0,n=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*n,l=1===s?(t=0)=>t*n:(t=0)=>a-t*n;return Array.from(t.variantChildren).sort(sortByTreeOrder).forEach((t,n)=>{t.notify("AnimationStart",e),r.push(animateVariant(t,e,{...o,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(r)}(t,e,o+n,r,a,i)}:()=>Promise.resolve(),{when:a}=s;if(a){const[t,e]="beforeChildren"===a?[o,r]:[r,o];return t().then(()=>e())}return Promise.all([o(),r(i.delay)])}function sortByTreeOrder(t,e){return t.sortNodePosition(e)}function shallowCompare(t,e){if(!Array.isArray(e))return!1;const i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}const Se=ce.length;function getVariantContext(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&getVariantContext(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let i=0;i<Se;i++){const n=ce[i],s=t.props[n];(isVariantLabel(s)||!1===s)&&(e[n]=s)}return e}const we=[...le].reverse(),Ve=le.length;function animateList(t){return e=>Promise.all(e.map(({animation:e,options:i})=>function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map(e=>animateVariant(t,e,i));n=Promise.all(s)}else if("string"==typeof e)n=animateVariant(t,e,i);else{const s="function"==typeof e?resolveVariant(t,e,i.custom):e;n=Promise.all(animateTarget(t,s,i))}return n.then(()=>{t.notify("AnimationComplete",e)})}(t,e,i)))}function createAnimationState(t){let e=animateList(t),i=createState(),n=!0;const buildResolvedTypeValues=e=>(i,n)=>{const s=resolveVariant(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(s){const{transition:t,transitionEnd:e,...n}=s;i={...i,...n,...e}}return i};function animateChanges(s){const{props:o}=t,r=getVariantContext(t.parent)||{},a=[],l=new Set;let c={},u=1/0;for(let e=0;e<Ve;e++){const h=we[e],d=i[h],m=void 0!==o[h]?o[h]:r[h],p=isVariantLabel(m),f=h===s?d.isActive:null;!1===f&&(u=e);let g=m===r[h]&&m!==o[h]&&p;if(g&&n&&t.manuallyAnimateOnMount&&(g=!1),d.protectedKeys={...c},!d.isActive&&null===f||!m&&!d.prevProp||isAnimationControls(m)||"boolean"==typeof m)continue;const y=checkVariantsDidChange(d.prevProp,m);let v=y||h===s&&d.isActive&&!g&&p||e>u&&p,x=!1;const T=Array.isArray(m)?m:[m];let P=T.reduce(buildResolvedTypeValues(h),{});!1===f&&(P={});const{prevResolvedValues:b={}}=d,A={...b,...P},markToAnimate=e=>{v=!0,l.has(e)&&(x=!0,l.delete(e)),d.needsAnimating[e]=!0;const i=t.getValue(e);i&&(i.liveStyle=!1)};for(const t in A){const e=P[t],i=b[t];if(c.hasOwnProperty(t))continue;let n=!1;n=isKeyframesTarget(e)&&isKeyframesTarget(i)?!shallowCompare(e,i):e!==i,n?null!=e?markToAnimate(t):l.add(t):void 0!==e&&l.has(t)?markToAnimate(t):d.protectedKeys[t]=!0}d.prevProp=m,d.prevResolvedValues=P,d.isActive&&(c={...c,...P}),n&&t.blockInitialAnimation&&(v=!1);v&&(!(g&&y)||x)&&a.push(...T.map(t=>({animation:t,options:{type:h}})))}if(l.size){const e={};if("boolean"!=typeof o.initial){const i=resolveVariant(t,Array.isArray(o.initial)?o.initial[0]:o.initial);i&&i.transition&&(e.transition=i.transition)}l.forEach(i=>{const n=t.getBaseTarget(i),s=t.getValue(i);s&&(s.liveStyle=!0),e[i]=n??null}),a.push({animation:e})}let h=Boolean(a.length);return!n||!1!==o.initial&&o.initial!==o.animate||t.manuallyAnimateOnMount||(h=!1),n=!1,h?e(a):Promise.resolve()}return{animateChanges:animateChanges,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;const s=animateChanges(e);for(const t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=createState(),n=!0}}}function checkVariantsDidChange(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!shallowCompare(e,t)}function createTypeState(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function createState(){return{animate:createTypeState(!0),whileInView:createTypeState(),whileHover:createTypeState(),whileTap:createTypeState(),whileDrag:createTypeState(),whileFocus:createTypeState(),exit:createTypeState()}}class Feature{constructor(t){this.isMounted=!1,this.node=t}update(){}}let Ee=0;const Me={animation:{Feature:class extends Feature{constructor(t){super(t),t.animationState||(t.animationState=createAnimationState(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();isAnimationControls(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends Feature{constructor(){super(...arguments),this.id=Ee++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function addDomEvent(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}function extractEventInfo(t){return{point:{x:t.pageX,y:t.pageY}}}function addPointerEvent(t,e,i,n){return addDomEvent(t,e,(t=>e=>isPrimaryPointer(e)&&t(e,extractEventInfo(e)))(i),n)}function convertBoundingBoxToBox({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function calcLength(t){return t.max-t.min}function calcAxisDelta(t,e,i,n=.5){t.origin=n,t.originPoint=mixNumber$1(e.min,e.max,t.origin),t.scale=calcLength(i)/calcLength(e),t.translate=mixNumber$1(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function calcBoxDelta(t,e,i,n){calcAxisDelta(t.x,e.x,i.x,n?n.originX:void 0),calcAxisDelta(t.y,e.y,i.y,n?n.originY:void 0)}function calcRelativeAxis(t,e,i){t.min=i.min+e.min,t.max=t.min+calcLength(e)}function calcRelativeAxisPosition(t,e,i){t.min=e.min-i.min,t.max=t.min+calcLength(e)}function calcRelativePosition(t,e,i){calcRelativeAxisPosition(t.x,e.x,i.x),calcRelativeAxisPosition(t.y,e.y,i.y)}const createBox=()=>({x:{min:0,max:0},y:{min:0,max:0}});function eachAxis(t){return[t("x"),t("y")]}function isIdentityScale(t){return void 0===t||1===t}function hasScale({scale:t,scaleX:e,scaleY:i}){return!isIdentityScale(t)||!isIdentityScale(e)||!isIdentityScale(i)}function hasTransform(t){return hasScale(t)||has2DTranslate(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function has2DTranslate(t){return is2DTranslate(t.x)||is2DTranslate(t.y)}function is2DTranslate(t){return t&&"0%"!==t}function scalePoint(t,e,i){return i+e*(t-i)}function applyPointDelta(t,e,i,n,s){return void 0!==s&&(t=scalePoint(t,s,n)),scalePoint(t,i,n)+e}function applyAxisDelta(t,e=0,i=1,n,s){t.min=applyPointDelta(t.min,e,i,n,s),t.max=applyPointDelta(t.max,e,i,n,s)}function applyBoxDelta(t,{x:e,y:i}){applyAxisDelta(t.x,e.translate,e.scale,e.originPoint),applyAxisDelta(t.y,i.translate,i.scale,i.originPoint)}const De=.999999999999,Ce=1.0000000000001;function translateAxis(t,e){t.min=t.min+e,t.max=t.max+e}function transformAxis(t,e,i,n,s=.5){applyAxisDelta(t,e,i,mixNumber$1(t.min,t.max,s),n)}function transformBox(t,e){transformAxis(t.x,e.x,e.scaleX,e.scale,e.originX),transformAxis(t.y,e.y,e.scaleY,e.scale,e.originY)}function measureViewportBox(t,e){return convertBoundingBoxToBox(function(t,e){if(!e)return t;const i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}const getContextWindow=({current:t})=>t?t.ownerDocument.defaultView:null,distance=(t,e)=>Math.abs(t-e);class PanSession{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=getPanInfo(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){const i=distance(t.x,e.x),n=distance(t.y,e.y);return Math.sqrt(i**2+n**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;const{point:n}=t,{timestamp:s}=M;this.history.push({...n,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=transformPoint(e,this.transformPagePoint),V.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:i,onSessionEnd:n,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=getPanInfo("pointercancel"===t.type?this.lastMoveEventInfo:transformPoint(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,o),n&&n(t,o)},!isPrimaryPointer(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;const o=transformPoint(extractEventInfo(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=M;this.history=[{...r,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,getPanInfo(o,this.history)),this.removeListeners=pipe(addPointerEvent(this.contextWindow,"pointermove",this.handlePointerMove),addPointerEvent(this.contextWindow,"pointerup",this.handlePointerUp),addPointerEvent(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),E(this.updatePoint)}}function transformPoint(t,e){return e?{point:e(t.point)}:t}function subtractPoint(t,e){return{x:t.x-e.x,y:t.y-e.y}}function getPanInfo({point:t},e){return{point:t,delta:subtractPoint(t,lastDevicePoint(e)),offset:subtractPoint(t,startDevicePoint(e)),velocity:getVelocity(e,.1)}}function startDevicePoint(t){return t[0]}function lastDevicePoint(t){return t[t.length-1]}function getVelocity(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null;const s=lastDevicePoint(t);for(;i>=0&&(n=t[i],!(s.timestamp-n.timestamp>secondsToMilliseconds(e)));)i--;if(!n)return{x:0,y:0};const o=millisecondsToSeconds(s.timestamp-n.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-n.x)/o,y:(s.y-n.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function calcRelativeAxisConstraints(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function calcViewportAxisConstraints(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}const je=.35;function resolveAxisElastic(t,e,i){return{min:resolvePointElastic(t,e),max:resolvePointElastic(t,i)}}function resolvePointElastic(t,e){return"number"==typeof t?t:t[e]||0}const Re=new WeakMap;class VisualElementDragControls{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;const{dragSnapToOrigin:n}=this.getProps();this.panSession=new PanSession(t,{onSessionStart:t=>{const{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(extractEventInfo(t).point)},onStart:(t,e)=>{const{drag:i,dragPropagation:n,onDragStart:s}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(o=i)||"y"===o?Jt[o]?null:(Jt[o]=!0,()=>{Jt[o]=!1}):Jt.x||Jt.y?null:(Jt.x=Jt.y=!0,()=>{Jt.x=Jt.y=!1}),!this.openDragLock))return;var o;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),eachAxis(t=>{let e=this.getAxisMotionValue(t).get()||0;if(H.test(e)){const{projection:i}=this.visualElement;if(i&&i.layout){const n=i.layout.layoutBox[t];if(n){e=calcLength(n)*(parseFloat(e)/100)}}}this.originPoint[t]=e}),s&&V.postRender(()=>s(t,e)),addValueToWillChange(this.visualElement,"transform");const{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:i,dragDirectionLock:n,onDirectionLock:s,onDrag:o}=this.getProps();if(!i&&!this.openDragLock)return;const{offset:r}=e;if(n&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let i=null;Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x");return i}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>eachAxis(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:getContextWindow(this.visualElement)})}stop(t,e){const i=this.isDragging;if(this.cancel(),!i)return;const{velocity:n}=e;this.startAnimation(n);const{onDragEnd:s}=this.getProps();s&&V.postRender(()=>s(t,e))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){const{drag:n}=this.getProps();if(!i||!shouldDrag(t,n,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?mixNumber$1(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?mixNumber$1(i,t,n.max):Math.min(t,i)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){const{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&isRefObject(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!i)&&function(t,{top:e,left:i,bottom:n,right:s}){return{x:calcRelativeAxisConstraints(t.x,i,s),y:calcRelativeAxisConstraints(t.y,e,n)}}(i.layoutBox,t),this.elastic=function(t=je){return!1===t?t=0:!0===t&&(t=je),{x:resolveAxisElastic(t,"left","right"),y:resolveAxisElastic(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&eachAxis(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!isRefObject(t))return!1;const i=t.current,{projection:n}=this.visualElement;if(!n||!n.layout)return!1;const s=function(t,e,i){const n=measureViewportBox(t,i),{scroll:s}=e;return s&&(translateAxis(n.x,s.offset.x),translateAxis(n.y,s.offset.y)),n}(i,n.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:calcViewportAxisConstraints(t.x,e.x),y:calcViewportAxisConstraints(t.y,e.y)}}(n.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=convertBoundingBoxToBox(t))}return o}startAnimation(t){const{drag:e,dragMomentum:i,dragElastic:n,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=eachAxis(r=>{if(!shouldDrag(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const c=n?200:1e6,u=n?40:1e7,h={type:"inertia",velocity:i?t[r]:0,bounceStiffness:c,bounceDamping:u,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,h)});return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const i=this.getAxisMotionValue(t);return addValueToWillChange(this.visualElement,t),i.start(animateMotionValue(t,i,0,e,this.visualElement,!1))}stopAnimation(){eachAxis(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){eachAxis(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps(),n=i[e];return n||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){eachAxis(e=>{const{drag:i}=this.getProps();if(!shouldDrag(e,i,this.currentDirection))return;const{projection:n}=this.visualElement,s=this.getAxisMotionValue(e);if(n&&n.layout){const{min:i,max:o}=n.layout.layoutBox[e];s.set(t[e]-mixNumber$1(i,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!isRefObject(e)||!i||!this.constraints)return;this.stopAnimation();const n={x:0,y:0};eachAxis(t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const i=e.get();n[t]=function(t,e){let i=.5;const n=calcLength(t),s=calcLength(e);return s>n?i=progress(e.min,e.max-n,t.min):n>s&&(i=progress(t.min,t.max-s,e.min)),clamp(0,1,i)}({min:i,max:i},this.constraints[t])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),eachAxis(e=>{if(!shouldDrag(e,t,null))return;const i=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];i.set(mixNumber$1(s,o,n[e]))})}addListeners(){if(!this.visualElement.current)return;Re.set(this.visualElement,this);const t=addPointerEvent(this.visualElement.current,"pointerdown",t=>{const{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),measureDragConstraints=()=>{const{dragConstraints:t}=this.getProps();isRefObject(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:e}=this.visualElement,i=e.addEventListener("measure",measureDragConstraints);e&&!e.layout&&(e.root&&e.root.updateScroll(),e.updateLayout()),V.read(measureDragConstraints);const n=addDomEvent(window,"resize",()=>this.scalePositionWithinConstraints()),s=e.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(eachAxis(e=>{const i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),i(),s&&s()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:s=!1,dragElastic:o=je,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function shouldDrag(t,e,i){return!(!0!==e&&e!==t||null!==i&&i!==t)}const asyncHandler=t=>(e,i)=>{t&&V.postRender(()=>t(e,i))};const ke={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function pixelsToPercent(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Le={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!K.test(t))return t;t=parseFloat(t)}return`${pixelsToPercent(t,e.target.x)}% ${pixelsToPercent(t,e.target.y)}%`}},Be={correct:(t,{treeScale:e,projectionDelta:i})=>{const n=t,s=et.parse(t);if(s.length>5)return n;const o=et.createTransformer(t),r="number"!=typeof s[0]?1:0,a=i.x.scale*e.x,l=i.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const c=mixNumber$1(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=c),"number"==typeof s[3+r]&&(s[3+r]/=c),o(s)}};class MeasureLayoutWithContext extends t.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:s}=t;!function(t){for(const e in t)me[e]=t[e],R(e)&&(me[e].isCSSVariable=!0)}(Fe),s&&(e.group&&e.group.add(s),i&&i.register&&n&&i.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),ke.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:i,drag:n,isPresent:s}=this.props,{projection:o}=i;return o?(o.isPresent=s,n||t.layoutDependency!==e||void 0===e||t.isPresent!==s?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||V.postRender(()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),_t.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function MeasureLayout(i){const[n,s]=usePresence(),o=t.useContext(d);return e.jsx(MeasureLayoutWithContext,{...i,layoutGroup:o,switchLayoutGroup:t.useContext(de),isPresent:n,safeToRemove:s})}const Fe={borderRadius:{...Le,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Le,borderTopRightRadius:Le,borderBottomLeftRadius:Le,borderBottomRightRadius:Le,boxShadow:Be};const compareByDepth=(t,e)=>t.depth-e.depth;class FlatTree{constructor(){this.children=[],this.isDirty=!1}add(t){addUniqueItem(this.children,t),this.isDirty=!0}remove(t){removeItem(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(compareByDepth),this.isDirty=!1,this.children.forEach(t)}}const Ie=["TopLeft","TopRight","BottomLeft","BottomRight"],Ne=Ie.length,asNumber=t=>"string"==typeof t?parseFloat(t):t,isPx=t=>"number"==typeof t||K.test(t);function getRadius(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const Oe=compress(0,.5,x),Ue=compress(.5,.95,noop);function compress(t,e,i){return n=>n<t?0:n>e?1:i(progress(t,e,n))}function copyAxisInto(t,e){t.min=e.min,t.max=e.max}function copyBoxInto(t,e){copyAxisInto(t.x,e.x),copyAxisInto(t.y,e.y)}function copyAxisDeltaInto(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function removePointDelta(t,e,i,n,s){return t=scalePoint(t-=e,1/i,n),void 0!==s&&(t=scalePoint(t,1/s,n)),t}function removeAxisTransforms(t,e,[i,n,s],o,r){!function(t,e=0,i=1,n=.5,s,o=t,r=t){H.test(e)&&(e=parseFloat(e),e=mixNumber$1(r.min,r.max,e/100)-r.min);if("number"!=typeof e)return;let a=mixNumber$1(o.min,o.max,n);t===o&&(a-=e),t.min=removePointDelta(t.min,e,i,a,s),t.max=removePointDelta(t.max,e,i,a,s)}(t,e[i],e[n],e[s],e.scale,o,r)}const We=["x","scaleX","originX"],$e=["y","scaleY","originY"];function removeBoxTransforms(t,e,i,n){removeAxisTransforms(t.x,e,We,i?i.x:void 0,n?n.x:void 0),removeAxisTransforms(t.y,e,$e,i?i.y:void 0,n?n.y:void 0)}function isAxisDeltaZero(t){return 0===t.translate&&1===t.scale}function isDeltaZero(t){return isAxisDeltaZero(t.x)&&isAxisDeltaZero(t.y)}function axisEquals(t,e){return t.min===e.min&&t.max===e.max}function axisEqualsRounded(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function boxEqualsRounded(t,e){return axisEqualsRounded(t.x,e.x)&&axisEqualsRounded(t.y,e.y)}function aspectRatio(t){return calcLength(t.x)/calcLength(t.y)}function axisDeltaEquals(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class NodeStack{constructor(){this.members=[]}add(t){addUniqueItem(this.members,t),t.scheduleRender()}remove(t){if(removeItem(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex(e=>t===e);if(0===e)return!1;let i;for(let t=e;t>=0;t--){const e=this.members[t];if(!1!==e.isPresent){i=e;break}}return!!i&&(this.promote(i),!0)}promote(t,e){const i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const ze=["","X","Y","Z"],He={visibility:"hidden"};let Ke=0;function resetDistortingTransform(t,e,i,n){const{latestValues:s}=e;s[t]&&(i[t]=s[t],e.setStaticValue(t,0),n&&(n[t]=0))}function cancelTreeOptimisedTransformAnimations(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const i=getOptimisedAppearId(e);if(window.MotionHasOptimisedAnimation(i,"transform")){const{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(i,"transform",V,!(e||n))}const{parent:n}=t;n&&!n.hasCheckedOptimisedAppear&&cancelTreeOptimisedTransformAnimations(n)}function createProjectionNode({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:s}){return class{constructor(t={},i=e?.()){this.id=Ke++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(propagateDirtyNodes),this.nodes.forEach(resolveTargetDelta),this.nodes.forEach(calcProjection),this.nodes.forEach(cleanDirtyNodes)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new FlatTree)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new SubscriptionManager),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;var i;this.isSVG=isSVGElement(e)&&!(isSVGElement(i=e)&&"svg"===i.tagName),this.instance=e;const{layoutId:n,layout:s,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||n)&&(this.isLayoutDirty=!0),t){let i;const resizeUnblockUpdate=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){const i=j.now(),checkElapsed=({timestamp:n})=>{const s=n-i;s>=e&&(E(checkElapsed),t(s-e))};return V.setup(checkElapsed,!0),()=>E(checkElapsed)}(resizeUnblockUpdate,250),ke.hasAnimatedSinceResize&&(ke.hasAnimatedSinceResize=!1,this.nodes.forEach(finishAnimation))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&o&&(n||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const s=this.options.transition||o.getDefaultTransition()||Ge,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!boxEqualsRounded(this.targetLayout,n),c=!e&&i;if(this.options.layoutRoot||this.resumeFrom||c||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const e={...getValueTransition(s,"layout"),onPlay:r,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,c)}else e||finishAnimation(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),E(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(resetSkewAndRotation),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&cancelTreeOptimisedTransformAnimations(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){const e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;const n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(clearMeasurements);if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(clearIsLayoutDirty);this.isUpdating||this.nodes.forEach(clearIsLayoutDirty),this.animationCommitId=this.animationId,this.isUpdating=!1,this.nodes.forEach(resetTransformStyle),this.nodes.forEach(updateLayout),this.nodes.forEach(notifyLayoutUpdate),this.clearAllSnapshots();const t=j.now();M.delta=clamp(0,1e3/60,t-M.timestamp),M.timestamp=t,M.isProcessing=!0,D.update.process(M),D.preRender.process(M),D.render.process(M),M.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,_t.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(clearSnapshot),this.sharedNodes.forEach(removeLeadSnapshots)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,V.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){V.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||calcLength(this.snapshot.measuredBox.x)||calcLength(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++){this.path[t].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){const e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!isDeltaZero(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,o=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||hasTransform(this.latestValues)||o)&&(s(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let i=this.removeElementScroll(e);var n;return t&&(i=this.removeTransform(i)),roundAxis((n=i).x),roundAxis(n.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:i,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(checkNodeWasScrollRoot))){const{scroll:t}=this.root;t&&(translateAxis(e.x,t.offset.x),translateAxis(e.y,t.offset.y))}return e}removeElementScroll(t){const e={x:{min:0,max:0},y:{min:0,max:0}};if(copyBoxInto(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){const n=this.path[i],{scroll:s,options:o}=n;n!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&copyBoxInto(e,t),translateAxis(e.x,s.offset.x),translateAxis(e.y,s.offset.y))}return e}applyTransform(t,e=!1){const i={x:{min:0,max:0},y:{min:0,max:0}};copyBoxInto(i,t);for(let t=0;t<this.path.length;t++){const n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&transformBox(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),hasTransform(n.latestValues)&&transformBox(i,n.latestValues)}return hasTransform(this.latestValues)&&transformBox(i,this.latestValues),i}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};copyBoxInto(e,t);for(let t=0;t<this.path.length;t++){const i=this.path[t];if(!i.instance)continue;if(!hasTransform(i.latestValues))continue;hasScale(i.latestValues)&&i.updateSnapshot();const n=createBox();copyBoxInto(n,i.measurePageBox()),removeBoxTransforms(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return hasTransform(this.latestValues)&&removeBoxTransforms(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==M.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){const e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);const i=Boolean(this.resumingFrom)||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:n,layoutId:s}=this.options;if(this.layout&&(n||s)){if(this.resolvedRelativeTargetAt=M.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},calcRelativePosition(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),copyBoxInto(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}var o,r,a;if(this.relativeTarget||this.targetDelta)if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),o=this.target,r=this.relativeTarget,a=this.relativeParent.target,calcRelativeAxis(o.x,r.x,a.x),calcRelativeAxis(o.y,r.y,a.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):copyBoxInto(this.target,this.layout.layoutBox),applyBoxDelta(this.target,this.targetDelta)):copyBoxInto(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},calcRelativePosition(this.relativeTargetOrigin,this.target,t.target),copyBoxInto(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(this.parent&&!hasScale(this.parent.latestValues)&&!has2DTranslate(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const t=this.getLead(),e=Boolean(this.resumingFrom)||this!==t;let i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===M.timestamp&&(i=!1),i)return;const{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!n&&!s)return;copyBoxInto(this.layoutCorrected,this.layout.layoutBox);const o=this.treeScale.x,r=this.treeScale.y;!function(t,e,i,n=!1){const s=i.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=i[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(n&&o.options.layoutScroll&&o.scroll&&o!==o.root&&transformBox(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,applyBoxDelta(t,r)),n&&hasTransform(o.latestValues)&&transformBox(t,o.latestValues))}e.x<Ce&&e.x>De&&(e.x=1),e.y<Ce&&e.y>De&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,e),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:a}=t;a?(this.projectionDelta&&this.prevProjectionDelta?(copyAxisDeltaInto(this.prevProjectionDelta.x,this.projectionDelta.x),copyAxisDeltaInto(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),calcBoxDelta(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===r&&axisDeltaEquals(this.projectionDelta.x,this.prevProjectionDelta.x)&&axisDeltaEquals(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a))):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const i=this.snapshot,n=i?i.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(i?i.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),c=!l||l.members.length<=1,u=Boolean(a&&!c&&!0===this.options.crossfade&&!this.path.some(hasOpacityCrossfade));let h;this.animationProgress=0,this.mixTargetDelta=e=>{const i=e/1e3;var l,d,m,p,f,g;mixAxisDelta(o.x,t.x,i),mixAxisDelta(o.y,t.y,i),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(calcRelativePosition(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),m=this.relativeTarget,p=this.relativeTargetOrigin,f=r,g=i,mixAxis(m.x,p.x,f.x,g),mixAxis(m.y,p.y,f.y,g),h&&(l=this.relativeTarget,d=h,axisEquals(l.x,d.x)&&axisEquals(l.y,d.y))&&(this.isProjectionDirty=!1),h||(h={x:{min:0,max:0},y:{min:0,max:0}}),copyBoxInto(h,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,i,n,s,o){s?(t.opacity=mixNumber$1(0,i.opacity??1,Oe(n)),t.opacityExit=mixNumber$1(e.opacity??1,0,Ue(n))):o&&(t.opacity=mixNumber$1(e.opacity??1,i.opacity??1,n));for(let s=0;s<Ne;s++){const o=`border${Ie[s]}Radius`;let r=getRadius(e,o),a=getRadius(i,o);void 0===r&&void 0===a||(r||(r=0),a||(a=0),0===r||0===a||isPx(r)===isPx(a)?(t[o]=Math.max(mixNumber$1(asNumber(r),asNumber(a),n),0),(H.test(a)||H.test(r))&&(t[o]+="%")):t[o]=a)}(e.rotate||i.rotate)&&(t.rotate=mixNumber$1(e.rotate||0,i.rotate||0,n))}(s,n,this.latestValues,i,u,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=i},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(E(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=V.update(()=>{ke.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=motionValue(0)),this.currentAnimation=function(t,e,i){const n=isMotionValue(t)?t:motionValue(t);return n.start(animateMotionValue("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:i,layout:n,latestValues:s}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&shouldAnimatePositionOnly(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=calcLength(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;const n=calcLength(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}copyBoxInto(e,i),transformBox(e,s),calcBoxDelta(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new NodeStack);this.sharedNodes.get(t).add(e);const i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){const{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){const{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){const n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;const n={};i.z&&resetDistortingTransform("z",t,n,this.animationValues);for(let e=0;e<ze.length;e++)resetDistortingTransform(`rotate${ze[e]}`,t,n,this.animationValues),resetDistortingTransform(`skew${ze[e]}`,t,n,this.animationValues);t.render();for(const e in n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return He;const e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=resolveMotionValue(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;const n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=resolveMotionValue(t?.pointerEvents)||""),this.hasProjected&&!hasTransform(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}const s=n.animationValues||n.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let n="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=i?.z||0;if((s||o||r)&&(n=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(n+=`scale(${1/e.x}, ${1/e.y}) `),i){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),s&&(n+=`rotateX(${s}deg) `),o&&(n+=`rotateY(${o}deg) `),r&&(n+=`skewX(${r}deg) `),a&&(n+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),i&&(e.transform=i(s,e.transform));const{x:o,y:r}=this.projectionDelta;e.transformOrigin=`${100*o.origin}% ${100*r.origin}% 0`,n.animationValues?e.opacity=n===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:e.opacity=n===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0;for(const t in me){if(void 0===s[t])continue;const{correct:i,applyTo:o,isCSSVariable:r}=me[t],a="none"===e.transform?s[t]:i(s[t],n);if(o){const t=o.length;for(let i=0;i<t;i++)e[o[i]]=a}else r?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=n===this?resolveMotionValue(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(clearMeasurements),this.root.sharedNodes.clear()}}}function updateLayout(t){t.updateLayout()}function notifyLayoutUpdate(t){const e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:i,measuredBox:n}=t.layout,{animationType:s}=t.options,o=e.source!==t.layout.source;"size"===s?eachAxis(t=>{const n=o?e.measuredBox[t]:e.layoutBox[t],s=calcLength(n);n.min=i[t].min,n.max=n.min+s}):shouldAnimatePositionOnly(s,e.layoutBox,i)&&eachAxis(n=>{const s=o?e.measuredBox[n]:e.layoutBox[n],r=calcLength(i[n]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+r)});const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};calcBoxDelta(r,i,e.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?calcBoxDelta(a,t.applyTransform(n,!0),e.measuredBox):calcBoxDelta(a,i,e.layoutBox);const l=!isDeltaZero(r);let c=!1;if(!t.resumeFrom){const n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){const{snapshot:s,layout:o}=n;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};calcRelativePosition(r,e.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};calcRelativePosition(a,i,o.layoutBox),boxEqualsRounded(r,a)||(c=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeLayoutChanged:c})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function propagateDirtyNodes(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function cleanDirtyNodes(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function clearSnapshot(t){t.clearSnapshot()}function clearMeasurements(t){t.clearMeasurements()}function clearIsLayoutDirty(t){t.isLayoutDirty=!1}function resetTransformStyle(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function finishAnimation(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function resolveTargetDelta(t){t.resolveTargetDelta()}function calcProjection(t){t.calcProjection()}function resetSkewAndRotation(t){t.resetSkewAndRotation()}function removeLeadSnapshots(t){t.removeLeadSnapshot()}function mixAxisDelta(t,e,i){t.translate=mixNumber$1(e.translate,0,i),t.scale=mixNumber$1(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function mixAxis(t,e,i,n){t.min=mixNumber$1(e.min,i.min,n),t.max=mixNumber$1(e.max,i.max,n)}function hasOpacityCrossfade(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const Ge={duration:.45,ease:[.4,0,.1,1]},userAgentContains=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Ye=userAgentContains("applewebkit/")&&!userAgentContains("chrome/")?Math.round:noop;function roundAxis(t){t.min=Ye(t.min),t.max=Ye(t.max)}function shouldAnimatePositionOnly(t,e,i){return"position"===t||"preserve-aspect"===t&&(n=aspectRatio(e),s=aspectRatio(i),o=.2,!(Math.abs(n-s)<=o));var n,s,o}function checkNodeWasScrollRoot(t){return t!==t.root&&t.scroll?.wasRoot}const Xe=createProjectionNode({attachResizeListener:(t,e)=>addDomEvent(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),qe={current:void 0},Ze=createProjectionNode({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!qe.current){const t=new Xe({});t.mount(window),t.setOptions({layoutScroll:!0}),qe.current=t}return qe.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)}),_e={pan:{Feature:class extends Feature{constructor(){super(...arguments),this.removePointerDownListener=noop}onPointerDown(t){this.session=new PanSession(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:getContextWindow(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:asyncHandler(t),onStart:asyncHandler(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&V.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=addPointerEvent(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Feature{constructor(t){super(t),this.removeGroupControls=noop,this.removeListeners=noop,this.controls=new VisualElementDragControls(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||noop}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Ze,MeasureLayout:MeasureLayout}};function handleHoverEvent(t,e,i){const{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);const s=n["onHover"+i];s&&V.postRender(()=>s(e,extractEventInfo(e)))}function handlePressEvent(t,e,i){const{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);const s=n["onTap"+("End"===i?"":i)];s&&V.postRender(()=>s(e,extractEventInfo(e)))}const Je=new WeakMap,Qe=new WeakMap,fireObserverCallback=t=>{const e=Je.get(t.target);e&&e(t)},fireAllObserverCallbacks=t=>{t.forEach(fireObserverCallback)};function observeIntersection(t,e,i){const n=function({root:t,...e}){const i=t||document;Qe.has(i)||Qe.set(i,{});const n=Qe.get(i),s=JSON.stringify(e);return n[s]||(n[s]=new IntersectionObserver(fireAllObserverCallbacks,{root:t,...e})),n[s]}(e);return Je.set(t,i),n.observe(t),()=>{Je.delete(t),n.unobserve(t)}}const ti={some:0,all:1};const ei={inView:{Feature:class extends Feature{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:ti[n]};return observeIntersection(this.node.current,o,t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),o=e?i:n;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Feature{mount(){const{current:t}=this.node;t&&(this.unmount=press(t,(t,e)=>(handlePressEvent(this.node,e,"Start"),(t,{success:e})=>handlePressEvent(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends Feature{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=pipe(addDomEvent(this.node.current,"focus",()=>this.onFocus()),addDomEvent(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends Feature{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){const[n,s,o]=setupGesture(t,i),onPointerEnter=t=>{if(!isValidHover(t))return;const{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;const onPointerLeave=t=>{isValidHover(t)&&(n(t),i.removeEventListener("pointerleave",onPointerLeave))};i.addEventListener("pointerleave",onPointerLeave,s)};return n.forEach(t=>{t.addEventListener("pointerenter",onPointerEnter,s)}),o}(t,(t,e)=>(handleHoverEvent(this.node,e,"Start"),t=>handleHoverEvent(this.node,t,"End"))))}unmount(){}}}},ii={layout:{ProjectionNode:Ze,MeasureLayout:MeasureLayout}},ni=null,si=new WeakMap;const oi=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class VisualElement{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=KeyframeResolver,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=j.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,V.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=isControllingVariants(e),this.isVariantNode=isVariantNode(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:c,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in u){const e=u[t];void 0!==a[t]&&isMotionValue(e)&&e.set(a[t],!1)}}mount(t){this.current=t,si.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ni),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),E(this.notifyUpdate),E(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const i=Vt.has(t);i&&this.onBindTransform&&this.onBindTransform();const n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&V.preRender(this.notifyUpdate),i&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),s(),o&&o(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in se){const e=se[t];if(!e)continue;const{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<oi.length;e++){const i=oi[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(const n in e){const s=e[n],o=i[n];if(isMotionValue(s))t.addValue(n,s);else if(isMotionValue(o))t.addValue(n,motionValue(s,{owner:t}));else if(o!==s)if(t.hasValue(n)){const e=t.getValue(n);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(n);t.addValue(n,motionValue(void 0!==e?e:s,{owner:t}))}}for(const n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=motionValue(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var n;return null!=i&&("string"==typeof i&&(isNumericalString(i)||isZeroValueString(i))?i=parseFloat(i):(n=i,!ee.find(testValueType(n))&&et.test(e)&&(i=getAnimatableNone(t,e))),this.setBaseTarget(t,isMotionValue(i)?i.get():i)),isMotionValue(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let i;if("string"==typeof e||"object"==typeof e){const n=resolveVariantFromProps(this.props,e,this.presenceContext?.custom);n&&(i=n[t])}if(e&&void 0!==i)return i;const n=this.getBaseTargetFromProps(this.props,t);return void 0===n||isMotionValue(n)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new SubscriptionManager),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class DOMVisualElement extends VisualElement{constructor(){super(...arguments),this.KeyframeResolver=DOMKeyframesResolver}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;isMotionValue(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function renderHTML(t,{style:e,vars:i},n,s){Object.assign(t.style,e,s&&s.getProjectionStyles(n));for(const e in i)t.style.setProperty(e,i[e])}class HTMLVisualElement extends DOMVisualElement{constructor(){super(...arguments),this.type="html",this.renderInstance=renderHTML}readValueFromInstance(t,e){if(Vt.has(e))return this.projection?.isProjecting?defaultTransformValue(e):((t,e)=>{const{transform:i="none"}=getComputedStyle(t);return parseValueFromTransform(i,e)})(t,e);{const n=(i=t,window.getComputedStyle(i)),s=(R(e)?n.getPropertyValue(e):n[e])||0;return"string"==typeof s?s.trim():s}var i}measureInstanceViewportBox(t,{transformPagePoint:e}){return measureViewportBox(t,e)}build(t,e,i){buildHTMLStyles(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return scrapeMotionValuesFromProps$1(t,e,i)}}const ri=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class SVGVisualElement extends DOMVisualElement{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=createBox}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(Vt.has(e)){const t=getDefaultValueType(e);return t&&t.default||0}return e=ri.has(e)?e:camelToDash(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return scrapeMotionValuesFromProps(t,e,i)}build(t,e,i){buildSVGAttrs(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){!function(t,e,i,n){renderHTML(t,e,void 0,n);for(const i in e.attrs)t.setAttribute(ri.has(i)?i:camelToDash(i),e.attrs[i])}(t,e,0,n)}mount(t){this.isSVGTag=isSVGTag(t.tagName),super.mount(t)}}const ai=createDOMMotionComponentProxy(createMotionComponentFactory({...Me,...ei,..._e,...ii},(e,i)=>isSVGComponent(e)?new SVGVisualElement(i):new HTMLVisualElement(i,{allowProjection:e!==t.Fragment}))),li=[{label:"Confidence",value:85},{label:"Lighting",value:78},{label:"Photo Quality",value:88}],AnimatedBar=({value:t})=>e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-4 mt-3",children:e.jsx(ai.div,{className:"bg-gradient-primary h-4 rounded-full",initial:{width:0},animate:{width:`${t}%`},transition:{duration:.88,ease:"easeOut"}})});function AnimatedPhoneMockup({rotation:i=0,className:s}){const[o,r]=t.useState(0);t.useEffect(()=>{const t=setInterval(()=>{r(t=>(t+1)%(li.length+2))},2200);return()=>clearInterval(t)},[]);return e.jsxs("div",{className:n("absolute h-[600px] w-[300px] rounded-[40px] bg-white shadow-2xl border-4 border-gray-200 overflow-hidden flex flex-col",s),style:{transform:`rotate(${i}deg)`},children:[e.jsx("img",{src:"/attractive-person-profile.avif",alt:"Profile",className:"w-full h-[350px] object-cover object-center flex-shrink-0 bg-gray-100"}),e.jsx("div",{className:"flex-grow flex items-center justify-center p-4",children:e.jsx(AnimatePresence,{mode:"wait",children:(()=>{if(0===o)return e.jsxs(ai.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.55},className:"px-4",children:[e.jsx("h3",{className:"text-xl font-bold",children:"Jessica, 26"}),e.jsx("p",{className:"text-sm text-graphite-60 mt-1",children:'"Lover of dogs, pizza, and spontaneous adventures."'})]},"profile-bio");if(o>0&&o<=li.length){const t=li[o-1];return e.jsxs(ai.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.55},className:"px-4 py-2",children:[e.jsx("p",{className:"text-lg font-semibold text-graphite-90 mb-1",children:t.label}),e.jsxs("p",{className:"text-2xl font-bold text-flame-red mb-2",children:[t.value,"%"]}),e.jsx(AnimatedBar,{value:t.value})]},t.label)}return o===li.length+1?e.jsxs(ai.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.55},className:"px-4 text-center",children:[e.jsx("p",{className:"text-lg font-semibold",children:"Overall Score"}),e.jsx("p",{className:"text-5xl font-bold bg-clip-text text-transparent bg-gradient-primary",children:"86"})]},"score"):void 0})()})})]})}function StaticPhoneMockup({rotation:t=0,className:i}){return e.jsx("div",{className:n("absolute h-[600px] w-[300px] rounded-[40px] bg-white shadow-2xl border-4 border-gray-200",i),style:{transform:`rotate(${t}deg)`},children:e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"w-full h-[350px] rounded-2xl bg-gray-200"}),e.jsxs("div",{className:"mt-4 space-y-3",children:[e.jsx("div",{className:"h-6 w-3/4 rounded bg-gray-200"}),e.jsx("div",{className:"h-4 w-full rounded bg-gray-200"}),e.jsx("div",{className:"h-4 w-5/6 rounded bg-gray-200"})]})]})})}function HeroSection(){return e.jsx("section",{className:"w-full bg-gradient-hero",children:e.jsxs("div",{className:"container mx-auto grid min-h-screen items-center gap-8 px-4 md:grid-cols-2 md:px-6 lg:gap-16",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("h1",{className:"text-h1-mobile md:text-h1",children:"Stop guessing. Start matching."}),e.jsx("p",{className:"text-body-lg text-graphite-60 max-w-md",children:"Upgrade your Tinder profile with AI-powered optimization. Get more matches, better conversations, and find your soulmate."}),e.jsx("div",{className:"flex flex-col gap-4 sm:flex-row sm:flex-wrap",children:e.jsxs("div",{className:"bg-red-200 flex flex-col gap-4",children:[e.jsx("h1",{children:"DEVELOPMENT BUTTONS"}),e.jsx(s,{asChild:!0,size:"lg",variant:"primary",children:e.jsx(i,{to:"/image-analyzer",children:"Try Image Analyzer"})}),e.jsx(s,{asChild:!0,size:"lg",variant:"primary",children:e.jsx(i,{to:"/welcome",children:"Get Started"})}),e.jsx(s,{asChild:!0,size:"lg",variant:"tertiary",className:"sm:w-auto",children:e.jsx(i,{to:"/bio-analyzer",children:"Try Bio Analyzer"})})]})})]}),e.jsxs("div",{className:"relative ml-10 h-full items-center justify-center md:flex",children:[e.jsx(StaticPhoneMockup,{rotation:-6}),e.jsx(AnimatedPhoneMockup,{rotation:6,className:"relative"})]})]})})}function SocialProofSection(){const t=[{icon:o,value:"+25%",label:"Average Match Rate Increase"},{icon:r,value:"9/10",label:"Users Report Higher Quality Matches"},{icon:a,value:"4s",label:"Average Analysis Time Per Photo"}];return e.jsx("section",{className:"py-12 md:py-24 bg-cloud-white",children:e.jsx("div",{className:"container mx-auto px-4 md:px-6",children:e.jsx("div",{className:"grid gap-8 md:grid-cols-3",children:t.map((t,i)=>e.jsxs("div",{className:"text-center",children:[e.jsx(t.icon,{className:"mx-auto h-12 w-12 text-flame-red"}),e.jsx("p",{className:"text-display-1-mobile md:text-display-1 mt-4",children:t.value}),e.jsx("p",{className:"text-body-md text-graphite-60",children:t.label})]},i))})})})}function HowItWorksSection(){const t=[{icon:l,title:"Upload Your Profile",description:"Securely upload your current dating profile photos and bio. We keep your data private."},{icon:c,title:"AI-Powered Analysis",description:"Our model scores your photos on key metrics and rewrites your bio to be more engaging."},{icon:u,title:"Get Your Glow-Up",description:"Implement our actionable tips, update your profile, and watch your matches increase."}];return e.jsx("section",{id:"image-analyzer",className:"py-12 md:py-24 bg-gray-50",children:e.jsxs("div",{className:"container mx-auto px-4 md:px-6",children:[e.jsxs("div",{className:"text-center max-w-2xl mx-auto",children:[e.jsx("h2",{className:"text-h2-mobile md:text-h2",children:"How It Works in 3 Simple Steps"}),e.jsx("p",{className:"text-body-lg text-graphite-60 mt-4",children:"Transform your dating profile from overlooked to overbooked."})]}),e.jsx("div",{className:"grid gap-8 md:grid-cols-3 mt-12",children:t.map((t,i)=>e.jsxs("div",{className:"flex flex-col items-center text-center p-6 rounded-lg",children:[e.jsx("div",{className:"flex h-16 w-16 items-center justify-center rounded-full bg-flame-red text-cloud-white mb-6",children:e.jsx(t.icon,{className:"h-8 w-8"})}),e.jsx("h3",{className:"text-xl font-semibold",children:t.title}),e.jsx("p",{className:"text-graphite-60 mt-2",children:t.description})]},i))})]})})}function Footer(){return e.jsx("footer",{className:"bg-graphite-90 text-cloud-white/80",children:e.jsxs("div",{className:"container mx-auto px-4 md:px-6 py-12",children:[e.jsxs("div",{className:"grid gap-8 md:grid-cols-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-lg text-cloud-white",children:"TinderOptimizer"}),e.jsx("p",{className:"mt-2 text-sm",children:"AI-powered profile analysis."})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-semibold text-cloud-white",children:"Product"}),e.jsxs("ul",{className:"mt-4 space-y-2 text-sm",children:[e.jsx("li",{children:e.jsx(i,{to:"/image-analyzer",className:"hover:text-cloud-white",children:"Image Analyzer"})}),e.jsx("li",{children:e.jsx(i,{to:"/bio-analyzer",className:"hover:text-cloud-white",children:"Bio Analyzer"})}),e.jsx("li",{children:e.jsx(i,{to:"#pricing",className:"hover:text-cloud-white",children:"Pricing"})})]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-semibold text-cloud-white",children:"Company"}),e.jsxs("ul",{className:"mt-4 space-y-2 text-sm",children:[e.jsx("li",{children:e.jsx(i,{to:"#",className:"hover:text-cloud-white",children:"About Us"})}),e.jsx("li",{children:e.jsx(i,{to:"#",className:"hover:text-cloud-white",children:"Privacy Policy"})}),e.jsx("li",{children:e.jsx(i,{to:"#",className:"hover:text-cloud-white",children:"Terms of Service"})})]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-semibold text-cloud-white",children:"Connect"}),e.jsx("div",{className:"flex space-x-4 mt-4"})]})]}),e.jsx("div",{className:"border-t border-graphite-60/50 mt-8 pt-8 text-center text-sm",children:e.jsxs("p",{children:["© ",(new Date).getFullYear()," TinderOptimizer. All rights reserved."]})})]})})}const SplitComponent=function(){return e.jsxs("div",{className:"bg-cloud-white text-graphite-90",children:[e.jsx(HeroSection,{}),e.jsx(SocialProofSection,{}),e.jsx(HowItWorksSection,{}),e.jsx(Footer,{})]})};export{SplitComponent as component};
//# sourceMappingURL=routes-CaJKaILO.mjs.map
