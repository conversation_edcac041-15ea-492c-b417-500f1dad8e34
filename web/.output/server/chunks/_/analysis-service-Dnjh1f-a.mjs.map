{"version": 3, "file": "analysis-service-Dnjh1f-a.mjs", "sources": ["../../../../../src/lib/image-analysis.ts", "../../../../../src/lib/bio-analysis.ts", "../../../../../src/lib/analysis-service.ts"], "sourcesContent": null, "names": ["imageAnalysisAgent", "constructor", "__publicField", "this", "<PERSON><PERSON><PERSON><PERSON>", "console", "error", "Error", "log", "substring", "globalThis", "process", "env", "OPENROUTER_API_KEY", "analyzeImage", "imageBase64: string", "_fileName: string", "onProgress?: (step: number, stepName: string, progress: number) => void", "results: <PERSON><PERSON><PERSON><PERSON>[]", "i", "ANALYSIS_STEPS", "length", "step", "onProgress", "id", "name", "startTime", "Date", "now", "result", "executeAnalysisStep", "imageBase64", "processingTime", "score", "insights", "confidence", "results", "push", "stepId", "<PERSON><PERSON><PERSON>", "avgScore", "reduce", "sum", "r", "averageScore", "toFixed", "stepScores", "map", "totalSteps", "stepId: number", "_stepName: string", "prompt", "getStepPrompt", "model", "openrouter", "text", "generateText", "messages", "role", "content", "type", "image", "maxTokens", "temperature", "parseAnalysisResult", "baseInstruction", "stepPrompts", "response: string", "jsonMatch", "response", "match", "parsed", "JSON", "parse", "Math", "max", "min", "parseInt", "Array", "isArray", "slice", "String", "calculateOverallScore", "weights", "weightedSum", "totalWeight", "for<PERSON>ach", "weight", "round", "generateFinalRecommendations", "recommendations: string[]", "lowScoreSteps", "filter", "sort", "a", "b", "mediumScoreSteps", "recommendations", "toLowerCase", "highestStep", "overallScore", "bioAnalysisAgent", "system", "bio: string", "bio", "analyzeBio", "onProgress?: (stepId: number, stepName: string, progress: number) => void", "BIO_ANALYSIS_STEPS", "stepPrompt", "analysisResult", "Promise", "resolve", "setTimeout", "stepResults: StepResult[]", "stepR<PERSON><PERSON>s", "randomStep", "floor", "random", "insight", "some", "rec", "includes", "generateImprovedBio", "originalBio: string", "tone: \"witty\" | \"sincere\" | \"adventurous\"", "weaknesses", "join", "tone", "originalBio", "trim", "analysisService", "storedImage: StoredImage", "config: AnalysisServiceConfig", "isAnalyzing", "convertBlobToBase64", "storedImage", "blob", "result: AnalysisResult", "fileName", "preview", "URL", "createObjectURL", "steps", "processed", "progress", "progressData: AnalysisProgress", "currentStep", "_a", "config", "call", "progressData", "step<PERSON><PERSON><PERSON>", "onStepComplete", "_b", "onComplete", "errorMessage", "message", "_c", "onError", "analyzeBatch", "storedImages: StoredImage[]", "results: AnalysisResult[]", "storedImages", "batchProgress", "config: BioAnalysisServiceConfig", "isBioAnalyzing", "result: BioAnalysisResult", "progressData: BioAnalysisProgress", "improvedBio", "isCurrentlyAnalyzing", "isCurrentlyAnalyzingBio"], "mappings": "wXAwXA,MAAaA,EAAqB,IAlXlC,MAGE,WAAAC,GAIE,GANFC,cAAAC,KAAA,UAIEA,KAAKC,OAAA,6EAEAD,KAAKC,OAER,MADAC,QAAQC,MAAM,iEACR,IAAIC,MACR,wFAIJF,QAAQG,IAAI,6CACZH,QAAQG,IAAA,uBAA2BL,KAAKC,OAAOK,UAAU,EAAG,UAIlC,oBAAfC,aACRA,WAAmBC,QAAWD,WAAmBC,SAAW,CAAA,EAC5DA,EAA2BC,IAAOD,EAA2BC,KAAO,CAAA,EACpED,EAA2BC,IAAIC,mBAAqBV,KAAKC,OAE7D,CAED,kBAAMU,CACJC,EACAC,EACAC,GAEA,MAAMC,EAAwB,GAE9B,IAAA,IAASC,EAAI,EAAGA,EAAIC,EAAeC,OAAQF,IAAK,CAC9C,MAAMG,EAAOF,EAAeD,GAC5B,MAAAI,GAAAA,EAAaD,EAAKE,GAAIF,EAAKG,KAAON,EAAIC,EAAeC,OAAU,KAE/D,MAAMK,EAAYC,KAAKC,MAEvB,IACEvB,QAAQG,iCAAiCc,EAAKE,OAAOF,EAAKG,QAC1D,MAAMI,QAAe1B,KAAK2B,oBAAoBR,EAAKE,GAAIF,EAAKG,KAAMM,GAC5DC,EAAiBL,KAAKC,MAAQF,EAEpCrB,QAAQG,8BAA8Bc,EAAKE,OAAOF,EAAKG,SAAU,CAC/DQ,MAAOJ,EAAOI,MACdC,SAAUL,EAAOK,SACjBC,WAAYN,EAAOM,WACnBH,eAAA,GAAmBA,QAGrBI,EAAQC,KAAK,CACXC,OAAQhB,EAAKE,GACbe,SAAUjB,EAAKG,KACfQ,MAAOJ,EAAOI,MACdC,SAAUL,EAAOK,SACjBC,WAAYN,EAAOM,WACnBH,mBAGF,MAAAT,GAAAA,EAAaD,EAAKE,GAAIF,EAAKG,MAAQN,EAAI,GAAKC,EAAeC,OAAU,IACtE,OAAQf,GACPD,QAAQC,MAAA,mBAAyBgB,EAAKE,OAAOF,EAAKG,SAAUnB,GAC5D8B,EAAQC,KAAK,CACXC,OAAQhB,EAAKE,GACbe,SAAUjB,EAAKG,KACfQ,MAAO,EACPC,SAAU,CAAC,oDACXC,WAAY,EACZH,eAAgBL,KAAKC,MAAQF,GAEhC,CACF,CAGD,MAAMc,EAAWJ,EAAQK,OAAO,CAACC,EAAKC,IAAMD,EAAMC,EAAEV,MAAO,GAAKG,EAAQf,OAOxE,OANAhB,QAAQG,uCAAwC,CAC9CoC,aAAcJ,EAASK,QAAQ,GAC/BC,WAAYV,EAAQW,IAAIJ,IAAA,CAAQrB,KAAMqB,EAAEJ,SAAUN,MAAOU,EAAEV,SAC3De,WAAYZ,EAAQf,SAGfe,CACR,CAED,yBAAcN,CACZmB,EACAC,EACAnC,GAEA,MAAMoC,EAAShD,KAAKiD,cAAcd,GAElCjC,QAAQG,IAAA,sCAA0C8B,yCAGlD,MAAMe,EAAQC,EAAW,4BAEnBC,KAAEA,SAAeC,EAAa,CAClCH,QACAI,SAAU,CACR,CACEC,KAAM,OACNC,QAAS,CACP,CACEC,KAAM,OACNL,KAAMJ,GAER,CACES,KAAM,QACNC,MAAA,0BAAiC9B,QAKzC+B,UAAW,IACXC,YAAa,KAGf,OAAO5D,KAAK6D,oBAAoBT,EACjC,CAED,aAAAH,CAAsBH,GACpB,MAAMgB,EAAA,g/BAuBAC,EAAc,CAClB,EAAA,GAAMD,4qBAeN,EAAA,GAAMA,m3BAkBN,EAAA,GAAMA,+kDAmCN,EAAA,GAAMA,u7BAmBN,EAAA,GAAMA,qzBAiBN,EAAA,GAAMA,wrBAiBR,OAAOC,EAAY5B,IAAuC4B,EAAY,EACvE,CAED,mBAAAF,CAA4BG,GAK1B,IAEE,MAAMC,EAAYC,EAASC,MAAM,eACjC,IAAKF,EACH,MAAM,IAAI7D,MAAM,6BAGlB,MAAMgE,EAASC,KAAKC,MAAML,EAAU,IAEpC,MAAO,CACLnC,MAAOyC,KAAKC,IAAI,EAAGD,KAAKE,IAAI,IAAKC,SAASN,EAAOtC,QAAU,IAC3DC,SAAU4C,MAAMC,QAAQR,EAAOrC,UAC3BqC,EAAOrC,SAAS8C,MAAM,EAAG,GAAGjC,IAAIkC,QAChC,CAAC,6CACL9C,WAAYuC,KAAKC,IAAI,EAAGD,KAAKE,IAAI,IAAKC,SAASN,EAAOpC,aAAe,IAExE,OAAQ7B,GAGP,OAFAD,QAAQC,MAAM,sCAAuCA,GACrDD,QAAQG,IAAI,mBAAoB6D,GACzB,CACLpC,MAAO,GACPC,SAAU,CACR,8DACA,+CAEFC,WAAY,GAEf,CACF,CAED,qBAAA+C,CAAsBhE,GACpB,GAAuB,IAAnBkB,EAAQf,OAAc,OAAO,EAGjC,MAAM8D,EAAU,CACd,EAAG,IACH,EAAG,IACH,EAAG,IACH,EAAG,IACH,EAAG,IACH,EAAG,KAGL,IAAIC,EAAc,EACdC,EAAc,EAQlB,OANAjD,EAAQkD,QAASzD,IACf,MAAM0D,EAASJ,EAAQtD,EAAOS,SAAmC,GACjE8C,GAAevD,EAAOI,MAAQsD,EAC9BF,GAAeE,IAGVb,KAAKc,MAAMJ,EAAcC,EACjC,CAED,4BAAAI,CAA6BvE,GAC3B,MAAMwE,EAA4B,GAG5BC,EAAgBvD,EAAQwD,OAAQjD,GAAMA,EAAEV,MAAQ,IAAI4D,KAAK,CAACC,EAAGC,IAAMD,EAAE7D,MAAQ8D,EAAE9D,OAC/E+D,EAAmB5D,EAAQwD,OAAQjD,GAAMA,EAAEV,OAAS,IAAMU,EAAEV,MAAQ,IAEtE0D,EAActE,OAAS,GACzB4E,EAAgB5D,0BACOsD,EAAc,GAAGpD,SAAS2D,yBAAyBP,EAAc,GAAG1D,cAIzF+D,EAAiB3E,OAAS,GAC5B4E,EAAgB5D,KAAA,sBACQ2D,EAAiB,GAAGzD,SAAS2D,oCAIvD,MAAMC,EAAc/D,EAAQK,OAAO,CAACkC,EAAKhC,IAAOA,EAAEV,MAAQ0C,EAAI1C,MAAQU,EAAIgC,EAAMvC,EAAQ,IACpF+D,EAAYlE,MAAQ,IACtBgE,EAAgB5D,KAAA,kBACI8D,EAAY5D,SAAS2D,yDAK3C,MAAME,EAAejG,KAAK+E,sBAAsB9C,GAShD,OARIgE,EAAe,GACjBH,EAAgB5D,KAAK,kEACZ+D,EAAe,GACxBH,EAAgB5D,KAAK,uEAErB4D,EAAgB5D,KAAK,uDAGhB4D,CACR,GCTH,MAAaI,EAAmB,IA9VhC,MAGE,WAAApG,GAIE,GANFC,cAAAC,KAAA,UAwBAD,cAAAC,KAAA,cAAsB,CACpB,EAAG,CACDsB,KAAM,kBACN6E,OAAQ,gLACRnD,OAASoD,GAAAA,qIAGPC,+uBA2BJ,EAAG,CACD/E,KAAM,qBACN6E,OAAQ,gLACRnD,OAASoD,GAAAA,sIAGPC,yyBA2BJ,EAAG,CACD/E,KAAM,oBACN6E,OAAQ,iKACRnD,OAASoD,GAAAA,sIAGPC,04BA2BJ,EAAG,CACD/E,KAAM,gBACN6E,OAAQ,+JACRnD,OAASoD,GAAAA,yHAGPC,41BA2BJ,EAAG,CACD/E,KAAM,oBACN6E,OAAQ,0KACRnD,OAASoD,GAAAA,sHAGPC,i3BA/JJrG,KAAKC,OAAA,6EAEAD,KAAKC,OAER,MADAC,QAAQC,MAAM,iEACR,IAAIC,MACR,wFAIJF,QAAQG,IAAI,8DACZH,QAAQG,IAAA,uBAA2BL,KAAKC,OAAOK,UAAU,EAAG,UAGlC,oBAAfC,aACRA,WAAmBC,QAAWD,WAAmBC,SAAW,CAAA,EAC5DA,EAA2BC,IAAOD,EAA2BC,KAAO,CAAA,EACpED,EAA2BC,IAAIC,mBAAqBV,KAAKC,OAE7D,CA0KD,gBAAMqG,CACJF,EACAG,GAEA,MAAMxF,EAAwB,GACxB8B,EAAa2D,EAAmBtF,OAEtC,IAAA,IAASF,EAAI,EAAGA,EAAI6B,EAAY7B,IAAK,CACnC,MAAMG,EAAOqF,EAAmBxF,GAC1ByF,EAAazG,KAAK+D,YAAY5C,EAAKE,IAEzC,MAAAD,GAAAA,EAAaD,EAAKE,GAAIF,EAAKG,KAAON,EAAI6B,EAAc,KAEpD,IACE,MAAMtB,EAAYC,KAAKC,OAEjB2B,KAAEA,SAAeC,EAAa,CAClCH,MAAOC,EAAW,sBAClBgD,OAAQM,EAAWN,OACnBnD,OAAQyD,EAAWzD,OAAOqD,KAGtBxE,EAAiBL,KAAKC,MAAQF,EAG9BmF,EAAiBrC,KAAKC,MAAMlB,GAElClD,QAAQG,4BAA4Bc,EAAKE,OAAOF,EAAKG,SAAU,CAC7D+E,IAAKA,EAAI/F,UAAU,EAAG,KAAO,MAC7BwB,MAAO4E,EAAe5E,MACtBC,SAAU2E,EAAe3E,SACzBC,WAAY0E,EAAe1E,aAG7BC,EAAQC,KAAK,CACXC,OAAQhB,EAAKE,GACbe,SAAUjB,EAAKG,KACfQ,MAAO4E,EAAe5E,MACtBC,SAAU2E,EAAe3E,SACzBC,WAAY0E,EAAe1E,WAC3BH,mBAGF,MAAAT,GAAAA,EAAaD,EAAKE,GAAIF,EAAKG,MAAQN,EAAI,GAAK6B,EAAc,KAGtD7B,EAAI6B,EAAa,SACb,IAAI8D,QAAQC,GAAWC,WAAWD,EAAS,KAEpD,OAAQzG,GACPD,QAAQC,MAAA,iBAAuBgB,EAAKE,MAAOlB,GAC3C8B,EAAQC,KAAK,CACXC,OAAQhB,EAAKE,GACbe,SAAUjB,EAAKG,KACfQ,MAAO,EACPC,SAAU,CAAC,iCACXC,WAAY,EACZH,eAAgB,GAEnB,CACF,CAED,OAAOI,CACR,CAED,qBAAA8C,CAAsB+B,GACpB,GAA2B,IAAvBC,EAAY7F,OAAc,OAAO,EAGrC,MAAM8D,EAAU,CACd,EAAG,IACH,EAAG,GACH,EAAG,GACH,EAAG,IACH,EAAG,IAGL,IAAIC,EAAc,EACdC,EAAc,EAQlB,OANA6B,EAAY5B,QAAQzD,IAClB,MAAM0D,EAASJ,EAAQtD,EAAOS,SAAmC,GACjE8C,GAAevD,EAAOI,MAAQsD,EAC9BF,GAAeE,IAGVb,KAAKc,MAAMJ,EAAcC,EACjC,CAED,4BAAAI,CAA6BwB,GAC3B,MAAMvB,EAA4B,GAGZ,IAAIwB,GAAarB,KAAK,CAACC,EAAGC,IAAMD,EAAE7D,MAAQ8D,EAAE9D,OAEpD+C,MAAM,EAAG,GAAGM,QAAQhE,IAC5BA,EAAKW,MAAQ,IACfgE,EAAgB5D,KAAA,WAAgBf,EAAKiB,SAAS2D,kBAAkB5E,EAAKY,SAAS,QAKlF,MAAMU,EAAesE,EAAYzE,OAAO,CAACC,EAAKpB,IAASoB,EAAMpB,EAAKW,MAAO,GAAKiF,EAAY7F,OAS1F,IAPIuB,EAAe,GACjBqD,EAAgB5D,KAAK,mEACZO,EAAe,IACxBqD,EAAgB5D,KAAK,6DAIhB4D,EAAgB5E,OAAS,GAAK6F,EAAY7F,OAAS,GAAG,CAC3D,MAAM8F,EAAaD,EAAYxC,KAAK0C,MAAM1C,KAAK2C,SAAWH,EAAY7F,SAChEiG,EAAUH,EAAWjF,SAASwC,KAAK0C,MAAM1C,KAAK2C,SAAWF,EAAWjF,SAASb,SAC9E4E,EAAgBsB,KAAKC,GAAOA,EAAIC,SAASH,KAC5CrB,EAAgB5D,KAAKiF,EAExB,CAED,OAAOrB,EAAgBjB,MAAM,EAAG,EACjC,CAED,yBAAM0C,CACJC,EACAV,EACAW,EAA4C,WAE5C,MAAMC,EAAaX,EAChBtB,OAAOtE,GAAQA,EAAKW,MAAQ,IAC5Bc,IAAIzB,MAAWA,EAAKiB,aAAajB,EAAKY,SAAS4F,KAAK,SACpDA,KAAK,OAEFvE,KAAEA,SAAeC,EAAa,CAClCH,MAAOC,EAAW,sBAClBgD,OAAA,+JAAuKyB,KACvK5E,OAAA,+EAEW6E,iCAGfH,oFAKQE,0NAQN,OAAOxE,EAAK0E,MACb,GCxIH,MAAaC,EAAkB,IA9M/B,MAAA,WAAAjI,GACEC,cAAAC,KAAA,eAAsB,GACtBD,cAAAC,KAAA,kBAAyB,EAAA,CAEzB,kBAAMW,CACJqH,EACAC,EAAgC,cAEhC,GAAIjI,KAAKkI,YACP,MAAM,IAAI9H,MAAM,gCAGlBJ,KAAKkI,aAAc,EAEnB,IAEE,MAAMtG,QAAoBuG,EAAoBC,EAAYC,MAEpDC,EAAyB,CAC7BC,SAAUH,EAAYG,SACtBC,QAASC,IAAIC,gBAAgBN,EAAYC,MACzCpC,aAAc,EACd0C,MAAO,GACP7C,gBAAiB,GACjB8C,WAAW,GAIP7B,QAAoBlH,EAAmBc,aAC3CiB,EACAwG,EAAYG,SACZ,CAACpG,EAAQC,EAAUyG,WACjB,MAAMC,EAAiC,CACrCP,SAAUH,EAAYG,SACtBQ,YAAa5G,EACbU,WAAY,EACZT,WACAyG,YAEF,OAAAG,EAAAC,EAAO7H,aAAP4H,EAAAE,KAAAD,EAAoBE,KAKxB,IAAA,MAAWC,KAAcrC,EACvBrF,EAAOiH,MAAMzG,KAAKkH,GAClB,OAAAJ,EAAAC,EAAOI,iBAAPL,EAAAE,KAAAD,EAAwBG,GAY1B,OARA1H,EAAOuE,aAAepG,EAAmBkF,sBAAsBgC,GAC/DrF,EAAOoE,gBAAkBjG,EAAmByF,6BAA6ByB,GACzErF,EAAOkH,WAAY,EAKnB,OAAAU,EAAAL,EAAOM,aAAPD,EAAAJ,KAAAD,EAAoBvH,GACbA,CACR,OAAQvB,GACP,MAAMqJ,EAAerJ,aAAiBC,MAAQD,EAAMsJ,QAAU,yBAG9D,OAFA,OAAAC,EAAAT,EAAOU,UAAPD,EAAAR,KAAAD,EAAiBO,GAEV,CACLjB,SAAUH,EAAYG,SACtBC,QAASC,IAAIC,gBAAgBN,EAAYC,MACzCpC,aAAc,EACd0C,MAAO,GACP7C,gBAAiB,CAAC,sCAClB8C,WAAW,EACXzI,MAAOqJ,EAEV,CAAA,QACCxJ,KAAKkI,aAAc,CACpB,CACF,CAED,kBAAM0B,CACJC,EACA5B,EAAgC,IAEhC,MAAM6B,EAA4B,GAElC,IAAA,IAAS9I,EAAI,EAAGA,EAAI+I,EAAa7I,OAAQF,IAAK,CAC5C,MAAM0C,EAAQqG,EAAa/I,GAE3B,IACE,MAAMU,QAAe1B,KAAKW,aAAa+C,EAAO,IACzCuF,EACH7H,WAAayH,UAEX,MAAMmB,EAAgB,IACjBnB,EACHA,SAAW7H,EAAI+I,EAAa7I,OAAU,IAAM2H,EAASA,SAAWkB,EAAa7I,QAE/E,OAAA8H,EAAAC,EAAO7H,aAAP4H,EAAAE,KAAAD,EAAoBe,MAIxB/H,EAAQC,KAAKR,GAGTV,EAAI+I,EAAa7I,OAAS,SACtB,IAAIyF,QAASC,GAAYC,WAAWD,EAAS,KAEtD,OAAQzG,GACPD,QAAQC,MAAA,qBAA2BuD,EAAM6E,YAAapI,GACtD8B,EAAQC,KAAK,CACXqG,SAAU7E,EAAM6E,SAChBC,QAASC,IAAIC,gBAAgBhF,EAAM2E,MACnCpC,aAAc,EACd0C,MAAO,GACP7C,gBAAiB,CAAC,mCAClB8C,WAAW,EACXzI,MAAOA,aAAiBC,MAAQD,EAAMsJ,QAAU,iBAEnD,CACF,CAED,OAAOxH,CACR,CAED,gBAAMqE,CACJF,EACAqB,EAA4C,UAC5CwC,EAAmC,CAAA,aAEnC,GAAIjK,KAAKkK,eACP,MAAM,IAAI9J,MAAM,oCAGlBJ,KAAKkK,gBAAiB,EAEtB,IACE,MAAMC,EAA4B,CAChCtC,YAAaxB,EACbJ,aAAc,EACd0C,MAAO,GACP7C,gBAAiB,GACjB8C,WAAW,GAIP7B,QAAoBb,EAAiBI,WACzCD,EACA,CAAClE,EAAQC,EAAUyG,WACjB,MAAMuB,EAAoC,CACxCrB,YAAa5G,EACbU,WAAY,EACZT,WACAyG,YAEF,OAAAG,EAAAC,EAAO7H,aAAP4H,EAAAE,KAAAD,EAAoBE,KAKxB,IAAA,MAAWC,KAAcrC,EACvBrF,EAAOiH,MAAMzG,KAAKkH,GAClB,OAAAJ,EAAAC,EAAOI,iBAAPL,EAAAE,KAAAD,EAAwBG,GAQ1B,GAJA1H,EAAOuE,aAAeC,EAAiBnB,sBAAsBgC,GAC7DrF,EAAOoE,gBAAkBI,EAAiBZ,6BAA6ByB,GAGnErF,EAAOuE,aAAe,GACxB,IACEvE,EAAO2I,kBAAoBnE,EAAiBqB,oBAAoBlB,EAAKU,EAAaa,EACnF,OAAQzH,GACPD,QAAQC,MAAM,mCAAoCA,EACnD,CAMH,OAHAuB,EAAOkH,WAAY,EAEnB,OAAAU,EAAAL,EAAOM,aAAPD,EAAAJ,KAAAD,EAAoBvH,GACbA,CACR,OAAQvB,GACP,MAAMqJ,EAAerJ,aAAiBC,MAAQD,EAAMsJ,QAAU,yBAG9D,OAFA,OAAAC,EAAAT,EAAOU,UAAPD,EAAAR,KAAAD,EAAiBO,GAEV,CACL3B,YAAaxB,EACbJ,aAAc,EACd0C,MAAO,GACP7C,gBAAiB,CAAC,sCAClB8C,WAAW,EACXzI,MAAOqJ,EAEV,CAAA,QACCxJ,KAAKkK,gBAAiB,CACvB,CACF,CAED,oBAAAI,GACE,OAAOtK,KAAKkI,WACb,CAED,uBAAAqC,GACE,OAAOvK,KAAKkK,cACb"}