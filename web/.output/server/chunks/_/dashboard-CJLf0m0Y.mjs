import{C as e,a as s,b as a,d as t,c as l}from"./card-zxSsGLJg.mjs";import{B as r,e as i,a as c,H as n,M as d,T as m,c as x,f as o,g as h}from"./lucide-react.mjs";import{B as j}from"./badge-Cwk-ZwYz.mjs";import{P as f}from"./ProtectedRoute-CFonv99q.mjs";import{j as p,u as N,U as u,L as b}from"./routeTree.gen-BFK54byf.mjs";import"../nitro/nitro.mjs";import"node:process";import"cloudflare:workers";import"node:events";import"node:buffer";import"node:timers";import"node:stream";import"node:stream/web";import"node:async_hooks";function Dashboard(){const{user:f}=N();return p.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:p.jsxs("div",{className:"max-w-7xl mx-auto",children:[p.jsxs("div",{className:"flex items-center justify-between mb-8",children:[p.jsxs("div",{children:[p.jsxs("h1",{className:"text-3xl font-bold text-slate-900",children:["Welcome back, ",(null==f?void 0:f.firstName)||"User","!"]}),p.jsx("p",{className:"text-slate-600 mt-1",children:"Track your dating profile performance and optimize for better matches"})]}),p.jsx(u,{}),p.jsxs(r,{variant:"secondary",size:"sm",children:[p.jsx(i,{className:"h-4 w-4 mr-2"}),"Settings"]})]}),p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[p.jsxs(e,{children:[p.jsxs(s,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[p.jsx(a,{className:"text-sm font-medium",children:"Profile Score"}),p.jsx(c,{className:"h-4 w-4 text-amber-500"})]}),p.jsxs(t,{children:[p.jsx("div",{className:"text-2xl font-bold",children:"8.2"}),p.jsx("p",{className:"text-xs text-muted-foreground",children:"+0.5 from last week"})]})]}),p.jsxs(e,{children:[p.jsxs(s,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[p.jsx(a,{className:"text-sm font-medium",children:"Matches"}),p.jsx(n,{className:"h-4 w-4 text-red-500"})]}),p.jsxs(t,{children:[p.jsx("div",{className:"text-2xl font-bold",children:"24"}),p.jsx("p",{className:"text-xs text-muted-foreground",children:"+12% from last week"})]})]}),p.jsxs(e,{children:[p.jsxs(s,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[p.jsx(a,{className:"text-sm font-medium",children:"Messages"}),p.jsx(d,{className:"h-4 w-4 text-blue-500"})]}),p.jsxs(t,{children:[p.jsx("div",{className:"text-2xl font-bold",children:"156"}),p.jsx("p",{className:"text-xs text-muted-foreground",children:"+8% from last week"})]})]}),p.jsxs(e,{children:[p.jsxs(s,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[p.jsx(a,{className:"text-sm font-medium",children:"Profile Views"}),p.jsx(m,{className:"h-4 w-4 text-green-500"})]}),p.jsxs(t,{children:[p.jsx("div",{className:"text-2xl font-bold",children:"342"}),p.jsx("p",{className:"text-xs text-muted-foreground",children:"+25% from last week"})]})]})]}),p.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[p.jsxs(e,{children:[p.jsxs(s,{children:[p.jsxs(a,{className:"flex items-center gap-2",children:[p.jsx(x,{className:"h-5 w-5 text-flame-red"}),"Photo Analysis"]}),p.jsx(l,{children:"Optimize your photos for maximum appeal"})]}),p.jsxs(t,{className:"space-y-4",children:[p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsx("span",{className:"text-sm",children:"Last analysis"}),p.jsx(j,{variant:"secondary",children:"3 days ago"})]}),p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsx("span",{className:"text-sm",children:"Photos analyzed"}),p.jsx("span",{className:"text-sm font-medium",children:"8/10"})]}),p.jsx(r,{asChild:!0,className:"w-full bg-gradient-to-r from-flame-red to-sparks-pink hover:opacity-90",children:p.jsx(b,{to:"/image-analyzer",children:"Analyze New Photos"})})]})]}),p.jsxs(e,{children:[p.jsxs(s,{children:[p.jsxs(a,{className:"flex items-center gap-2",children:[p.jsx(o,{className:"h-5 w-5 text-flame-red"}),"Bio Optimization"]}),p.jsx(l,{children:"Craft the perfect bio to attract matches"})]}),p.jsxs(t,{className:"space-y-4",children:[p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsx("span",{className:"text-sm",children:"Bio score"}),p.jsx(j,{variant:"secondary",children:"7.8/10"})]}),p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsx("span",{className:"text-sm",children:"Last updated"}),p.jsx("span",{className:"text-sm font-medium",children:"1 week ago"})]}),p.jsx(r,{asChild:!0,className:"w-full bg-gradient-to-r from-flame-red to-sparks-pink hover:opacity-90",children:p.jsx(b,{to:"/bio-analyzer",children:"Optimize Bio"})})]})]})]}),p.jsxs(e,{children:[p.jsxs(s,{children:[p.jsxs(a,{className:"flex items-center gap-2",children:[p.jsx(h,{className:"h-5 w-5"}),"Recent Activity"]}),p.jsx(l,{children:"Your latest profile improvements and results"})]}),p.jsx(t,{children:p.jsxs("div",{className:"space-y-4",children:[p.jsxs("div",{className:"flex items-center justify-between border-b pb-2",children:[p.jsxs("div",{className:"flex items-center gap-3",children:[p.jsx("div",{className:"h-2 w-2 bg-green-500 rounded-full"}),p.jsx("span",{className:"text-sm",children:"Photo analysis completed"})]}),p.jsx("span",{className:"text-xs text-muted-foreground",children:"2 hours ago"})]}),p.jsxs("div",{className:"flex items-center justify-between border-b pb-2",children:[p.jsxs("div",{className:"flex items-center gap-3",children:[p.jsx("div",{className:"h-2 w-2 bg-blue-500 rounded-full"}),p.jsx("span",{className:"text-sm",children:"Bio updated with AI suggestions"})]}),p.jsx("span",{className:"text-xs text-muted-foreground",children:"1 day ago"})]}),p.jsxs("div",{className:"flex items-center justify-between border-b pb-2",children:[p.jsxs("div",{className:"flex items-center gap-3",children:[p.jsx("div",{className:"h-2 w-2 bg-amber-500 rounded-full"}),p.jsx("span",{className:"text-sm",children:"New match milestone reached"})]}),p.jsx("span",{className:"text-xs text-muted-foreground",children:"3 days ago"})]}),p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsxs("div",{className:"flex items-center gap-3",children:[p.jsx("div",{className:"h-2 w-2 bg-purple-500 rounded-full"}),p.jsx("span",{className:"text-sm",children:"Profile optimization suggestions received"})]}),p.jsx("span",{className:"text-xs text-muted-foreground",children:"1 week ago"})]})]})})]}),p.jsx("div",{className:"mt-8 flex justify-center",children:p.jsx(r,{asChild:!0,variant:"secondary",className:"flex items-center gap-2",children:p.jsxs(b,{to:"/account-settings",children:[p.jsx(i,{className:"h-4 w-4"}),"Account Settings"]})})})]})})}const SplitComponent=function(){return p.jsx(f,{children:p.jsx(Dashboard,{})})};export{SplitComponent as component};
//# sourceMappingURL=dashboard-CJLf0m0Y.mjs.map
