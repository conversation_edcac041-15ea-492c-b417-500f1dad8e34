import{r as e,j as t,o as n,R as o,c as r}from"./routeTree.gen-BFK54byf.mjs";import{q as s}from"./lucide-react.mjs";function composeEventHandlers(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e?.(o),!1===n||!o.defaultPrevented)return t?.(o)}}function createContextScope(n,o=[]){let r=[];const createScope=()=>{const t=r.map(t=>e.createContext(t));return function(o){const r=o?.[n]||t;return e.useMemo(()=>({[`__scope${n}`]:{...o,[n]:r}}),[o,r])}};return createScope.scopeName=n,[function(o,s){const a=e.createContext(s),i=r.length;r=[...r,s];const Provider=o=>{const{scope:r,children:s,...c}=o,u=r?.[n]?.[i]||a,l=e.useMemo(()=>c,Object.values(c));return t.jsx(u.Provider,{value:l,children:s})};return Provider.displayName=o+"Provider",[Provider,function(t,r){const c=r?.[n]?.[i]||a,u=e.useContext(c);if(u)return u;if(void 0!==s)return s;throw new Error(`\`${t}\` must be used within \`${o}\``)}]},composeContextScopes(createScope,...o)]}function composeContextScopes(...t){const n=t[0];if(1===t.length)return n;const createScope=()=>{const o=t.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(t){const r=o.reduce((e,{useScope:n,scopeName:o})=>({...e,...n(t)[`__scope${o}`]}),{});return e.useMemo(()=>({[`__scope${n.scopeName}`]:r}),[r])}};return createScope.scopeName=n.scopeName,createScope}function setRef$1(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function useComposedRefs$1(...t){return e.useCallback(function(...e){return t=>{let n=!1;const o=e.map(e=>{const o=setRef$1(e,t);return n||"function"!=typeof o||(n=!0),o});if(n)return()=>{for(let t=0;t<o.length;t++){const n=o[t];"function"==typeof n?n():setRef$1(e[t],null)}}}}(...t),t)}var a=globalThis?.document?e.useLayoutEffect:()=>{},i=o[" useId ".trim().toString()]||(()=>{}),c=0;function useId(t){const[n,o]=e.useState(i());return a(()=>{o(e=>e??String(c++))},[t]),t||(n?`radix-${n}`:"")}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((n,o)=>{const r=s(`Primitive.${o}`),a=e.forwardRef((e,n)=>{const{asChild:s,...a}=e,i=s?r:o;return t.jsx(i,{...a,ref:n})});return a.displayName=`Primitive.${o}`,{...n,[o]:a}},{});var l=globalThis?.document?e.useLayoutEffect:()=>{},f=o[" useInsertionEffect ".trim().toString()]||l;function useControllableState({prop:t,defaultProp:n,onChange:o=()=>{},caller:r}){const[s,a,i]=function({defaultProp:t,onChange:n}){const[o,r]=e.useState(t),s=e.useRef(o),a=e.useRef(n);return f(()=>{a.current=n},[n]),e.useEffect(()=>{s.current!==o&&(a.current?.(o),s.current=o)},[o,s]),[o,r,a]}({defaultProp:n,onChange:o}),c=void 0!==t,u=c?t:s;{const n=e.useRef(void 0!==t);e.useEffect(()=>{const e=n.current;if(e!==c){const t=e?"controlled":"uncontrolled",n=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${t} to ${n}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}n.current=c},[c,r])}const l=e.useCallback(e=>{if(c){const n=function(e){return"function"==typeof e}(e)?e(t):e;n!==t&&i.current?.(n)}else a(e)},[c,t,a,i]);return[u,l]}var d=e.createContext(void 0);function useDirection(t){const n=e.useContext(d);return t||n||"ltr"}var m="rovingFocusGroup.onEntryFocus",p={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[b,g,y]=function(e){const o=e+"CollectionProvider",[r,a]=createContextScope(o),[i,c]=r(o,{collectionRef:{current:null},itemMap:new Map}),CollectionProvider=e=>{const{scope:o,children:r}=e,s=n.useRef(null),a=n.useRef(new Map).current;return t.jsx(i,{scope:o,itemMap:a,collectionRef:s,children:r})};CollectionProvider.displayName=o;const u=e+"CollectionSlot",l=s(u),f=n.forwardRef((e,n)=>{const{scope:o,children:r}=e,s=useComposedRefs$1(n,c(u,o).collectionRef);return t.jsx(l,{ref:s,children:r})});f.displayName=u;const d=e+"CollectionItemSlot",m="data-radix-collection-item",p=s(d),v=n.forwardRef((e,o)=>{const{scope:r,children:s,...a}=e,i=n.useRef(null),u=useComposedRefs$1(o,i),l=c(d,r);return n.useEffect(()=>(l.itemMap.set(i,{ref:i,...a}),()=>{l.itemMap.delete(i)})),t.jsx(p,{[m]:"",ref:u,children:s})});return v.displayName=d,[{Provider:CollectionProvider,Slot:f,ItemSlot:v},function(t){const o=c(e+"CollectionConsumer",t);return n.useCallback(()=>{const e=o.collectionRef.current;if(!e)return[];const t=Array.from(e.querySelectorAll(`[${m}]`));return Array.from(o.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[o.collectionRef,o.itemMap])},a]}(v),[h,R]=createContextScope(v,[y]),[N,C]=h(v),w=e.forwardRef((e,n)=>t.jsx(b.Provider,{scope:e.__scopeRovingFocusGroup,children:t.jsx(b.Slot,{scope:e.__scopeRovingFocusGroup,children:t.jsx(x,{...e,ref:n})})}));w.displayName=v;var x=e.forwardRef((n,o)=>{const{__scopeRovingFocusGroup:r,orientation:s,loop:a=!1,dir:i,currentTabStopId:c,defaultCurrentTabStopId:l,onCurrentTabStopIdChange:f,onEntryFocus:d,preventScrollOnEntryFocus:b=!1,...y}=n,h=e.useRef(null),R=useComposedRefs$1(o,h),C=useDirection(i),[w,x]=useControllableState({prop:c,defaultProp:l??null,onChange:f,caller:v}),[I,T]=e.useState(!1),E=function(t){const n=e.useRef(t);return e.useEffect(()=>{n.current=t}),e.useMemo(()=>(...e)=>n.current?.(...e),[])}(d),S=g(r),M=e.useRef(!1),[A,F]=e.useState(0);return e.useEffect(()=>{const e=h.current;if(e)return e.addEventListener(m,E),()=>e.removeEventListener(m,E)},[E]),t.jsx(N,{scope:r,orientation:s,dir:C,loop:a,currentTabStopId:w,onItemFocus:e.useCallback(e=>x(e),[x]),onItemShiftTab:e.useCallback(()=>T(!0),[]),onFocusableItemAdd:e.useCallback(()=>F(e=>e+1),[]),onFocusableItemRemove:e.useCallback(()=>F(e=>e-1),[]),children:t.jsx(u.div,{tabIndex:I||0===A?-1:0,"data-orientation":s,...y,ref:R,style:{outline:"none",...n.style},onMouseDown:composeEventHandlers(n.onMouseDown,()=>{M.current=!0}),onFocus:composeEventHandlers(n.onFocus,e=>{const t=!M.current;if(e.target===e.currentTarget&&t&&!I){const t=new CustomEvent(m,p);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){const e=S().filter(e=>e.focusable);focusFirst([e.find(e=>e.active),e.find(e=>e.id===w),...e].filter(Boolean).map(e=>e.ref.current),b)}}M.current=!1}),onBlur:composeEventHandlers(n.onBlur,()=>T(!1))})})}),I="RovingFocusGroupItem",T=e.forwardRef((n,o)=>{const{__scopeRovingFocusGroup:r,focusable:s=!0,active:a=!1,tabStopId:i,children:c,...l}=n,f=useId(),d=i||f,m=C(I,r),p=m.currentTabStopId===d,v=g(r),{onFocusableItemAdd:y,onFocusableItemRemove:h,currentTabStopId:R}=m;return e.useEffect(()=>{if(s)return y(),()=>h()},[s,y,h]),t.jsx(b.ItemSlot,{scope:r,id:d,focusable:s,active:a,children:t.jsx(u.span,{tabIndex:p?0:-1,"data-orientation":m.orientation,...l,ref:o,onMouseDown:composeEventHandlers(n.onMouseDown,e=>{s?m.onItemFocus(d):e.preventDefault()}),onFocus:composeEventHandlers(n.onFocus,()=>m.onItemFocus(d)),onKeyDown:composeEventHandlers(n.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;const t=function(e,t,n){const o=function(e,t){return"rtl"!==t?e:"ArrowLeft"===e?"ArrowRight":"ArrowRight"===e?"ArrowLeft":e}(e.key,n);return"vertical"===t&&["ArrowLeft","ArrowRight"].includes(o)||"horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)?void 0:E[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();const s=r.indexOf(e.currentTarget);r=m.loop?(o=s+1,(n=r).map((e,t)=>n[(o+t)%n.length])):r.slice(s+1)}setTimeout(()=>focusFirst(r))}var n,o}),children:"function"==typeof c?c({isCurrentTabStop:p,hasTabStop:null!=R}):c})})});T.displayName=I;var E={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function focusFirst(e,t=!1){const n=document.activeElement;for(const o of e){if(o===n)return;if(o.focus({preventScroll:t}),document.activeElement!==n)return}}var S=w,M=T;function setRef(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function useComposedRefs(...t){return e.useCallback(function(...e){return t=>{let n=!1;const o=e.map(e=>{const o=setRef(e,t);return n||"function"!=typeof o||(n=!0),o});if(n)return()=>{for(let t=0;t<o.length;t++){const n=o[t];"function"==typeof n?n():setRef(e[t],null)}}}}(...t),t)}var A=globalThis?.document?e.useLayoutEffect:()=>{};var Presence=t=>{const{present:n,children:o}=t,r=function(t){const[n,o]=e.useState(),r=e.useRef(null),s=e.useRef(t),a=e.useRef("none"),i=t?"mounted":"unmounted",[c,u]=function(t,n){return e.useReducer((e,t)=>n[e][t]??e,t)}(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return e.useEffect(()=>{const e=getAnimationName(r.current);a.current="mounted"===c?e:"none"},[c]),A(()=>{const e=r.current,n=s.current;if(n!==t){const o=a.current,r=getAnimationName(e);if(t)u("MOUNT");else if("none"===r||"none"===e?.display)u("UNMOUNT");else{u(n&&o!==r?"ANIMATION_OUT":"UNMOUNT")}s.current=t}},[t,u]),A(()=>{if(n){let e;const t=n.ownerDocument.defaultView??window,handleAnimationEnd=o=>{const a=getAnimationName(r.current).includes(o.animationName);if(o.target===n&&a&&(u("ANIMATION_END"),!s.current)){const o=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=o)})}},handleAnimationStart=e=>{e.target===n&&(a.current=getAnimationName(r.current))};return n.addEventListener("animationstart",handleAnimationStart),n.addEventListener("animationcancel",handleAnimationEnd),n.addEventListener("animationend",handleAnimationEnd),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",handleAnimationStart),n.removeEventListener("animationcancel",handleAnimationEnd),n.removeEventListener("animationend",handleAnimationEnd)}}u("ANIMATION_END")},[n,u]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:e.useCallback(e=>{r.current=e?getComputedStyle(e):null,o(e)},[])}}(n),s="function"==typeof o?o({present:r.isPresent}):e.Children.only(o),a=useComposedRefs(r.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;if(n)return e.ref;if(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n)return e.props.ref;return e.props.ref||e.ref}(s));return"function"==typeof o||r.isPresent?e.cloneElement(s,{ref:a}):null};function getAnimationName(e){return e?.animationName||"none"}Presence.displayName="Presence";var F="Tabs",[j,D]=createContextScope(F,[R]),_=R(),[P,$]=j(F),O=e.forwardRef((e,n)=>{const{__scopeTabs:o,value:r,onValueChange:s,defaultValue:a,orientation:i="horizontal",dir:c,activationMode:l="automatic",...f}=e,d=useDirection(c),[m,p]=useControllableState({prop:r,onChange:s,defaultProp:a??"",caller:F});return t.jsx(P,{scope:o,baseId:useId(),value:m,onValueChange:p,orientation:i,dir:d,activationMode:l,children:t.jsx(u.div,{dir:d,"data-orientation":i,...f,ref:n})})});O.displayName=F;var k="TabsList",L=e.forwardRef((e,n)=>{const{__scopeTabs:o,loop:r=!0,...s}=e,a=$(k,o),i=_(o);return t.jsx(S,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:r,children:t.jsx(u.div,{role:"tablist","aria-orientation":a.orientation,...s,ref:n})})});L.displayName=k;var U="TabsTrigger",H=e.forwardRef((e,n)=>{const{__scopeTabs:o,value:r,disabled:s=!1,...a}=e,i=$(U,o),c=_(o),l=makeTriggerId(i.baseId,r),f=makeContentId(i.baseId,r),d=r===i.value;return t.jsx(M,{asChild:!0,...c,focusable:!s,active:d,children:t.jsx(u.button,{type:"button",role:"tab","aria-selected":d,"aria-controls":f,"data-state":d?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:l,...a,ref:n,onMouseDown:composeEventHandlers(e.onMouseDown,e=>{s||0!==e.button||!1!==e.ctrlKey?e.preventDefault():i.onValueChange(r)}),onKeyDown:composeEventHandlers(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&i.onValueChange(r)}),onFocus:composeEventHandlers(e.onFocus,()=>{const e="manual"!==i.activationMode;d||s||!e||i.onValueChange(r)})})})});H.displayName=U;var K="TabsContent",G=e.forwardRef((n,o)=>{const{__scopeTabs:r,value:s,forceMount:a,children:i,...c}=n,l=$(K,r),f=makeTriggerId(l.baseId,s),d=makeContentId(l.baseId,s),m=s===l.value,p=e.useRef(m);return e.useEffect(()=>{const e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),t.jsx(Presence,{present:a||m,children:({present:e})=>t.jsx(u.div,{"data-state":m?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":f,hidden:!e,id:d,tabIndex:0,...c,ref:o,style:{...n.style,animationDuration:p.current?"0s":void 0},children:e&&i})})});function makeTriggerId(e,t){return`${e}-trigger-${t}`}function makeContentId(e,t){return`${e}-content-${t}`}G.displayName=K;var V=L,B=H,W=G;const q=O,z=e.forwardRef(({className:e,...n},o)=>t.jsx(V,{ref:o,className:r("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...n}));z.displayName=V.displayName;const J=e.forwardRef(({className:e,...n},o)=>t.jsx(B,{ref:o,className:r("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-base font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-gradient-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm",e),...n}));J.displayName=B.displayName;const Q=e.forwardRef(({className:e,...n},o)=>t.jsx(W,{ref:o,className:r("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...n}));Q.displayName=W.displayName;export{q as T,z as a,J as b,Q as c};
//# sourceMappingURL=tabs-D9-6MReb.mjs.map
