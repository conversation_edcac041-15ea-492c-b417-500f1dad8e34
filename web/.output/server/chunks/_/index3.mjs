import{r as e,j as t}from"./routeTree.gen-BFK54byf.mjs";function composeEventHandlers(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e?.(o),!1===n||!o.defaultPrevented)return t?.(o)}}function createContext2(n,o){const r=e.createContext(o),Provider=n=>{const{children:o,...c}=n,s=e.useMemo(()=>c,Object.values(c));return t.jsx(r.Provider,{value:s,children:o})};return Provider.displayName=n+"Provider",[Provider,function(t){const c=e.useContext(r);if(c)return c;if(void 0!==o)return o;throw new Error(`\`${t}\` must be used within \`${n}\``)}]}function createContextScope(n,o=[]){let r=[];const createScope=()=>{const t=r.map(t=>e.createContext(t));return function(o){const r=o?.[n]||t;return e.useMemo(()=>({[`__scope${n}`]:{...o,[n]:r}}),[o,r])}};return createScope.scopeName=n,[function(o,c){const s=e.createContext(c),u=r.length;r=[...r,c];const Provider=o=>{const{scope:r,children:c,...a}=o,f=r?.[n]?.[u]||s,i=e.useMemo(()=>a,Object.values(a));return t.jsx(f.Provider,{value:i,children:c})};return Provider.displayName=o+"Provider",[Provider,function(t,r){const a=r?.[n]?.[u]||s,f=e.useContext(a);if(f)return f;if(void 0!==c)return c;throw new Error(`\`${t}\` must be used within \`${o}\``)}]},composeContextScopes(createScope,...o)]}function composeContextScopes(...t){const n=t[0];if(1===t.length)return n;const createScope=()=>{const o=t.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(t){const r=o.reduce((e,{useScope:n,scopeName:o})=>({...e,...n(t)[`__scope${o}`]}),{});return e.useMemo(()=>({[`__scope${n.scopeName}`]:r}),[r])}};return createScope.scopeName=n.scopeName,createScope}function useCallbackRef(t){const n=e.useRef(t);return e.useEffect(()=>{n.current=t}),e.useMemo(()=>(...e)=>n.current?.(...e),[])}function useControllableState({prop:t,defaultProp:n,onChange:o=()=>{}}){const[r,c]=function({defaultProp:t,onChange:n}){const o=e.useState(t),[r]=o,c=e.useRef(r),s=useCallbackRef(n);return e.useEffect(()=>{c.current!==r&&(s(r),c.current=r)},[r,c,s]),o}({defaultProp:n,onChange:o}),s=void 0!==t,u=s?t:r,a=useCallbackRef(o);return[u,e.useCallback(e=>{if(s){const n="function"==typeof e?e(t):e;n!==t&&a(n)}else c(e)},[s,t,c,a])]}var n=Boolean(globalThis?.document)?e.useLayoutEffect:()=>{};export{useCallbackRef as a,useControllableState as b,composeEventHandlers as c,createContextScope as d,createContext2 as e,n as u};
//# sourceMappingURL=index3.mjs.map
