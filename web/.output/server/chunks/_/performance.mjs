import{c as e}from"../nitro/nitro.mjs";const r=globalThis.performance?.timeOrigin??Date.now(),t=globalThis.performance?.now?globalThis.performance.now.bind(globalThis.performance):()=>Date.now()-r,n=["event","mark","measure","resource"];class _PerformanceEntry{__unenv__=!0;detail;entryType="event";name;startTime;constructor(e,r){this.name=e,this.startTime=r?.startTime||t(),this.detail=r?.detail}get duration(){return t()-this.startTime}toJSON(){return{name:this.name,entryType:this.entryType,startTime:this.startTime,duration:this.duration,detail:this.detail}}}class _PerformanceMark extends _PerformanceEntry{entryType="mark"}class _PerformanceMeasure extends _PerformanceEntry{entryType="measure"}class _Performance{__unenv__=!0;timeOrigin=r;eventCounts=new Map;_entries=[];_resourceTimingBufferSize=0;navigation=void 0;timing=void 0;onresourcetimingbufferfull=null;now(){return this.timeOrigin===r?t():Date.now()-this.timeOrigin}clearMarks(e){this._entries=e?this._entries.filter(r=>r.name!==e):this._entries.filter(e=>"mark"!==e.entryType)}clearMeasures(e){this._entries=e?this._entries.filter(r=>r.name!==e):this._entries.filter(e=>"measure"!==e.entryType)}clearResourceTimings(){this._entries=this._entries.filter(e=>"resource"!==e.entryType||"navigation"!==e.entryType)}getEntries(){return this._entries}getEntriesByName(e,r){return this._entries.filter(t=>t.name===e&&(!r||t.entryType===r))}getEntriesByType(e){return this._entries.filter(r=>r.entryType===e)}mark(e,r){const t=new _PerformanceMark(e,r);return this._entries.push(t),t}measure(e,r,t){let n,s;"string"==typeof r?(n=this.getEntriesByName(r,"mark")[0]?.startTime,s=this.getEntriesByName(t,"mark")[0]?.startTime):(n=Number.parseFloat(r?.start)||this.now(),s=Number.parseFloat(r?.end)||this.now());const i=new _PerformanceMeasure(e,{startTime:n,detail:{start:n,end:s}});return this._entries.push(i),i}setResourceTimingBufferSize(e){this._resourceTimingBufferSize=e}toJSON(){return this}addEventListener(r,t,n){throw e("Performance.addEventListener")}removeEventListener(r,t,n){throw e("Performance.removeEventListener")}dispatchEvent(r){throw e("Performance.dispatchEvent")}}const s=globalThis.PerformanceEntry||_PerformanceEntry,i=globalThis.PerformanceMark||_PerformanceMark,a=globalThis.PerformanceMeasure||_PerformanceMeasure,o=globalThis.PerformanceResourceTiming||class extends _PerformanceEntry{entryType="resource";serverTiming=[];connectEnd=0;connectStart=0;decodedBodySize=0;domainLookupEnd=0;domainLookupStart=0;encodedBodySize=0;fetchStart=0;initiatorType="";name="";nextHopProtocol="";redirectEnd=0;redirectStart=0;requestStart=0;responseEnd=0;responseStart=0;secureConnectionStart=0;startTime=0;transferSize=0;workerStart=0;responseStatus=0},c=globalThis.PerformanceObserver||class{__unenv__=!0;static supportedEntryTypes=n;_callback=null;constructor(e){this._callback=e}takeRecords(){return[]}disconnect(){throw e("PerformanceObserver.disconnect")}observe(r){throw e("PerformanceObserver.observe")}},m=globalThis.Performance||_Performance,l=globalThis.PerformanceObserverEntryList||class{__unenv__=!0;getEntries(){return[]}getEntriesByName(e,r){return[]}getEntriesByType(e){return[]}},h=globalThis.performance&&"addEventListener"in globalThis.performance?globalThis.performance:new _Performance;globalThis.performance||=h,globalThis.Performance||=m,globalThis.PerformanceEntry||=s,globalThis.PerformanceMark||=i,globalThis.PerformanceMeasure||=a,globalThis.PerformanceObserver||=c,globalThis.PerformanceObserverEntryList||=l,globalThis.PerformanceResourceTiming||=o;const f=globalThis.performance;export{f as p};
//# sourceMappingURL=performance.mjs.map
