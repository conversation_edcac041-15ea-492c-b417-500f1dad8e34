{"version": 3, "file": "account-settings-CdZt_AKP.mjs", "sources": ["../../../../node_modules/@radix-ui/react-separator/dist/index.mjs", "../../../../../src/components/ui/separator.tsx", "../../../../../src/pages/AccountSettings.tsx", "../../../../../src/routes/_authed/account-settings.tsx?tsr-split=component"], "sourcesContent": null, "names": ["DEFAULT_ORIENTATION", "ORIENTATIONS", "Separator", "React.forwardRef", "props", "forwardedRef", "decorative", "orientation", "orientationProp", "domProps", "includes", "isValidOrientation", "semanticProps", "role", "jsx", "Primitive", "div", "ref", "displayName", "Root", "className", "SeparatorPrimitive.Root", "cn", "AccountSettings", "user", "useUser", "children", "jsxs", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "variant", "size", "Link", "to", "ArrowLeft", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "UserProfile", "appearance", "elements", "rootBox", "card", "navbarMobileMenuButton", "Shield", "Badge", "Bell", "_a", "firstName", "fullName", "_b", "primaryEmail<PERSON>dd<PERSON>", "emailAddress", "Date", "createdAt", "now", "toLocaleDateString", "CreditCard", "Download", "Trash2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SplitComponent", "ProtectedRoute"], "mappings": "", "x_google_ignoreList": [0]}