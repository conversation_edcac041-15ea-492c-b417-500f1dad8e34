{"version": 3, "file": "index2.mjs", "sources": ["../../../../node_modules/@openrouter/ai-sdk-provider/dist/index.mjs"], "sourcesContent": null, "names": ["__defProp", "Object", "defineProperty", "__defProps", "defineProperties", "__getOwnPropDescs", "getOwnPropertyDescriptors", "__getOwnPropSymbols", "getOwnPropertySymbols", "__hasOwnProp", "prototype", "hasOwnProperty", "__propIsEnum", "propertyIsEnumerable", "__defNormalProp", "obj", "key", "value", "enumerable", "configurable", "writable", "__spreadValues", "a", "b", "prop", "call", "__spreadProps", "__objRest", "source", "exclude", "target", "indexOf", "ReasoningDetailUnionSchema", "z.union", "z.object", "type", "z.literal", "summary", "z.string", "data", "text", "nullish", "signature", "ReasoningDetailArraySchema", "z.array", "z.unknown", "transform", "d", "filter", "d2", "getCacheControl", "providerMetadata", "_a", "_b", "_c", "anthropic", "openrouter2", "openrouter", "cacheControl", "cache_control", "convertToOpenRouterChatMessages", "prompt", "messages", "role", "content", "push", "length", "messageCacheControl", "contentParts", "map", "part", "_a2", "_b2", "_c2", "_d", "image_url", "url", "image", "URL", "toString", "mimeType", "convertUint8ArrayToBase64", "file", "filename", "String", "file_data", "Uint8Array", "Error", "reasoning", "reasoningDetails", "toolCalls", "id", "toolCallId", "function", "name", "toolName", "arguments", "JSON", "stringify", "args", "tool_calls", "reasoning_details", "toolResponse", "tool_call_id", "result", "mapOpenRouterChatLogProbsOutput", "logprobs", "token", "logprob", "top_logprobs", "topLogprobs", "token2", "logprob2", "mapOpenRouterFinishReason", "finishReason", "OpenRouterErrorResponseSchema", "z2.object", "error", "code", "z2.union", "z2.string", "z2.number", "nullable", "message", "param", "z2.any", "openrouterFailedResponseHandler", "createJsonErrorResponseHandler", "errorSchema", "errorToMessage", "OpenRouterChatLanguageModel", "constructor", "modelId", "settings", "config", "this", "specificationVersion", "defaultObjectGenerationMode", "provider", "getArgs", "mode", "maxTokens", "temperature", "topP", "frequencyPenalty", "presencePenalty", "seed", "stopSequences", "responseFormat", "topK", "extraCallingBody", "baseArgs", "model", "models", "logit_bias", "logitBias", "user", "parallel_tool_calls", "parallelToolCalls", "max_tokens", "top_p", "frequency_penalty", "presence_penalty", "stop", "response_format", "top_k", "include_reasoning", "includeReasoning", "usage", "extraBody", "tools", "tool_choice", "mappedTools", "tool", "isFunctionTool", "description", "parameters", "toolChoice", "prepareToolsAndToolChoice", "UnsupportedFunctionalityError", "functionality", "doGenerate", "options", "_e", "_f", "_g", "_h", "_i", "_j", "responseHeaders", "response", "postJsonToApi", "path", "headers", "combineHeaders", "body", "failedResponseHandler", "successfulResponseHandler", "createJsonResponseHandler", "OpenRouterNonStreamChatCompletionResponseSchema", "abortSignal", "fetch", "rawPrompt", "rawSettings", "choice", "choices", "usageInfo", "promptTokens", "prompt_tokens", "completionTokens", "completion_tokens", "include", "promptTokensDetails", "prompt_tokens_details", "cachedTokens", "cached_tokens", "completionTokensDetails", "completion_tokens_details", "reasoningTokens", "reasoning_tokens", "cost", "totalTokens", "total_tokens", "hasProviderMetadata", "keys", "detail", "p", "toolCall", "toolCallType", "generateId", "finish_reason", "rawCall", "rawResponse", "warnings", "doStream", "stream", "stream_options", "compatibility", "include_usage", "createEventSourceResponseHandler", "OpenRouterStreamChatCompletionChunkSchema", "Number", "NaN", "openrouterUsage", "shouldIncludeUsageAccounting", "pipeThrough", "TransformStream", "chunk", "controller", "_k", "_l", "_m", "_n", "success", "enqueue", "delta", "text<PERSON><PERSON><PERSON>", "mappedLogprobs", "toolCallDelta", "index", "InvalidResponseDataError", "sent", "toolCall2", "isParsable<PERSON>son", "argsTextDelta", "flush", "OpenRouterChatCompletionBaseResponseSchema", "z3.object", "z3.string", "optional", "z3.number", "extend", "z3.array", "z3.literal", "z3.union", "z3.enum", "mapOpenRouterCompletionLogProbs", "tokens", "token_logprobs", "entries", "OpenRouterCompletionLanguageModel", "inputFormat", "completionPrompt", "assistant", "slice", "InvalidPromptError", "UnsupportedFunctionalityError2", "join", "convertToOpenRouterCompletionPrompt", "suffix", "UnsupportedFunctionalityError3", "postJsonToApi2", "combineHeaders2", "createJsonResponseHandler2", "OpenRouterCompletionChunkSchema", "createEventSourceResponseHandler2", "z4.union", "z4.object", "z4.string", "z4.array", "z4.number", "z4.record", "baseURL", "withoutTrailingSlash2", "baseUrl", "getHeaders", "Authorization", "loadApiKey2", "<PERSON><PERSON><PERSON><PERSON>", "environmentVariableName", "createChatModel", "createCompletionModel", "createLanguageModel", "languageModel", "chat", "completion", "createOpenRouter"], "mappings": "", "x_google_ignoreList": [0]}