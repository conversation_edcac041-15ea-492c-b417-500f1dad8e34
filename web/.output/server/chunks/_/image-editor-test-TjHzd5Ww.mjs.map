{"version": 3, "file": "image-editor-test-TjHzd5Ww.mjs", "sources": ["../../../../node_modules/@ai-sdk/openai/dist/index.mjs", "../../../../../src/lib/image-editing/security.ts", "../../../../../src/lib/image-editing/cache.ts", "../../../../../src/lib/image-editing/rate-limiter.ts", "../../../../../src/lib/image-editing/image-editing-service.ts", "../../../../node_modules/@radix-ui/react-switch/dist/index.mjs", "../../../../node_modules/@radix-ui/react-use-previous/dist/index.mjs", "../../../../node_modules/@radix-ui/react-use-size/dist/index.mjs", "../../../../node_modules/@radix-ui/react-label/dist/index.mjs", "../../../../../src/components/ui/alert.tsx", "../../../../../src/components/ui/switch.tsx", "../../../../../src/components/ui/label.tsx", "../../../../../src/lib/image-editing/error-handler.ts", "../../../../../src/lib/image-editing/logger.ts", "../../../../../src/lib/image-editing/queue-manager.ts", "../../../../../src/lib/image-editing/hooks/use-image-editing.ts", "../../../../../src/routes/_authed/image-editor-test.tsx?tsr-split=component", "../../../../../src/pages/ImageEditorTest.tsx", "../../../../../src/lib/image-editing/hooks/use-image-editing-queue.ts", "../../../../../src/lib/image-editing/hooks/use-image-editing-analytics.ts"], "sourcesContent": null, "names": ["mapOpenAIChatLogProbsOutput", "logprobs", "_a", "_b", "content", "map", "token", "logprob", "top_logprobs", "topLogprobs", "token2", "logprob2", "mapOpenAIFinishReason", "finishReason", "openaiErrorDataSchema", "z.object", "error", "message", "z.string", "type", "nullish", "param", "z.any", "code", "z.union", "z.number", "openaiFailedResponseHandler", "createJsonErrorResponseHandler", "errorSchema", "errorToMessage", "data", "getResponseMetadata", "id", "model", "created", "modelId", "timestamp", "Date", "OpenAIChatLanguageModel", "constructor", "settings", "config", "this", "specificationVersion", "supportsStructuredOutputs", "structuredOutputs", "isReasoningModel", "defaultObjectGenerationMode", "startsWith", "provider", "supportsImageUrls", "downloadImages", "getArgs", "mode", "prompt", "maxTokens", "temperature", "topP", "topK", "frequencyPenalty", "presencePenalty", "stopSequences", "responseFormat", "seed", "providerMetadata", "_c", "_d", "_e", "_f", "_g", "_h", "warnings", "push", "setting", "schema", "details", "useLegacyFunctionCalling", "parallelToolCalls", "UnsupportedFunctionalityError3", "functionality", "messages", "messageWarnings", "systemMessageMode", "role", "Error", "length", "text", "part", "index", "image_url", "url", "image", "URL", "toString", "mimeType", "convertUint8ArrayToBase64", "detail", "openai", "imageDetail", "UnsupportedFunctionalityError", "input_audio", "format", "file", "filename", "file_data", "toolCalls", "toolCallId", "function", "name", "toolName", "arguments", "JSON", "stringify", "args", "function_call", "tool_calls", "toolResponse", "result", "tool_call_id", "convertToOpenAIChatMessages", "getSystemMessageMode", "baseArgs", "logit_bias", "logitBias", "user", "parallel_tool_calls", "max_tokens", "top_p", "frequency_penalty", "presence_penalty", "response_format", "json_schema", "strict", "description", "stop", "max_completion_tokens", "maxCompletionTokens", "store", "metadata", "prediction", "reasoning_effort", "<PERSON><PERSON><PERSON>ort", "tools", "tool_choice", "functions", "toolWarnings", "toolChoice", "openaiFunctions", "tool", "parameters", "UnsupportedFunctionalityError2", "openaiTools2", "prepareTools", "doGenerate", "options", "body", "responseHeaders", "value", "response", "rawValue", "rawResponse", "postJsonToApi", "path", "headers", "combineHeaders", "failedResponseHandler", "successfulResponseHandler", "createJsonResponseHandler", "openaiChatResponseSchema", "abortSignal", "fetch", "rawPrompt", "rawSettings", "choice", "choices", "completionTokenDetails", "usage", "completion_tokens_details", "promptTokenDetails", "prompt_tokens_details", "reasoning_tokens", "reasoningTokens", "accepted_prediction_tokens", "acceptedPredictionTokens", "rejected_prediction_tokens", "rejectedPredictionTokens", "cached_tokens", "cachedPromptTokens", "toolCallType", "generateId", "toolCall", "_a2", "finish_reason", "promptTokens", "prompt_tokens", "NaN", "completionTokens", "completion_tokens", "rawCall", "request", "doStream", "simulateStreaming", "stream", "ReadableStream", "start", "controller", "enqueue", "text<PERSON><PERSON><PERSON>", "argsTextDelta", "close", "stream_options", "compatibility", "include_usage", "createEventSourceResponseHandler", "openaiChatChunkSchema", "isFirstChunk", "pipeThrough", "TransformStream", "transform", "chunk", "_i", "_j", "_k", "_l", "success", "delta", "mappedLogprobs", "mappedToolCalls", "toolCallDelta", "InvalidResponseDataError", "hasFinished", "toolCall2", "isParsable<PERSON>son", "flush", "openaiTokenUsageSchema", "z2.object", "z2.number", "z2.string", "z2.array", "z2.literal", "nullable", "z2.union", "z2.enum", "optional", "reasoningModels", "o3", "mapOpenAICompletionLogProbs", "tokens", "token_logprobs", "Object", "entries", "OpenAICompletionLanguageModel", "inputFormat", "userStopSequences", "completionPrompt", "assistant", "slice", "InvalidPromptError", "UnsupportedFunctionalityError4", "join", "convertToOpenAICompletionPrompt", "echo", "suffix", "UnsupportedFunctionalityError5", "postJsonToApi2", "combineHeaders2", "createJsonResponseHandler2", "openaiCompletionResponseSchema", "createEventSourceResponseHandler2", "openaiCompletionChunkSchema", "Number", "z3.object", "z3.string", "z3.number", "z3.array", "z3.record", "z3.union", "OpenAIEmbeddingModel", "maxEmbeddingsPerCall", "supportsParallelCalls", "doEmbed", "values", "TooManyEmbeddingValuesForCallError", "postJsonToApi3", "combineHeaders3", "input", "encoding_format", "dimensions", "createJsonResponseHandler3", "openaiTextEmbeddingResponseSchema", "embeddings", "item", "embedding", "z4.object", "z4.array", "z4.number", "modelMaxImagesPerCall", "hasDefaultResponseFormat", "Set", "OpenAIImageModel", "maxImagesPerCall", "n", "size", "aspectRatio", "providerOptions", "currentDate", "_internal", "call", "postJsonToApi4", "combineHeaders4", "has", "createJsonResponseHandler4", "openaiImageResponseSchema", "images", "b64_json", "z5.object", "z5.array", "z5.string", "openAIProviderOptionsSchema", "z6.object", "include", "z6.array", "z6.string", "language", "z6.number", "min", "max", "default", "timestampGranularities", "z6.enum", "languageMap", "afrikaans", "arabic", "armenian", "azerbaijani", "belarusian", "bosnian", "bulgarian", "catalan", "chinese", "croatian", "czech", "danish", "dutch", "english", "estonian", "finnish", "french", "galician", "german", "greek", "hebrew", "hindi", "hungarian", "icelandic", "indonesian", "italian", "japanese", "kannada", "kazakh", "korean", "latvian", "lithuanian", "macedonian", "malay", "marathi", "maori", "nepali", "norwegian", "persian", "polish", "portuguese", "romanian", "russian", "serbian", "slovak", "slovenian", "spanish", "swahili", "swedish", "tagalog", "tamil", "thai", "turkish", "ukrainian", "urdu", "vietnamese", "welsh", "OpenAITranscriptionModel", "audio", "mediaType", "openAIOptions", "parseProviderOptions", "formData", "FormData", "blob", "Uint8Array", "Blob", "convertBase64ToUint8Array", "append", "File", "transcriptionModelOptions", "timestamp_granularities", "key", "String", "postFormDataToApi", "combineHeaders5", "createJsonResponseHandler5", "openaiTranscriptionResponseSchema", "segments", "words", "word", "startSecond", "endSecond", "end", "durationInSeconds", "duration", "mapOpenAIResponseFinishReason", "hasToolCalls", "OpenAIResponsesLanguageModel", "modelConfig", "requiredAutoTruncation", "getResponsesModelConfig", "convertUint8ArrayToBase642", "UnsupportedFunctionalityError6", "call_id", "output", "convertToOpenAIResponsesMessages", "openaiOptions", "parseProviderOptions2", "openaiResponsesProviderOptionsSchema", "isStrict", "strictSchemas", "max_output_tokens", "previous_response_id", "previousResponseId", "instructions", "reasoning<PERSON><PERSON><PERSON><PERSON>", "reasoning", "effort", "summary", "truncation", "search_context_size", "searchContextSize", "user_location", "userLocation", "UnsupportedFunctionalityError7", "prepareResponsesTools", "postJsonToApi5", "combineHeaders6", "createJsonResponseHandler6", "z7.object", "z7.string", "created_at", "z7.number", "z7.array", "z7.discriminatedUnion", "z7.literal", "annotations", "start_index", "end_index", "title", "incomplete_details", "reason", "usageSchema", "outputTextElements", "filter", "flatMap", "find", "sources", "annotation", "_b2", "_c2", "sourceType", "generateId2", "input_tokens", "output_tokens", "responseId", "input_tokens_details", "output_tokens_details", "createEventSourceResponseHandler3", "openaiResponsesChunkSchema", "self", "ongoingToolCalls", "isResponseOutputItemAddedChunk", "output_index", "isResponseFunctionCallArgumentsDeltaChunk", "isResponseCreatedChunk", "isTextDeltaChunk", "isResponseReasoningSummaryTextDeltaChunk", "isResponseOutputItemDoneChunk", "isResponseFinishedChunk", "isResponseAnnotationAddedChunk", "source", "z7.union", "z7.enum", "status", "item_id", "summary_index", "passthrough", "z7.any", "z7.boolean", "WebSearchPreviewParameters", "z8.object", "openaiTools", "webSearchPreview", "OpenAIProviderOptionsSchema", "z9.object", "z9.string", "speed", "z9.number", "OpenAISpeechModel", "voice", "outputFormat", "parseProviderOptions3", "requestBody", "includes", "speechModelOptions", "postJsonToApi6", "combineHeaders7", "createBinaryResponseHandler", "baseURL", "withoutTrailingSlash", "providerName", "getHeaders", "Authorization", "loadApiKey", "<PERSON><PERSON><PERSON><PERSON>", "environmentVariableName", "organization", "project", "createChatModel", "createCompletionModel", "createEmbeddingModel", "createImageModel", "createTranscriptionModel", "createSpeechModel", "createLanguageModel", "languageModel", "chat", "completion", "responses", "textEmbedding", "textEmbeddingModel", "imageModel", "transcription", "transcriptionModel", "speech", "speechModel", "createOpenAI", "securityService", "config?: Partial<SecurityConfig>", "__publicField", "maxFileSize", "maxDimensions", "width", "height", "allowedMimeTypes", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enableContentScanning", "enableMetadataRemoval", "validateFile", "file: File", "errors: string[]", "warnings: string[]", "errors", "sanitizedFileName", "sanitizeFilename", "contentValidation", "validateImageContent", "valid", "getImageDimensions", "sanitizedData", "fileName", "validateBase64Image", "base64Data: string", "fileName: string", "parseBase64Metadata", "base64Data", "getBase64ImageDimensions", "filename: string", "sanitized", "replace", "trim", "ext", "split", "pop", "nameWithoutExt", "removeImageMetadata", "img", "Image", "canvas", "document", "createElement", "ctx", "getContext", "Promise", "resolve", "reject", "onload", "drawImage", "cleanBase64", "toDataURL", "onerror", "src", "console", "warn", "arrayBuffer", "uint8Array", "hasValidImageHeader", "suspiciousPatterns", "headerString", "Array", "from", "byte", "fromCharCode", "pattern", "test", "uint8Array: Uint8Array", "mimeType: string", "objectUrl", "createObjectURL", "revokeObjectURL", "mimeMatch", "match", "base64Content", "Math", "floor", "validateContentPolicy", "issues: string[]", "issues", "compliant", "generateSecureCacheKey", "data: string", "salt?: string", "dataBytes", "TextEncoder", "encode", "salt", "crypto", "subtle", "digest", "then", "hash<PERSON><PERSON><PERSON>", "b", "padStart", "catch", "hash", "i", "charCodeAt", "abs", "sanitizePrompt", "prompt: string", "substring", "imageEditingCache", "config?: Partial<CacheConfig>", "Map", "hits", "misses", "evictions", "memoryUsage", "entryCount", "hitRate", "averageAccessTime", "maxMemorySize", "maxEntries", "defaultTTL", "enablePersistence", "compressionLevel", "gcInterval", "initializeCache", "startGarbageCollection", "persistedData", "localStorage", "getItem", "parsedData", "parse", "now", "entry", "cacheEntry", "ttl", "cache", "set", "updateStats", "gcTimer", "clearInterval", "window", "setInterval", "performGarbageCollection", "get", "key: string", "startTime", "performance", "stats", "updateHitRate", "delete", "accessCount", "lastAccess", "accessTime", "recordAccessTime", "compressed", "decompress", "data: EditingAnalysisResult", "ttl: number", "dataSize", "calculateDataSize", "ensureCapacity", "finalData", "compress", "entry: <PERSON><PERSON><PERSON><PERSON><PERSON>", "persistCache", "deleted", "clear", "removeItem", "getStats", "getConfig", "updateConfig", "newConfig: Partial<CacheConfig>", "newConfig", "generate<PERSON>ache<PERSON>ey", "imageData: string", "preferences?: {\n      conservative?: boolean;\n      style?: string;\n      maxBudget?: number;\n      priorityTypes?: string[];\n    }", "combined", "imageData", "preferences", "requiredSize: number", "requiredSize", "evictLRU", "sort", "a", "freedBytes", "requiredBytes", "expiredKeys: string[]", "<PERSON><PERSON><PERSON><PERSON>", "cacheData", "fromEntries", "serialized", "availableSpace", "getAvailableStorageSpace", "setItem", "totalSpace", "maxSpace", "gzipCompress", "_compressed", "_data", "decompressed", "gzipDecompress", "CompressionStream", "writer", "writable", "getWriter", "reader", "readable", "<PERSON><PERSON><PERSON><PERSON>", "chunks: Uint8Array[]", "write", "read", "done", "chunks", "reduce", "acc", "offset", "btoa", "compressedData: string", "compressedData", "DecompressionStream", "binaryString", "atob", "bytes", "TextDecoder", "decode", "time: number", "accessTimes", "time", "shift", "sum", "t", "total", "totalSize", "dispose", "rateLimiter", "config?: Partial<RateLimitConfig>", "totalUsers", "activeUsers", "totalRequests", "totalCost", "averageResponseTime", "errorRate", "concurrentRequests", "analysisPerHour", "analysisPerDay", "generationPerHour", "generationPerDay", "maxConcurrentRequests", "maxConcurrentPerUser", "dailyCostLimit", "monthlyCostLimit", "burstAnalysisLimit", "burstGenerationLimit", "burst<PERSON><PERSON>ow", "analysisRecoveryRate", "generationRecoveryRate", "premiumMultiplier", "proMultiplier", "initializeFromStorage", "startCleanupTimer", "startPersistenceTimer", "checkAnalysisLimit", "userId: string", "userTier: 'free' | 'premium' | 'pro'", "getUserStats", "userId", "userTier", "limits", "getEffectiveLimits", "updateUsageCounts", "allowed", "remainingRequests", "resetTime", "retryAfter", "costRemaining", "getRemainingCost", "globalStats", "burstUsage", "analysis", "timeSinceWindow", "windowStart", "analysisCount", "hour", "hourResetTime", "day", "dayResetTime", "checkGenerationLimit", "estimatedCost: number", "costUsage", "daily", "estimatedCost", "dailyResetTime", "monthly", "monthlyResetTime", "generation", "generationCount", "recordAnalysisRequest", "processingTime: number", "recordResponseTime", "processingTime", "setTimeout", "recordGenerationRequest", "cost: number", "cost", "recordFailedRequest", "reason: string", "violations", "count", "lastViolation", "updateErrorRate", "getUserUsageStats", "lastUsed", "dailyLimits", "getGlobalStats", "resetUserLimits", "newConfig: Partial<RateLimitConfig>", "multiplier", "userStats", "stats: UserUsageStats", "requestTimes", "errorCount", "stored", "persistToStorage", "cleanupTimer", "cleanupExpiredStats", "persistenceTimer", "expiredUsers: string[]", "expiredUsers", "imageEditingService", "log", "analyzeImageForEditing", "request: EditingRequest", "config: EditingServiceConfig", "isProcessing", "cache<PERSON>ey", "cachedResult", "onComplete", "securityResult", "currentUserId", "rateLimitResult", "result: EditingAnalysisResult", "originalImage", "canEdit", "editingRecommendations", "quickFixes", "transformativeEdits", "totalRecommendations", "estimatedTotalCost", "confidence", "processed", "recommendations", "executeEditingAnalysis", "stepId", "<PERSON><PERSON><PERSON>", "progress", "progressData: EditingProgress", "currentStep", "totalSteps", "EDITING_ANALYSIS_STEPS", "estimatedTimeRemaining", "estimateTimeRemaining", "onProgress", "progressData", "categorizedRecommendations", "categorizeRecommendations", "all", "transformative", "calculateTotalCost", "calculateOverallConfidence", "errorMessage", "onError", "generateEditedImage", "request: ImageGenerationRequest", "estimateGenerationCost", "editingPrompt", "generateEditingPrompt", "recommendation", "editedImage", "callOpenAIImageAPI", "quality", "inputFidelity", "actualCost", "calculateActualCost", "onProgress?: (step: number, stepName: string, progress: number) => void", "recommendations: EditingRecommendation[]", "steps", "step", "stepRecommendations", "executeEditingStep", "stepId: number", "stepName: string", "getEditingStepPrompt", "generateText", "parseEditingRecommendations", "baseInstruction", "stepPrompts", "response: string", "jsonMatch", "parsed", "isArray", "rec: any", "index: number", "rec", "category", "impactScore", "parseInt", "difficulty", "estimatedTime", "toolsRecommended", "parseFloat", "applied", "dismissed", "createdAt", "totalConfidence", "round", "recommendation: EditingRecommendation", "typePrompts", "lighting", "background", "composition", "color", "style", "clothing", "expression", "posture", "enhancement", "object_removal", "originalImage: string", "quality: 'standard' | 'high' | 'ultra'", "inputFidelity: 'standard' | 'high'", "standard", "high", "ultra", "baseCost", "timeMultiplier", "progress: number", "startTime: number", "elapsed", "remaining", "setUserId", "isCurrentlyProcessing", "getServiceStats", "apiKeyConfigured", "SWITCH_NAME", "createSwitchContext", "createSwitchScope", "createContextScope", "SwitchProvider", "useSwitchContext", "Switch", "React.forwardRef", "props", "forwardedRef", "__scopeSwitch", "checked", "checkedProp", "defaultChecked", "required", "disabled", "onCheckedChange", "form", "switchProps", "button", "setButton", "React.useState", "composedRefs", "useComposedRefs", "node", "hasConsumerStoppedPropagationRef", "React.useRef", "isFormControl", "closest", "setChecked", "useControllableState", "prop", "defaultProp", "onChange", "jsxs", "scope", "children", "jsx", "Primitive", "getState", "ref", "onClick", "composeEventHandlers", "event", "prevChecked", "current", "isPropagationStopped", "stopPropagation", "BubbleInput", "control", "bubbles", "displayName", "THUMB_NAME", "SwitchThumb", "thumbProps", "context", "span", "inputProps", "previous", "React.useMemo", "usePrevious", "controlSize", "element", "setSize", "useLayoutEffect", "offsetWidth", "offsetHeight", "resizeObserver", "ResizeObserver", "borderSizeEntry", "borderSize", "observe", "box", "unobserve", "useSize", "React.useEffect", "inputProto", "HTMLInputElement", "prototype", "getOwnPropertyDescriptor", "Event", "dispatchEvent", "tabIndex", "position", "pointerEvents", "opacity", "margin", "Root", "Thumb", "Label", "label", "onMouseDown", "target", "defaultPrevented", "preventDefault", "alertVariants", "cva", "variants", "variant", "destructive", "defaultVariants", "<PERSON><PERSON>", "React$1.forwardRef", "className", "cn", "AlertDescription", "SwitchPrimitives.Root", "SwitchPrimitives.Thumb", "labelVariants", "LabelPrimitive.Root", "handleWithRetry", "operation: () => Promise<T>", "context: string", "retryCount: number", "operation", "errorInfo", "categorizeError", "logError", "retryCount", "retryable", "maxRetries", "delay", "calculateDelay", "sleep", "transformError", "error: <PERSON><PERSON><PERSON>", "toLowerCase", "errorString", "extractRetryAfter", "originalMessage", "stack", "originalError: <PERSON><PERSON><PERSON>", "errorInfo: ErrorInfo", "originalError", "message: string", "minuteMatch", "retryAfter?: number", "max<PERSON><PERSON><PERSON>", "exponentialDelay", "baseDelay", "pow", "jitter", "random", "errorDetails", "toISOString", "userAgent", "navigator", "location", "href", "sendToErrorTracking", "errorDetails: any", "splice", "e", "getRecentErrors", "clearErrors", "ms: number", "ms", "validateImage", "imageBase64: string", "sizeInBytes", "imageBase64", "maxSizeInBytes", "toFixed", "supportedFormats", "getImageFormat", "isValidBase64", "base64: string", "header", "base64", "str: string", "str", "imageEditingLogger", "startHealthMonitoring", "logCacheHit", "requestId: string", "imageHash: string", "requestId", "imageHash", "logCache<PERSON><PERSON>", "logApiRequest", "endpoint: string", "method: string", "endpoint", "method", "logApiResponse", "statusCode: number", "responseTime: number", "statusCode", "responseTime", "logImageAnalysis", "data: {\n    imageHash: string;\n    processingTime: number;\n    recommendationsCount: number;\n    improvementScore: number;\n  }", "recommendationsCount", "improvementScore", "logImageGeneration", "data: {\n    prompt: string;\n    fidelityLevel: string;\n    tokens: number;\n    cost: number;\n  }", "prompt<PERSON><PERSON><PERSON>", "fidelityLevel", "errorStack", "logRateLimitHit", "limitType: string", "limitType", "logPerformanceMetrics", "metrics: {\n    totalTime: number;\n    analysisTime: number;\n    generationTime: number;\n    cacheHitRate: number;\n    queueTime?: number;\n  }", "metrics", "logUserAction", "action: string", "metadata?: any", "action", "logSystemHealth", "health: ServiceHealth", "health", "getLogs", "filter?: {\n    level?: LogLevel;\n    type?: string;\n    requestId?: string;\n    userId?: string;\n    startTime?: number;\n    endTime?: number;\n    limit?: number;\n  }", "filtered", "logs", "level", "endTime", "limit", "getAnalytics", "timeRange: '1h' | '24h' | '7d' | '30d'", "getTimeRangeMs", "timeRange", "logsInRange", "successfulRequests", "errorRequests", "cacheHits", "cacheMisses", "responseTimes", "typeCount", "popularEditingTypes", "hourlyCount", "getHours", "peakHours", "costLogs", "averageCostPerRequest", "costByType", "successRate", "cacheHitRate", "costAnalysis", "exportLogs", "format: 'json' | 'csv'", "rows", "row", "clearLogs", "persistLogs", "level: LogLevel", "type: string", "data: any", "shouldLog", "entry: <PERSON>g<PERSON><PERSON><PERSON>", "maxLogs", "levels", "currentIndex", "indexOf", "logLevel", "timeRange: string", "ranges", "logsToStore", "loadPersistedLogs", "checkSystemHealth", "recentLogs", "errorLogs", "status: 'healthy' | 'degraded' | 'down'", "<PERSON><PERSON><PERSON><PERSON>", "queueSize", "calculateCacheHitRate", "activeRequests", "totalCacheRequests", "imageEditingQueueManager", "startProcessing", "request: ImageEditingRequest", "priority: number", "queue", "maxQueueSize", "generateQueueId", "queueItem: QueueItem", "priority", "insertByPriority", "queueItem", "queueId", "getQueueStatus", "waitTimes", "averageWaitTime", "estimatedWaitTime", "processing", "getItemStatus", "id: string", "queueIndex", "findIndex", "remove", "pause", "processingInterval", "resume", "totalProcessed", "averageProcessingTime", "currentThroughput", "peakQueueSize", "retryRate", "processQueue", "processingStartedAt", "processingPromise", "processItem", "notifyCompletion", "retry<PERSON><PERSON><PERSON>", "notifyFailure", "item: QueueItem", "imageEditingService$1", "imageEditingServiceCzvHMWQo", "notify<PERSON><PERSON>ress", "insertIndex", "substr", "result: ImageEditingResult", "CustomEvent", "progress: any", "enqueueBatch", "requests: ImageEditingRequest[]", "ids: string[]", "requests", "batchPriority", "ids", "updatePriority", "newPriority: number", "newPriority", "getHealth", "oldestItemAge", "oldest", "is<PERSON><PERSON><PERSON>", "processingCount", "fileToBase64", "FileReader", "readAsDataURL", "SplitComponent", "selectedFile", "setSelectedFile", "useState", "previewUrl", "setPreviewUrl", "editingPreferences", "setEditingPreferences", "conservative", "max<PERSON><PERSON><PERSON>", "isLoading", "analyzeImage", "cancelAnalysis", "analytics", "options: UseImageEditingOptions", "enableQueue", "enableBatch", "autoRetry", "setIsLoading", "setProgress", "setResult", "setError", "queueStatus", "setQueueStatus", "setAnalytics", "currentRequestRef", "useRef", "abortControllerRef", "useEffect", "abort", "handleQueueProgress", "event: CustomEvent", "handleQueueComplete", "handleQueueError", "addEventListener", "removeEventListener", "useCallback", "async", "preferences?: any", "validation", "sanitizedFile", "checkStatus", "itemStatus", "result$1", "progressUpdate", "updateAnalytics", "err", "retryAnalysis", "originalImageHash", "analyzeBatch", "files: File[]", "files", "results: ImageEditingResult[]", "maxConcurrent", "batchPromises", "batchResults", "allSettled", "results", "success: boolean", "prev", "useImageEditing", "hasProgress", "hasResult", "<PERSON><PERSON><PERSON><PERSON>", "queueHealth", "pauseQueue", "resumeQueue", "clearQueue", "setQueueHealth", "updateStatus", "analyticsData", "interval", "updateItemPriority", "useImageEditingQueue", "detailedAnalytics", "recentErrors", "systemHealth", "refreshAnalytics", "exportAnalytics", "setTimeRange", "filters", "setFilters", "setRecentErrors", "setSystemHealth", "formattedErrors", "healthLogs", "healthData", "clearAnalytics", "useImageEditingAnalytics", "handleFileSelect", "event: React.ChangeEvent<HTMLInputElement>", "fileSize", "fileType", "handleAnalyze", "analysisResult", "error$1", "handleGenerateEdit", "recommendation: any", "editedImageLength", "editedBlob", "<PERSON><PERSON><PERSON>", "editedUrl", "Tabs", "defaultValue", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "Upload", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "accept", "htmlFor", "alt", "Settings", "Slide<PERSON>", "onValueChange", "<PERSON><PERSON>", "Fragment", "RefreshCw", "Zap", "Clock", "stage", "percentage", "Progress", "AlertCircle", "overallScore", "Badge", "expectedImprovement", "<PERSON><PERSON><PERSON>", "Pause", "Play", "Trash2", "processingRate", "Download", "error: any", "uptime", "services", "service"], "mappings": "", "x_google_ignoreList": [0, 5, 6, 7, 8]}