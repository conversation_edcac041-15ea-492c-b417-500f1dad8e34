import e from"node:process";import{env as t}from"cloudflare:workers";import{EventEmitter as r}from"node:events";import{Buffer as s}from"node:buffer";import{setImmediate as a,clearImmediate as c}from"node:timers";"global"in globalThis||(globalThis.global=globalThis);class WriteStream{fd;columns=80;rows=24;isTTY=!1;constructor(e){this.fd=e}clearLine(e,t){return t&&t(),!1}clearScreenDown(e){return e&&e(),!1}cursorTo(e,t,r){return r&&"function"==typeof r&&r(),!1}moveCursor(e,t,r){return r&&r(),!1}getColorDepth(e){return 1}hasColors(e,t){return!1}getWindowSize(){return[this.columns,this.rows]}write(e,t,r){e instanceof Uint8Array&&(e=(new TextDecoder).decode(e));try{console.log(e)}catch{}return r&&"function"==typeof r&&r(),!1}}class ReadStream{fd;isRaw=!1;isTTY=!1;constructor(e){this.fd=e}setRawMode(e){return this.isRaw=e,this}}function createNotImplementedError(e){return new Error(`[unenv] ${e} is not implemented yet!`)}function notImplemented(e){return Object.assign(()=>{throw createNotImplementedError(e)},{__unenv__:!0})}const u="22.14.0";class Process extends r{env;hrtime;nextTick;constructor(e){super(),this.env=e.env,this.hrtime=e.hrtime,this.nextTick=e.nextTick;for(const e of[...Object.getOwnPropertyNames(Process.prototype),...Object.getOwnPropertyNames(r.prototype)]){const t=this[e];"function"==typeof t&&(this[e]=t.bind(this))}}emitWarning(e,t,r){console.warn(`${r?`[${r}] `:""}${t?`${t}: `:""}${e}`)}emit(...e){return super.emit(...e)}listeners(e){return super.listeners(e)}#e;#t;#r;get stdin(){return this.#e??=new ReadStream(0)}get stdout(){return this.#t??=new WriteStream(1)}get stderr(){return this.#r??=new WriteStream(2)}#s="/";chdir(e){this.#s=e}cwd(){return this.#s}arch="";platform="";argv=[];argv0="";execArgv=[];execPath="";title="";pid=200;ppid=100;get version(){return`v${u}`}get versions(){return{node:u}}get allowedNodeEnvironmentFlags(){return new Set}get sourceMapsEnabled(){return!1}get debugPort(){return 0}get throwDeprecation(){return!1}get traceDeprecation(){return!1}get features(){return{}}get release(){return{}}get connected(){return!1}get config(){return{}}get moduleLoadList(){return[]}constrainedMemory(){return 0}availableMemory(){return 0}uptime(){return 0}resourceUsage(){return{}}ref(){}unref(){}umask(){throw createNotImplementedError("process.umask")}getBuiltinModule(){}getActiveResourcesInfo(){throw createNotImplementedError("process.getActiveResourcesInfo")}exit(){throw createNotImplementedError("process.exit")}reallyExit(){throw createNotImplementedError("process.reallyExit")}kill(){throw createNotImplementedError("process.kill")}abort(){throw createNotImplementedError("process.abort")}dlopen(){throw createNotImplementedError("process.dlopen")}setSourceMapsEnabled(){throw createNotImplementedError("process.setSourceMapsEnabled")}loadEnvFile(){throw createNotImplementedError("process.loadEnvFile")}disconnect(){throw createNotImplementedError("process.disconnect")}cpuUsage(){throw createNotImplementedError("process.cpuUsage")}setUncaughtExceptionCaptureCallback(){throw createNotImplementedError("process.setUncaughtExceptionCaptureCallback")}hasUncaughtExceptionCaptureCallback(){throw createNotImplementedError("process.hasUncaughtExceptionCaptureCallback")}initgroups(){throw createNotImplementedError("process.initgroups")}openStdin(){throw createNotImplementedError("process.openStdin")}assert(){throw createNotImplementedError("process.assert")}binding(){throw createNotImplementedError("process.binding")}permission={has:notImplemented("process.permission.has")};report={directory:"",filename:"",signal:"SIGUSR2",compact:!1,reportOnFatalError:!1,reportOnSignal:!1,reportOnUncaughtException:!1,getReport:notImplemented("process.report.getReport"),writeReport:notImplemented("process.report.writeReport")};finalization={register:notImplemented("process.finalization.register"),unregister:notImplemented("process.finalization.unregister"),registerBeforeExit:notImplemented("process.finalization.registerBeforeExit")};memoryUsage=Object.assign(()=>({arrayBuffers:0,rss:0,external:0,heapTotal:0,heapUsed:0}),{rss:()=>0});mainModule=void 0;domain=void 0;send=void 0;exitCode=void 0;channel=void 0;getegid=void 0;geteuid=void 0;getgid=void 0;getgroups=void 0;getuid=void 0;setegid=void 0;seteuid=void 0;setgid=void 0;setgroups=void 0;setuid=void 0;_events=void 0;_eventsCount=void 0;_exiting=void 0;_maxListeners=void 0;_debugEnd=void 0;_debugProcess=void 0;_fatalException=void 0;_getActiveHandles=void 0;_getActiveRequests=void 0;_kill=void 0;_preload_modules=void 0;_rawDebug=void 0;_startProfilerIdleNotifier=void 0;_stopProfilerIdleNotifier=void 0;_tickCallback=void 0;_disconnect=void 0;_handleQueue=void 0;_pendingMessage=void 0;_channel=void 0;_send=void 0;_linkedBinding=void 0}const d=Object.create(null),h=globalThis.process,_getEnv=e=>globalThis.__env__||h?.env||(e?d:globalThis),f=new Proxy(d,{get:(e,t)=>_getEnv()[t]??d[t],has:(e,t)=>t in _getEnv()||t in d,set:(e,t,r)=>(_getEnv(!0)[t]=r,!0),deleteProperty:(e,t)=>(delete _getEnv(!0)[t],!0),ownKeys(){const e=_getEnv();return Object.keys(e)},getOwnPropertyDescriptor(e,t){const r=_getEnv();if(t in r)return{value:r[t],writable:!0,enumerable:!0,configurable:!0}}}),m=Object.assign(function(e){const t=Date.now(),r=Math.trunc(t/1e3),s=t%1e3*1e6;if(e){let t=r-e[0],a=s-e[0];return a<0&&(t-=1,a=1e9+a),[t,a]}return[r,s]},{bigint:function(){return BigInt(1e6*Date.now())}});globalThis.__env__=t;const g=new Process({env:f,hrtime:m,nextTick:e.nextTick});for(const t of["exit","getBuiltinModule","platform"])t in e&&(g[t]=e[t]);e.features&&Object.defineProperty(g,"features",{get:()=>e.features});const{abort:_,addListener:j,allowedNodeEnvironmentFlags:x,hasUncaughtExceptionCaptureCallback:E,setUncaughtExceptionCaptureCallback:R,loadEnvFile:C,sourceMapsEnabled:T,arch:S,argv:P,argv0:H,chdir:I,config:z,connected:M,constrainedMemory:B,availableMemory:O,cpuUsage:N,cwd:U,debugPort:q,dlopen:L,disconnect:D,emit:W,emitWarning:K,env:$,eventNames:Z,execArgv:F,execPath:J,exit:Q,finalization:G,features:Y,getBuiltinModule:X,getActiveResourcesInfo:V,getMaxListeners:ee,hrtime:te,kill:re,listeners:se,listenerCount:oe,memoryUsage:ne,nextTick:ae,on:ie,off:ce,once:ue,pid:le,platform:de,ppid:he,prependListener:pe,prependOnceListener:fe,rawListeners:me,release:ge,removeAllListeners:ye,removeListener:we,report:ve,resourceUsage:be,setMaxListeners:_e,setSourceMapsEnabled:je,stderr:xe,stdin:ke,stdout:Ee,title:Re,umask:Ce,uptime:Te,version:Se,versions:Ae,domain:Pe,initgroups:He,moduleLoadList:Ie,reallyExit:ze,openStdin:Me,assert:Be,binding:Oe,send:Ne,exitCode:Ue,channel:qe,getegid:Le,geteuid:De,getgid:We,getgroups:Ke,getuid:$e,setegid:Ze,seteuid:Fe,setgid:Je,setgroups:Qe,setuid:Ge,permission:Ye,mainModule:Xe,_events:Ve,_eventsCount:et,_exiting:tt,_maxListeners:rt,_debugEnd:st,_debugProcess:ot,_fatalException:nt,_getActiveHandles:at,_getActiveRequests:it,_kill:ct,_preload_modules:ut,_rawDebug:lt,_startProfilerIdleNotifier:dt,_stopProfilerIdleNotifier:ht,_tickCallback:pt,_disconnect:ft,_handleQueue:mt,_pendingMessage:gt,_channel:yt,_send:wt,_linkedBinding:vt}=g,bt=globalThis.process;globalThis.process=bt?new Proxy(bt,{get:(e,t,r)=>Reflect.has(e,t)?Reflect.get(e,t,r):Reflect.get(g,t,r)}):g,globalThis.Buffer||(globalThis.Buffer=s),globalThis.setImmediate||(globalThis.setImmediate=a),globalThis.clearImmediate||(globalThis.clearImmediate=c);const _t=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,jt=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,xt=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function jsonParseTransform(e,t){if(!("__proto__"===e||"constructor"===e&&t&&"object"==typeof t&&"prototype"in t))return t;!function(e){console.warn(`[destr] Dropping "${e}" key to prevent prototype pollution.`)}(e)}function destr(e,t={}){if("string"!=typeof e)return e;if('"'===e[0]&&'"'===e[e.length-1]&&-1===e.indexOf("\\"))return e.slice(1,-1);const r=e.trim();if(r.length<=9)switch(r.toLowerCase()){case"true":return!0;case"false":return!1;case"undefined":return;case"null":return null;case"nan":return Number.NaN;case"infinity":return Number.POSITIVE_INFINITY;case"-infinity":return Number.NEGATIVE_INFINITY}if(!xt.test(e)){if(t.strict)throw new SyntaxError("[destr] Invalid JSON");return e}try{if(_t.test(e)||jt.test(e)){if(t.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(e,jsonParseTransform)}return JSON.parse(e)}catch(r){if(t.strict)throw r;return e}}const kt=/#/g,Et=/&/g,Rt=/\//g,Ct=/=/g,Tt=/\+/g,St=/%5e/gi,At=/%60/gi,Pt=/%7c/gi,Ht=/%20/gi;function encodeQueryValue(e){return(t="string"==typeof e?e:JSON.stringify(e),encodeURI(""+t).replace(Pt,"|")).replace(Tt,"%2B").replace(Ht,"+").replace(kt,"%23").replace(Et,"%26").replace(At,"`").replace(St,"^").replace(Rt,"%2F");var t}function encodeQueryKey(e){return encodeQueryValue(e).replace(Ct,"%3D")}function decode(e=""){try{return decodeURIComponent(""+e)}catch{return""+e}}function decodeQueryKey(e){return decode(e.replace(Tt," "))}function decodeQueryValue(e){return decode(e.replace(Tt," "))}function parseQuery(e=""){const t=Object.create(null);"?"===e[0]&&(e=e.slice(1));for(const r of e.split("&")){const e=r.match(/([^=]+)=?(.*)/)||[];if(e.length<2)continue;const s=decodeQueryKey(e[1]);if("__proto__"===s||"constructor"===s)continue;const a=decodeQueryValue(e[2]||"");void 0===t[s]?t[s]=a:Array.isArray(t[s])?t[s].push(a):t[s]=[t[s],a]}return t}function stringifyQuery(e){return Object.keys(e).filter(t=>void 0!==e[t]).map(t=>{return r=t,"number"!=typeof(s=e[t])&&"boolean"!=typeof s||(s=String(s)),s?Array.isArray(s)?s.map(e=>`${encodeQueryKey(r)}=${encodeQueryValue(e)}`).join("&"):`${encodeQueryKey(r)}=${encodeQueryValue(s)}`:encodeQueryKey(r);var r,s}).filter(Boolean).join("&")}const It=/^[\s\w\0+.-]{2,}:([/\\]{1,2})/,zt=/^[\s\w\0+.-]{2,}:([/\\]{2})?/,Mt=/^([/\\]\s*){2,}[^/\\]/,Bt=/^\.?\//;function hasProtocol(e,t={}){return"boolean"==typeof t&&(t={acceptRelative:t}),t.strict?It.test(e):zt.test(e)||!!t.acceptRelative&&Mt.test(e)}function withoutTrailingSlash(e="",t){return(function(e=""){return e.endsWith("/")}(e)?e.slice(0,-1):e)||"/"}function withTrailingSlash(e="",t){return e.endsWith("/")?e:e+"/"}function withoutBase(e,t){if(isEmptyURL(t))return e;const r=withoutTrailingSlash(t);if(!e.startsWith(r))return e;const s=e.slice(r.length);return"/"===s[0]?s:"/"+s}function withQuery(e,t){const r=parseURL(e),s={...parseQuery(r.search),...t};return r.search=stringifyQuery(s),function(e){const t=e.pathname||"",r=e.search?(e.search.startsWith("?")?"":"?")+e.search:"",s=e.hash||"",a=e.auth?e.auth+"@":"",c=e.host||"",u=e.protocol||e[Ot]?(e.protocol||"")+"//":"";return u+a+c+t+r+s}(r)}function getQuery(e){return parseQuery(parseURL(e).search)}function isEmptyURL(e){return!e||"/"===e}function joinURL(e,...t){let r=e||"";for(const e of t.filter(e=>function(e){return e&&"/"!==e}(e)))if(r){const t=e.replace(Bt,"");r=withTrailingSlash(r)+t}else r=e;return r}const Ot=Symbol.for("ufo:protocolRelative");function parseURL(e="",t){const r=e.match(/^[\s\0]*(blob:|data:|javascript:|vbscript:)(.*)/i);if(r){const[,e,t=""]=r;return{protocol:e.toLowerCase(),pathname:t,href:e+t,auth:"",host:"",search:"",hash:""}}if(!hasProtocol(e,{acceptRelative:!0}))return parsePath(e);const[,s="",a,c=""]=e.replace(/\\/g,"/").match(/^[\s\0]*([\w+.-]{2,}:)?\/\/([^/@]+@)?(.*)/)||[];let[,u="",d=""]=c.match(/([^#/?]*)(.*)?/)||[];"file:"===s&&(d=d.replace(/\/(?=[A-Za-z]:)/,""));const{pathname:h,search:f,hash:m}=parsePath(d);return{protocol:s.toLowerCase(),auth:a?a.slice(0,Math.max(0,a.length-1)):"",host:u,pathname:h,search:f,hash:m,[Ot]:!s}}function parsePath(e=""){const[t="",r="",s=""]=(e.match(/([^#?]*)(\?[^#]*)?(#.*)?/)||[]).splice(1);return{pathname:t,search:r,hash:s}}const Nt=0,Ut=1,qt=2;function createRouter$1(e={}){const t={options:e,rootNode:createRadixNode(),staticRoutesMap:{}},normalizeTrailingSlash=t=>e.strictTrailingSlash?t:t.replace(/\/$/,"")||"/";if(e.routes)for(const r in e.routes)insert(t,normalizeTrailingSlash(r),e.routes[r]);return{ctx:t,lookup:e=>function(e,t){const r=e.staticRoutesMap[t];if(r)return r.data;const s=t.split("/"),a={};let c=!1,u=null,d=e.rootNode,h=null;for(let e=0;e<s.length;e++){const t=s[e];null!==d.wildcardChildNode&&(u=d.wildcardChildNode,h=s.slice(e).join("/"));const r=d.children.get(t);if(void 0===r){if(d&&d.placeholderChildren.length>1){const t=s.length-e;d=d.placeholderChildren.find(e=>e.maxDepth===t)||null}else d=d.placeholderChildren[0]||null;if(!d)break;d.paramName&&(a[d.paramName]=t),c=!0}else d=r}null!==d&&null!==d.data||null===u||(d=u,a[d.paramName||"_"]=h,c=!0);if(!d)return null;if(c)return{...d.data,params:c?a:void 0};return d.data}(t,normalizeTrailingSlash(e)),insert:(e,r)=>insert(t,normalizeTrailingSlash(e),r),remove:e=>function(e,t){let r=!1;const s=t.split("/");let a=e.rootNode;for(const e of s)if(a=a.children.get(e),!a)return r;if(a.data){const e=s.at(-1)||"";a.data=null,0===Object.keys(a.children).length&&a.parent&&(a.parent.children.delete(e),a.parent.wildcardChildNode=null,a.parent.placeholderChildren=[]),r=!0}return r}(t,normalizeTrailingSlash(e))}}function insert(e,t,r){let s=!0;const a=t.split("/");let c=e.rootNode,u=0;const d=[c];for(const e of a){let t;if(t=c.children.get(e))c=t;else{const r=getNodeType(e);t=createRadixNode({type:r,parent:c}),c.children.set(e,t),r===qt?(t.paramName="*"===e?"_"+u++:e.slice(1),c.placeholderChildren.push(t),s=!1):r===Ut&&(c.wildcardChildNode=t,t.paramName=e.slice(3)||"_",s=!1),d.push(t),c=t}}for(const[e,t]of d.entries())t.maxDepth=Math.max(d.length-e,t.maxDepth||0);return c.data=r,!0===s&&(e.staticRoutesMap[t]=c),c}function createRadixNode(e={}){return{type:e.type||Nt,maxDepth:0,parent:e.parent||null,children:new Map,data:e.data||null,paramName:e.paramName||null,wildcardChildNode:null,placeholderChildren:[]}}function getNodeType(e){return e.startsWith("**")?Ut:":"===e[0]||"*"===e?qt:Nt}function toRouteMatcher(e){return function(e,t){return{ctx:{table:e},matchAll:r=>_matchRoutes(r,e,t)}}(_routerNodeToTable("",e.ctx.rootNode),e.ctx.options.strictTrailingSlash)}function _matchRoutes(e,t,r){!0!==r&&e.endsWith("/")&&(e=e.slice(0,-1)||"/");const s=[];for(const[r,a]of _sortRoutesMap(t.wildcard))(e===r||e.startsWith(r+"/"))&&s.push(a);for(const[r,a]of _sortRoutesMap(t.dynamic))if(e.startsWith(r+"/")){const t="/"+e.slice(r.length).split("/").splice(2).join("/");s.push(..._matchRoutes(t,a))}const a=t.static.get(e);return a&&s.push(a),s.filter(Boolean)}function _sortRoutesMap(e){return[...e.entries()].sort((e,t)=>e[0].length-t[0].length)}function _routerNodeToTable(e,t){const r={static:new Map,wildcard:new Map,dynamic:new Map};return function _addNode(e,t){if(e)if(t.type!==Nt||e.includes("*")||e.includes(":")){if(t.type===Ut)r.wildcard.set(e.replace("/**",""),t.data);else if(t.type===qt){const s=_routerNodeToTable("",t);return t.data&&s.static.set("/",t.data),void r.dynamic.set(e.replace(/\/\*|\/:\w+/,""),s)}}else t.data&&r.static.set(e,t.data);for(const[r,s]of t.children.entries())_addNode(`${e}/${r}`.replace("//","/"),s)}(e,t),r}function isPlainObject(e){if(null===e||"object"!=typeof e)return!1;const t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&(!(Symbol.iterator in e)&&(!(Symbol.toStringTag in e)||"[object Module]"===Object.prototype.toString.call(e)))}function _defu(e,t,r=".",s){if(!isPlainObject(t))return _defu(e,{},r,s);const a=Object.assign({},t);for(const t in e){if("__proto__"===t||"constructor"===t)continue;const c=e[t];null!=c&&(s&&s(a,t,c,r)||(Array.isArray(c)&&Array.isArray(a[t])?a[t]=[...c,...a[t]]:isPlainObject(c)&&isPlainObject(a[t])?a[t]=_defu(c,a[t],(r?`${r}.`:"")+t.toString(),s):a[t]=c))}return a}function createDefu(e){return(...t)=>t.reduce((t,r)=>_defu(t,r,"",e),{})}const Lt=createDefu(),Dt=createDefu((e,t,r)=>{if(void 0!==e[t]&&"function"==typeof r)return e[t]=r(e[t]),!0});function o(e){throw new Error(`${e} is not implemented yet!`)}class i extends r{__unenv__={};readableEncoding=null;readableEnded=!0;readableFlowing=!1;readableHighWaterMark=0;readableLength=0;readableObjectMode=!1;readableAborted=!1;readableDidRead=!1;closed=!1;errored=null;readable=!1;destroyed=!1;static from(e,t){return new i(t)}constructor(e){super()}_read(e){}read(e){}setEncoding(e){return this}pause(){return this}resume(){return this}isPaused(){return!0}unpipe(e){return this}unshift(e,t){}wrap(e){return this}push(e,t){return!1}_destroy(e,t){this.removeAllListeners()}destroy(e){return this.destroyed=!0,this._destroy(e),this}pipe(e,t){return{}}compose(e,t){throw new Error("Method not implemented.")}[Symbol.asyncDispose](){return this.destroy(),Promise.resolve()}async*[Symbol.asyncIterator](){throw o("Readable.asyncIterator")}iterator(e){throw o("Readable.iterator")}map(e,t){throw o("Readable.map")}filter(e,t){throw o("Readable.filter")}forEach(e,t){throw o("Readable.forEach")}reduce(e,t,r){throw o("Readable.reduce")}find(e,t){throw o("Readable.find")}findIndex(e,t){throw o("Readable.findIndex")}some(e,t){throw o("Readable.some")}toArray(e){throw o("Readable.toArray")}every(e,t){throw o("Readable.every")}flatMap(e,t){throw o("Readable.flatMap")}drop(e,t){throw o("Readable.drop")}take(e,t){throw o("Readable.take")}asIndexedPairs(e){throw o("Readable.asIndexedPairs")}}let Wt=class extends r{__unenv__={};writable=!0;writableEnded=!1;writableFinished=!1;writableHighWaterMark=0;writableLength=0;writableObjectMode=!1;writableCorked=0;closed=!1;errored=null;writableNeedDrain=!1;writableAborted=!1;destroyed=!1;_data;_encoding="utf8";constructor(e){super()}pipe(e,t){return{}}_write(e,t,r){if(this.writableEnded)r&&r();else{if(void 0===this._data)this._data=e;else{const r="string"==typeof this._data?s.from(this._data,this._encoding||t||"utf8"):this._data,a="string"==typeof e?s.from(e,t||this._encoding||"utf8"):e;this._data=s.concat([r,a])}this._encoding=t,r&&r()}}_writev(e,t){}_destroy(e,t){}_final(e){}write(e,t,r){const s="string"==typeof t?this._encoding:"utf8",a="function"==typeof t?t:"function"==typeof r?r:void 0;return this._write(e,s,a),!0}setDefaultEncoding(e){return this}end(e,t,r){const s="function"==typeof e?e:"function"==typeof t?t:"function"==typeof r?r:void 0;if(this.writableEnded)return s&&s(),this;const a=e===s?void 0:e;if(a){const e=t===s?void 0:t;this.write(a,e,s)}return this.writableEnded=!0,this.writableFinished=!0,this.emit("close"),this.emit("finish"),this}cork(){}uncork(){}destroy(e){return this.destroyed=!0,delete this._data,this.removeAllListeners(),this}compose(e,t){throw new Error("Method not implemented.")}};const Kt=class{allowHalfOpen=!0;_destroy;constructor(e=new i,t=new Wt){Object.assign(this,e),Object.assign(this,t),this._destroy=function(...e){return function(...t){for(const r of e)r(...t)}}(e._destroy,t._destroy)}};const $t=(Object.assign(Kt.prototype,i.prototype),Object.assign(Kt.prototype,Wt.prototype),Kt);class A extends $t{__unenv__={};bufferSize=0;bytesRead=0;bytesWritten=0;connecting=!1;destroyed=!1;pending=!1;localAddress="";localPort=0;remoteAddress="";remoteFamily="";remotePort=0;autoSelectFamilyAttemptedAddresses=[];readyState="readOnly";constructor(e){super()}write(e,t,r){return!1}connect(e,t,r){return this}end(e,t,r){return this}setEncoding(e){return this}pause(){return this}resume(){return this}setTimeout(e,t){return this}setNoDelay(e){return this}setKeepAlive(e,t){return this}address(){return{}}unref(){return this}ref(){return this}destroySoon(){this.destroy()}resetAndDestroy(){const e=new Error("ERR_SOCKET_CLOSED");return e.code="ERR_SOCKET_CLOSED",this.destroy(e),this}}class y extends i{aborted=!1;httpVersion="1.1";httpVersionMajor=1;httpVersionMinor=1;complete=!0;connection;socket;headers={};trailers={};method="GET";url="/";statusCode=200;statusMessage="";closed=!1;errored=null;readable=!1;constructor(e){super(),this.socket=this.connection=e||new A}get rawHeaders(){const e=this.headers,t=[];for(const r in e)if(Array.isArray(e[r]))for(const s of e[r])t.push(r,s);else t.push(r,e[r]);return t}get rawTrailers(){return[]}setTimeout(e,t){return this}get headersDistinct(){return p(this.headers)}get trailersDistinct(){return p(this.trailers)}}function p(e){const t={};for(const[r,s]of Object.entries(e))r&&(t[r]=(Array.isArray(s)?s:[s]).filter(Boolean));return t}class w extends Wt{statusCode=200;statusMessage="";upgrading=!1;chunkedEncoding=!1;shouldKeepAlive=!1;useChunkedEncodingByDefault=!1;sendDate=!1;finished=!1;headersSent=!1;strictContentLength=!1;connection=null;socket=null;req;_headers={};constructor(e){super(),this.req=e}assignSocket(e){e._httpMessage=this,this.socket=e,this.connection=e,this.emit("socket",e),this._flush()}_flush(){this.flushHeaders()}detachSocket(e){}writeContinue(e){}writeHead(e,t,r){e&&(this.statusCode=e),"string"==typeof t&&(this.statusMessage=t,t=void 0);const s=r||t;if(s&&!Array.isArray(s))for(const e in s)this.setHeader(e,s[e]);return this.headersSent=!0,this}writeProcessing(){}setTimeout(e,t){return this}appendHeader(e,t){e=e.toLowerCase();const r=this._headers[e],s=[...Array.isArray(r)?r:[r],...Array.isArray(t)?t:[t]].filter(Boolean);return this._headers[e]=s.length>1?s:s[0],this}setHeader(e,t){return this._headers[e.toLowerCase()]=t,this}setHeaders(e){for(const[t,r]of Object.entries(e))this.setHeader(t,r);return this}getHeader(e){return this._headers[e.toLowerCase()]}getHeaders(){return this._headers}getHeaderNames(){return Object.keys(this._headers)}hasHeader(e){return e.toLowerCase()in this._headers}removeHeader(e){delete this._headers[e.toLowerCase()]}addTrailers(e){}flushHeaders(){}writeEarlyHints(e,t){"function"==typeof t&&t()}}const Zt=(()=>{const n=function(){};return n.prototype=Object.create(null),n})();function v(e={}){if(e instanceof Headers)return e;const t=new Headers;for(const[r,s]of Object.entries(e))if(void 0!==s){if(Array.isArray(s)){for(const e of s)t.append(r,String(e));continue}t.set(r,String(s))}return t}const Ft=new Set([101,204,205,304]);async function b(e,t){const r=new y,s=new w(r);let a;if(r.url=t.url?.toString()||"/",!r.url.startsWith("/")){const e=new URL(r.url);a=e.host,r.url=e.pathname+e.search+e.hash}r.method=t.method||"GET",r.headers=function(e={}){const t=new Zt,r=Array.isArray(e)||function(e){return"function"==typeof e?.entries}(e)?e:Object.entries(e);for(const[e,s]of r)if(s){if(void 0===t[e]){t[e]=s;continue}t[e]=[...Array.isArray(t[e])?t[e]:[t[e]],...Array.isArray(s)?s:[s]]}return t}(t.headers||{}),r.headers.host||(r.headers.host=t.host||a||"localhost"),r.connection.encrypted=r.connection.encrypted||"https"===t.protocol,r.body=t.body||null,r.__unenv__=t.context,await e(r,s);let c=s._data;(Ft.has(s.statusCode)||"HEAD"===r.method.toUpperCase())&&(c=null,delete s._headers["content-length"]);const u={status:s.statusCode,statusText:s.statusMessage,headers:s._headers,body:c};return r.destroy(),s.destroy(),u}function hasProp(e,t){try{return t in e}catch{return!1}}class H3Error extends Error{static __h3_error__=!0;statusCode=500;fatal=!1;unhandled=!1;statusMessage;data;cause;constructor(e,t={}){super(e,t),t.cause&&!this.cause&&(this.cause=t.cause)}toJSON(){const e={message:this.message,statusCode:sanitizeStatusCode(this.statusCode,500)};return this.statusMessage&&(e.statusMessage=sanitizeStatusMessage(this.statusMessage)),void 0!==this.data&&(e.data=this.data),e}}function createError(e){if("string"==typeof e)return new H3Error(e);if(isError(e))return e;const t=new H3Error(e.message??e.statusMessage??"",{cause:e.cause||e});if(hasProp(e,"stack"))try{Object.defineProperty(t,"stack",{get:()=>e.stack})}catch{try{t.stack=e.stack}catch{}}if(e.data&&(t.data=e.data),e.statusCode?t.statusCode=sanitizeStatusCode(e.statusCode,t.statusCode):e.status&&(t.statusCode=sanitizeStatusCode(e.status,t.statusCode)),e.statusMessage?t.statusMessage=e.statusMessage:e.statusText&&(t.statusMessage=e.statusText),t.statusMessage){const e=t.statusMessage;sanitizeStatusMessage(t.statusMessage)!==e&&console.warn("[h3] Please prefer using `message` for longer error messages instead of `statusMessage`. In the future, `statusMessage` will be sanitized by default.")}return void 0!==e.fatal&&(t.fatal=e.fatal),void 0!==e.unhandled&&(t.unhandled=e.unhandled),t}function isError(e){return!0===e?.constructor?.__h3_error__}const Jt=Symbol.for("h3RawBody"),Qt=["PATCH","POST","PUT","DELETE"];function readRawBody(e,t="utf8"){!function(e,t){if(!function(e,t){if("string"==typeof t){if(e.method===t)return!0}else if(t.includes(e.method))return!0;return!1}(e,t))throw createError({statusCode:405,statusMessage:"HTTP method is not allowed."})}(e,Qt);const r=e._requestBody||e.web?.request?.body||e.node.req[Jt]||e.node.req.rawBody||e.node.req.body;if(r){const e=Promise.resolve(r).then(e=>s.isBuffer(e)?e:"function"==typeof e.pipeTo?new Promise((t,r)=>{const a=[];e.pipeTo(new WritableStream({write(e){a.push(e)},close(){t(s.concat(a))},abort(e){r(e)}})).catch(r)}):"function"==typeof e.pipe?new Promise((t,r)=>{const a=[];e.on("data",e=>{a.push(e)}).on("end",()=>{t(s.concat(a))}).on("error",r)}):e.constructor===Object?s.from(JSON.stringify(e)):e instanceof URLSearchParams?s.from(e.toString()):e instanceof FormData?new Response(e).bytes().then(e=>s.from(e)):s.from(e));return t?e.then(e=>e.toString(t)):e}if(!Number.parseInt(e.node.req.headers["content-length"]||"")&&!String(e.node.req.headers["transfer-encoding"]??"").split(",").map(e=>e.trim()).filter(Boolean).includes("chunked"))return Promise.resolve(void 0);const a=e.node.req[Jt]=new Promise((t,r)=>{const a=[];e.node.req.on("error",e=>{r(e)}).on("data",e=>{a.push(e)}).on("end",()=>{t(s.concat(a))})});return t?a.then(e=>e.toString(t)):a}function handleCacheHeaders(e,t){const r=["public",...t.cacheControls||[]];let s=!1;if(void 0!==t.maxAge&&r.push("max-age="+ +t.maxAge,"s-maxage="+ +t.maxAge),t.modifiedTime){const r=new Date(t.modifiedTime),a=e.node.req.headers["if-modified-since"];e.node.res.setHeader("last-modified",r.toUTCString()),a&&new Date(a)>=r&&(s=!0)}if(t.etag){e.node.res.setHeader("etag",t.etag);e.node.req.headers["if-none-match"]===t.etag&&(s=!0)}return e.node.res.setHeader("cache-control",r.join(", ")),!!s&&(e.node.res.statusCode=304,e.handled||e.node.res.end(),!0)}const Gt={html:"text/html",json:"application/json"},Yt=/[^\u0009\u0020-\u007E]/g;function sanitizeStatusMessage(e=""){return e.replace(Yt,"")}function sanitizeStatusCode(e,t=200){return e?("string"==typeof e&&(e=Number.parseInt(e,10)),e<100||e>999?t:e):t}function splitCookiesString(e){if(Array.isArray(e))return e.flatMap(e=>splitCookiesString(e));if("string"!=typeof e)return[];const t=[];let r,s,a,c,u,d=0;const skipWhitespace=()=>{for(;d<e.length&&/\s/.test(e.charAt(d));)d+=1;return d<e.length},notSpecialChar=()=>(s=e.charAt(d),"="!==s&&";"!==s&&","!==s);for(;d<e.length;){for(r=d,u=!1;skipWhitespace();)if(s=e.charAt(d),","===s){for(a=d,d+=1,skipWhitespace(),c=d;d<e.length&&notSpecialChar();)d+=1;d<e.length&&"="===e.charAt(d)?(u=!0,d=c,t.push(e.slice(r,a)),r=d):d=a+1}else d+=1;(!u||d>=e.length)&&t.push(e.slice(r))}return t}const Xt=void 0===a?e=>e():a;function send(e,t,r){return r&&function(e,t){t&&304!==e.node.res.statusCode&&!e.node.res.getHeader("content-type")&&e.node.res.setHeader("content-type",t)}(e,r),new Promise(r=>{Xt(()=>{e.handled||e.node.res.end(t),r()})})}function setResponseStatus(e,t,r){t&&(e.node.res.statusCode=sanitizeStatusCode(t,e.node.res.statusCode)),r&&(e.node.res.statusMessage=sanitizeStatusMessage(r))}function setResponseHeaders(e,t){for(const[r,s]of Object.entries(t))e.node.res.setHeader(r,s)}const Vt=setResponseHeaders;function sendStream(e,t){if(!t||"object"!=typeof t)throw new Error("[h3] Invalid stream provided.");if(e.node.res._data=t,!e.node.res.socket)return e._handled=!0,Promise.resolve();if(hasProp(t,"pipeTo")&&"function"==typeof t.pipeTo)return t.pipeTo(new WritableStream({write(t){e.node.res.write(t)}})).then(()=>{e.node.res.end()});if(hasProp(t,"pipe")&&"function"==typeof t.pipe)return new Promise((r,s)=>{t.pipe(e.node.res),t.on&&(t.on("end",()=>{e.node.res.end(),r()}),t.on("error",e=>{s(e)})),e.node.res.on("close",()=>{t.abort&&t.abort()})});throw new Error("[h3] Invalid or incompatible stream provided.")}function sendWebResponse(e,t){for(const[r,s]of t.headers)"set-cookie"===r?e.node.res.appendHeader(r,splitCookiesString(s)):e.node.res.setHeader(r,s);if(t.status&&(e.node.res.statusCode=sanitizeStatusCode(t.status,e.node.res.statusCode)),t.statusText&&(e.node.res.statusMessage=sanitizeStatusMessage(t.statusText)),t.redirected&&e.node.res.setHeader("location",t.url),t.body)return sendStream(e,t.body);e.node.res.end()}const er=new Set(["PATCH","POST","PUT","DELETE"]),tr=new Set(["transfer-encoding","accept-encoding","connection","keep-alive","upgrade","expect","host","accept"]);async function proxyRequest(e,t,r={}){let s,a;er.has(e.method)&&(r.streamRequest?(s=function(e){if(!Qt.includes(e.method))return;const t=e.web?.request?.body||e._requestBody;return t||(Jt in e.node.req||"rawBody"in e.node.req||"body"in e.node.req||"__unenv__"in e.node.req?new ReadableStream({async start(t){const r=await readRawBody(e,!1);r&&t.enqueue(r),t.close()}}):new ReadableStream({start:t=>{e.node.req.on("data",e=>{t.enqueue(e)}),e.node.req.on("end",()=>{t.close()}),e.node.req.on("error",e=>{t.error(e)})}}))}(e),a="half"):s=await readRawBody(e,!1).catch(()=>{}));const c=r.fetchOptions?.method||e.method,u=function(e,...t){const r=t.filter(Boolean);if(0===r.length)return e;const s=new Headers(e);for(const e of r){const t=Array.isArray(e)?e:"function"==typeof e.entries?e.entries():Object.entries(e);for(const[e,r]of t)void 0!==r&&s.set(e,r)}return s}(getProxyRequestHeaders(e,{host:t.startsWith("/")}),r.fetchOptions?.headers,r.headers);return async function(e,t,r={}){let s;try{s=await _getFetch(r.fetch)(t,{headers:r.headers,ignoreResponseError:!0,...r.fetchOptions})}catch(e){throw createError({status:502,statusMessage:"Bad Gateway",cause:e})}e.node.res.statusCode=sanitizeStatusCode(s.status,e.node.res.statusCode),e.node.res.statusMessage=sanitizeStatusMessage(s.statusText);const a=[];for(const[t,r]of s.headers.entries())"content-encoding"!==t&&"content-length"!==t&&("set-cookie"!==t?e.node.res.setHeader(t,r):a.push(...splitCookiesString(r)));a.length>0&&e.node.res.setHeader("set-cookie",a.map(e=>(r.cookieDomainRewrite&&(e=rewriteCookieProperty(e,r.cookieDomainRewrite,"domain")),r.cookiePathRewrite&&(e=rewriteCookieProperty(e,r.cookiePathRewrite,"path")),e)));r.onResponse&&await r.onResponse(e,s);if(void 0!==s._data)return s._data;if(e.handled)return;if(!1===r.sendStream){const t=new Uint8Array(await s.arrayBuffer());return e.node.res.end(t)}if(s.body)for await(const t of s.body)e.node.res.write(t);return e.node.res.end()}(e,t,{...r,fetchOptions:{method:c,body:s,duplex:a,...r.fetchOptions,headers:u}})}function getProxyRequestHeaders(e,t){const r=Object.create(null),s=function(e){const t={};for(const r in e.node.req.headers){const s=e.node.req.headers[r];t[r]=Array.isArray(s)?s.filter(Boolean).join(", "):s}return t}(e);for(const e in s)(!tr.has(e)||"host"===e&&t?.host)&&(r[e]=s[e]);return r}function fetchWithEvent(e,t,r,s){return _getFetch(s?.fetch)(t,{...r,context:r?.context||e.context,headers:{...getProxyRequestHeaders(e,{host:"string"==typeof t&&t.startsWith("/")}),...r?.headers}})}function _getFetch(e){if(e)return e;if(globalThis.fetch)return globalThis.fetch;throw new Error("fetch is not available. Try importing `node-fetch-native/polyfill` for Node.js.")}function rewriteCookieProperty(e,t,r){const s="string"==typeof t?{"*":t}:t;return e.replace(new RegExp(`(;\\s*${r}=)([^;]+)`,"gi"),(e,t,r)=>{let a;if(r in s)a=s[r];else{if(!("*"in s))return e;a=s["*"]}return a?t+a:""})}class H3Event{__is_event__=!0;node;web;context={};_method;_path;_headers;_requestBody;_handled=!1;_onBeforeResponseCalled;_onAfterResponseCalled;constructor(e,t){this.node={req:e,res:t}}get method(){return this._method||(this._method=(this.node.req.method||"GET").toUpperCase()),this._method}get path(){return this._path||this.node.req.url||"/"}get headers(){return this._headers||(this._headers=function(e){const t=new Headers;for(const[r,s]of Object.entries(e))if(Array.isArray(s))for(const e of s)t.append(r,e);else s&&t.set(r,s);return t}(this.node.req.headers)),this._headers}get handled(){return this._handled||this.node.res.writableEnded||this.node.res.headersSent}respondWith(e){return Promise.resolve(e).then(e=>sendWebResponse(this,e))}toString(){return`[${this.method}] ${this.path}`}toJSON(){return this.toString()}get req(){return this.node.req}get res(){return this.node.res}}function isEvent(e){return hasProp(e,"__is_event__")}function createEvent(e,t){return new H3Event(e,t)}function defineEventHandler(e){if("function"==typeof e)return e.__is_handler__=!0,e;const t={onRequest:_normalizeArray(e.onRequest),onBeforeResponse:_normalizeArray(e.onBeforeResponse)},_handler=r=>async function(e,t,r){if(r.onRequest)for(const t of r.onRequest)if(await t(e),e.handled)return;const s=await t(e),a={body:s};if(r.onBeforeResponse)for(const t of r.onBeforeResponse)await t(e,a);return a.body}(r,e.handler,t);return _handler.__is_handler__=!0,_handler.__resolve__=e.handler.__resolve__,_handler.__websocket__=e.websocket,_handler}function _normalizeArray(e){return e?Array.isArray(e)?e:[e]:void 0}const rr=defineEventHandler;function isEventHandler(e){return hasProp(e,"__is_handler__")}function toEventHandler(e,t,r){return isEventHandler(e)||console.warn("[h3] Implicit event handler conversion is deprecated. Use `eventHandler()` or `fromNodeMiddleware()` to define event handlers.",r&&"/"!==r?`\n     Route: ${r}`:"",`\n     Handler: ${e}`),e}const lazyEventHandler=function(e){let t,r;const resolveHandler=()=>r?Promise.resolve(r):(t||(t=Promise.resolve(e()).then(e=>{const t=e.default||e;if("function"!=typeof t)throw new TypeError("Invalid lazy handler result. It should be a function:",t);return r={handler:toEventHandler(e.default||e)},r})),t),s=rr(e=>r?r.handler(e):resolveHandler().then(t=>t.handler(e)));return s.__resolve__=resolveHandler,s};function createApp(e={}){const t=[],r=function(e,t){const r=t.debug?2:void 0;return rr(async s=>{s.node.req.originalUrl=s.node.req.originalUrl||s.node.req.url||"/";const a=s._path||s.node.req.url||"/";let c;t.onRequest&&await t.onRequest(s);for(const u of e){if(u.route.length>1){if(!a.startsWith(u.route))continue;c=a.slice(u.route.length)||"/"}else c=a;if(u.match&&!u.match(c,s))continue;s._path=c,s.node.req.url=c;const e=await u.handler(s),d=void 0===e?void 0:await e;if(void 0!==d){const e={body:d};return t.onBeforeResponse&&(s._onBeforeResponseCalled=!0,await t.onBeforeResponse(s,e)),await handleHandlerResponse(s,e.body,r),void(t.onAfterResponse&&(s._onAfterResponseCalled=!0,await t.onAfterResponse(s,e)))}if(s.handled)return void(t.onAfterResponse&&(s._onAfterResponseCalled=!0,await t.onAfterResponse(s,void 0)))}if(!s.handled)throw createError({statusCode:404,statusMessage:`Cannot find any path matching ${s.path||"/"}.`});t.onAfterResponse&&(s._onAfterResponseCalled=!0,await t.onAfterResponse(s,void 0))})}(t,e),s=function(e){return async t=>{let r;for(const s of e){if("/"===s.route&&!s.handler.__resolve__)continue;if(!t.startsWith(s.route))continue;if(r=t.slice(s.route.length)||"/",s.match&&!s.match(r,void 0))continue;let e={route:s.route,handler:s.handler};if(e.handler.__resolve__){const t=await e.handler.__resolve__(r);if(!t)continue;e={...e,...t,route:joinURL(e.route||"/",t.route||"/")}}return e}}}(t);r.__resolve__=s;const a=function(e){let t;return()=>(t||(t=e()),t)}(()=>{return t=s,{...e.websocket,async resolve(e){const r=e.request?.url||e.url||"/",{pathname:s}="string"==typeof r?parseURL(r):r,a=await t(s);return a?.handler?.__websocket__||{}}};var t}),c={use:(e,t,r)=>use(c,e,t,r),resolve:s,handler:r,stack:t,options:e,get websocket(){return a()}};return c}function use(e,t,r,s){if(Array.isArray(t))for(const a of t)use(e,a,r,s);else if(Array.isArray(r))for(const a of r)use(e,t,a,s);else"string"==typeof t?e.stack.push(normalizeLayer({...s,route:t,handler:r})):"function"==typeof t?e.stack.push(normalizeLayer({...r,handler:t})):e.stack.push(normalizeLayer({...t}));return e}function normalizeLayer(e){let t=e.handler;return t.handler&&(t=t.handler),e.lazy?t=lazyEventHandler(t):isEventHandler(t)||(t=toEventHandler(t,0,e.route)),{route:withoutTrailingSlash(e.route),match:e.match,handler:t}}function handleHandlerResponse(e,t,r){if(null===t)return function(e,t){if(e.handled)return;t||200===e.node.res.statusCode||(t=e.node.res.statusCode);const r=sanitizeStatusCode(t,204);204===r&&e.node.res.removeHeader("content-length"),e.node.res.writeHead(r),e.node.res.end()}(e);if(t){if(a=t,"undefined"!=typeof Response&&a instanceof Response)return sendWebResponse(e,t);if(function(e){if(!e||"object"!=typeof e)return!1;if("function"==typeof e.pipe){if("function"==typeof e._read)return!0;if("function"==typeof e.abort)return!0}return"function"==typeof e.pipeTo}(t))return sendStream(e,t);if(t.buffer)return send(e,t);if(t.arrayBuffer&&"function"==typeof t.arrayBuffer)return t.arrayBuffer().then(r=>send(e,s.from(r),t.type));if(t instanceof Error)throw createError(t);if("function"==typeof t.end)return!0}var a;const c=typeof t;if("string"===c)return send(e,t,Gt.html);if("object"===c||"boolean"===c||"number"===c)return send(e,JSON.stringify(t,void 0,r),Gt.json);if("bigint"===c)return send(e,t.toString(),Gt.json);throw createError({statusCode:500,statusMessage:`[h3] Cannot send ${c} as response.`})}const sr=["connect","delete","get","head","options","post","put","trace","patch"];function toNodeListener(e){return async function(t,r){const s=createEvent(t,r);try{await e.handler(s)}catch(t){const r=createError(t);if(isError(t)||(r.unhandled=!0),setResponseStatus(s,r.statusCode,r.statusMessage),e.options.onError&&await e.options.onError(r,s),s.handled)return;(r.unhandled||r.fatal)&&console.error("[h3]",r.fatal?"[fatal]":"[unhandled]",r),e.options.onBeforeResponse&&!s._onBeforeResponseCalled&&await e.options.onBeforeResponse(s,{body:r}),await function(e,t,r){if(e.handled)return;const s=isError(t)?t:createError(t),a={statusCode:s.statusCode,statusMessage:s.statusMessage,stack:[],data:s.data};if(r&&(a.stack=(s.stack||"").split("\n").map(e=>e.trim())),e.handled)return;setResponseStatus(e,Number.parseInt(s.statusCode),s.statusMessage),e.node.res.setHeader("content-type",Gt.json),e.node.res.end(JSON.stringify(a,void 0,2))}(s,r,!!e.options.debug),e.options.onAfterResponse&&!s._onAfterResponseCalled&&await e.options.onAfterResponse(s,{body:r})}}}function flatHooks(e,t={},r){for(const s in e){const a=e[s],c=r?`${r}:${s}`:s;"object"==typeof a&&null!==a?flatHooks(a,t,c):"function"==typeof a&&(t[c]=a)}return t}const or={run:e=>e()},nr=void 0!==console.createTask?console.createTask:()=>or;function serialTaskCaller(e,t){const r=t.shift(),s=nr(r);return e.reduce((e,r)=>e.then(()=>s.run(()=>r(...t))),Promise.resolve())}function parallelTaskCaller(e,t){const r=t.shift(),s=nr(r);return Promise.all(e.map(e=>s.run(()=>e(...t))))}function callEachWith(e,t){for(const r of[...e])r(t)}class Hookable{constructor(){this._hooks={},this._before=void 0,this._after=void 0,this._deprecatedMessages=void 0,this._deprecatedHooks={},this.hook=this.hook.bind(this),this.callHook=this.callHook.bind(this),this.callHookWith=this.callHookWith.bind(this)}hook(e,t,r={}){if(!e||"function"!=typeof t)return()=>{};const s=e;let a;for(;this._deprecatedHooks[e];)a=this._deprecatedHooks[e],e=a.to;if(a&&!r.allowDeprecated){let e=a.message;e||(e=`${s} hook has been deprecated`+(a.to?`, please use ${a.to}`:"")),this._deprecatedMessages||(this._deprecatedMessages=new Set),this._deprecatedMessages.has(e)||(console.warn(e),this._deprecatedMessages.add(e))}if(!t.name)try{Object.defineProperty(t,"name",{get:()=>"_"+e.replace(/\W+/g,"_")+"_hook_cb",configurable:!0})}catch{}return this._hooks[e]=this._hooks[e]||[],this._hooks[e].push(t),()=>{t&&(this.removeHook(e,t),t=void 0)}}hookOnce(e,t){let r,_function=(...e)=>("function"==typeof r&&r(),r=void 0,_function=void 0,t(...e));return r=this.hook(e,_function),r}removeHook(e,t){if(this._hooks[e]){const r=this._hooks[e].indexOf(t);-1!==r&&this._hooks[e].splice(r,1),0===this._hooks[e].length&&delete this._hooks[e]}}deprecateHook(e,t){this._deprecatedHooks[e]="string"==typeof t?{to:t}:t;const r=this._hooks[e]||[];delete this._hooks[e];for(const t of r)this.hook(e,t)}deprecateHooks(e){Object.assign(this._deprecatedHooks,e);for(const t in e)this.deprecateHook(t,e[t])}addHooks(e){const t=flatHooks(e),r=Object.keys(t).map(e=>this.hook(e,t[e]));return()=>{for(const e of r.splice(0,r.length))e()}}removeHooks(e){const t=flatHooks(e);for(const e in t)this.removeHook(e,t[e])}removeAllHooks(){for(const e in this._hooks)delete this._hooks[e]}callHook(e,...t){return t.unshift(e),this.callHookWith(serialTaskCaller,e,...t)}callHookParallel(e,...t){return t.unshift(e),this.callHookWith(parallelTaskCaller,e,...t)}callHookWith(e,t,...r){const s=this._before||this._after?{name:t,args:r,context:{}}:void 0;this._before&&callEachWith(this._before,s);const a=e(t in this._hooks?[...this._hooks[t]]:[],r);return a instanceof Promise?a.finally(()=>{this._after&&s&&callEachWith(this._after,s)}):(this._after&&s&&callEachWith(this._after,s),a)}beforeEach(e){return this._before=this._before||[],this._before.push(e),()=>{if(void 0!==this._before){const t=this._before.indexOf(e);-1!==t&&this._before.splice(t,1)}}}afterEach(e){return this._after=this._after||[],this._after.push(e),()=>{if(void 0!==this._after){const t=this._after.indexOf(e);-1!==t&&this._after.splice(t,1)}}}}const ar=globalThis;class FetchError extends Error{constructor(e,t){super(e,t),this.name="FetchError",t?.cause&&!this.cause&&(this.cause=t.cause)}}const ir=new Set(Object.freeze(["PATCH","POST","PUT","DELETE"]));function isPayloadMethod(e="GET"){return ir.has(e.toUpperCase())}const cr=new Set(["image/svg","application/xml","application/xhtml","application/html"]),ur=/^application\/(?:[\w!#$%&*.^`~-]*\+)?json(;.+)?$/i;function resolveFetchOptions(e,t,r,s){const a=function(e,t,r){if(!t)return new r(e);const s=new r(t);if(e)for(const[t,a]of Symbol.iterator in e||Array.isArray(e)?e:new r(e))s.set(t,a);return s}(t?.headers??e?.headers,r?.headers,s);let c;return(r?.query||r?.params||t?.params||t?.query)&&(c={...r?.params,...r?.query,...t?.params,...t?.query}),{...r,...t,query:c,params:c,headers:a}}async function callHooks(e,t){if(t)if(Array.isArray(t))for(const r of t)await r(e);else await t(e)}const lr=new Set([408,409,425,429,500,502,503,504]),dr=new Set([101,204,205,304]);function createFetch(e={}){const{fetch:t=globalThis.fetch,Headers:r=globalThis.Headers,AbortController:s=globalThis.AbortController}=e;async function onError(e){const t=e.error&&"AbortError"===e.error.name&&!e.options.timeout||!1;if(!1!==e.options.retry&&!t){let t;t="number"==typeof e.options.retry?e.options.retry:isPayloadMethod(e.options.method)?0:1;const r=e.response&&e.response.status||500;if(t>0&&(Array.isArray(e.options.retryStatusCodes)?e.options.retryStatusCodes.includes(r):lr.has(r))){const r="function"==typeof e.options.retryDelay?e.options.retryDelay(e):e.options.retryDelay||0;return r>0&&await new Promise(e=>setTimeout(e,r)),$fetchRaw(e.request,{...e.options,retry:t-1})}}const r=function(e){const t=e.error?.message||e.error?.toString()||"",r=e.request?.method||e.options?.method||"GET",s=e.request?.url||String(e.request)||"/",a=`[${r}] ${JSON.stringify(s)}`,c=e.response?`${e.response.status} ${e.response.statusText}`:"<no response>",u=new FetchError(`${a}: ${c}${t?` ${t}`:""}`,e.error?{cause:e.error}:void 0);for(const t of["request","options","response"])Object.defineProperty(u,t,{get:()=>e[t]});for(const[t,r]of[["data","_data"],["status","status"],["statusCode","status"],["statusText","statusText"],["statusMessage","statusText"]])Object.defineProperty(u,t,{get:()=>e.response&&e.response[r]});return u}(e);throw Error.captureStackTrace&&Error.captureStackTrace(r,$fetchRaw),r}const $fetchRaw=async function(a,c={}){const u={request:a,options:resolveFetchOptions(a,c,e.defaults,r),response:void 0,error:void 0};let d;if(u.options.method&&(u.options.method=u.options.method.toUpperCase()),u.options.onRequest&&await callHooks(u,u.options.onRequest),"string"==typeof u.request&&(u.options.baseURL&&(u.request=function(e,t){if(isEmptyURL(t)||hasProtocol(e))return e;const r=withoutTrailingSlash(t);return e.startsWith(r)?e:joinURL(r,e)}(u.request,u.options.baseURL)),u.options.query&&(u.request=withQuery(u.request,u.options.query),delete u.options.query),"query"in u.options&&delete u.options.query,"params"in u.options&&delete u.options.params),u.options.body&&isPayloadMethod(u.options.method)&&(!function(e){if(void 0===e)return!1;const t=typeof e;return"string"===t||"number"===t||"boolean"===t||null===t||"object"===t&&(!!Array.isArray(e)||!e.buffer&&(e.constructor&&"Object"===e.constructor.name||"function"==typeof e.toJSON))}(u.options.body)?("pipeTo"in u.options.body&&"function"==typeof u.options.body.pipeTo||"function"==typeof u.options.body.pipe)&&("duplex"in u.options||(u.options.duplex="half")):(u.options.body="string"==typeof u.options.body?u.options.body:JSON.stringify(u.options.body),u.options.headers=new r(u.options.headers||{}),u.options.headers.has("content-type")||u.options.headers.set("content-type","application/json"),u.options.headers.has("accept")||u.options.headers.set("accept","application/json"))),!u.options.signal&&u.options.timeout){const e=new s;d=setTimeout(()=>{const t=new Error("[TimeoutError]: The operation was aborted due to timeout");t.name="TimeoutError",t.code=23,e.abort(t)},u.options.timeout),u.options.signal=e.signal}try{u.response=await t(u.request,u.options)}catch(e){return u.error=e,u.options.onRequestError&&await callHooks(u,u.options.onRequestError),await onError(u)}finally{d&&clearTimeout(d)}if((u.response.body||u.response._bodyInit)&&!dr.has(u.response.status)&&"HEAD"!==u.options.method){const e=(u.options.parseResponse?"json":u.options.responseType)||function(e=""){if(!e)return"json";const t=e.split(";").shift()||"";return ur.test(t)?"json":cr.has(t)||t.startsWith("text/")?"text":"blob"}(u.response.headers.get("content-type")||"");switch(e){case"json":{const e=await u.response.text(),t=u.options.parseResponse||destr;u.response._data=t(e);break}case"stream":u.response._data=u.response.body||u.response._bodyInit;break;default:u.response._data=await u.response[e]()}}return u.options.onResponse&&await callHooks(u,u.options.onResponse),!u.options.ignoreResponseError&&u.response.status>=400&&u.response.status<600?(u.options.onResponseError&&await callHooks(u,u.options.onResponseError),await onError(u)):u.response},$fetch=async function(e,t){return(await $fetchRaw(e,t))._data};return $fetch.raw=$fetchRaw,$fetch.native=(...e)=>t(...e),$fetch.create=(t={},r={})=>createFetch({...e,...r,defaults:{...e.defaults,...r.defaults,...t}}),$fetch}const hr=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if(void 0!==ar)return ar;throw new Error("unable to locate global object")}(),pr=hr.fetch?(...e)=>hr.fetch(...e):()=>Promise.reject(new Error("[ofetch] global.fetch is not supported!")),fr=hr.Headers,mr=hr.AbortController;function asyncCall(e,...t){try{return(r=e(...t))&&"function"==typeof r.then?r:Promise.resolve(r)}catch(e){return Promise.reject(e)}var r}function stringify(e){if(function(e){const t=typeof e;return null===e||"object"!==t&&"function"!==t}(e))return String(e);if(function(e){const t=Object.getPrototypeOf(e);return!t||t.isPrototypeOf(Object)}(e)||Array.isArray(e))return JSON.stringify(e);if("function"==typeof e.toJSON)return stringify(e.toJSON());throw new Error("[unstorage] Cannot stringify value!")}createFetch({fetch:pr,Headers:fr,AbortController:mr});const gr="base64:";function serializeRaw(e){return"string"==typeof e?e:gr+function(e){if(globalThis.Buffer)return s.from(e).toString("base64");return globalThis.btoa(String.fromCodePoint(...e))}(e)}function deserializeRaw(e){return"string"!=typeof e?e:e.startsWith(gr)?function(e){if(globalThis.Buffer)return s.from(e,"base64");return Uint8Array.from(globalThis.atob(e),e=>e.codePointAt(0))}(e.slice(7)):e}const yr=["has","hasItem","get","getItem","getItemRaw","set","setItem","setItemRaw","del","remove","removeItem","getMeta","setMeta","removeMeta","getKeys","clear","mount","unmount"];function normalizeKey$1(e){return e&&e.split("?")[0]?.replace(/[/\\]/g,":").replace(/:+/g,":").replace(/^:|:$/g,"")||""}function joinKeys(...e){return normalizeKey$1(e.join(":"))}function normalizeBaseKey(e){return(e=normalizeKey$1(e))?e+":":""}const memory=()=>{const e=new Map;return{name:"memory",getInstance:()=>e,hasItem:t=>e.has(t),getItem:t=>e.get(t)??null,getItemRaw:t=>e.get(t)??null,setItem(t,r){e.set(t,r)},setItemRaw(t,r){e.set(t,r)},removeItem(t){e.delete(t)},getKeys:()=>[...e.keys()],clear(){e.clear()},dispose(){e.clear()}}};function watch(e,t,r){return e.watch?e.watch((e,s)=>t(e,r+s)):()=>{}}async function dispose(e){"function"==typeof e.dispose&&await asyncCall(e.dispose)}const wr={},normalizeKey=function(e){return e&&e.split("?")[0]?.replace(/[/\\]/g,":").replace(/:+/g,":").replace(/^:|:$/g,"")||""},vr={getKeys:()=>Promise.resolve(Object.keys(wr)),hasItem:e=>(e=normalizeKey(e),Promise.resolve(e in wr)),getItem:e=>(e=normalizeKey(e),Promise.resolve(wr[e]?wr[e].import():null)),getMeta:e=>(e=normalizeKey(e),Promise.resolve(wr[e]?wr[e].meta:{}))},br=function(e={}){const t={mounts:{"":e.driver||memory()},mountpoints:[""],watching:!1,watchListeners:[],unwatch:{}},getMount=e=>{for(const r of t.mountpoints)if(e.startsWith(r))return{base:r,relativeKey:e.slice(r.length),driver:t.mounts[r]};return{base:"",relativeKey:e,driver:t.mounts[""]}},getMounts=(e,r)=>t.mountpoints.filter(t=>t.startsWith(e)||r&&e.startsWith(t)).map(r=>({relativeBase:e.length>r.length?e.slice(r.length):void 0,mountpoint:r,driver:t.mounts[r]})),onChange=(e,r)=>{if(t.watching){r=normalizeKey$1(r);for(const s of t.watchListeners)s(e,r)}},stopWatch=async()=>{if(t.watching){for(const e in t.unwatch)await t.unwatch[e]();t.unwatch={},t.watching=!1}},runBatch=(e,t,r)=>{const s=new Map,getBatch=e=>{let t=s.get(e.base);return t||(t={driver:e.driver,base:e.base,items:[]},s.set(e.base,t)),t};for(const r of e){const e="string"==typeof r,s=normalizeKey$1(e?r:r.key),a=e?void 0:r.value,c=e||!r.options?t:{...t,...r.options},u=getMount(s);getBatch(u).items.push({key:s,value:a,relativeKey:u.relativeKey,options:c})}return Promise.all([...s.values()].map(e=>r(e))).then(e=>e.flat())},r={hasItem(e,t={}){e=normalizeKey$1(e);const{relativeKey:r,driver:s}=getMount(e);return asyncCall(s.hasItem,r,t)},getItem(e,t={}){e=normalizeKey$1(e);const{relativeKey:r,driver:s}=getMount(e);return asyncCall(s.getItem,r,t).then(e=>destr(e))},getItems:(e,t={})=>runBatch(e,t,e=>e.driver.getItems?asyncCall(e.driver.getItems,e.items.map(e=>({key:e.relativeKey,options:e.options})),t).then(t=>t.map(t=>({key:joinKeys(e.base,t.key),value:destr(t.value)}))):Promise.all(e.items.map(t=>asyncCall(e.driver.getItem,t.relativeKey,t.options).then(e=>({key:t.key,value:destr(e)}))))),getItemRaw(e,t={}){e=normalizeKey$1(e);const{relativeKey:r,driver:s}=getMount(e);return s.getItemRaw?asyncCall(s.getItemRaw,r,t):asyncCall(s.getItem,r,t).then(e=>deserializeRaw(e))},async setItem(e,t,s={}){if(void 0===t)return r.removeItem(e);e=normalizeKey$1(e);const{relativeKey:a,driver:c}=getMount(e);c.setItem&&(await asyncCall(c.setItem,a,stringify(t),s),c.watch||onChange("update",e))},async setItems(e,t){await runBatch(e,t,async e=>{if(e.driver.setItems)return asyncCall(e.driver.setItems,e.items.map(e=>({key:e.relativeKey,value:stringify(e.value),options:e.options})),t);e.driver.setItem&&await Promise.all(e.items.map(t=>asyncCall(e.driver.setItem,t.relativeKey,stringify(t.value),t.options)))})},async setItemRaw(e,t,s={}){if(void 0===t)return r.removeItem(e,s);e=normalizeKey$1(e);const{relativeKey:a,driver:c}=getMount(e);if(c.setItemRaw)await asyncCall(c.setItemRaw,a,t,s);else{if(!c.setItem)return;await asyncCall(c.setItem,a,serializeRaw(t),s)}c.watch||onChange("update",e)},async removeItem(e,t={}){"boolean"==typeof t&&(t={removeMeta:t}),e=normalizeKey$1(e);const{relativeKey:r,driver:s}=getMount(e);s.removeItem&&(await asyncCall(s.removeItem,r,t),(t.removeMeta||t.removeMata)&&await asyncCall(s.removeItem,r+"$",t),s.watch||onChange("remove",e))},async getMeta(e,t={}){"boolean"==typeof t&&(t={nativeOnly:t}),e=normalizeKey$1(e);const{relativeKey:r,driver:s}=getMount(e),a=Object.create(null);if(s.getMeta&&Object.assign(a,await asyncCall(s.getMeta,r,t)),!t.nativeOnly){const e=await asyncCall(s.getItem,r+"$",t).then(e=>destr(e));e&&"object"==typeof e&&("string"==typeof e.atime&&(e.atime=new Date(e.atime)),"string"==typeof e.mtime&&(e.mtime=new Date(e.mtime)),Object.assign(a,e))}return a},setMeta(e,t,r={}){return this.setItem(e+"$",t,r)},removeMeta(e,t={}){return this.removeItem(e+"$",t)},async getKeys(e,t={}){e=normalizeBaseKey(e);const r=getMounts(e,!0);let s=[];const a=[];let c=!0;for(const e of r){e.driver.flags?.maxDepth||(c=!1);const r=await asyncCall(e.driver.getKeys,e.relativeBase,t);for(const t of r){const r=e.mountpoint+normalizeKey$1(t);s.some(e=>r.startsWith(e))||a.push(r)}s=[e.mountpoint,...s.filter(t=>!t.startsWith(e.mountpoint))]}const u=void 0!==t.maxDepth&&!c;return a.filter(r=>(!u||function(e,t){if(void 0===t)return!0;let r=0,s=e.indexOf(":");for(;s>-1;)r++,s=e.indexOf(":",s+1);return r<=t}(r,t.maxDepth))&&function(e,t){return t?e.startsWith(t)&&"$"!==e[e.length-1]:"$"!==e[e.length-1]}(r,e))},async clear(e,t={}){e=normalizeBaseKey(e),await Promise.all(getMounts(e,!1).map(async e=>{if(e.driver.clear)return asyncCall(e.driver.clear,e.relativeBase,t);if(e.driver.removeItem){const r=await e.driver.getKeys(e.relativeBase||"",t);return Promise.all(r.map(r=>e.driver.removeItem(r,t)))}}))},async dispose(){await Promise.all(Object.values(t.mounts).map(e=>dispose(e)))},watch:async e=>(await(async()=>{if(!t.watching){t.watching=!0;for(const e in t.mounts)t.unwatch[e]=await watch(t.mounts[e],onChange,e)}})(),t.watchListeners.push(e),async()=>{t.watchListeners=t.watchListeners.filter(t=>t!==e),0===t.watchListeners.length&&await stopWatch()}),async unwatch(){t.watchListeners=[],await stopWatch()},mount(e,s){if((e=normalizeBaseKey(e))&&t.mounts[e])throw new Error(`already mounted at ${e}`);return e&&(t.mountpoints.push(e),t.mountpoints.sort((e,t)=>t.length-e.length)),t.mounts[e]=s,t.watching&&Promise.resolve(watch(s,onChange,e)).then(r=>{t.unwatch[e]=r}).catch(console.error),r},async unmount(e,r=!0){(e=normalizeBaseKey(e))&&t.mounts[e]&&(t.watching&&e in t.unwatch&&(t.unwatch[e]?.(),delete t.unwatch[e]),r&&await dispose(t.mounts[e]),t.mountpoints=t.mountpoints.filter(t=>t!==e),delete t.mounts[e])},getMount(e=""){e=normalizeKey$1(e)+":";const t=getMount(e);return{driver:t.driver,base:t.base}},getMounts(e="",t={}){e=normalizeKey$1(e);return getMounts(e,t.parents).map(e=>({driver:e.driver,base:e.mountpoint}))},keys:(e,t={})=>r.getKeys(e,t),get:(e,t={})=>r.getItem(e,t),set:(e,t,s={})=>r.setItem(e,t,s),has:(e,t={})=>r.hasItem(e,t),del:(e,t={})=>r.removeItem(e,t),remove:(e,t={})=>r.removeItem(e,t)};return r}({});function useStorage(e=""){return e?function(e,t){if(!(t=normalizeBaseKey(t)))return e;const r={...e};for(const s of yr)r[s]=(r="",...a)=>e[s](t+r,...a);return r.getKeys=(r="",...s)=>e.getKeys(t+r,...s).then(e=>e.map(e=>e.slice(t.length))),r.getItems=async(r,s)=>{const a=r.map(e=>"string"==typeof e?t+e:{...e,key:t+e.key});return(await e.getItems(a,s)).map(e=>({key:e.key.slice(t.length),value:e.value}))},r.setItems=async(r,s)=>{const a=r.map(e=>({key:t+e.key,value:e.value,options:e.options}));return e.setItems(a,s)},r}(br,e):br}br.mount("/assets",vr);const _r=[1779033703,-1150833019,1013904242,-1521486534,1359893119,-1694144372,528734635,1541459225],jr=[1116352408,1899447441,-1245643825,-373957723,961987163,1508970993,-1841331548,-1424204075,-670586216,310598401,607225278,1426881987,1925078388,-2132889090,-1680079193,-1046744716,-459576895,-272742522,264347078,604807628,770255983,1249150122,1555081692,1996064986,-1740746414,-1473132947,-1341970488,-1084653625,-958395405,-710438585,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,-2117940946,-1838011259,-1564481375,-1474664885,-1035236496,-949202525,-778901479,-694614492,-200395387,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,-2067236844,-1933114872,-1866530822,-1538233109,-1090935817,-965641998],xr=[];class k{_data=new l;_hash=new l([..._r]);_nDataBytes=0;_minBufferSize=0;finalize(e){e&&this._append(e);const t=8*this._nDataBytes,r=8*this._data.sigBytes;return this._data.words[r>>>5]|=128<<24-r%32,this._data.words[14+(r+64>>>9<<4)]=Math.floor(t/4294967296),this._data.words[15+(r+64>>>9<<4)]=t,this._data.sigBytes=4*this._data.words.length,this._process(),this._hash}_doProcessBlock(e,t){const r=this._hash.words;let s=r[0],a=r[1],c=r[2],u=r[3],d=r[4],h=r[5],f=r[6],m=r[7];for(let r=0;r<64;r++){if(r<16)xr[r]=0|e[t+r];else{const e=xr[r-15],t=(e<<25|e>>>7)^(e<<14|e>>>18)^e>>>3,s=xr[r-2],a=(s<<15|s>>>17)^(s<<13|s>>>19)^s>>>10;xr[r]=t+xr[r-7]+a+xr[r-16]}const g=s&a^s&c^a&c,_=(s<<30|s>>>2)^(s<<19|s>>>13)^(s<<10|s>>>22),j=m+((d<<26|d>>>6)^(d<<21|d>>>11)^(d<<7|d>>>25))+(d&h^~d&f)+jr[r]+xr[r];m=f,f=h,h=d,d=u+j|0,u=c,c=a,a=s,s=j+(_+g)|0}r[0]=r[0]+s|0,r[1]=r[1]+a|0,r[2]=r[2]+c|0,r[3]=r[3]+u|0,r[4]=r[4]+d|0,r[5]=r[5]+h|0,r[6]=r[6]+f|0,r[7]=r[7]+m|0}_append(e){"string"==typeof e&&(e=l.fromUtf8(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes}_process(e){let t,r=this._data.sigBytes/64;r=e?Math.ceil(r):Math.max((0|r)-this._minBufferSize,0);const s=16*r,a=Math.min(4*s,this._data.sigBytes);if(s){for(let e=0;e<s;e+=16)this._doProcessBlock(this._data.words,e);t=this._data.words.splice(0,s),this._data.sigBytes-=a}return new l(t,a)}}class l{words;sigBytes;constructor(e,t){e=this.words=e||[],this.sigBytes=void 0===t?4*e.length:t}static fromUtf8(e){const t=unescape(encodeURIComponent(e)),r=t.length,s=[];for(let e=0;e<r;e++)s[e>>>2]|=(255&t.charCodeAt(e))<<24-e%4*8;return new l(s,r)}toBase64(){const e=[];for(let t=0;t<this.sigBytes;t+=3){const r=(this.words[t>>>2]>>>24-t%4*8&255)<<16|(this.words[t+1>>>2]>>>24-(t+1)%4*8&255)<<8|this.words[t+2>>>2]>>>24-(t+2)%4*8&255;for(let s=0;s<4&&8*t+6*s<8*this.sigBytes;s++)e.push("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".charAt(r>>>6*(3-s)&63))}return e.join("")}concat(e){if(this.words[this.sigBytes>>>2]&=4294967295<<32-this.sigBytes%4*8,this.words.length=Math.ceil(this.sigBytes/4),this.sigBytes%4)for(let t=0;t<e.sigBytes;t++){const r=e.words[t>>>2]>>>24-t%4*8&255;this.words[this.sigBytes+t>>>2]|=r<<24-(this.sigBytes+t)%4*8}else for(let t=0;t<e.sigBytes;t+=4)this.words[this.sigBytes+t>>>2]=e.words[t>>>2];this.sigBytes+=e.sigBytes}}const kr=(()=>{class Hasher2{buff="";#o=new Map;write(e){this.buff+=e}dispatch(e){return this[null===e?"null":typeof e](e)}object(e){if(e&&"function"==typeof e.toJSON)return this.object(e.toJSON());const t=Object.prototype.toString.call(e);let r="";const a=t.length;r=a<10?"unknown:["+t+"]":t.slice(8,a-1),r=r.toLowerCase();let c=null;if(void 0!==(c=this.#o.get(e)))return this.dispatch("[CIRCULAR:"+c+"]");if(this.#o.set(e,this.#o.size),void 0!==s&&s.isBuffer&&s.isBuffer(e))return this.write("buffer:"),this.write(e.toString("utf8"));if("object"!==r&&"function"!==r&&"asyncfunction"!==r)this[r]?this[r](e):this.unknown(e,r);else{const t=Object.keys(e).sort(),r=[];this.write("object:"+(t.length+r.length)+":");const dispatchForKey=t=>{this.dispatch(t),this.write(":"),this.dispatch(e[t]),this.write(",")};for(const e of t)dispatchForKey(e);for(const e of r)dispatchForKey(e)}}array(e,t){if(t=void 0!==t&&t,this.write("array:"+e.length+":"),!t||e.length<=1){for(const t of e)this.dispatch(t);return}const r=new Map,s=e.map(e=>{const t=new Hasher2;t.dispatch(e);for(const[e,s]of t.#o)r.set(e,s);return t.toString()});return this.#o=r,s.sort(),this.array(s,!1)}date(e){return this.write("date:"+e.toJSON())}symbol(e){return this.write("symbol:"+e.toString())}unknown(e,t){if(this.write(t),e)return this.write(":"),e&&"function"==typeof e.entries?this.array([...e.entries()],!0):void 0}error(e){return this.write("error:"+e.toString())}boolean(e){return this.write("bool:"+e)}string(e){this.write("string:"+e.length+":"),this.write(e)}function(e){this.write("fn:"),!function(e){if("function"!=typeof e)return!1;return"[native code] }"===Function.prototype.toString.call(e).slice(-15)}(e)?this.dispatch(e.toString()):this.dispatch("[native]")}number(e){return this.write("number:"+e)}null(){return this.write("Null")}undefined(){return this.write("Undefined")}regexp(e){return this.write("regex:"+e.toString())}arraybuffer(e){return this.write("arraybuffer:"),this.dispatch(new Uint8Array(e))}url(e){return this.write("url:"+e.toString())}map(e){this.write("map:");const t=[...e];return this.array(t,!1)}set(e){this.write("set:");const t=[...e];return this.array(t,!1)}bigint(e){return this.write("bigint:"+e.toString())}}for(const e of["uint8array","uint8clampedarray","unt8array","uint16array","unt16array","uint32array","unt32array","float32array","float64array"])Hasher2.prototype[e]=function(t){return this.write(e+":"),this.array([...t],!1)};return Hasher2})();function hash(e){return function(e){return(new k).finalize(e).toBase64()}("string"==typeof e?e:function(e){const t=new kr;return t.dispatch(e),t.buff}(e)).replace(/[-_]/g,"").slice(0,10)}function defineCachedFunction(e,t={}){t={name:"_",base:"/cache",swr:!0,maxAge:1,...t};const r={},s=t.group||"nitro/functions",a=t.name||e.name||"_",c=t.integrity||hash([e,t]),u=t.validate||(e=>void 0!==e.value);return async(...d)=>{if(await(t.shouldBypassCache?.(...d)))return e(...d);const h=await(t.getKey||getKey)(...d),f=await(t.shouldInvalidateCache?.(...d)),m=await async function(e,d,h,f){const m=[t.base,s,a,e+".json"].filter(Boolean).join(":").replace(/:\/$/,":index");let g=await useStorage().getItem(m).catch(e=>{console.error("[cache] Cache read error.",e),useNitroApp().captureError(e,{event:f,tags:["cache"]})})||{};if("object"!=typeof g){g={};const e=new Error("Malformed data read from cache.");console.error("[cache]",e),useNitroApp().captureError(e,{event:f,tags:["cache"]})}const _=1e3*(t.maxAge??0);_&&(g.expires=Date.now()+_);const j=h||g.integrity!==c||_&&Date.now()-(g.mtime||0)>_||!1===u(g),x=j?(async()=>{const s=r[e];s||(void 0!==g.value&&(t.staleMaxAge||0)>=0&&!1===t.swr&&(g.value=void 0,g.integrity=void 0,g.mtime=void 0,g.expires=void 0),r[e]=Promise.resolve(d()));try{g.value=await r[e]}catch(t){throw s||delete r[e],t}if(!s&&(g.mtime=Date.now(),g.integrity=c,delete r[e],!1!==u(g))){let e;t.maxAge&&!t.swr&&(e={ttl:t.maxAge});const r=useStorage().setItem(m,g,e).catch(e=>{console.error("[cache] Cache write error.",e),useNitroApp().captureError(e,{event:f,tags:["cache"]})});f?.waitUntil&&f.waitUntil(r)}})():Promise.resolve();return void 0===g.value?await x:j&&f&&f.waitUntil&&f.waitUntil(x),t.swr&&!1!==u(g)?(x.catch(e=>{console.error("[cache] SWR handler error.",e),useNitroApp().captureError(e,{event:f,tags:["cache"]})}),g):x.then(()=>g)}(h,()=>e(...d),f,d[0]&&isEvent(d[0])?d[0]:void 0);let g=m.value;return t.transform&&(g=await t.transform(m,...d)||g),g}}function getKey(...e){return e.length>0?hash(e):""}function escapeKey(e){return String(e).replace(/\W/g,"")}function cloneWithProxy(e,t){return new Proxy(e,{get:(e,r,s)=>r in t?t[r]:Reflect.get(e,r,s),set:(e,r,s,a)=>r in t?(t[r]=s,!0):Reflect.set(e,r,s,a)})}const cachedEventHandler=function(e,t={name:"_",base:"/cache",swr:!0,maxAge:1}){const r=(t.varies||[]).filter(Boolean).map(e=>e.toLowerCase()).sort(),s={...t,getKey:async e=>{const s=await(t.getKey?.(e));if(s)return escapeKey(s);const a=e.node.req.originalUrl||e.node.req.url||e.path;let c;try{c=escapeKey(decodeURI(parseURL(a).pathname)).slice(0,16)||"index"}catch{c="-"}return[`${c}.${hash(a)}`,...r.map(t=>[t,e.node.req.headers[t]]).map(([e,t])=>`${escapeKey(e)}.${hash(t)}`)].join(":")},validate:e=>!!e.value&&(!(e.value.code>=400)&&(void 0!==e.value.body&&("undefined"!==e.value.headers.etag&&"undefined"!==e.value.headers["last-modified"]))),group:t.group||"nitro/handlers",integrity:t.integrity||hash([e,t])},a=function(e,t={}){return defineCachedFunction(e,t)}(async a=>{const c={};for(const e of r){const t=a.node.req.headers[e];void 0!==t&&(c[e]=t)}const u=cloneWithProxy(a.node.req,{headers:c}),d={};let h;const f=createEvent(u,cloneWithProxy(a.node.res,{statusCode:200,writableEnded:!1,writableFinished:!1,headersSent:!1,closed:!1,getHeader:e=>d[e],setHeader(e,t){return d[e]=t,this},getHeaderNames:()=>Object.keys(d),hasHeader:e=>e in d,removeHeader(e){delete d[e]},getHeaders:()=>d,end(e,t,r){return"string"==typeof e&&(h=e),"function"==typeof t&&t(),"function"==typeof r&&r(),this},write:(e,t,r)=>("string"==typeof e&&(h=e),"function"==typeof t&&t(void 0),"function"==typeof r&&r(),!0),writeHead(e,t){if(this.statusCode=e,t){if(Array.isArray(t)||"string"==typeof t)throw new TypeError("Raw headers  is not supported.");for(const e in t){const r=t[e];void 0!==r&&this.setHeader(e,r)}}return this}}));f.fetch=(e,t)=>fetchWithEvent(f,e,t,{fetch:useNitroApp().localFetch}),f.$fetch=(e,t)=>fetchWithEvent(f,e,t,{fetch:globalThis.$fetch}),f.waitUntil=a.waitUntil,f.context=a.context,f.context.cache={options:s};const m=await e(f)||h,g=f.node.res.getHeaders();g.etag=String(g.Etag||g.etag||`W/"${hash(m)}"`),g["last-modified"]=String(g["Last-Modified"]||g["last-modified"]||(new Date).toUTCString());const _=[];t.swr?(t.maxAge&&_.push(`s-maxage=${t.maxAge}`),t.staleMaxAge?_.push(`stale-while-revalidate=${t.staleMaxAge}`):_.push("stale-while-revalidate")):t.maxAge&&_.push(`max-age=${t.maxAge}`),_.length>0&&(g["cache-control"]=_.join(", "));return{code:f.node.res.statusCode,headers:g,body:m}},s);return defineEventHandler(async r=>{if(t.headersOnly){if(handleCacheHeaders(r,{maxAge:t.maxAge}))return;return e(r)}const s=await a(r);if(r.node.res.headersSent||r.node.res.writableEnded)return s.body;if(!handleCacheHeaders(r,{modifiedTime:new Date(s.headers["last-modified"]),etag:s.headers.etag,maxAge:t.maxAge})){r.node.res.statusCode=s.code;for(const e in s.headers){const t=s.headers[e];"set-cookie"===e?r.node.res.appendHeader(e,splitCookiesString(t)):void 0!==t&&r.node.res.setHeader(e,t)}return s.body}})};function klona(e){if("object"!=typeof e)return e;var t,r,s=Object.prototype.toString.call(e);if("[object Object]"===s){if(e.constructor!==Object&&"function"==typeof e.constructor)for(t in r=new e.constructor,e)e.hasOwnProperty(t)&&r[t]!==e[t]&&(r[t]=klona(e[t]));else for(t in r={},e)"__proto__"===t?Object.defineProperty(r,t,{value:klona(e[t]),configurable:!0,enumerable:!0,writable:!0}):r[t]=klona(e[t]);return r}if("[object Array]"===s){for(t=e.length,r=Array(t);t--;)r[t]=klona(e[t]);return r}return"[object Set]"===s?(r=new Set,e.forEach(function(e){r.add(klona(e))}),r):"[object Map]"===s?(r=new Map,e.forEach(function(e,t){r.set(klona(t),klona(e))}),r):"[object Date]"===s?new Date(+e):"[object RegExp]"===s?((r=new RegExp(e.source,e.flags)).lastIndex=e.lastIndex,r):"[object DataView]"===s?new e.constructor(klona(e.buffer)):"[object ArrayBuffer]"===s?e.slice(0):"Array]"===s.slice(-6)?new e.constructor(e):e}const Er=Dt({}),Rr=/\d/,Cr=["-","_","/","."];function isUppercase(e=""){if(!Rr.test(e))return e!==e.toLowerCase()}function kebabCase(e,t){return e?(Array.isArray(e)?e:function(e){const t=Cr,r=[];if(!e||"string"!=typeof e)return r;let s,a,c="";for(const u of e){const e=t.includes(u);if(!0===e){r.push(c),c="",s=void 0;continue}const d=isUppercase(u);if(!1===a){if(!1===s&&!0===d){r.push(c),c=u,s=d;continue}if(!0===s&&!1===d&&c.length>1){const e=c.at(-1);r.push(c.slice(0,Math.max(0,c.length-1))),c=e+u,s=d;continue}}c+=u,s=d,a=e}return r.push(c),r}(e)).map(e=>e.toLowerCase()).join(t):""}function getEnv(e,t){const r=(s=e,kebabCase(s||"","_")).toUpperCase();var s;return destr(g.env[t.prefix+r]??g.env[t.altPrefix+r])}function _isObject(e){return"object"==typeof e&&!Array.isArray(e)}const Tr=/\{\{([^{}]*)\}\}/g;function _expandFromEnv(e){return e.replace(Tr,(e,t)=>g.env[t]||e)}const Sr={app:{baseURL:"/"},nitro:{routeRules:{}}},Ar={prefix:"NITRO_",altPrefix:Sr.nitro.envPrefix??g.env.NITRO_ENV_PREFIX??"_",envExpansion:Sr.nitro.envExpansion??g.env.NITRO_ENV_EXPANSION??!1},Pr=_deepFreeze(function applyEnv(e,t,r=""){for(const s in e){const a=r?`${r}_${s}`:s,c=getEnv(a,t);_isObject(e[s])?_isObject(c)?(e[s]={...e[s],...c},applyEnv(e[s],t,a)):void 0===c?applyEnv(e[s],t,a):e[s]=c??e[s]:e[s]=c??e[s],t.envExpansion&&"string"==typeof e[s]&&(e[s]=_expandFromEnv(e[s]))}return e}(klona(Sr),Ar));function useRuntimeConfig(e){return Pr}function _deepFreeze(e){const t=Object.getOwnPropertyNames(e);for(const r of t){const t=e[r];t&&"object"==typeof t&&_deepFreeze(t)}return Object.freeze(e)}_deepFreeze(klona(Er)),new Proxy(Object.create(null),{get:(e,t)=>{console.warn("Please use `useRuntimeConfig()` instead of accessing config directly.");const r=useRuntimeConfig();if(t in r)return r[t]}});const Hr="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:void 0!==ar?ar:{},Ir="__unctx__",zr=Hr[Ir]||(Hr[Ir]=function(e={}){const t={};return{get:(r,s={})=>(t[r]||(t[r]=function(e={}){let t,r=!1;const checkConflict=e=>{if(t&&t!==e)throw new Error("Context conflict")};let s;if(e.asyncContext){const t=e.AsyncLocalStorage||globalThis.AsyncLocalStorage;t?s=new t:console.warn("[unctx] `AsyncLocalStorage` is not provided.")}const _getCurrentInstance=()=>{if(s){const e=s.getStore();if(void 0!==e)return e}return t};return{use:()=>{const e=_getCurrentInstance();if(void 0===e)throw new Error("Context is not available");return e},tryUse:()=>_getCurrentInstance(),set:(e,s)=>{s||checkConflict(e),t=e,r=!0},unset:()=>{t=void 0,r=!1},call:(e,a)=>{checkConflict(e),t=e;try{return s?s.run(e,a):a()}finally{r||(t=void 0)}},async callAsync(e,a){t=e;const onRestore=()=>{t=e},onLeave=()=>t===e?onRestore:void 0;Br.add(onLeave);try{const c=s?s.run(e,a):a();return r||(t=void 0),await c}finally{Br.delete(onLeave)}}}}({...e,...s})),t[r])}}()),Mr="__unctx_async_handlers__",Br=Hr[Mr]||(Hr[Mr]=new Set);((e,t={})=>{zr.get(e,t)})("nitro-app",{asyncContext:void 0,AsyncLocalStorage:void 0});const Or=toRouteMatcher(createRouter$1({routes:useRuntimeConfig().nitro.routeRules}));function createRouteRulesHandler(e){return rr(t=>{const r=function(e){e.context._nitro=e.context._nitro||{},e.context._nitro.routeRules||(e.context._nitro.routeRules=getRouteRulesForPath(withoutBase(e.path.split("?")[0],useRuntimeConfig().app.baseURL)));return e.context._nitro.routeRules}(t);if(r.headers&&Vt(t,r.headers),r.redirect){let e=r.redirect.to;if(e.endsWith("/**")){let s=t.path;const a=r.redirect._redirectStripBase;a&&(s=withoutBase(s,a)),e=joinURL(e.slice(0,-3),s)}else if(t.path.includes("?")){e=withQuery(e,getQuery(t.path))}return function(e,t,r=302){return e.node.res.statusCode=sanitizeStatusCode(r,e.node.res.statusCode),e.node.res.setHeader("location",t),send(e,`<!DOCTYPE html><html><head><meta http-equiv="refresh" content="0; url=${t.replace(/"/g,"%22")}"></head></html>`,Gt.html)}(t,e,r.redirect.statusCode)}if(r.proxy){let s=r.proxy.to;if(s.endsWith("/**")){let e=t.path;const a=r.proxy._proxyStripBase;a&&(e=withoutBase(e,a)),s=joinURL(s.slice(0,-3),e)}else if(t.path.includes("?")){s=withQuery(s,getQuery(t.path))}return proxyRequest(t,s,{fetch:e.localFetch,...r.proxy})}})}function getRouteRulesForPath(e){return Lt({},...Or.matchAll(e).reverse())}const Nr=/post|put|patch/i;function joinHeaders(e){return Array.isArray(e)?e.join(", "):String(e)}function normalizeCookieHeader(e=""){return splitCookiesString(joinHeaders(e))}function normalizeCookieHeaders(e){const t=new Headers;for(const[r,s]of e)if("set-cookie"===r)for(const e of normalizeCookieHeader(s))t.append("set-cookie",e);else t.set(r,joinHeaders(s));return t}function defaultHandler(e,t,r){const s=e.unhandled||e.fatal,a=e.statusCode||500,c=e.statusMessage||"Server Error",u=function(e,t={}){const r=function(e,t={}){if(t.xForwardedHost){const t=e.node.req.headers["x-forwarded-host"];if(t)return t}return e.node.req.headers.host||"localhost"}(e,t),s=function(e,t={}){return!1!==t.xForwardedProto&&"https"===e.node.req.headers["x-forwarded-proto"]||e.node.req.connection?.encrypted?"https":"http"}(e,t),a=(e.node.req.originalUrl||e.path).replace(/^[/\\]+/g,"/");return new URL(a,`${s}://${r}`)}(t,{xForwardedHost:!0,xForwardedProto:!0});if(404===a){const e="/";if(/^\/[^/]/.test(e)&&!u.pathname.startsWith(e)){return{status:302,statusText:"Found",headers:{location:`${e}${u.pathname.slice(1)}${u.search}`},body:"Redirecting..."}}}if(s&&!r?.silent){const r=[e.unhandled&&"[unhandled]",e.fatal&&"[fatal]"].filter(Boolean).join(" ");console.error(`[request error] ${r} [${t.method}] ${u}\n`,e)}const d={"content-type":"application/json","x-content-type-options":"nosniff","x-frame-options":"DENY","referrer-policy":"no-referrer","content-security-policy":"script-src 'none'; frame-ancestors 'none';"};setResponseStatus(t,a,c),404!==a&&function(e,t){return e.node.res.getHeader(t)}(t,"cache-control")||(d["cache-control"]="no-cache");return{status:a,statusText:c,headers:d,body:{error:!0,url:u.href,statusCode:a,statusMessage:c,message:s?"Server Error":e.message,data:s?void 0:e.data}}}const Ur=[function(e,t){const r=defaultHandler(e,t);return setResponseHeaders(t,r.headers),setResponseStatus(t,r.status,r.statusText),send(t,JSON.stringify(r.body,null,2))}];const qr=[],Lr=[{route:"/**",handler:()=>import("../_/routeTree.gen-BFK54byf.mjs").then(function(e){return e.t}),lazy:!0,middleware:!1,method:void 0}];const Dr=function(){const e=useRuntimeConfig(),t=new Hookable,captureError=(e,r={})=>{const s=t.callHookParallel("error",e,r).catch(e=>{console.error("Error while capturing another error",e)});if(r.event&&isEvent(r.event)){const t=r.event.context.nitro?.errors;t&&t.push({error:e,context:r}),r.event.waitUntil&&r.event.waitUntil(s)}},r=createApp({debug:destr(!1),onError:(e,t)=>(captureError(e,{event:t,tags:["request"]}),async function(e,t){for(const r of Ur)try{if(await r(e,t,{defaultHandler:defaultHandler}),t.handled)return}catch(e){console.error(e)}}(e,t)),onRequest:async e=>{e.context.nitro=e.context.nitro||{errors:[]};const t=e.node.req?.__unenv__;t?._platform&&(e.context={_platform:t?._platform,...t._platform,...e.context}),!e.context.waitUntil&&t?.waitUntil&&(e.context.waitUntil=t.waitUntil),e.fetch=(t,r)=>fetchWithEvent(e,t,r,{fetch:localFetch}),e.$fetch=(t,r)=>fetchWithEvent(e,t,r,{fetch:c}),e.waitUntil=t=>{e.context.nitro._waitUntilPromises||(e.context.nitro._waitUntilPromises=[]),e.context.nitro._waitUntilPromises.push(t),e.context.waitUntil&&e.context.waitUntil(t)},e.captureError=(t,r)=>{captureError(t,{event:e,...r})},await Dr.hooks.callHook("request",e).catch(t=>{captureError(t,{event:e,tags:["request"]})})},onBeforeResponse:async(e,t)=>{await Dr.hooks.callHook("beforeResponse",e,t).catch(t=>{captureError(t,{event:e,tags:["request","response"]})})},onAfterResponse:async(e,t)=>{await Dr.hooks.callHook("afterResponse",e,t).catch(t=>{captureError(t,{event:e,tags:["request","response"]})})}}),s=function(e={}){const t=createRouter$1({}),r={};let s;const a={},addRoute=(e,s,c)=>{let u=r[e];if(u||(r[e]=u={path:e,handlers:{}},t.insert(e,u)),Array.isArray(c))for(const t of c)addRoute(e,s,t);else u.handlers[c]=toEventHandler(s,0,e);return a};a.use=a.add=(e,t,r)=>addRoute(e,t,r||"all");for(const e of sr)a[e]=(t,r)=>a.add(t,r,e);const matchHandler=(e="/",r="get")=>{const a=e.indexOf("?");-1!==a&&(e=e.slice(0,Math.max(0,a)));const c=t.lookup(e);if(!c||!c.handlers)return{error:createError({statusCode:404,name:"Not Found",statusMessage:`Cannot find any route matching ${e||"/"}.`})};let u=c.handlers[r]||c.handlers.all;if(!u){s||(s=toRouteMatcher(t));const a=s.matchAll(e).reverse();for(const e of a){if(e.handlers[r]){u=e.handlers[r],c.handlers[r]=c.handlers[r]||u;break}if(e.handlers.all){u=e.handlers.all,c.handlers.all=c.handlers.all||u;break}}}return u?{matched:c,handler:u}:{error:createError({statusCode:405,name:"Method Not Allowed",statusMessage:`Method ${r} is not allowed on this route.`})}},c=e.preemptive||e.preemtive;return a.handler=rr(e=>{const t=matchHandler(e.path,e.method.toLowerCase());if("error"in t){if(c)throw t.error;return}e.context.matchedRoute=t.matched;const r=t.matched.params||{};return e.context.params=r,Promise.resolve(t.handler(e)).then(e=>void 0===e&&c?null:e)}),a.handler.__resolve__=async e=>{e=function(e=""){return function(e=""){return e.startsWith("/")}(e)?e:"/"+e}(e);const t=matchHandler(e);if("error"in t)return;let r={route:t.matched.path,handler:t.handler};if(t.handler.__resolve__){const s=await t.handler.__resolve__(e);if(!s)return;r={...r,...s}}return r},a}({preemptive:!0}),a=toNodeListener(r),localFetch=(e,t)=>e.toString().startsWith("/")?async function(e,t,r={}){try{const s=await b(e,{url:t,...r});return new Response(s.body,{status:s.status,statusText:s.statusText,headers:v(s.headers)})}catch(e){return new Response(e.toString(),{status:Number.parseInt(e.statusCode||e.code)||500,statusText:e.statusText})}}(a,e,t).then(e=>function(e){return e.headers.has("set-cookie")?new Response(e.body,{status:e.status,statusText:e.statusText,headers:normalizeCookieHeaders(e.headers)}):e}(e)):globalThis.fetch(e,t),c=createFetch({fetch:localFetch,Headers:fr,defaults:{baseURL:e.app.baseURL}});globalThis.$fetch=c,r.use(createRouteRulesHandler({localFetch:localFetch}));for(const t of Lr){let a=t.lazy?lazyEventHandler(t.handler):t.handler;if(t.middleware||!t.route){const s=(e.app.baseURL+(t.route||"/")).replace(/\/+/g,"/");r.use(s,a)}else{const e=getRouteRulesForPath(t.route.replace(/:\w+|\*\*/g,"_"));e.cache&&(a=cachedEventHandler(a,{group:"nitro/routes",...e.cache})),s.use(t.route,a,t.method)}}return r.use(e.app.baseURL,s.handler),{hooks:t,h3App:r,router:s,localCall:e=>b(a,e),localFetch:localFetch,captureError:captureError}}();function useNitroApp(){return Dr}!function(e){for(const t of qr)try{t(e)}catch(t){throw e.captureError(t,{tags:["plugin"]}),t}}(Dr);const Wr={"/Selection_405.png":{type:"image/png",etag:'"1b2cc-e4hSOz8fHDTHwmduhosdc1I9EIE"',mtime:"2025-07-18T17:52:04.494Z",size:111308,path:"../public/Selection_405.png"},"/attractive-person-profile.avif":{type:"image/avif",etag:'"2095-235q3XTyc4s3kN0QviGNSb/wz70"',mtime:"2025-07-18T17:52:04.494Z",size:8341,path:"../public/attractive-person-profile.avif"},"/image.webp":{type:"image/webp",etag:'"2c40-g+G5kLTY8WhcL/5JkZj4vCfCM6g"',mtime:"2025-07-18T17:52:04.493Z",size:11328,path:"../public/image.webp"},"/placeholder-logo.png":{type:"image/png",etag:'"3be-qsopa2uV0uSFvNDDy9FW4Z5JLqE"',mtime:"2025-07-18T17:52:04.493Z",size:958,path:"../public/placeholder-logo.png"},"/placeholder-logo.svg":{type:"image/svg+xml",etag:'"c88-Vv8IA2xgjEZAiN2dErxBClzvxAM"',mtime:"2025-07-18T17:52:04.494Z",size:3208,path:"../public/placeholder-logo.svg"},"/placeholder-user.jpg":{type:"image/jpeg",etag:'"a37-APyF+TsfT3sqrarJvcMR1CG+w6M"',mtime:"2025-07-18T17:52:04.494Z",size:2615,path:"../public/placeholder-user.jpg"},"/placeholder.jpg":{type:"image/jpeg",etag:'"63c-cB8/OSmG/LRrjxzpGh9HI2hQFfc"',mtime:"2025-07-18T17:52:04.494Z",size:1596,path:"../public/placeholder.jpg"},"/placeholder.svg":{type:"image/svg+xml",etag:'"cb5-3cfZ/x0uNhX4kurZGAkOBE4K/G0"',mtime:"2025-07-18T17:52:04.494Z",size:3253,path:"../public/placeholder.svg"},"/.vite/manifest.json":{type:"application/json",etag:'"3324-w7jDj88nS2IkVe1rQBApHPA2jz0"',mtime:"2025-07-18T17:52:04.487Z",size:13092,path:"../public/.vite/manifest.json"},"/assets/404-JXD3VwG4.js":{type:"text/javascript; charset=utf-8",etag:'"1d8-i3OYUo7z92JdghXrxLE/MKezCVU"',mtime:"2025-07-18T17:52:04.487Z",size:472,path:"../public/assets/404-JXD3VwG4.js"},"/assets/PrivacyNotice-C9kEPnp4.js":{type:"text/javascript; charset=utf-8",etag:'"17713-n0PTyuBOd/BYmgo25nolegxVCfU"',mtime:"2025-07-18T17:52:04.488Z",size:96019,path:"../public/assets/PrivacyNotice-C9kEPnp4.js"},"/assets/ProtectedRoute-DIpB4JWB.js":{type:"text/javascript; charset=utf-8",etag:'"b1-Fh2mllqfzQ3qLEIZk3B0s4mr2IU"',mtime:"2025-07-18T17:52:04.489Z",size:177,path:"../public/assets/ProtectedRoute-DIpB4JWB.js"},"/assets/account-settings-C00NADvv.js":{type:"text/javascript; charset=utf-8",etag:'"2463-N34YMfF5tE1U9qxnrW0UO2nh3nw"',mtime:"2025-07-18T17:52:04.488Z",size:9315,path:"../public/assets/account-settings-C00NADvv.js"},"/assets/advanced-scoring-CKP2w-nj.js":{type:"text/javascript; charset=utf-8",etag:'"2032-sFTaKZPEflSYNcASk2+Hq12bjPk"',mtime:"2025-07-18T17:52:04.488Z",size:8242,path:"../public/assets/advanced-scoring-CKP2w-nj.js"},"/assets/analysis-BhwX0Tn_.js":{type:"text/javascript; charset=utf-8",etag:'"6bb-LAlugVfvfiRI3dZgT4jRiY299PI"',mtime:"2025-07-18T17:52:04.488Z",size:1723,path:"../public/assets/analysis-BhwX0Tn_.js"},"/assets/analysis-comparison-Cnr7fkj1.js":{type:"text/javascript; charset=utf-8",etag:'"2767-+lp4zfnRuQgh1MxiuZuWmbglzZE"',mtime:"2025-07-18T17:52:04.488Z",size:10087,path:"../public/assets/analysis-comparison-Cnr7fkj1.js"},"/assets/analysis-service-DxWJ7Psk.js":{type:"text/javascript; charset=utf-8",etag:'"540a-4w6ZTne6/kuQ81lkYHz8yTh8N8k"',mtime:"2025-07-18T17:52:04.489Z",size:21514,path:"../public/assets/analysis-service-DxWJ7Psk.js"},"/assets/arrow-left-CrPLcqAC.js":{type:"text/javascript; charset=utf-8",etag:'"c2-P4/nTpgObjUEMuAcH7hJkse+UMI"',mtime:"2025-07-18T17:52:04.488Z",size:194,path:"../public/assets/arrow-left-CrPLcqAC.js"},"/assets/badge-B_rsQKkv.js":{type:"text/javascript; charset=utf-8",etag:'"325-rJuJCEvXDalxJoPG+W2kWK0de8g"',mtime:"2025-07-18T17:52:04.489Z",size:805,path:"../public/assets/badge-B_rsQKkv.js"},"/assets/bio-analyzer-DgsoUF1K.js":{type:"text/javascript; charset=utf-8",etag:'"89f7-jm2uy4xPOiCR69gHLRDrO3OMEQU"',mtime:"2025-07-18T17:52:04.489Z",size:35319,path:"../public/assets/bio-analyzer-DgsoUF1K.js"},"/assets/bio-analyzer-pro-DWKz7lQ3.js":{type:"text/javascript; charset=utf-8",etag:'"ac74-B+5/kF2FHShjPjDbMbInH4ExTzA"',mtime:"2025-07-18T17:52:04.489Z",size:44148,path:"../public/assets/bio-analyzer-pro-DWKz7lQ3.js"},"/assets/brain-wUBLYMm0.js":{type:"text/javascript; charset=utf-8",etag:'"2f8-pGaUgBzn0i66/JXPPRZej6Jvx3g"',mtime:"2025-07-18T17:52:04.489Z",size:760,path:"../public/assets/brain-wUBLYMm0.js"},"/assets/camera-P6IreDGA.js":{type:"text/javascript; charset=utf-8",etag:'"113-jGp451KzAApOxC3IGd0losj/sK8"',mtime:"2025-07-18T17:52:04.489Z",size:275,path:"../public/assets/camera-P6IreDGA.js"},"/assets/card-Bzfaa5B9.js":{type:"text/javascript; charset=utf-8",etag:'"469-DH4j7ZGhEHEjfLnwkAznsiHKKOk"',mtime:"2025-07-18T17:52:04.489Z",size:1129,path:"../public/assets/card-Bzfaa5B9.js"},"/assets/createLucideIcon-JB7IMeGf.js":{type:"text/javascript; charset=utf-8",etag:'"1278-U+o7uidi0fDkTfZssf5mhC/9o4w"',mtime:"2025-07-18T17:52:04.491Z",size:4728,path:"../public/assets/createLucideIcon-JB7IMeGf.js"},"/assets/crown-BUU0WoJh.js":{type:"text/javascript; charset=utf-8",etag:'"184-UAoLbbus7Ldj8Js+dxDLuOZ38XI"',mtime:"2025-07-18T17:52:04.490Z",size:388,path:"../public/assets/crown-BUU0WoJh.js"},"/assets/dashboard-BBZPk5g4.js":{type:"text/javascript; charset=utf-8",etag:'"1e16-Qq/f2oy4cpAZEavYbWmdHQUAs6A"',mtime:"2025-07-18T17:52:04.490Z",size:7702,path:"../public/assets/dashboard-BBZPk5g4.js"},"/assets/dist-C61kQO-x.js":{type:"text/javascript; charset=utf-8",etag:'"4c63-rzMJ2IgiUsY5oi9eTRvqlSNrV9s"',mtime:"2025-07-18T17:52:04.490Z",size:19555,path:"../public/assets/dist-C61kQO-x.js"},"/assets/dist-CgCb95MR.js":{type:"text/javascript; charset=utf-8",etag:'"25293-A+Y1F3bx5NBarPatMmqdqlTk6cc"',mtime:"2025-07-18T17:52:04.490Z",size:152211,path:"../public/assets/dist-CgCb95MR.js"},"/assets/dist-DGuqR-4V.js":{type:"text/javascript; charset=utf-8",etag:'"889-Y/9MqLX1Ix+DtvCZ/cW7QWE2Pmw"',mtime:"2025-07-18T17:52:04.490Z",size:2185,path:"../public/assets/dist-DGuqR-4V.js"},"/assets/download-D5v-g3Ed.js":{type:"text/javascript; charset=utf-8",etag:'"123-xzjCj0nwG9x6JbRPnoeQxPw7vnk"',mtime:"2025-07-18T17:52:04.490Z",size:291,path:"../public/assets/download-D5v-g3Ed.js"},"/assets/image-analyzer-mcjslnNz.js":{type:"text/javascript; charset=utf-8",etag:'"2797-7fMDrp/S03JYX0uhM1EJ42Xdoe0"',mtime:"2025-07-18T17:52:04.491Z",size:10135,path:"../public/assets/image-analyzer-mcjslnNz.js"},"/assets/image-analyzer-pro-BSNjoE5E.js":{type:"text/javascript; charset=utf-8",etag:'"9a7b-kUDJlzaPaWZ1sxOIO643sMTtGFo"',mtime:"2025-07-18T17:52:04.490Z",size:39547,path:"../public/assets/image-analyzer-pro-BSNjoE5E.js"},"/assets/image-editing-service-Bu1i98x1.js":{type:"text/javascript; charset=utf-8",etag:'"be-lfN1D11N3uyGb4LY0Qy9ZzFQGlw"',mtime:"2025-07-18T17:52:04.490Z",size:190,path:"../public/assets/image-editing-service-Bu1i98x1.js"},"/assets/image-editing-service-Ck8uvW7j.js":{type:"text/javascript; charset=utf-8",etag:'"11cd8-Sl01gmzp7+/Sh1J/cGqN7niPRCg"',mtime:"2025-07-18T17:52:04.491Z",size:72920,path:"../public/assets/image-editing-service-Ck8uvW7j.js"},"/assets/image-editor-test-BwhwjrcH.js":{type:"text/javascript; charset=utf-8",etag:'"a265-MAzF0nrgXUIU/SQh9OoZThrKrxw"',mtime:"2025-07-18T17:52:04.490Z",size:41573,path:"../public/assets/image-editor-test-BwhwjrcH.js"},"/assets/main-B4G73TvM.js":{type:"text/javascript; charset=utf-8",etag:'"5c1c3-GLOg+vxuaUSTzSFUzi7UiDXW4+o"',mtime:"2025-07-18T17:52:04.492Z",size:377283,path:"../public/assets/main-B4G73TvM.js"},"/assets/main-Cx3HDUKW.css":{type:"text/css; charset=utf-8",etag:'"a59b-WgX4S+F5OERZbTy3zD4Nkogk0lM"',mtime:"2025-07-18T17:52:04.491Z",size:42395,path:"../public/assets/main-Cx3HDUKW.css"},"/assets/progress-BIjJajwn.js":{type:"text/javascript; charset=utf-8",etag:'"d1c-gnSsYX3EOqzJ61Pw7KntrzSQP1A"',mtime:"2025-07-18T17:52:04.491Z",size:3356,path:"../public/assets/progress-BIjJajwn.js"},"/assets/routes-BLsNgnxf.js":{type:"text/javascript; charset=utf-8",etag:'"1f4d9-FVK7imrmXOxYWqYsk67cQwUZcWQ"',mtime:"2025-07-18T17:52:04.492Z",size:128217,path:"../public/assets/routes-BLsNgnxf.js"},"/assets/settings-Tged_CGs.js":{type:"text/javascript; charset=utf-8",etag:'"2f2-XsxFXXXpQsXBy68Mx+JLVy0JMLE"',mtime:"2025-07-18T17:52:04.491Z",size:754,path:"../public/assets/settings-Tged_CGs.js"},"/assets/shield-Ct2yHJj1.js":{type:"text/javascript; charset=utf-8",etag:'"12b-k4S5L3IeG/k2CZL6o0A76mWHhq0"',mtime:"2025-07-18T17:52:04.492Z",size:299,path:"../public/assets/shield-Ct2yHJj1.js"},"/assets/slider-DbQhj1b4.js":{type:"text/javascript; charset=utf-8",etag:'"3361-tUNjJpCg3sB4lII57NpD56pOn10"',mtime:"2025-07-18T17:52:04.491Z",size:13153,path:"../public/assets/slider-DbQhj1b4.js"},"/assets/sparkles-BbQK6jEu.js":{type:"text/javascript; charset=utf-8",etag:'"219-o7BrTnTTwi3kIdsGcdAfywnBiiY"',mtime:"2025-07-18T17:52:04.492Z",size:537,path:"../public/assets/sparkles-BbQK6jEu.js"},"/assets/star-Dm2nqXPr.js":{type:"text/javascript; charset=utf-8",etag:'"2b0-1Sq13IvtZp/ADObEvaFth7f4abM"',mtime:"2025-07-18T17:52:04.492Z",size:688,path:"../public/assets/star-Dm2nqXPr.js"},"/assets/storage-BLisfAL7.js":{type:"text/javascript; charset=utf-8",etag:'"e25-Oc7+aP23DtXfvkJNEGNb6f0dSFY"',mtime:"2025-07-18T17:52:04.492Z",size:3621,path:"../public/assets/storage-BLisfAL7.js"},"/assets/tabs-CvPwdQTd.js":{type:"text/javascript; charset=utf-8",etag:'"33c4-ueUbuSJg1RHFC4qs7nfmPTHG888"',mtime:"2025-07-18T17:52:04.492Z",size:13252,path:"../public/assets/tabs-CvPwdQTd.js"},"/assets/textarea-BtYj4OS8.js":{type:"text/javascript; charset=utf-8",etag:'"335-g+BQt3FZkw7UwwWNiJpMF1fC2EY"',mtime:"2025-07-18T17:52:04.492Z",size:821,path:"../public/assets/textarea-BtYj4OS8.js"},"/assets/trash-2-CkLpzZy2.js":{type:"text/javascript; charset=utf-8",etag:'"9e9-R+AQAPIZ3wlmkorFRS18r1Ek5O8"',mtime:"2025-07-18T17:52:04.492Z",size:2537,path:"../public/assets/trash-2-CkLpzZy2.js"},"/assets/trending-up-BvkHvnfP.js":{type:"text/javascript; charset=utf-8",etag:'"eb-O4qEbRzigMO//mmRLUbXJH740fE"',mtime:"2025-07-18T17:52:04.493Z",size:235,path:"../public/assets/trending-up-BvkHvnfP.js"},"/assets/uiComponents-BgWMuAlY.js":{type:"text/javascript; charset=utf-8",etag:'"42e-+cec2Lt9EzXHmmRp1NyKeuT/FRs"',mtime:"2025-07-18T17:52:04.492Z",size:1070,path:"../public/assets/uiComponents-BgWMuAlY.js"},"/assets/user-DxSNRym-.js":{type:"text/javascript; charset=utf-8",etag:'"dd-PHog06GmZsGnpfGfnV/7r8Z9bAc"',mtime:"2025-07-18T17:52:04.493Z",size:221,path:"../public/assets/user-DxSNRym-.js"},"/assets/welcome-DaUTS0x0.js":{type:"text/javascript; charset=utf-8",etag:'"1b7b-cA3u1SfIJPyYDb2jzxMjey4ji7Y"',mtime:"2025-07-18T17:52:04.493Z",size:7035,path:"../public/assets/welcome-DaUTS0x0.js"}},Kr={};const $r=function(e){const t=useNitroApp();return{async fetch(r,a,c){const u={},d=new URL(r.url);if(e.fetch){const t=await e.fetch(r,a,c,d,u);if(t)return t}return async function(e,t,r,a=new URL(e.url),c=useNitroApp(),u){let d;(function(e){return Nr.test(e.method)})(e)&&(d=s.from(await e.arrayBuffer()));return globalThis.__env__=t,c.localFetch(a.pathname+a.search,{context:{waitUntil:e=>r.waitUntil(e),_platform:{cf:e.cf,cloudflare:{request:e,env:t,context:r,url:a,...u}}},host:a.hostname,protocol:a.protocol,method:e.method,headers:e.headers,body:d})}(r,a,c,d,t,u)},scheduled(e,r,s){globalThis.__env__=r,s.waitUntil(t.hooks.callHook("cloudflare:scheduled",{controller:e,env:r,context:s}))},email(e,r,s){globalThis.__env__=r,s.waitUntil(t.hooks.callHook("cloudflare:email",{message:e,event:e,env:r,context:s}))},queue(e,r,s){globalThis.__env__=r,s.waitUntil(t.hooks.callHook("cloudflare:queue",{batch:e,event:e,env:r,context:s}))},tail(e,r,s){globalThis.__env__=r,s.waitUntil(t.hooks.callHook("cloudflare:tail",{traces:e,env:r,context:s}))},trace(e,r,s){globalThis.__env__=r,s.waitUntil(t.hooks.callHook("cloudflare:trace",{traces:e,env:r,context:s}))}}}({fetch(e,t,r,s){if(t.ASSETS&&function(e=""){if(Wr[e])return!0;for(const t in Kr)if(e.startsWith(t))return!0;return!1}(s.pathname))return t.ASSETS.fetch(e)}});export{$r as a,createNotImplementedError as c,ar as g,g as m};
//# sourceMappingURL=nitro.mjs.map
