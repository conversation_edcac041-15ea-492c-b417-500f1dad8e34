import{__toESM as e,cn as t,require_jsx_runtime as n,require_react as r}from"./main-B4G73TvM.js";import{createLucideIcon as i}from"./createLucideIcon-JB7IMeGf.js";const a=i(`Copy`,[[`rect`,{width:`14`,height:`14`,x:`8`,y:`8`,rx:`2`,ry:`2`,key:`17jyea`}],[`path`,{d:`M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2`,key:`zix9uf`}]]);var o=e(r()),s=e(n());const c=o.forwardRef(({className:e,...n},r)=>(0,s.jsx)(`textarea`,{className:t(`flex w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-flame-red focus-visible:ring-offset-0`,`min-h-[80px]`,`disabled:cursor-not-allowed disabled:opacity-50`,e),ref:r,...n}));c.displayName=`Textarea`;export{a as Copy,c as Textarea};