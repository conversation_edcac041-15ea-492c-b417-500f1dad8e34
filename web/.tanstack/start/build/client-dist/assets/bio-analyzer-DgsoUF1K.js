import{Link as e,__toESM as t,cn as n,require_jsx_runtime as r,require_react as i,require_react_dom as a}from"./main-B4G73TvM.js";import"./dist-CgCb95MR.js";import{BIO_ANALYSIS_STEPS as o}from"./analysis-BhwX0Tn_.js";import{Button as s,createSlot as c,cva as l}from"./createLucideIcon-JB7IMeGf.js";import{Badge as u}from"./badge-B_rsQKkv.js";import{ArrowLeft as d}from"./arrow-left-CrPLcqAC.js";import{Copy as f,Textarea as p}from"./textarea-BtYj4OS8.js";import{Crown as m}from"./crown-BUU0WoJh.js";import{LoaderCircle as h}from"./dist-C61kQO-x.js";import{RefreshCw as g,Slider as _}from"./slider-DbQhj1b4.js";import{Sparkles as v}from"./sparkles-BbQK6jEu.js";import{X as y}from"./storage-BLisfAL7.js";import{Progress as b}from"./progress-BIjJajwn.js";import{Tabs as x,TabsContent as S,TabsList as ee,TabsTrigger as C}from"./tabs-CvPwdQTd.js";import{analysisService as w}from"./analysis-service-DxWJ7Psk.js";var T=t(i());const E=1,D=5e3;let O=0;function k(){return O=(O+1)%(2**53-1),O.toString()}const A=new Map,j=e=>{if(A.has(e))return;let t=setTimeout(()=>{A.delete(e),P({type:`REMOVE_TOAST`,toastId:e})},D);A.set(e,t)},te=(e,t)=>{switch(t.type){case`ADD_TOAST`:return{...e,toasts:[t.toast,...e.toasts].slice(0,E)};case`UPDATE_TOAST`:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case`DISMISS_TOAST`:{let{toastId:n}=t;return n?j(n):e.toasts.forEach(e=>{j(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===n||n===void 0?{...e,open:!1}:e)}}case`REMOVE_TOAST`:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},M=[];let N={toasts:[]};function P(e){N=te(N,e),M.forEach(e=>{e(N)})}function ne({...e}){let t=k(),n=e=>P({type:`UPDATE_TOAST`,toast:{...e,id:t}}),r=()=>P({type:`DISMISS_TOAST`,toastId:t});return P({type:`ADD_TOAST`,toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:n}}function re(){let[e,t]=T.useState(N);return T.useEffect(()=>(M.push(t),()=>{let e=M.indexOf(t);e>-1&&M.splice(e,1)}),[e]),{...e,toast:ne,dismiss:e=>P({type:`DISMISS_TOAST`,toastId:e})}}function F(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),n===!1||!r.defaultPrevented)return t?.(r)}}function ie(e,t){if(typeof e==`function`)return e(t);e!=null&&(e.current=t)}function ae(...e){return t=>{let n=!1,r=e.map(e=>{let r=ie(e,t);return!n&&typeof r==`function`&&(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];typeof n==`function`?n():ie(e[t],null)}}}}function I(...e){return T.useCallback(ae(...e),e)}var L=t(r(),1);function oe(e,t=[]){let n=[];function r(t,r){let i=T.createContext(r),a=n.length;n=[...n,r];let o=t=>{let{scope:n,children:r,...o}=t,s=n?.[e]?.[a]||i,c=T.useMemo(()=>o,Object.values(o));return(0,L.jsx)(s.Provider,{value:c,children:r})};o.displayName=t+`Provider`;function s(n,o){let s=o?.[e]?.[a]||i,c=T.useContext(s);if(c)return c;if(r!==void 0)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}return[o,s]}let i=()=>{let t=n.map(e=>T.createContext(e));return function(n){let r=n?.[e]||t;return T.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return i.scopeName=e,[r,se(i,...t)]}function se(...e){let t=e[0];if(e.length===1)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e),a=i[`__scope${r}`];return{...t,...a}},{});return T.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}function ce(e){let t=e+`CollectionProvider`,[n,r]=oe(t),[i,a]=n(t,{collectionRef:{current:null},itemMap:new Map}),o=e=>{let{scope:t,children:n}=e,r=T.useRef(null),a=T.useRef(new Map).current;return(0,L.jsx)(i,{scope:t,itemMap:a,collectionRef:r,children:n})};o.displayName=t;let s=e+`CollectionSlot`,l=c(s),u=T.forwardRef((e,t)=>{let{scope:n,children:r}=e,i=a(s,n),o=I(t,i.collectionRef);return(0,L.jsx)(l,{ref:o,children:r})});u.displayName=s;let d=e+`CollectionItemSlot`,f=`data-radix-collection-item`,p=c(d),m=T.forwardRef((e,t)=>{let{scope:n,children:r,...i}=e,o=T.useRef(null),s=I(t,o),c=a(d,n);return T.useEffect(()=>(c.itemMap.set(o,{ref:o,...i}),()=>void c.itemMap.delete(o))),(0,L.jsx)(p,{[f]:``,ref:s,children:r})});m.displayName=d;function h(t){let n=a(e+`CollectionConsumer`,t),r=T.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${f}]`)),r=Array.from(n.itemMap.values()),i=r.sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current));return i},[n.collectionRef,n.itemMap]);return r}return[{Provider:o,Slot:u,ItemSlot:m},h,r]}var le=t(a(),1),ue=[`a`,`button`,`div`,`form`,`h2`,`h3`,`img`,`input`,`label`,`li`,`nav`,`ol`,`p`,`select`,`span`,`svg`,`ul`],R=ue.reduce((e,t)=>{let n=c(`Primitive.${t}`),r=T.forwardRef((e,r)=>{let{asChild:i,...a}=e,o=i?n:t;return typeof window<`u`&&(window[Symbol.for(`radix-ui`)]=!0),(0,L.jsx)(o,{...a,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function de(e,t){e&&le.flushSync(()=>e.dispatchEvent(t))}function z(e){let t=T.useRef(e);return T.useEffect(()=>{t.current=e}),T.useMemo(()=>(...e)=>t.current?.(...e),[])}function fe(e,t=globalThis?.document){let n=z(e);T.useEffect(()=>{let e=e=>{e.key===`Escape`&&n(e)};return t.addEventListener(`keydown`,e,{capture:!0}),()=>t.removeEventListener(`keydown`,e,{capture:!0})},[n,t])}var pe=`DismissableLayer`,B=`dismissableLayer.update`,me=`dismissableLayer.pointerDownOutside`,he=`dismissableLayer.focusOutside`,ge,_e=T.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),ve=T.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:i,onFocusOutside:a,onInteractOutside:o,onDismiss:s,...c}=e,l=T.useContext(_e),[u,d]=T.useState(null),f=u?.ownerDocument??globalThis?.document,[,p]=T.useState({}),m=I(t,e=>d(e)),h=Array.from(l.layers),[g]=[...l.layersWithOutsidePointerEventsDisabled].slice(-1),_=h.indexOf(g),v=u?h.indexOf(u):-1,y=l.layersWithOutsidePointerEventsDisabled.size>0,b=v>=_,x=xe(e=>{let t=e.target,n=[...l.branches].some(e=>e.contains(t));!b||n||(i?.(e),o?.(e),e.defaultPrevented||s?.())},f),S=Se(e=>{let t=e.target,n=[...l.branches].some(e=>e.contains(t));n||(a?.(e),o?.(e),e.defaultPrevented||s?.())},f);return fe(e=>{let t=v===l.layers.size-1;t&&(r?.(e),!e.defaultPrevented&&s&&(e.preventDefault(),s()))},f),T.useEffect(()=>{if(u)return n&&(l.layersWithOutsidePointerEventsDisabled.size===0&&(ge=f.body.style.pointerEvents,f.body.style.pointerEvents=`none`),l.layersWithOutsidePointerEventsDisabled.add(u)),l.layers.add(u),Ce(),()=>{n&&l.layersWithOutsidePointerEventsDisabled.size===1&&(f.body.style.pointerEvents=ge)}},[u,f,n,l]),T.useEffect(()=>()=>{u&&(l.layers.delete(u),l.layersWithOutsidePointerEventsDisabled.delete(u),Ce())},[u,l]),T.useEffect(()=>{let e=()=>p({});return document.addEventListener(B,e),()=>document.removeEventListener(B,e)},[]),(0,L.jsx)(R.div,{...c,ref:m,style:{pointerEvents:y?b?`auto`:`none`:void 0,...e.style},onFocusCapture:F(e.onFocusCapture,S.onFocusCapture),onBlurCapture:F(e.onBlurCapture,S.onBlurCapture),onPointerDownCapture:F(e.onPointerDownCapture,x.onPointerDownCapture)})});ve.displayName=pe;var ye=`DismissableLayerBranch`,be=T.forwardRef((e,t)=>{let n=T.useContext(_e),r=T.useRef(null),i=I(t,r);return T.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,L.jsx)(R.div,{...e,ref:i})});be.displayName=ye;function xe(e,t=globalThis?.document){let n=z(e),r=T.useRef(!1),i=T.useRef(()=>{});return T.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){we(me,n,o,{discrete:!0})};var a=r;let o={originalEvent:e};e.pointerType===`touch`?(t.removeEventListener(`click`,i.current),i.current=r,t.addEventListener(`click`,i.current,{once:!0})):r()}else t.removeEventListener(`click`,i.current);r.current=!1},a=window.setTimeout(()=>{t.addEventListener(`pointerdown`,e)},0);return()=>{window.clearTimeout(a),t.removeEventListener(`pointerdown`,e),t.removeEventListener(`click`,i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Se(e,t=globalThis?.document){let n=z(e),r=T.useRef(!1);return T.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let t={originalEvent:e};we(he,n,t,{discrete:!1})}};return t.addEventListener(`focusin`,e),()=>t.removeEventListener(`focusin`,e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Ce(){let e=new CustomEvent(B);document.dispatchEvent(e)}function we(e,t,n,{discrete:r}){let i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),r?de(i,a):i.dispatchEvent(a)}var Te=ve,Ee=be,V=globalThis?.document?T.useLayoutEffect:()=>{},De=t(a(),1),Oe=`Portal`,ke=T.forwardRef((e,t)=>{let{container:n,...r}=e,[i,a]=T.useState(!1);V(()=>a(!0),[]);let o=n||i&&globalThis?.document?.body;return o?De.createPortal((0,L.jsx)(R.div,{...r,ref:t}),o):null});ke.displayName=Oe;function Ae(e,t){return T.useReducer((e,n)=>{let r=t[e][n];return r??e},e)}var je=e=>{let{present:t,children:n}=e,r=Me(t),i=typeof n==`function`?n({present:r.isPresent}):T.Children.only(n),a=I(r.ref,Ne(i)),o=typeof n==`function`;return o||r.isPresent?T.cloneElement(i,{ref:a}):null};je.displayName=`Presence`;function Me(e){let[t,n]=T.useState(),r=T.useRef(null),i=T.useRef(e),a=T.useRef(`none`),o=e?`mounted`:`unmounted`,[s,c]=Ae(o,{mounted:{UNMOUNT:`unmounted`,ANIMATION_OUT:`unmountSuspended`},unmountSuspended:{MOUNT:`mounted`,ANIMATION_END:`unmounted`},unmounted:{MOUNT:`mounted`}});return T.useEffect(()=>{let e=H(r.current);a.current=s===`mounted`?e:`none`},[s]),V(()=>{let t=r.current,n=i.current,o=n!==e;if(o){let r=a.current,o=H(t);if(e)c(`MOUNT`);else if(o===`none`||t?.display===`none`)c(`UNMOUNT`);else{let e=r!==o;c(n&&e?`ANIMATION_OUT`:`UNMOUNT`)}i.current=e}},[e,c]),V(()=>{if(t){let e,n=t.ownerDocument.defaultView??window,o=a=>{let o=H(r.current),s=o.includes(a.animationName);if(a.target===t&&s&&(c(`ANIMATION_END`),!i.current)){let r=t.style.animationFillMode;t.style.animationFillMode=`forwards`,e=n.setTimeout(()=>{t.style.animationFillMode===`forwards`&&(t.style.animationFillMode=r)})}},s=e=>{e.target===t&&(a.current=H(r.current))};return t.addEventListener(`animationstart`,s),t.addEventListener(`animationcancel`,o),t.addEventListener(`animationend`,o),()=>{n.clearTimeout(e),t.removeEventListener(`animationstart`,s),t.removeEventListener(`animationcancel`,o),t.removeEventListener(`animationend`,o)}}else c(`ANIMATION_END`)},[t,c]),{isPresent:[`mounted`,`unmountSuspended`].includes(s),ref:T.useCallback(e=>{r.current=e?getComputedStyle(e):null,n(e)},[])}}function H(e){return e?.animationName||`none`}function Ne(e){let t=Object.getOwnPropertyDescriptor(e.props,`ref`)?.get,n=t&&`isReactWarning`in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,`ref`)?.get,n=t&&`isReactWarning`in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Pe=T.useInsertionEffect||V;function Fe({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,o]=Ie({defaultProp:t,onChange:n}),s=e!==void 0,c=s?e:i;{let t=T.useRef(e!==void 0);T.useEffect(()=>{let e=t.current;if(e!==s){let t=e?`controlled`:`uncontrolled`,n=s?`controlled`:`uncontrolled`;console.warn(`${r} is changing from ${t} to ${n}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,r])}let l=T.useCallback(t=>{if(s){let n=Le(t)?t(e):t;n!==e&&o.current?.(n)}else a(t)},[s,e,a,o]);return[c,l]}function Ie({defaultProp:e,onChange:t}){let[n,r]=T.useState(e),i=T.useRef(n),a=T.useRef(t);return Pe(()=>{a.current=t},[t]),T.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}function Le(e){return typeof e==`function`}var Re=Symbol(`RADIX:SYNC_STATE`),ze=Object.freeze({position:`absolute`,border:0,width:1,height:1,padding:0,margin:-1,overflow:`hidden`,clip:`rect(0, 0, 0, 0)`,whiteSpace:`nowrap`,wordWrap:`normal`}),Be=`VisuallyHidden`,U=T.forwardRef((e,t)=>(0,L.jsx)(R.span,{...e,ref:t,style:{...ze,...e.style}}));U.displayName=Be;var Ve=t(a(),1),W=`ToastProvider`,[G,He,Ue]=ce(`Toast`),[We,Ge]=oe(`Toast`,[Ue]),[Ke,K]=We(W),qe=e=>{let{__scopeToast:t,label:n=`Notification`,duration:r=5e3,swipeDirection:i=`right`,swipeThreshold:a=50,children:o}=e,[s,c]=T.useState(null),[l,u]=T.useState(0),d=T.useRef(!1),f=T.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${W}\`. Expected non-empty \`string\`.`),(0,L.jsx)(G.Provider,{scope:t,children:(0,L.jsx)(Ke,{scope:t,label:n,duration:r,swipeDirection:i,swipeThreshold:a,toastCount:l,viewport:s,onViewportChange:c,onToastAdd:T.useCallback(()=>u(e=>e+1),[]),onToastRemove:T.useCallback(()=>u(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:f,children:o})})};qe.displayName=W;var Je=`ToastViewport`,Ye=[`F8`],q=`toast.viewportPause`,J=`toast.viewportResume`,Xe=T.forwardRef((e,t)=>{let{__scopeToast:n,hotkey:r=Ye,label:i=`Notifications ({hotkey})`,...a}=e,o=K(Je,n),s=He(n),c=T.useRef(null),l=T.useRef(null),u=T.useRef(null),d=T.useRef(null),f=I(t,d,o.onViewportChange),p=r.join(`+`).replace(/Key/g,``).replace(/Digit/g,``),m=o.toastCount>0;T.useEffect(()=>{let e=e=>{let t=r.length!==0&&r.every(t=>e[t]||e.code===t);t&&d.current?.focus()};return document.addEventListener(`keydown`,e),()=>document.removeEventListener(`keydown`,e)},[r]),T.useEffect(()=>{let e=c.current,t=d.current;if(m&&e&&t){let n=()=>{if(!o.isClosePausedRef.current){let e=new CustomEvent(q);t.dispatchEvent(e),o.isClosePausedRef.current=!0}},r=()=>{if(o.isClosePausedRef.current){let e=new CustomEvent(J);t.dispatchEvent(e),o.isClosePausedRef.current=!1}},i=t=>{let n=!e.contains(t.relatedTarget);n&&r()},a=()=>{let t=e.contains(document.activeElement);t||r()};return e.addEventListener(`focusin`,n),e.addEventListener(`focusout`,i),e.addEventListener(`pointermove`,n),e.addEventListener(`pointerleave`,a),window.addEventListener(`blur`,n),window.addEventListener(`focus`,r),()=>{e.removeEventListener(`focusin`,n),e.removeEventListener(`focusout`,i),e.removeEventListener(`pointermove`,n),e.removeEventListener(`pointerleave`,a),window.removeEventListener(`blur`,n),window.removeEventListener(`focus`,r)}}},[m,o.isClosePausedRef]);let h=T.useCallback(({tabbingDirection:e})=>{let t=s(),n=t.map(t=>{let n=t.ref.current,r=[n,...yt(n)];return e===`forwards`?r:r.reverse()});return(e===`forwards`?n.reverse():n).flat()},[s]);return T.useEffect(()=>{let e=d.current;if(e){let t=t=>{let n=t.altKey||t.ctrlKey||t.metaKey,r=t.key===`Tab`&&!n;if(r){let n=document.activeElement,r=t.shiftKey,i=t.target===e;if(i&&r){l.current?.focus();return}let a=r?`backwards`:`forwards`,o=h({tabbingDirection:a}),s=o.findIndex(e=>e===n);$(o.slice(s+1))?t.preventDefault():r?l.current?.focus():u.current?.focus()}};return e.addEventListener(`keydown`,t),()=>e.removeEventListener(`keydown`,t)}},[s,h]),(0,L.jsxs)(Ee,{ref:c,role:`region`,"aria-label":i.replace(`{hotkey}`,p),tabIndex:-1,style:{pointerEvents:m?void 0:`none`},children:[m&&(0,L.jsx)(Y,{ref:l,onFocusFromOutsideViewport:()=>{let e=h({tabbingDirection:`forwards`});$(e)}}),(0,L.jsx)(G.Slot,{scope:n,children:(0,L.jsx)(R.ol,{tabIndex:-1,...a,ref:f})}),m&&(0,L.jsx)(Y,{ref:u,onFocusFromOutsideViewport:()=>{let e=h({tabbingDirection:`backwards`});$(e)}})]})});Xe.displayName=Je;var Ze=`ToastFocusProxy`,Y=T.forwardRef((e,t)=>{let{__scopeToast:n,onFocusFromOutsideViewport:r,...i}=e,a=K(Ze,n);return(0,L.jsx)(U,{"aria-hidden":!0,tabIndex:0,...i,ref:t,style:{position:`fixed`},onFocus:e=>{let t=e.relatedTarget,n=!a.viewport?.contains(t);n&&r()}})});Y.displayName=Ze;var X=`Toast`,Qe=`toast.swipeStart`,$e=`toast.swipeMove`,et=`toast.swipeCancel`,tt=`toast.swipeEnd`,nt=T.forwardRef((e,t)=>{let{forceMount:n,open:r,defaultOpen:i,onOpenChange:a,...o}=e,[s,c]=Fe({prop:r,defaultProp:i??!0,onChange:a,caller:X});return(0,L.jsx)(je,{present:n||s,children:(0,L.jsx)(at,{open:s,...o,ref:t,onClose:()=>c(!1),onPause:z(e.onPause),onResume:z(e.onResume),onSwipeStart:F(e.onSwipeStart,e=>{e.currentTarget.setAttribute(`data-swipe`,`start`)}),onSwipeMove:F(e.onSwipeMove,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute(`data-swipe`,`move`),e.currentTarget.style.setProperty(`--radix-toast-swipe-move-x`,`${t}px`),e.currentTarget.style.setProperty(`--radix-toast-swipe-move-y`,`${n}px`)}),onSwipeCancel:F(e.onSwipeCancel,e=>{e.currentTarget.setAttribute(`data-swipe`,`cancel`),e.currentTarget.style.removeProperty(`--radix-toast-swipe-move-x`),e.currentTarget.style.removeProperty(`--radix-toast-swipe-move-y`),e.currentTarget.style.removeProperty(`--radix-toast-swipe-end-x`),e.currentTarget.style.removeProperty(`--radix-toast-swipe-end-y`)}),onSwipeEnd:F(e.onSwipeEnd,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute(`data-swipe`,`end`),e.currentTarget.style.removeProperty(`--radix-toast-swipe-move-x`),e.currentTarget.style.removeProperty(`--radix-toast-swipe-move-y`),e.currentTarget.style.setProperty(`--radix-toast-swipe-end-x`,`${t}px`),e.currentTarget.style.setProperty(`--radix-toast-swipe-end-y`,`${n}px`),c(!1)})})})});nt.displayName=X;var[rt,it]=We(X,{onClose(){}}),at=T.forwardRef((e,t)=>{let{__scopeToast:n,type:r=`foreground`,duration:i,open:a,onClose:o,onEscapeKeyDown:s,onPause:c,onResume:l,onSwipeStart:u,onSwipeMove:d,onSwipeCancel:f,onSwipeEnd:p,...m}=e,h=K(X,n),[g,_]=T.useState(null),v=I(t,e=>_(e)),y=T.useRef(null),b=T.useRef(null),x=i||h.duration,S=T.useRef(0),ee=T.useRef(x),C=T.useRef(0),{onToastAdd:w,onToastRemove:E}=h,D=z(()=>{let e=g?.contains(document.activeElement);e&&h.viewport?.focus(),o()}),O=T.useCallback(e=>{!e||e===1/0||(window.clearTimeout(C.current),S.current=new Date().getTime(),C.current=window.setTimeout(D,e))},[D]);T.useEffect(()=>{let e=h.viewport;if(e){let t=()=>{O(ee.current),l?.()},n=()=>{let e=new Date().getTime()-S.current;ee.current-=e,window.clearTimeout(C.current),c?.()};return e.addEventListener(q,n),e.addEventListener(J,t),()=>{e.removeEventListener(q,n),e.removeEventListener(J,t)}}},[h.viewport,x,c,l,O]),T.useEffect(()=>{a&&!h.isClosePausedRef.current&&O(x)},[a,x,h.isClosePausedRef,O]),T.useEffect(()=>(w(),()=>E()),[w,E]);let k=T.useMemo(()=>g?ht(g):null,[g]);return h.viewport?(0,L.jsxs)(L.Fragment,{children:[k&&(0,L.jsx)(ot,{__scopeToast:n,role:`status`,"aria-live":r===`foreground`?`assertive`:`polite`,"aria-atomic":!0,children:k}),(0,L.jsx)(rt,{scope:n,onClose:D,children:Ve.createPortal((0,L.jsx)(G.ItemSlot,{scope:n,children:(0,L.jsx)(Te,{asChild:!0,onEscapeKeyDown:F(s,()=>{h.isFocusedToastEscapeKeyDownRef.current||D(),h.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,L.jsx)(R.li,{role:`status`,"aria-live":`off`,"aria-atomic":!0,tabIndex:0,"data-state":a?`open`:`closed`,"data-swipe-direction":h.swipeDirection,...m,ref:v,style:{userSelect:`none`,touchAction:`none`,...e.style},onKeyDown:F(e.onKeyDown,e=>{e.key===`Escape`&&(s?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(h.isFocusedToastEscapeKeyDownRef.current=!0,D()))}),onPointerDown:F(e.onPointerDown,e=>{e.button===0&&(y.current={x:e.clientX,y:e.clientY})}),onPointerMove:F(e.onPointerMove,e=>{if(!y.current)return;let t=e.clientX-y.current.x,n=e.clientY-y.current.y,r=!!b.current,i=[`left`,`right`].includes(h.swipeDirection),a=[`left`,`up`].includes(h.swipeDirection)?Math.min:Math.max,o=i?a(0,t):0,s=i?0:a(0,n),c=e.pointerType===`touch`?10:2,l={x:o,y:s},f={originalEvent:e,delta:l};r?(b.current=l,Q($e,d,f,{discrete:!1})):gt(l,h.swipeDirection,c)?(b.current=l,Q(Qe,u,f,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>c||Math.abs(n)>c)&&(y.current=null)}),onPointerUp:F(e.onPointerUp,e=>{let t=b.current,n=e.target;if(n.hasPointerCapture(e.pointerId)&&n.releasePointerCapture(e.pointerId),b.current=null,y.current=null,t){let n=e.currentTarget,r={originalEvent:e,delta:t};gt(t,h.swipeDirection,h.swipeThreshold)?Q(tt,p,r,{discrete:!0}):Q(et,f,r,{discrete:!0}),n.addEventListener(`click`,e=>e.preventDefault(),{once:!0})}})})})}),h.viewport)})]}):null}),ot=e=>{let{__scopeToast:t,children:n,...r}=e,i=K(X,t),[a,o]=T.useState(!1),[s,c]=T.useState(!1);return _t(()=>o(!0)),T.useEffect(()=>{let e=window.setTimeout(()=>c(!0),1e3);return()=>window.clearTimeout(e)},[]),s?null:(0,L.jsx)(ke,{asChild:!0,children:(0,L.jsx)(U,{...r,children:a&&(0,L.jsxs)(L.Fragment,{children:[i.label,` `,n]})})})},st=`ToastTitle`,ct=T.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,L.jsx)(R.div,{...r,ref:t})});ct.displayName=st;var lt=`ToastDescription`,ut=T.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,L.jsx)(R.div,{...r,ref:t})});ut.displayName=lt;var dt=`ToastAction`,ft=T.forwardRef((e,t)=>{let{altText:n,...r}=e;return n.trim()?(0,L.jsx)(mt,{altText:n,asChild:!0,children:(0,L.jsx)(Z,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${dt}\`. Expected non-empty \`string\`.`),null)});ft.displayName=dt;var pt=`ToastClose`,Z=T.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e,i=it(pt,n);return(0,L.jsx)(mt,{asChild:!0,children:(0,L.jsx)(R.button,{type:`button`,...r,ref:t,onClick:F(e.onClick,i.onClose)})})});Z.displayName=pt;var mt=T.forwardRef((e,t)=>{let{__scopeToast:n,altText:r,...i}=e;return(0,L.jsx)(R.div,{"data-radix-toast-announce-exclude":``,"data-radix-toast-announce-alt":r||void 0,...i,ref:t})});function ht(e){let t=[],n=Array.from(e.childNodes);return n.forEach(e=>{if(e.nodeType===e.TEXT_NODE&&e.textContent&&t.push(e.textContent),vt(e)){let n=e.ariaHidden||e.hidden||e.style.display===`none`,r=e.dataset.radixToastAnnounceExclude===``;if(!n)if(r){let n=e.dataset.radixToastAnnounceAlt;n&&t.push(n)}else t.push(...ht(e))}}),t}function Q(e,t,n,{discrete:r}){let i=n.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),r?de(i,a):i.dispatchEvent(a)}var gt=(e,t,n=0)=>{let r=Math.abs(e.x),i=Math.abs(e.y),a=r>i;return t===`left`||t===`right`?a&&r>n:!a&&i>n};function _t(e=()=>{}){let t=z(e);V(()=>{let e=0,n=0;return e=window.requestAnimationFrame(()=>n=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(n)}},[t])}function vt(e){return e.nodeType===e.ELEMENT_NODE}function yt(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t=e.tagName===`INPUT`&&e.type===`hidden`;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function $(e){let t=document.activeElement;return e.some(e=>e===t?!0:(e.focus(),document.activeElement!==t))}var bt=qe,xt=Xe,St=nt,Ct=ct,wt=ut,Tt=ft,Et=Z;const Dt=bt,Ot=T.forwardRef(({className:e,...t},r)=>(0,L.jsx)(xt,{ref:r,className:n(`fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]`,e),...t}));Ot.displayName=xt.displayName;const kt=l(`group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full`,{variants:{variant:{default:`border bg-background text-foreground`,destructive:`destructive group border-destructive bg-destructive text-destructive-foreground`}},defaultVariants:{variant:`default`}}),At=T.forwardRef(({className:e,variant:t,...r},i)=>(0,L.jsx)(St,{ref:i,className:n(kt({variant:t}),e),...r}));At.displayName=St.displayName;const jt=T.forwardRef(({className:e,...t},r)=>(0,L.jsx)(Tt,{ref:r,className:n(`inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive`,e),...t}));jt.displayName=Tt.displayName;const Mt=T.forwardRef(({className:e,...t},r)=>(0,L.jsx)(Et,{ref:r,className:n(`absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600`,e),"data-toast-close":``,...t,children:(0,L.jsx)(y,{className:`h-4 w-4`})}));Mt.displayName=Et.displayName;const Nt=T.forwardRef(({className:e,...t},r)=>(0,L.jsx)(Ct,{ref:r,className:n(`text-sm font-semibold`,e),...t}));Nt.displayName=Ct.displayName;const Pt=T.forwardRef(({className:e,...t},r)=>(0,L.jsx)(wt,{ref:r,className:n(`text-sm opacity-90`,e),...t}));Pt.displayName=wt.displayName;function Ft(){let{toasts:e}=re();return(0,L.jsxs)(Dt,{children:[e.map(({id:e,title:t,description:n,action:r,...i})=>(0,L.jsxs)(At,{...i,children:[(0,L.jsxs)(`div`,{className:`grid gap-1`,children:[t&&(0,L.jsx)(Nt,{children:t}),n&&(0,L.jsx)(Pt,{children:n})]}),r,(0,L.jsx)(Mt,{})]},e)),(0,L.jsx)(Ot,{})]})}const It=({result:e})=>{let t=e=>e>=80?`bg-gradient-primary`:e>=50?`bg-warning-amber`:`bg-error-crimson`;return(0,L.jsx)(`div`,{className:`bg-white rounded-lg shadow-md overflow-hidden animate-fade-in`,children:(0,L.jsxs)(`div`,{className:`p-6`,children:[(0,L.jsxs)(`div`,{className:`flex justify-between items-center mb-4`,children:[(0,L.jsx)(`h3`,{className:`text-xl font-semibold text-graphite-90`,children:`Bio Analysis`}),(0,L.jsx)(`div`,{className:`px-4 py-2 text-lg font-bold text-white rounded-full ${t(e.overallScore)}`,children:e.overallScore})]}),(0,L.jsx)(`div`,{className:`mb-6 space-y-2`,children:e.steps.map(e=>(0,L.jsxs)(`div`,{className:`flex justify-between items-center`,children:[(0,L.jsx)(`span`,{className:`text-graphite-60 font-medium`,children:e.stepName}),(0,L.jsxs)(`div`,{className:`flex items-center gap-2`,children:[(0,L.jsx)(`span`,{className:`font-semibold ${e.score>=70?`text-success-green`:e.score>=50?`text-warning-amber`:`text-error-crimson`}`,children:e.score}),(0,L.jsx)(`div`,{className:`w-16 h-2 bg-gray-200 rounded-full`,children:(0,L.jsx)(`div`,{className:`h-full rounded-full ${e.score>=70?`bg-success-green`:e.score>=50?`bg-warning-amber`:`bg-error-crimson`}`,style:{width:`${e.score}%`}})})]})]},e.stepId))}),(0,L.jsxs)(`div`,{className:`mb-4`,children:[(0,L.jsx)(`h4`,{className:`font-semibold text-graphite-90 mb-2`,children:`Key Recommendations`}),(0,L.jsx)(`ul`,{className:`space-y-2`,children:e.recommendations.slice(0,3).map((e,t)=>(0,L.jsxs)(`li`,{className:`flex items-start text-sm text-graphite-60`,children:[(0,L.jsx)(v,{className:`h-4 w-4 mr-2 mt-0.5 text-flame-red flex-shrink-0`}),(0,L.jsx)(`span`,{children:e})]},t))})]}),e.error&&(0,L.jsx)(`div`,{className:`mt-3 p-3 bg-error-crimson/10 rounded text-sm text-error-crimson`,children:e.error})]})})},Lt=({progress:e})=>(0,L.jsx)(`div`,{className:`bg-white rounded-lg shadow-md overflow-hidden`,children:(0,L.jsxs)(`div`,{className:`p-6`,children:[(0,L.jsxs)(`div`,{className:`mb-4`,children:[(0,L.jsx)(`div`,{className:`h-6 w-3/4 rounded bg-gray-200 animate-pulse mb-2`}),(0,L.jsx)(`div`,{className:`h-4 w-1/2 rounded bg-gray-200 animate-pulse`})]}),e&&(0,L.jsxs)(`div`,{className:`space-y-3 mb-6`,children:[(0,L.jsxs)(`div`,{className:`flex justify-between text-sm`,children:[(0,L.jsxs)(`span`,{className:`text-graphite-60`,children:[`Step `,e.currentStep,`/5`]}),(0,L.jsxs)(`span`,{className:`text-flame-red font-medium`,children:[Math.round(e.progress),`%`]})]}),(0,L.jsx)(`div`,{className:`text-sm text-graphite-90 font-medium`,children:e.stepName}),(0,L.jsx)(b,{value:e.progress,className:`h-2`})]}),(0,L.jsx)(`div`,{className:`space-y-2`,children:o.map(t=>(0,L.jsxs)(`div`,{className:`flex items-center text-sm ${e&&e.currentStep>t.id?`text-success-green`:e&&e.currentStep===t.id?`text-flame-red`:`text-gray-400`}`,children:[(0,L.jsx)(`div`,{className:`w-2 h-2 rounded-full mr-3 ${e&&e.currentStep>t.id?`bg-success-green`:e&&e.currentStep===t.id?`bg-flame-red animate-pulse`:`bg-gray-300`}`}),(0,L.jsx)(`span`,{children:t.name})]},t.id))})]})}),Rt=({original:e,rewritten:t})=>{let n=e.split(/(\s+)/);return(0,L.jsx)(`p`,{className:`text-body-md whitespace-pre-wrap`,children:t.split(/(\s+)/).map((e,t)=>n.includes(e)?(0,L.jsx)(`span`,{children:e},t):(0,L.jsx)(`span`,{className:`bg-success-green/20 text-success-green rounded px-1`,children:e},t))})};function zt(){let[t,n]=(0,T.useState)(``),[r,i]=(0,T.useState)(1),[a,o]=(0,T.useState)(null),[c,l]=(0,T.useState)(null),[y,b]=(0,T.useState)(`idle`),[E,D]=(0,T.useState)(null),{toast:O}=re(),k=[`Witty`,`Sincere`,`Adventurous`],A={0:`witty`,1:`sincere`,2:`adventurous`},j=e=>{navigator.clipboard.writeText(e),O({title:`Copied to clipboard!`,description:`The bio is now ready to paste.`})},te=(0,T.useCallback)(async()=>{if(t.length<20){l(`Bio must be at least 20 characters.`);return}if(t.length>500){l(`Bio must be 500 characters or less.`);return}b(`processing`),l(null),D(null);try{await w.analyzeBio(t,A[r],{onProgress:e=>{D(e)},onComplete:e=>{o(e),b(`done`)},onError:e=>{l(e),b(`idle`)}})}catch(e){console.error(`Bio analysis failed:`,e),l(`Analysis failed. Please try again.`),b(`idle`)}},[t,r]),M=e=>{e.preventDefault(),te()},N=()=>{b(`idle`),o(null),D(null),l(null)};return(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(Ft,{}),(0,L.jsxs)(`div`,{className:`min-h-screen bg-gray-50`,children:[(0,L.jsxs)(`header`,{className:`bg-cloud-white shadow-sm sticky top-0 z-10`,children:[(0,L.jsx)(`div`,{className:`container mx-auto px-4 md:px-6 h-16 flex items-center justify-between`,children:(0,L.jsxs)(e,{to:`/image-analyzer`,className:`flex items-center gap-2 text-graphite-60 hover:text-graphite-90`,children:[(0,L.jsx)(d,{className:`h-5 w-5`}),(0,L.jsx)(`span`,{className:`font-semibold`,children:`Back to Images`})]})}),y===`processing`&&E&&(0,L.jsx)(`div`,{className:`h-1 bg-gray-200`,children:(0,L.jsx)(`div`,{className:`h-full bg-gradient-primary transition-all duration-300`,style:{width:`${E.progress}%`}})})]}),(0,L.jsx)(`main`,{className:`container mx-auto px-4 md:px-6 py-8`,children:(0,L.jsxs)(`div`,{className:`max-w-4xl mx-auto`,children:[(0,L.jsxs)(`div`,{className:`text-center`,children:[(0,L.jsx)(`h1`,{className:`text-h2-mobile md:text-h2`,children:`Bio Analyzer`}),(0,L.jsx)(`p`,{className:`text-body-lg text-graphite-60 mt-2`,children:`Get AI-powered analysis and optimization for your dating bio.`}),(0,L.jsxs)(`div`,{className:`flex justify-center gap-2 mt-4`,children:[(0,L.jsxs)(u,{variant:`secondary`,className:`flex items-center gap-1`,children:[(0,L.jsx)(v,{className:`h-3 w-3`}),`Multi-step AI analysis`]}),(0,L.jsxs)(u,{variant:`outline`,className:`flex items-center gap-1`,children:[(0,L.jsx)(g,{className:`h-3 w-3`}),`Personalized improvements`]})]})]}),y!==`done`&&(0,L.jsxs)(`div`,{className:`mt-8 max-w-2xl mx-auto`,children:[(0,L.jsxs)(`form`,{onSubmit:M,className:`space-y-6`,children:[(0,L.jsxs)(`div`,{children:[(0,L.jsx)(p,{placeholder:`Paste your current bio here...`,value:t,onChange:e=>n(e.target.value),className:`min-h-[160px] md:min-h-[200px] bg-white`,maxLength:500,required:!0,disabled:y===`processing`}),(0,L.jsxs)(`p`,{className:`text-right text-caption text-graphite-60 mt-1`,children:[t.length,` / 500`]})]}),(0,L.jsxs)(`div`,{children:[(0,L.jsx)(`label`,{className:`font-semibold text-body-md`,children:`Select Improvement Tone`}),(0,L.jsxs)(`div`,{className:`mt-4`,children:[(0,L.jsx)(_,{value:[r],onValueChange:e=>i(e[0]),max:2,step:1,className:`w-full`,disabled:y===`processing`}),(0,L.jsx)(`div`,{className:`flex justify-between text-caption text-graphite-60 mt-2`,children:k.map(e=>(0,L.jsx)(`span`,{children:e},e))})]})]}),(0,L.jsx)(s,{type:`submit`,size:`lg`,disabled:t.length<20||y===`processing`,className:`w-full`,children:y===`processing`?(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(h,{className:`mr-2 h-5 w-5 animate-spin`}),`Analyzing with AI...`]}):`Analyze Bio with AI`})]}),c&&(0,L.jsx)(`p`,{className:`mt-4 text-center text-error-crimson`,children:c})]}),y===`processing`&&(0,L.jsx)(`div`,{className:`mt-12 max-w-2xl mx-auto`,children:(0,L.jsx)(Lt,{progress:E||void 0})}),y===`done`&&a&&(0,L.jsxs)(`div`,{className:`mt-12`,children:[(0,L.jsxs)(`div`,{className:`text-center mb-6`,children:[(0,L.jsx)(`h2`,{className:`text-xl font-semibold`,children:`Analysis Complete`}),(0,L.jsx)(`p`,{className:`text-graphite-60 mt-2`,children:`Your bio has been analyzed using our 5-step AI assessment`})]}),(0,L.jsxs)(`div`,{className:`grid lg:grid-cols-2 gap-8`,children:[(0,L.jsx)(It,{result:a}),(0,L.jsx)(`div`,{className:`space-y-6`,children:(0,L.jsxs)(x,{defaultValue:`original`,className:`w-full`,children:[(0,L.jsxs)(ee,{className:`grid w-full grid-cols-2`,children:[(0,L.jsx)(C,{value:`original`,children:`Original Bio`}),(0,L.jsx)(C,{value:`improved`,disabled:!a.improvedBio,children:`AI Improved`})]}),(0,L.jsxs)(S,{value:`original`,className:`mt-4 p-6 bg-white rounded-lg border`,children:[(0,L.jsx)(`div`,{className:`mb-4`,children:(0,L.jsx)(`h4`,{className:`font-semibold text-graphite-90 mb-2`,children:`Your Original Bio`})}),(0,L.jsx)(`p`,{className:`text-body-md whitespace-pre-wrap text-graphite-70`,children:a.originalBio}),(0,L.jsx)(`div`,{className:`mt-4`,children:(0,L.jsxs)(s,{onClick:()=>j(a.originalBio),size:`sm`,variant:`secondary`,children:[(0,L.jsx)(f,{className:`mr-2 h-4 w-4`}),` Copy Original`]})})]}),a.improvedBio&&(0,L.jsxs)(S,{value:`improved`,className:`mt-4 p-6 bg-white rounded-lg border`,children:[(0,L.jsxs)(`div`,{className:`mb-4`,children:[(0,L.jsx)(`h4`,{className:`font-semibold text-graphite-90 mb-2`,children:`AI-Improved Bio`}),(0,L.jsxs)(u,{variant:`secondary`,children:[k[r],` tone`]})]}),(0,L.jsx)(Rt,{original:a.originalBio,rewritten:a.improvedBio}),(0,L.jsx)(`div`,{className:`mt-4`,children:(0,L.jsxs)(s,{onClick:()=>j(a.improvedBio),size:`sm`,children:[(0,L.jsx)(f,{className:`mr-2 h-4 w-4`}),` Copy Improved`]})})]})]})})]}),(0,L.jsxs)(`div`,{className:`mt-8 text-center space-y-4`,children:[(0,L.jsxs)(`div`,{className:`flex justify-center space-x-4`,children:[(0,L.jsx)(s,{onClick:N,variant:`secondary`,children:`Analyze Another Bio`}),(0,L.jsx)(s,{asChild:!0,size:`lg`,children:(0,L.jsxs)(e,{to:`/image-analyzer`,children:[`Analyze Photos Next `,(0,L.jsx)(v,{className:`ml-2 h-5 w-5`})]})})]}),(0,L.jsxs)(`div`,{className:`pt-4 border-t`,children:[(0,L.jsx)(`p`,{className:`text-sm text-gray-600 mb-3`,children:`Want professional-grade analysis?`}),(0,L.jsx)(s,{asChild:!0,variant:`outline`,className:`border-purple-300 text-purple-700 hover:bg-purple-50`,children:(0,L.jsxs)(e,{to:`/bio-analyzer-pro`,children:[`Try Advanced Analysis `,(0,L.jsx)(m,{className:`ml-2 h-4 w-4`})]})})]})]})]})]})})]})]})}const Bt=zt;export{Bt as component};