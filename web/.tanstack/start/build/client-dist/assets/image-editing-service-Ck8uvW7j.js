import{InvalidPromptError as e,InvalidResponseDataError as t,TooManyEmbeddingValuesForCallError as n,UnsupportedFunctionalityError as r,anyType as i,arrayType as a,booleanType as o,combineHeaders as s,convertBase64ToUint8Array as c,convertUint8ArrayToBase64 as l,createBinaryResponseHandler as u,createEventSourceResponseHandler as d,createJsonErrorResponseHandler as f,createJsonResponseHandler as p,discriminatedUnionType as m,enumType as h,generateId as g,generateText as _,isParsableJson as v,literalType as y,loadApiKey as b,numberType as x,objectType as S,parseProviderOptions as C,postFormDataToApi as w,postJsonToApi as T,recordType as E,stringType as D,unionType as O,withoutTrailingSlash as ee}from"./dist-CgCb95MR.js";import{EDITING_ANALYSIS_STEPS as k}from"./analysis-BhwX0Tn_.js";function te({prompt:e,useLegacyFunctionCalling:t=!1,systemMessageMode:n=`system`}){let i=[],a=[];for(let{role:o,content:s}of e)switch(o){case`system`:switch(n){case`system`:i.push({role:`system`,content:s});break;case`developer`:i.push({role:`developer`,content:s});break;case`remove`:a.push({type:`other`,message:`system messages are removed for this model`});break;default:{let e=n;throw Error(`Unsupported system message mode: ${e}`)}}break;case`user`:if(s.length===1&&s[0].type===`text`){i.push({role:`user`,content:s[0].text});break}i.push({role:`user`,content:s.map((e,t)=>{var n,i,a,o;switch(e.type){case`text`:return{type:`text`,text:e.text};case`image`:return{type:`image_url`,image_url:{url:e.image instanceof URL?e.image.toString():`data:${(n=e.mimeType)??`image/jpeg`};base64,${l(e.image)}`,detail:(a=(i=e.providerMetadata)?.openai)?.imageDetail}};case`file`:if(e.data instanceof URL)throw new r({functionality:`'File content parts with URL data' functionality not supported.`});switch(e.mimeType){case`audio/wav`:return{type:`input_audio`,input_audio:{data:e.data,format:`wav`}};case`audio/mp3`:case`audio/mpeg`:return{type:`input_audio`,input_audio:{data:e.data,format:`mp3`}};case`application/pdf`:return{type:`file`,file:{filename:(o=e.filename)??`part-${t}.pdf`,file_data:`data:application/pdf;base64,${e.data}`}};default:throw new r({functionality:`File content part type ${e.mimeType} in user messages`})}}})});break;case`assistant`:{let e=``,n=[];for(let t of s)switch(t.type){case`text`:e+=t.text;break;case`tool-call`:n.push({id:t.toolCallId,type:`function`,function:{name:t.toolName,arguments:JSON.stringify(t.args)}});break}if(t){if(n.length>1)throw new r({functionality:`useLegacyFunctionCalling with multiple tool calls in one message`});i.push({role:`assistant`,content:e,function_call:n.length>0?n[0].function:void 0})}else i.push({role:`assistant`,content:e,tool_calls:n.length>0?n:void 0});break}case`tool`:for(let e of s)t?i.push({role:`function`,name:e.toolName,content:JSON.stringify(e.result)}):i.push({role:`tool`,tool_call_id:e.toolCallId,content:JSON.stringify(e.result)});break;default:{let e=o;throw Error(`Unsupported role: ${e}`)}}return{messages:i,warnings:a}}function A(e){var t,n;return(n=(t=e?.content)?.map(({token:e,logprob:t,top_logprobs:n})=>({token:e,logprob:t,topLogprobs:n?n.map(({token:e,logprob:t})=>({token:e,logprob:t})):[]})))??void 0}function j(e){switch(e){case`stop`:return`stop`;case`length`:return`length`;case`content_filter`:return`content-filter`;case`function_call`:case`tool_calls`:return`tool-calls`;default:return`unknown`}}var M=S({error:S({message:D(),type:D().nullish(),param:i().nullish(),code:O([D(),x()]).nullish()})}),N=f({errorSchema:M,errorToMessage:e=>e.error.message});function P({id:e,model:t,created:n}){return{id:e??void 0,modelId:t??void 0,timestamp:n==null?void 0:new Date(n*1e3)}}function F({mode:e,useLegacyFunctionCalling:t=!1,structuredOutputs:n}){var i;let a=(i=e.tools)?.length?e.tools:void 0,o=[];if(a==null)return{tools:void 0,tool_choice:void 0,toolWarnings:o};let s=e.toolChoice;if(t){let e=[];for(let t of a)t.type===`provider-defined`?o.push({type:`unsupported-tool`,tool:t}):e.push({name:t.name,description:t.description,parameters:t.parameters});if(s==null)return{functions:e,function_call:void 0,toolWarnings:o};let t=s.type;switch(t){case`auto`:case`none`:case void 0:return{functions:e,function_call:void 0,toolWarnings:o};case`required`:throw new r({functionality:`useLegacyFunctionCalling and toolChoice: required`});default:return{functions:e,function_call:{name:s.toolName},toolWarnings:o}}}let c=[];for(let e of a)e.type===`provider-defined`?o.push({type:`unsupported-tool`,tool:e}):c.push({type:`function`,function:{name:e.name,description:e.description,parameters:e.parameters,strict:n?!0:void 0}});if(s==null)return{tools:c,tool_choice:void 0,toolWarnings:o};let l=s.type;switch(l){case`auto`:case`none`:case`required`:return{tools:c,tool_choice:l,toolWarnings:o};case`tool`:return{tools:c,tool_choice:{type:`function`,function:{name:s.toolName}},toolWarnings:o};default:{let e=l;throw new r({functionality:`Unsupported tool choice type: ${e}`})}}}var ne=class{constructor(e,t,n){this.specificationVersion=`v1`,this.modelId=e,this.settings=t,this.config=n}get supportsStructuredOutputs(){var e;return(e=this.settings.structuredOutputs)??L(this.modelId)}get defaultObjectGenerationMode(){return ae(this.modelId)?`tool`:this.supportsStructuredOutputs?`json`:`tool`}get provider(){return this.config.provider}get supportsImageUrls(){return!this.settings.downloadImages}getArgs({mode:e,prompt:t,maxTokens:n,temperature:i,topP:a,topK:o,frequencyPenalty:s,presencePenalty:c,stopSequences:l,responseFormat:u,seed:d,providerMetadata:f}){var p,m,h,g,_,v,y,b;let x=e.type,S=[];o!=null&&S.push({type:`unsupported-setting`,setting:`topK`}),u?.type===`json`&&u.schema!=null&&!this.supportsStructuredOutputs&&S.push({type:`unsupported-setting`,setting:`responseFormat`,details:`JSON response format schema is only supported with structuredOutputs`});let C=this.settings.useLegacyFunctionCalling;if(C&&this.settings.parallelToolCalls===!0)throw new r({functionality:`useLegacyFunctionCalling with parallelToolCalls`});if(C&&this.supportsStructuredOutputs)throw new r({functionality:`structuredOutputs with useLegacyFunctionCalling`});let{messages:w,warnings:T}=te({prompt:t,useLegacyFunctionCalling:C,systemMessageMode:oe(this.modelId)});S.push(...T);let E={model:this.modelId,logit_bias:this.settings.logitBias,logprobs:this.settings.logprobs===!0||typeof this.settings.logprobs==`number`?!0:void 0,top_logprobs:typeof this.settings.logprobs==`number`?this.settings.logprobs:typeof this.settings.logprobs==`boolean`&&this.settings.logprobs?0:void 0,user:this.settings.user,parallel_tool_calls:this.settings.parallelToolCalls,max_tokens:n,temperature:i,top_p:a,frequency_penalty:s,presence_penalty:c,response_format:u?.type===`json`?this.supportsStructuredOutputs&&u.schema!=null?{type:`json_schema`,json_schema:{schema:u.schema,strict:!0,name:(p=u.name)??`response`,description:u.description}}:{type:`json_object`}:void 0,stop:l,seed:d,max_completion_tokens:(m=f?.openai)?.maxCompletionTokens,store:(h=f?.openai)?.store,metadata:(g=f?.openai)?.metadata,prediction:(_=f?.openai)?.prediction,reasoning_effort:(y=(v=f?.openai)?.reasoningEffort)??this.settings.reasoningEffort,messages:w};switch(L(this.modelId)?(E.temperature!=null&&(E.temperature=void 0,S.push({type:`unsupported-setting`,setting:`temperature`,details:`temperature is not supported for reasoning models`})),E.top_p!=null&&(E.top_p=void 0,S.push({type:`unsupported-setting`,setting:`topP`,details:`topP is not supported for reasoning models`})),E.frequency_penalty!=null&&(E.frequency_penalty=void 0,S.push({type:`unsupported-setting`,setting:`frequencyPenalty`,details:`frequencyPenalty is not supported for reasoning models`})),E.presence_penalty!=null&&(E.presence_penalty=void 0,S.push({type:`unsupported-setting`,setting:`presencePenalty`,details:`presencePenalty is not supported for reasoning models`})),E.logit_bias!=null&&(E.logit_bias=void 0,S.push({type:`other`,message:`logitBias is not supported for reasoning models`})),E.logprobs!=null&&(E.logprobs=void 0,S.push({type:`other`,message:`logprobs is not supported for reasoning models`})),E.top_logprobs!=null&&(E.top_logprobs=void 0,S.push({type:`other`,message:`topLogprobs is not supported for reasoning models`})),E.max_tokens!=null&&(E.max_completion_tokens??=E.max_tokens,E.max_tokens=void 0)):(this.modelId.startsWith(`gpt-4o-search-preview`)||this.modelId.startsWith(`gpt-4o-mini-search-preview`))&&E.temperature!=null&&(E.temperature=void 0,S.push({type:`unsupported-setting`,setting:`temperature`,details:`temperature is not supported for the search preview models and has been removed.`})),x){case`regular`:{let{tools:t,tool_choice:n,functions:r,function_call:i,toolWarnings:a}=F({mode:e,useLegacyFunctionCalling:C,structuredOutputs:this.supportsStructuredOutputs});return{args:{...E,tools:t,tool_choice:n,functions:r,function_call:i},warnings:[...S,...a]}}case`object-json`:return{args:{...E,response_format:this.supportsStructuredOutputs&&e.schema!=null?{type:`json_schema`,json_schema:{schema:e.schema,strict:!0,name:(b=e.name)??`response`,description:e.description}}:{type:`json_object`}},warnings:S};case`object-tool`:return{args:C?{...E,function_call:{name:e.tool.name},functions:[{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters}]}:{...E,tool_choice:{type:`function`,function:{name:e.tool.name}},tools:[{type:`function`,function:{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters,strict:this.supportsStructuredOutputs?!0:void 0}}]},warnings:S};default:{let e=x;throw Error(`Unsupported type: ${e}`)}}}async doGenerate(e){var t,n,r,i,a,o,c,l;let{args:u,warnings:d}=this.getArgs(e),{responseHeaders:f,value:m,rawValue:h}=await T({url:this.config.url({path:`/chat/completions`,modelId:this.modelId}),headers:s(this.config.headers(),e.headers),body:u,failedResponseHandler:N,successfulResponseHandler:p(re),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:_,...v}=u,y=m.choices[0],b=(t=m.usage)?.completion_tokens_details,x=(n=m.usage)?.prompt_tokens_details,S={openai:{}};return b?.reasoning_tokens!=null&&(S.openai.reasoningTokens=b?.reasoning_tokens),b?.accepted_prediction_tokens!=null&&(S.openai.acceptedPredictionTokens=b?.accepted_prediction_tokens),b?.rejected_prediction_tokens!=null&&(S.openai.rejectedPredictionTokens=b?.rejected_prediction_tokens),x?.cached_tokens!=null&&(S.openai.cachedPromptTokens=x?.cached_tokens),{text:(r=y.message.content)??void 0,toolCalls:this.settings.useLegacyFunctionCalling&&y.message.function_call?[{toolCallType:`function`,toolCallId:g(),toolName:y.message.function_call.name,args:y.message.function_call.arguments}]:(i=y.message.tool_calls)?.map(e=>{var t;return{toolCallType:`function`,toolCallId:(t=e.id)??g(),toolName:e.function.name,args:e.function.arguments}}),finishReason:j(y.finish_reason),usage:{promptTokens:(o=(a=m.usage)?.prompt_tokens)??NaN,completionTokens:(l=(c=m.usage)?.completion_tokens)??NaN},rawCall:{rawPrompt:_,rawSettings:v},rawResponse:{headers:f,body:h},request:{body:JSON.stringify(u)},response:P(m),warnings:d,logprobs:A(y.logprobs),providerMetadata:S}}async doStream(e){if(this.settings.simulateStreaming){let t=await this.doGenerate(e),n=new ReadableStream({start(e){if(e.enqueue({type:`response-metadata`,...t.response}),t.text&&e.enqueue({type:`text-delta`,textDelta:t.text}),t.toolCalls)for(let n of t.toolCalls)e.enqueue({type:`tool-call-delta`,toolCallType:`function`,toolCallId:n.toolCallId,toolName:n.toolName,argsTextDelta:n.args}),e.enqueue({type:`tool-call`,...n});e.enqueue({type:`finish`,finishReason:t.finishReason,usage:t.usage,logprobs:t.logprobs,providerMetadata:t.providerMetadata}),e.close()}});return{stream:n,rawCall:t.rawCall,rawResponse:t.rawResponse,warnings:t.warnings}}let{args:n,warnings:r}=this.getArgs(e),i={...n,stream:!0,stream_options:this.config.compatibility===`strict`?{include_usage:!0}:void 0},{responseHeaders:a,value:o}=await T({url:this.config.url({path:`/chat/completions`,modelId:this.modelId}),headers:s(this.config.headers(),e.headers),body:i,failedResponseHandler:N,successfulResponseHandler:d(ie),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:c,...l}=n,u=[],f=`unknown`,p={promptTokens:void 0,completionTokens:void 0},m,h=!0,{useLegacyFunctionCalling:_}=this.settings,y={openai:{}};return{stream:o.pipeThrough(new TransformStream({transform(e,n){var r,i,a,o,s,c,l,d,b,x,S,C;if(!e.success){f=`error`,n.enqueue({type:`error`,error:e.error});return}let w=e.value;if(`error`in w){f=`error`,n.enqueue({type:`error`,error:w.error});return}if(h&&(h=!1,n.enqueue({type:`response-metadata`,...P(w)})),w.usage!=null){let{prompt_tokens:e,completion_tokens:t,prompt_tokens_details:n,completion_tokens_details:r}=w.usage;p={promptTokens:e??void 0,completionTokens:t??void 0},r?.reasoning_tokens!=null&&(y.openai.reasoningTokens=r?.reasoning_tokens),r?.accepted_prediction_tokens!=null&&(y.openai.acceptedPredictionTokens=r?.accepted_prediction_tokens),r?.rejected_prediction_tokens!=null&&(y.openai.rejectedPredictionTokens=r?.rejected_prediction_tokens),n?.cached_tokens!=null&&(y.openai.cachedPromptTokens=n?.cached_tokens)}let T=w.choices[0];if(T?.finish_reason!=null&&(f=j(T.finish_reason)),T?.delta==null)return;let E=T.delta;E.content!=null&&n.enqueue({type:`text-delta`,textDelta:E.content});let D=A(T?.logprobs);D?.length&&(m===void 0&&(m=[]),m.push(...D));let O=_&&E.function_call!=null?[{type:`function`,id:g(),function:E.function_call,index:0}]:E.tool_calls;if(O!=null)for(let e of O){let f=e.index;if(u[f]==null){if(e.type!==`function`)throw new t({data:e,message:`Expected 'function' type.`});if(e.id==null)throw new t({data:e,message:`Expected 'id' to be a string.`});if((r=e.function)?.name==null)throw new t({data:e,message:`Expected 'function.name' to be a string.`});u[f]={id:e.id,type:`function`,function:{name:e.function.name,arguments:(i=e.function.arguments)??``},hasFinished:!1};let c=u[f];(a=c.function)?.name!=null&&(o=c.function)?.arguments!=null&&(c.function.arguments.length>0&&n.enqueue({type:`tool-call-delta`,toolCallType:`function`,toolCallId:c.id,toolName:c.function.name,argsTextDelta:c.function.arguments}),v(c.function.arguments)&&(n.enqueue({type:`tool-call`,toolCallType:`function`,toolCallId:(s=c.id)??g(),toolName:c.function.name,args:c.function.arguments}),c.hasFinished=!0));continue}let p=u[f];if(p.hasFinished)continue;(c=e.function)?.arguments!=null&&(p.function.arguments+=(d=(l=e.function)?.arguments)??``),n.enqueue({type:`tool-call-delta`,toolCallType:`function`,toolCallId:p.id,toolName:p.function.name,argsTextDelta:(b=e.function.arguments)??``}),(x=p.function)?.name!=null&&(S=p.function)?.arguments!=null&&v(p.function.arguments)&&(n.enqueue({type:`tool-call`,toolCallType:`function`,toolCallId:(C=p.id)??g(),toolName:p.function.name,args:p.function.arguments}),p.hasFinished=!0)}},flush(e){var t,n;e.enqueue({type:`finish`,finishReason:f,logprobs:m,usage:{promptTokens:(t=p.promptTokens)??NaN,completionTokens:(n=p.completionTokens)??NaN},...y==null?{}:{providerMetadata:y}})}})),rawCall:{rawPrompt:c,rawSettings:l},rawResponse:{headers:a},request:{body:JSON.stringify(i)},warnings:r}}},I=S({prompt_tokens:x().nullish(),completion_tokens:x().nullish(),prompt_tokens_details:S({cached_tokens:x().nullish()}).nullish(),completion_tokens_details:S({reasoning_tokens:x().nullish(),accepted_prediction_tokens:x().nullish(),rejected_prediction_tokens:x().nullish()}).nullish()}).nullish(),re=S({id:D().nullish(),created:x().nullish(),model:D().nullish(),choices:a(S({message:S({role:y(`assistant`).nullish(),content:D().nullish(),function_call:S({arguments:D(),name:D()}).nullish(),tool_calls:a(S({id:D().nullish(),type:y(`function`),function:S({name:D(),arguments:D()})})).nullish()}),index:x(),logprobs:S({content:a(S({token:D(),logprob:x(),top_logprobs:a(S({token:D(),logprob:x()}))})).nullable()}).nullish(),finish_reason:D().nullish()})),usage:I}),ie=O([S({id:D().nullish(),created:x().nullish(),model:D().nullish(),choices:a(S({delta:S({role:h([`assistant`]).nullish(),content:D().nullish(),function_call:S({name:D().optional(),arguments:D().optional()}).nullish(),tool_calls:a(S({index:x(),id:D().nullish(),type:y(`function`).nullish(),function:S({name:D().nullish(),arguments:D().nullish()})})).nullish()}).nullish(),logprobs:S({content:a(S({token:D(),logprob:x(),top_logprobs:a(S({token:D(),logprob:x()}))})).nullable()}).nullish(),finish_reason:D().nullish(),index:x()})),usage:I}),M]);function L(e){return e.startsWith(`o`)}function ae(e){return e.startsWith(`gpt-4o-audio-preview`)}function oe(e){var t,n;return L(e)?(n=(t=se[e])?.systemMessageMode)??`developer`:`system`}var se={"o1-mini":{systemMessageMode:`remove`},"o1-mini-2024-09-12":{systemMessageMode:`remove`},"o1-preview":{systemMessageMode:`remove`},"o1-preview-2024-09-12":{systemMessageMode:`remove`},o3:{systemMessageMode:`developer`},"o3-2025-04-16":{systemMessageMode:`developer`},"o3-mini":{systemMessageMode:`developer`},"o3-mini-2025-01-31":{systemMessageMode:`developer`},"o4-mini":{systemMessageMode:`developer`},"o4-mini-2025-04-16":{systemMessageMode:`developer`}};function ce({prompt:t,inputFormat:n,user:i=`user`,assistant:a=`assistant`}){if(n===`prompt`&&t.length===1&&t[0].role===`user`&&t[0].content.length===1&&t[0].content[0].type===`text`)return{prompt:t[0].content[0].text};let o=``;t[0].role===`system`&&(o+=`${t[0].content}

`,t=t.slice(1));for(let{role:n,content:s}of t)switch(n){case`system`:throw new e({message:"Unexpected system message in prompt: ${content}",prompt:t});case`user`:{let e=s.map(e=>{switch(e.type){case`text`:return e.text;case`image`:throw new r({functionality:`images`})}}).join(``);o+=`${i}:
${e}

`;break}case`assistant`:{let e=s.map(e=>{switch(e.type){case`text`:return e.text;case`tool-call`:throw new r({functionality:`tool-call messages`})}}).join(``);o+=`${a}:
${e}

`;break}case`tool`:throw new r({functionality:`tool messages`});default:{let e=n;throw Error(`Unsupported role: ${e}`)}}return o+=`${a}:
`,{prompt:o,stopSequences:[`
${i}:`]}}function R(e){return e?.tokens.map((t,n)=>({token:t,logprob:e.token_logprobs[n],topLogprobs:e.top_logprobs?Object.entries(e.top_logprobs[n]).map(([e,t])=>({token:e,logprob:t})):[]}))}var z=class{constructor(e,t,n){this.specificationVersion=`v1`,this.defaultObjectGenerationMode=void 0,this.modelId=e,this.settings=t,this.config=n}get provider(){return this.config.provider}getArgs({mode:e,inputFormat:t,prompt:n,maxTokens:i,temperature:a,topP:o,topK:s,frequencyPenalty:c,presencePenalty:l,stopSequences:u,responseFormat:d,seed:f}){var p;let m=e.type,h=[];s!=null&&h.push({type:`unsupported-setting`,setting:`topK`}),d!=null&&d.type!==`text`&&h.push({type:`unsupported-setting`,setting:`responseFormat`,details:`JSON response format is not supported.`});let{prompt:g,stopSequences:_}=ce({prompt:n,inputFormat:t}),v=[..._??[],...u??[]],y={model:this.modelId,echo:this.settings.echo,logit_bias:this.settings.logitBias,logprobs:typeof this.settings.logprobs==`number`?this.settings.logprobs:typeof this.settings.logprobs==`boolean`&&this.settings.logprobs?0:void 0,suffix:this.settings.suffix,user:this.settings.user,max_tokens:i,temperature:a,top_p:o,frequency_penalty:c,presence_penalty:l,seed:f,prompt:g,stop:v.length>0?v:void 0};switch(m){case`regular`:if((p=e.tools)?.length)throw new r({functionality:`tools`});if(e.toolChoice)throw new r({functionality:`toolChoice`});return{args:y,warnings:h};case`object-json`:throw new r({functionality:`object-json mode`});case`object-tool`:throw new r({functionality:`object-tool mode`});default:{let e=m;throw Error(`Unsupported type: ${e}`)}}}async doGenerate(e){let{args:t,warnings:n}=this.getArgs(e),{responseHeaders:r,value:i,rawValue:a}=await T({url:this.config.url({path:`/completions`,modelId:this.modelId}),headers:s(this.config.headers(),e.headers),body:t,failedResponseHandler:N,successfulResponseHandler:p(B),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:o,...c}=t,l=i.choices[0];return{text:l.text,usage:{promptTokens:i.usage.prompt_tokens,completionTokens:i.usage.completion_tokens},finishReason:j(l.finish_reason),logprobs:R(l.logprobs),rawCall:{rawPrompt:o,rawSettings:c},rawResponse:{headers:r,body:a},response:P(i),warnings:n,request:{body:JSON.stringify(t)}}}async doStream(e){let{args:t,warnings:n}=this.getArgs(e),r={...t,stream:!0,stream_options:this.config.compatibility===`strict`?{include_usage:!0}:void 0},{responseHeaders:i,value:a}=await T({url:this.config.url({path:`/completions`,modelId:this.modelId}),headers:s(this.config.headers(),e.headers),body:r,failedResponseHandler:N,successfulResponseHandler:d(V),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:o,...c}=t,l=`unknown`,u={promptTokens:NaN,completionTokens:NaN},f,p=!0;return{stream:a.pipeThrough(new TransformStream({transform(e,t){if(!e.success){l=`error`,t.enqueue({type:`error`,error:e.error});return}let n=e.value;if(`error`in n){l=`error`,t.enqueue({type:`error`,error:n.error});return}p&&(p=!1,t.enqueue({type:`response-metadata`,...P(n)})),n.usage!=null&&(u={promptTokens:n.usage.prompt_tokens,completionTokens:n.usage.completion_tokens});let r=n.choices[0];r?.finish_reason!=null&&(l=j(r.finish_reason)),r?.text!=null&&t.enqueue({type:`text-delta`,textDelta:r.text});let i=R(r?.logprobs);i?.length&&(f===void 0&&(f=[]),f.push(...i))},flush(e){e.enqueue({type:`finish`,finishReason:l,logprobs:f,usage:u})}})),rawCall:{rawPrompt:o,rawSettings:c},rawResponse:{headers:i},warnings:n,request:{body:JSON.stringify(r)}}}},B=S({id:D().nullish(),created:x().nullish(),model:D().nullish(),choices:a(S({text:D(),finish_reason:D(),logprobs:S({tokens:a(D()),token_logprobs:a(x()),top_logprobs:a(E(D(),x())).nullable()}).nullish()})),usage:S({prompt_tokens:x(),completion_tokens:x()})}),V=O([S({id:D().nullish(),created:x().nullish(),model:D().nullish(),choices:a(S({text:D(),finish_reason:D().nullish(),index:x(),logprobs:S({tokens:a(D()),token_logprobs:a(x()),top_logprobs:a(E(D(),x())).nullable()}).nullish()})),usage:S({prompt_tokens:x(),completion_tokens:x()}).nullish()}),M]),H=class{constructor(e,t,n){this.specificationVersion=`v1`,this.modelId=e,this.settings=t,this.config=n}get provider(){return this.config.provider}get maxEmbeddingsPerCall(){var e;return(e=this.settings.maxEmbeddingsPerCall)??2048}get supportsParallelCalls(){var e;return(e=this.settings.supportsParallelCalls)??!0}async doEmbed({values:e,headers:t,abortSignal:r}){if(e.length>this.maxEmbeddingsPerCall)throw new n({provider:this.provider,modelId:this.modelId,maxEmbeddingsPerCall:this.maxEmbeddingsPerCall,values:e});let{responseHeaders:i,value:a}=await T({url:this.config.url({path:`/embeddings`,modelId:this.modelId}),headers:s(this.config.headers(),t),body:{model:this.modelId,input:e,encoding_format:`float`,dimensions:this.settings.dimensions,user:this.settings.user},failedResponseHandler:N,successfulResponseHandler:p(U),abortSignal:r,fetch:this.config.fetch});return{embeddings:a.data.map(e=>e.embedding),usage:a.usage?{tokens:a.usage.prompt_tokens}:void 0,rawResponse:{headers:i}}}},U=S({data:a(S({embedding:a(x())})),usage:S({prompt_tokens:x()}).nullish()}),W={"dall-e-3":1,"dall-e-2":10,"gpt-image-1":10},G=new Set([`gpt-image-1`]),le=class{constructor(e,t,n){this.modelId=e,this.settings=t,this.config=n,this.specificationVersion=`v1`}get maxImagesPerCall(){var e,t;return(t=(e=this.settings.maxImagesPerCall)??W[this.modelId])??1}get provider(){return this.config.provider}async doGenerate({prompt:e,n:t,size:n,aspectRatio:r,seed:i,providerOptions:a,headers:o,abortSignal:c}){var l,u,d,f;let m=[];r!=null&&m.push({type:`unsupported-setting`,setting:`aspectRatio`,details:"This model does not support aspect ratio. Use `size` instead."}),i!=null&&m.push({type:`unsupported-setting`,setting:`seed`});let h=(d=(u=(l=this.config._internal)?.currentDate)?.call(l))??new Date,{value:g,responseHeaders:_}=await T({url:this.config.url({path:`/images/generations`,modelId:this.modelId}),headers:s(this.config.headers(),o),body:{model:this.modelId,prompt:e,n:t,size:n,...(f=a.openai)??{},...G.has(this.modelId)?{}:{response_format:`b64_json`}},failedResponseHandler:N,successfulResponseHandler:p(ue),abortSignal:c,fetch:this.config.fetch});return{images:g.data.map(e=>e.b64_json),warnings:m,response:{timestamp:h,modelId:this.modelId,headers:_}}}},ue=S({data:a(S({b64_json:D()}))}),de=S({include:a(D()).nullish(),language:D().nullish(),prompt:D().nullish(),temperature:x().min(0).max(1).nullish().default(0),timestampGranularities:a(h([`word`,`segment`])).nullish().default([`segment`])}),K={afrikaans:`af`,arabic:`ar`,armenian:`hy`,azerbaijani:`az`,belarusian:`be`,bosnian:`bs`,bulgarian:`bg`,catalan:`ca`,chinese:`zh`,croatian:`hr`,czech:`cs`,danish:`da`,dutch:`nl`,english:`en`,estonian:`et`,finnish:`fi`,french:`fr`,galician:`gl`,german:`de`,greek:`el`,hebrew:`he`,hindi:`hi`,hungarian:`hu`,icelandic:`is`,indonesian:`id`,italian:`it`,japanese:`ja`,kannada:`kn`,kazakh:`kk`,korean:`ko`,latvian:`lv`,lithuanian:`lt`,macedonian:`mk`,malay:`ms`,marathi:`mr`,maori:`mi`,nepali:`ne`,norwegian:`no`,persian:`fa`,polish:`pl`,portuguese:`pt`,romanian:`ro`,russian:`ru`,serbian:`sr`,slovak:`sk`,slovenian:`sl`,spanish:`es`,swahili:`sw`,swedish:`sv`,tagalog:`tl`,tamil:`ta`,thai:`th`,turkish:`tr`,ukrainian:`uk`,urdu:`ur`,vietnamese:`vi`,welsh:`cy`},fe=class{constructor(e,t){this.modelId=e,this.config=t,this.specificationVersion=`v1`}get provider(){return this.config.provider}getArgs({audio:e,mediaType:t,providerOptions:n}){var r,i,a,o,s;let l=[],u=C({provider:`openai`,providerOptions:n,schema:de}),d=new FormData,f=e instanceof Uint8Array?new Blob([e]):new Blob([c(e)]);if(d.append(`model`,this.modelId),d.append(`file`,new File([f],`audio`,{type:t})),u){let e={include:(r=u.include)??void 0,language:(i=u.language)??void 0,prompt:(a=u.prompt)??void 0,temperature:(o=u.temperature)??void 0,timestamp_granularities:(s=u.timestampGranularities)??void 0};for(let t in e){let n=e[t];n!==void 0&&d.append(t,String(n))}}return{formData:d,warnings:l}}async doGenerate(e){var t,n,r,i,a,o;let c=(r=(n=(t=this.config._internal)?.currentDate)?.call(t))??new Date,{formData:l,warnings:u}=this.getArgs(e),{value:d,responseHeaders:f,rawValue:m}=await w({url:this.config.url({path:`/audio/transcriptions`,modelId:this.modelId}),headers:s(this.config.headers(),e.headers),formData:l,failedResponseHandler:N,successfulResponseHandler:p(pe),abortSignal:e.abortSignal,fetch:this.config.fetch}),h=d.language!=null&&d.language in K?K[d.language]:void 0;return{text:d.text,segments:(a=(i=d.words)?.map(e=>({text:e.word,startSecond:e.start,endSecond:e.end})))??[],language:h,durationInSeconds:(o=d.duration)??void 0,warnings:u,response:{timestamp:c,modelId:this.modelId,headers:f,body:m}}}},pe=S({text:D(),language:D().nullish(),duration:x().nullish(),words:a(S({word:D(),start:x(),end:x()})).nullish()});function me({prompt:e,systemMessageMode:t}){let n=[],i=[];for(let{role:a,content:o}of e)switch(a){case`system`:switch(t){case`system`:n.push({role:`system`,content:o});break;case`developer`:n.push({role:`developer`,content:o});break;case`remove`:i.push({type:`other`,message:`system messages are removed for this model`});break;default:{let e=t;throw Error(`Unsupported system message mode: ${e}`)}}break;case`user`:n.push({role:`user`,content:o.map((e,t)=>{var n,i,a,o;switch(e.type){case`text`:return{type:`input_text`,text:e.text};case`image`:return{type:`input_image`,image_url:e.image instanceof URL?e.image.toString():`data:${(n=e.mimeType)??`image/jpeg`};base64,${l(e.image)}`,detail:(a=(i=e.providerMetadata)?.openai)?.imageDetail};case`file`:if(e.data instanceof URL)throw new r({functionality:`File URLs in user messages`});switch(e.mimeType){case`application/pdf`:return{type:`input_file`,filename:(o=e.filename)??`part-${t}.pdf`,file_data:`data:application/pdf;base64,${e.data}`};default:throw new r({functionality:`Only PDF files are supported in user messages`})}}})});break;case`assistant`:for(let e of o)switch(e.type){case`text`:n.push({role:`assistant`,content:[{type:`output_text`,text:e.text}]});break;case`tool-call`:n.push({type:`function_call`,call_id:e.toolCallId,name:e.toolName,arguments:JSON.stringify(e.args)});break}break;case`tool`:for(let e of o)n.push({type:`function_call_output`,call_id:e.toolCallId,output:JSON.stringify(e.result)});break;default:{let e=a;throw Error(`Unsupported role: ${e}`)}}return{messages:n,warnings:i}}function q({finishReason:e,hasToolCalls:t}){switch(e){case void 0:case null:return t?`tool-calls`:`stop`;case`max_output_tokens`:return`length`;case`content_filter`:return`content-filter`;default:return t?`tool-calls`:`unknown`}}function he({mode:e,strict:t}){var n;let i=(n=e.tools)?.length?e.tools:void 0,a=[];if(i==null)return{tools:void 0,tool_choice:void 0,toolWarnings:a};let o=e.toolChoice,s=[];for(let e of i)switch(e.type){case`function`:s.push({type:`function`,name:e.name,description:e.description,parameters:e.parameters,strict:t?!0:void 0});break;case`provider-defined`:switch(e.id){case`openai.web_search_preview`:s.push({type:`web_search_preview`,search_context_size:e.args.searchContextSize,user_location:e.args.userLocation});break;default:a.push({type:`unsupported-tool`,tool:e});break}break;default:a.push({type:`unsupported-tool`,tool:e});break}if(o==null)return{tools:s,tool_choice:void 0,toolWarnings:a};let c=o.type;switch(c){case`auto`:case`none`:case`required`:return{tools:s,tool_choice:c,toolWarnings:a};case`tool`:return o.toolName===`web_search_preview`?{tools:s,tool_choice:{type:`web_search_preview`},toolWarnings:a}:{tools:s,tool_choice:{type:`function`,name:o.toolName},toolWarnings:a};default:{let e=c;throw new r({functionality:`Unsupported tool choice type: ${e}`})}}}var ge=class{constructor(e,t){this.specificationVersion=`v1`,this.defaultObjectGenerationMode=`json`,this.supportsStructuredOutputs=!0,this.modelId=e,this.config=t}get provider(){return this.config.provider}getArgs({mode:e,maxTokens:t,temperature:n,stopSequences:r,topP:i,topK:a,presencePenalty:o,frequencyPenalty:s,seed:c,prompt:l,providerMetadata:u,responseFormat:d}){var f,p,m;let h=[],g=Pe(this.modelId),_=e.type;a!=null&&h.push({type:`unsupported-setting`,setting:`topK`}),c!=null&&h.push({type:`unsupported-setting`,setting:`seed`}),o!=null&&h.push({type:`unsupported-setting`,setting:`presencePenalty`}),s!=null&&h.push({type:`unsupported-setting`,setting:`frequencyPenalty`}),r!=null&&h.push({type:`unsupported-setting`,setting:`stopSequences`});let{messages:v,warnings:y}=me({prompt:l,systemMessageMode:g.systemMessageMode});h.push(...y);let b=C({provider:`openai`,providerOptions:u,schema:Fe}),x=(f=b?.strictSchemas)??!0,S={model:this.modelId,input:v,temperature:n,top_p:i,max_output_tokens:t,...d?.type===`json`&&{text:{format:d.schema==null?{type:`json_object`}:{type:`json_schema`,strict:x,name:(p=d.name)??`response`,description:d.description,schema:d.schema}}},metadata:b?.metadata,parallel_tool_calls:b?.parallelToolCalls,previous_response_id:b?.previousResponseId,store:b?.store,user:b?.user,instructions:b?.instructions,...g.isReasoningModel&&(b?.reasoningEffort!=null||b?.reasoningSummary!=null)&&{reasoning:{...b?.reasoningEffort!=null&&{effort:b.reasoningEffort},...b?.reasoningSummary!=null&&{summary:b.reasoningSummary}}},...g.requiredAutoTruncation&&{truncation:`auto`}};switch(g.isReasoningModel&&(S.temperature!=null&&(S.temperature=void 0,h.push({type:`unsupported-setting`,setting:`temperature`,details:`temperature is not supported for reasoning models`})),S.top_p!=null&&(S.top_p=void 0,h.push({type:`unsupported-setting`,setting:`topP`,details:`topP is not supported for reasoning models`}))),_){case`regular`:{let{tools:t,tool_choice:n,toolWarnings:r}=he({mode:e,strict:x});return{args:{...S,tools:t,tool_choice:n},warnings:[...h,...r]}}case`object-json`:return{args:{...S,text:{format:e.schema==null?{type:`json_object`}:{type:`json_schema`,strict:x,name:(m=e.name)??`response`,description:e.description,schema:e.schema}}},warnings:h};case`object-tool`:return{args:{...S,tool_choice:{type:`function`,name:e.tool.name},tools:[{type:`function`,name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters,strict:x}]},warnings:h};default:{let e=_;throw Error(`Unsupported type: ${e}`)}}}async doGenerate(e){var t,n,r,i,o,c,l;let{args:u,warnings:d}=this.getArgs(e),{responseHeaders:f,value:h,rawValue:_}=await T({url:this.config.url({path:`/responses`,modelId:this.modelId}),headers:s(this.config.headers(),e.headers),body:u,failedResponseHandler:N,successfulResponseHandler:p(S({id:D(),created_at:x(),model:D(),output:a(m(`type`,[S({type:y(`message`),role:y(`assistant`),content:a(S({type:y(`output_text`),text:D(),annotations:a(S({type:y(`url_citation`),start_index:x(),end_index:x(),url:D(),title:D()}))}))}),S({type:y(`function_call`),call_id:D(),name:D(),arguments:D()}),S({type:y(`web_search_call`)}),S({type:y(`computer_call`)}),S({type:y(`reasoning`),summary:a(S({type:y(`summary_text`),text:D()}))})])),incomplete_details:S({reason:D()}).nullable(),usage:J})),abortSignal:e.abortSignal,fetch:this.config.fetch}),v=h.output.filter(e=>e.type===`message`).flatMap(e=>e.content).filter(e=>e.type===`output_text`),b=h.output.filter(e=>e.type===`function_call`).map(e=>({toolCallType:`function`,toolCallId:e.call_id,toolName:e.name,args:e.arguments})),C=(n=(t=h.output.find(e=>e.type===`reasoning`))?.summary)??null;return{text:v.map(e=>e.text).join(`
`),sources:v.flatMap(e=>e.annotations.map(e=>{var t,n,r;return{sourceType:`url`,id:(r=(n=(t=this.config).generateId)?.call(t))??g(),url:e.url,title:e.title}})),finishReason:q({finishReason:(r=h.incomplete_details)?.reason,hasToolCalls:b.length>0}),toolCalls:b.length>0?b:void 0,reasoning:C?C.map(e=>({type:`text`,text:e.text})):void 0,usage:{promptTokens:h.usage.input_tokens,completionTokens:h.usage.output_tokens},rawCall:{rawPrompt:void 0,rawSettings:{}},rawResponse:{headers:f,body:_},request:{body:JSON.stringify(u)},response:{id:h.id,timestamp:new Date(h.created_at*1e3),modelId:h.model},providerMetadata:{openai:{responseId:h.id,cachedPromptTokens:(o=(i=h.usage.input_tokens_details)?.cached_tokens)??null,reasoningTokens:(l=(c=h.usage.output_tokens_details)?.reasoning_tokens)??null}},warnings:d}}async doStream(e){let{args:t,warnings:n}=this.getArgs(e),{responseHeaders:r,value:i}=await T({url:this.config.url({path:`/responses`,modelId:this.modelId}),headers:s(this.config.headers(),e.headers),body:{...t,stream:!0},failedResponseHandler:N,successfulResponseHandler:d(Te),abortSignal:e.abortSignal,fetch:this.config.fetch}),a=this,o=`unknown`,c=NaN,l=NaN,u=null,f=null,p=null,m={},h=!1;return{stream:i.pipeThrough(new TransformStream({transform(e,t){var n,r,i,s,d,_,v,y;if(!e.success){o=`error`,t.enqueue({type:`error`,error:e.error});return}let b=e.value;if(je(b))b.item.type===`function_call`&&(m[b.output_index]={toolName:b.item.name,toolCallId:b.item.call_id},t.enqueue({type:`tool-call-delta`,toolCallType:`function`,toolCallId:b.item.call_id,toolName:b.item.name,argsTextDelta:b.item.arguments}));else if(Ae(b)){let e=m[b.output_index];e!=null&&t.enqueue({type:`tool-call-delta`,toolCallType:`function`,toolCallId:e.toolCallId,toolName:e.toolName,argsTextDelta:b.delta})}else ke(b)?(p=b.response.id,t.enqueue({type:`response-metadata`,id:b.response.id,timestamp:new Date(b.response.created_at*1e3),modelId:b.response.model})):Ee(b)?t.enqueue({type:`text-delta`,textDelta:b.delta}):Ne(b)?t.enqueue({type:`reasoning`,textDelta:b.delta}):De(b)&&b.item.type===`function_call`?(m[b.output_index]=void 0,h=!0,t.enqueue({type:`tool-call`,toolCallType:`function`,toolCallId:b.item.call_id,toolName:b.item.name,args:b.item.arguments})):Oe(b)?(o=q({finishReason:(n=b.response.incomplete_details)?.reason,hasToolCalls:h}),c=b.response.usage.input_tokens,l=b.response.usage.output_tokens,u=(i=(r=b.response.usage.input_tokens_details)?.cached_tokens)??u,f=(d=(s=b.response.usage.output_tokens_details)?.reasoning_tokens)??f):Me(b)&&t.enqueue({type:`source`,source:{sourceType:`url`,id:(y=(v=(_=a.config).generateId)?.call(_))??g(),url:b.annotation.url,title:b.annotation.title}})},flush(e){e.enqueue({type:`finish`,finishReason:o,usage:{promptTokens:c,completionTokens:l},...(u!=null||f!=null)&&{providerMetadata:{openai:{responseId:p,cachedPromptTokens:u,reasoningTokens:f}}}})}})),rawCall:{rawPrompt:void 0,rawSettings:{}},rawResponse:{headers:r},request:{body:JSON.stringify(t)},warnings:n}}},J=S({input_tokens:x(),input_tokens_details:S({cached_tokens:x().nullish()}).nullish(),output_tokens:x(),output_tokens_details:S({reasoning_tokens:x().nullish()}).nullish()}),_e=S({type:y(`response.output_text.delta`),delta:D()}),ve=S({type:h([`response.completed`,`response.incomplete`]),response:S({incomplete_details:S({reason:D()}).nullish(),usage:J})}),ye=S({type:y(`response.created`),response:S({id:D(),created_at:x(),model:D()})}),be=S({type:y(`response.output_item.done`),output_index:x(),item:m(`type`,[S({type:y(`message`)}),S({type:y(`function_call`),id:D(),call_id:D(),name:D(),arguments:D(),status:y(`completed`)})])}),xe=S({type:y(`response.function_call_arguments.delta`),item_id:D(),output_index:x(),delta:D()}),Se=S({type:y(`response.output_item.added`),output_index:x(),item:m(`type`,[S({type:y(`message`)}),S({type:y(`function_call`),id:D(),call_id:D(),name:D(),arguments:D()})])}),Ce=S({type:y(`response.output_text.annotation.added`),annotation:S({type:y(`url_citation`),url:D(),title:D()})}),we=S({type:y(`response.reasoning_summary_text.delta`),item_id:D(),output_index:x(),summary_index:x(),delta:D()}),Te=O([_e,ve,ye,be,xe,Se,Ce,we,S({type:D()}).passthrough()]);function Ee(e){return e.type===`response.output_text.delta`}function De(e){return e.type===`response.output_item.done`}function Oe(e){return e.type===`response.completed`||e.type===`response.incomplete`}function ke(e){return e.type===`response.created`}function Ae(e){return e.type===`response.function_call_arguments.delta`}function je(e){return e.type===`response.output_item.added`}function Me(e){return e.type===`response.output_text.annotation.added`}function Ne(e){return e.type===`response.reasoning_summary_text.delta`}function Pe(e){return e.startsWith(`o`)?e.startsWith(`o1-mini`)||e.startsWith(`o1-preview`)?{isReasoningModel:!0,systemMessageMode:`remove`,requiredAutoTruncation:!1}:{isReasoningModel:!0,systemMessageMode:`developer`,requiredAutoTruncation:!1}:{isReasoningModel:!1,systemMessageMode:`system`,requiredAutoTruncation:!1}}var Fe=S({metadata:i().nullish(),parallelToolCalls:o().nullish(),previousResponseId:D().nullish(),store:o().nullish(),user:D().nullish(),reasoningEffort:D().nullish(),strictSchemas:o().nullish(),instructions:D().nullish(),reasoningSummary:D().nullish()}),Ie=S({});function Le({searchContextSize:e,userLocation:t}={}){return{type:`provider-defined`,id:`openai.web_search_preview`,args:{searchContextSize:e,userLocation:t},parameters:Ie}}var Re={webSearchPreview:Le},Y=S({instructions:D().nullish(),speed:x().min(.25).max(4).default(1).nullish()}),ze=class{constructor(e,t){this.modelId=e,this.config=t,this.specificationVersion=`v1`}get provider(){return this.config.provider}getArgs({text:e,voice:t=`alloy`,outputFormat:n=`mp3`,speed:r,instructions:i,providerOptions:a}){let o=[],s=C({provider:`openai`,providerOptions:a,schema:Y}),c={model:this.modelId,input:e,voice:t,response_format:`mp3`,speed:r,instructions:i};if(n&&([`mp3`,`opus`,`aac`,`flac`,`wav`,`pcm`].includes(n)?c.response_format=n:o.push({type:`unsupported-setting`,setting:`outputFormat`,details:`Unsupported output format: ${n}. Using mp3 instead.`})),s){let e={};for(let t in e){let n=e[t];n!==void 0&&(c[t]=n)}}return{requestBody:c,warnings:o}}async doGenerate(e){var t,n,r;let i=(r=(n=(t=this.config._internal)?.currentDate)?.call(t))??new Date,{requestBody:a,warnings:o}=this.getArgs(e),{value:c,responseHeaders:l,rawValue:d}=await T({url:this.config.url({path:`/audio/speech`,modelId:this.modelId}),headers:s(this.config.headers(),e.headers),body:a,failedResponseHandler:N,successfulResponseHandler:u(),abortSignal:e.abortSignal,fetch:this.config.fetch});return{audio:c,warnings:o,request:{body:JSON.stringify(a)},response:{timestamp:i,modelId:this.modelId,headers:l,body:d}}}};function Be(e={}){var t,n,r;let i=(t=ee(e.baseURL))??`https://api.openai.com/v1`,a=(n=e.compatibility)??`compatible`,o=(r=e.name)??`openai`,s=()=>({Authorization:`Bearer ${b({apiKey:e.apiKey,environmentVariableName:`OPENAI_API_KEY`,description:`OpenAI`})}`,"OpenAI-Organization":e.organization,"OpenAI-Project":e.project,...e.headers}),c=(t,n={})=>new ne(t,n,{provider:`${o}.chat`,url:({path:e})=>`${i}${e}`,headers:s,compatibility:a,fetch:e.fetch}),l=(t,n={})=>new z(t,n,{provider:`${o}.completion`,url:({path:e})=>`${i}${e}`,headers:s,compatibility:a,fetch:e.fetch}),u=(t,n={})=>new H(t,n,{provider:`${o}.embedding`,url:({path:e})=>`${i}${e}`,headers:s,fetch:e.fetch}),d=(t,n={})=>new le(t,n,{provider:`${o}.image`,url:({path:e})=>`${i}${e}`,headers:s,fetch:e.fetch}),f=t=>new fe(t,{provider:`${o}.transcription`,url:({path:e})=>`${i}${e}`,headers:s,fetch:e.fetch}),p=t=>new ze(t,{provider:`${o}.speech`,url:({path:e})=>`${i}${e}`,headers:s,fetch:e.fetch}),m=(e,t)=>{if(new.target)throw Error(`The OpenAI model function cannot be called with the new keyword.`);return e===`gpt-3.5-turbo-instruct`?l(e,t):c(e,t)},h=t=>new ge(t,{provider:`${o}.responses`,url:({path:e})=>`${i}${e}`,headers:s,fetch:e.fetch}),g=function(e,t){return m(e,t)};return g.languageModel=m,g.chat=c,g.completion=l,g.responses=h,g.embedding=u,g.textEmbedding=u,g.textEmbeddingModel=u,g.image=d,g.imageModel=d,g.transcription=f,g.transcriptionModel=f,g.speech=p,g.speechModel=p,g.tools=Re,g}var Ve=Be({compatibility:`strict`}),He=class{config;constructor(e){this.config={maxFileSize:20*1024*1024,maxDimensions:{width:4096,height:4096},allowedMimeTypes:[`image/jpeg`,`image/png`,`image/webp`],maxFilenameLength:255,enableContentScanning:!0,enableMetadataRemoval:!0,...e}}async validateFile(e){let t=[],n=[];e.size>this.config.maxFileSize&&t.push(`File size exceeds maximum limit of ${this.config.maxFileSize/(1024*1024)}MB`),this.config.allowedMimeTypes.includes(e.type)||t.push(`File type ${e.type} is not supported. Allowed types: ${this.config.allowedMimeTypes.join(`, `)}`);let r=this.sanitizeFilename(e.name);if(r.length>this.config.maxFilenameLength&&t.push(`Filename exceeds maximum length of ${this.config.maxFilenameLength} characters`),this.config.enableContentScanning){let r=await this.validateImageContent(e);r.valid||t.push(...r.errors),n.push(...r.warnings)}try{let i=await this.getImageDimensions(e);return(i.width>this.config.maxDimensions.width||i.height>this.config.maxDimensions.height)&&t.push(`Image dimensions exceed maximum size of ${this.config.maxDimensions.width}x${this.config.maxDimensions.height}`),{valid:t.length===0,errors:t,warnings:n,sanitizedData:{fileName:r,mimeType:e.type,size:e.size,dimensions:i}}}catch{return t.push(`Failed to validate image format`),{valid:!1,errors:t,warnings:n}}}async validateBase64Image(e,t){let n=[],r=[];try{let i=this.parseBase64Metadata(e);this.config.allowedMimeTypes.includes(i.mimeType)||n.push(`Image type ${i.mimeType} is not supported`),i.size>this.config.maxFileSize&&n.push(`Image size exceeds maximum limit of ${this.config.maxFileSize/(1024*1024)}MB`);let a=this.sanitizeFilename(t);a.length>this.config.maxFilenameLength&&n.push(`Filename exceeds maximum length of ${this.config.maxFilenameLength} characters`);let o=await this.getBase64ImageDimensions(e);return(o.width>this.config.maxDimensions.width||o.height>this.config.maxDimensions.height)&&n.push(`Image dimensions exceed maximum size of ${this.config.maxDimensions.width}x${this.config.maxDimensions.height}`),{valid:n.length===0,errors:n,warnings:r,sanitizedData:{fileName:a,mimeType:i.mimeType,size:i.size,dimensions:o}}}catch{return n.push(`Failed to validate base64 image data`),{valid:!1,errors:n,warnings:r}}}sanitizeFilename(e){let t=e.replace(/[/\\:*?"<>|]/g,`_`);if(t=t.replace(/[\x00-\x1f\x7f-\x9f]/g,``),t=t.trim().replace(/^\.+|\.+$/g,``),t||=`untitled`,t.length>this.config.maxFilenameLength){let e=t.split(`.`).pop()||``,n=t.slice(0,-(e.length+1));t=n.slice(0,this.config.maxFilenameLength-e.length-1)+`.`+e}return t}async removeImageMetadata(e){if(!this.config.enableMetadataRemoval)return e;try{let t=new Image,n=document.createElement(`canvas`),r=n.getContext(`2d`);if(!r)throw Error(`Failed to create canvas context`);return new Promise((i,a)=>{t.onload=()=>{n.width=t.width,n.height=t.height,r.drawImage(t,0,0);let e=n.toDataURL(`image/jpeg`,.9);i(e)},t.onerror=()=>a(Error(`Failed to load image for metadata removal`)),t.src=e})}catch(t){return console.warn(`Failed to remove image metadata:`,t),e}}async validateImageContent(e){let t=[],n=[];try{let r=await e.arrayBuffer(),i=new Uint8Array(r);this.hasValidImageHeader(i,e.type)||t.push(`Invalid image file header`);let a=[/<script/i,/javascript:/i,/data:text\/html/i,/vbscript:/i,/<object/i,/<embed/i,/<iframe/i],o=Array.from(i.slice(0,1024)).map(e=>String.fromCharCode(e)).join(``);for(let e of a)if(e.test(o)){t.push(`Suspicious content detected in image file`);break}return{valid:t.length===0,errors:t,warnings:n}}catch{return t.push(`Failed to validate image content`),{valid:!1,errors:t,warnings:n}}}hasValidImageHeader(e,t){if(e.length<8)return!1;switch(t){case`image/jpeg`:return e[0]===255&&e[1]===216;case`image/png`:return e[0]===137&&e[1]===80&&e[2]===78&&e[3]===71;case`image/webp`:return e[0]===82&&e[1]===73&&e[2]===70&&e[3]===70&&e[8]===87&&e[9]===69&&e[10]===66&&e[11]===80;default:return!1}}async getImageDimensions(e){return new Promise((t,n)=>{let r=new Image,i=URL.createObjectURL(e);r.onload=()=>{URL.revokeObjectURL(i),t({width:r.width,height:r.height})},r.onerror=()=>{URL.revokeObjectURL(i),n(Error(`Failed to load image for dimension analysis`))},r.src=i})}async getBase64ImageDimensions(e){return new Promise((t,n)=>{let r=new Image;r.onload=()=>{t({width:r.width,height:r.height})},r.onerror=()=>{n(Error(`Failed to load base64 image for dimension analysis`))},r.src=e})}parseBase64Metadata(e){let t=e.match(/^data:([^;]+);base64,/),n=t?t[1]:`image/jpeg`,r=e.split(`,`)[1]||``,i=Math.floor(r.length*.75);return{mimeType:n,size:i}}async validateContentPolicy(e){let t=[];try{let n=await this.getBase64ImageDimensions(e);return(n.width<10||n.height<10)&&t.push(`Image dimensions are too small`),(n.width>1e4||n.height>1e4)&&t.push(`Image dimensions are excessively large`),{compliant:t.length===0,issues:t}}catch{return t.push(`Failed to validate content policy`),{compliant:!1,issues:t}}}generateSecureCacheKey(e,t){let n=new TextEncoder,r=n.encode(e+(t||``));return crypto.subtle.digest(`SHA-256`,r).then(e=>{let t=Array.from(new Uint8Array(e));return t.map(e=>e.toString(16).padStart(2,`0`)).join(``)}).catch(()=>{let t=0;for(let n=0;n<e.length;n++){let r=e.charCodeAt(n);t=(t<<5)-t+r,t&=t}return Math.abs(t).toString(16)})}sanitizePrompt(e){let t=e.replace(/[<>]/g,``).replace(/javascript:/gi,``).replace(/data:/gi,``).replace(/vbscript:/gi,``).replace(/file:/gi,``).trim();return t.length>2e3&&(t=t.substring(0,2e3)),t}};const X=new He;var Ue=class{cache=new Map;config;stats={hits:0,misses:0,evictions:0,memoryUsage:0,entryCount:0,hitRate:0,averageAccessTime:0};gcTimer;accessTimes=[];constructor(e){this.config={maxMemorySize:50,maxEntries:1e3,defaultTTL:24*60*60*1e3,enablePersistence:!0,compressionLevel:6,gcInterval:5*60*1e3,...e},this.initializeCache(),this.startGarbageCollection()}initializeCache(){if(this.config.enablePersistence)try{let e=localStorage.getItem(`tinderop-editing-cache`);if(e){let t=JSON.parse(e),n=Date.now();for(let[e,r]of Object.entries(t)){let t=r;n-t.timestamp<t.ttl&&this.cache.set(e,t)}this.updateStats()}}catch(e){console.warn(`Failed to initialize cache from localStorage:`,e)}}startGarbageCollection(){this.gcTimer&&clearInterval(this.gcTimer),this.gcTimer=window.setInterval(()=>{this.performGarbageCollection()},this.config.gcInterval)}async get(e){let t=performance.now(),n=this.cache.get(e);if(!n)return this.stats.misses++,this.updateHitRate(),null;let r=Date.now();if(r-n.timestamp>n.ttl)return this.cache.delete(e),this.stats.misses++,this.stats.evictions++,this.updateStats(),null;n.accessCount++,n.lastAccess=r,this.stats.hits++;let i=performance.now()-t;if(this.recordAccessTime(i),this.updateHitRate(),n.compressed)try{let e=await this.decompress(n.data);return e}catch(t){return console.warn(`Failed to decompress cached data:`,t),this.cache.delete(e),null}return n.data}async set(e,t,n=this.config.defaultTTL){try{let r=this.calculateDataSize(t);await this.ensureCapacity(r);let i=t,a=!1;if(r>100*1024)try{i=await this.compress(t),a=!0}catch(e){console.warn(`Failed to compress cache data:`,e)}let o={key:e,data:i,timestamp:Date.now(),ttl:n,accessCount:1,lastAccess:Date.now(),size:r,compressed:a};this.cache.set(e,o),this.updateStats(),this.config.enablePersistence&&await this.persistCache()}catch(e){console.error(`Failed to cache editing result:`,e)}}has(e){let t=this.cache.get(e);if(!t)return!1;let n=Date.now();return n-t.timestamp>t.ttl?(this.cache.delete(e),this.updateStats(),!1):!0}delete(e){let t=this.cache.delete(e);return t&&this.updateStats(),t}clear(){this.cache.clear(),this.stats={hits:0,misses:0,evictions:0,memoryUsage:0,entryCount:0,hitRate:0,averageAccessTime:0},this.config.enablePersistence&&localStorage.removeItem(`tinderop-editing-cache`)}getStats(){return{...this.stats}}getConfig(){return{...this.config}}updateConfig(e){this.config={...this.config,...e},e.gcInterval&&this.startGarbageCollection()}generateCacheKey(e,t){let n=t?JSON.stringify(t):``,r=e+n,i=0;for(let e=0;e<r.length;e++){let t=r.charCodeAt(e);i=(i<<5)-i+t,i&=i}return`editing_${Math.abs(i).toString(16)}`}async ensureCapacity(e){let t=this.stats.memoryUsage*1024*1024,n=this.config.maxMemorySize*1024*1024;t+e>n&&await this.evictLRU(e),this.cache.size>=this.config.maxEntries&&await this.evictLRU(0)}async evictLRU(e){let t=Array.from(this.cache.entries());t.sort((e,t)=>e[1].lastAccess-t[1].lastAccess);let n=0,r=e;for(let[e,i]of t){if(n>=r&&this.cache.size<this.config.maxEntries)break;this.cache.delete(e),n+=i.size,this.stats.evictions++}this.updateStats()}performGarbageCollection(){let e=Date.now(),t=[];for(let[n,r]of this.cache.entries())e-r.timestamp>r.ttl&&t.push(n);for(let e of t)this.cache.delete(e),this.stats.evictions++;t.length>0&&this.updateStats()}async persistCache(){if(this.config.enablePersistence)try{let e=Object.fromEntries(this.cache.entries()),t=JSON.stringify(e),n=this.getAvailableStorageSpace();if(t.length>n){console.warn(`Insufficient localStorage space for cache persistence`);return}localStorage.setItem(`tinderop-editing-cache`,t)}catch(e){console.warn(`Failed to persist cache to localStorage:`,e)}}getAvailableStorageSpace(){try{let e=`test`,t=JSON.stringify(localStorage).length,n=5*1024*1024;return Math.max(0,n-t)}catch{return 0}}calculateDataSize(e){try{let t=JSON.stringify(e);return new Blob([t]).size}catch{return 0}}async compress(e){try{let t=JSON.stringify(e),n=await this.gzipCompress(t);return{...e,_compressed:!0,_data:n}}catch(t){return console.warn(`Compression failed:`,t),e}}async decompress(e){try{if(!e._compressed)return e;let t=e._data,n=await this.gzipDecompress(t);return JSON.parse(n)}catch(t){return console.warn(`Decompression failed:`,t),e}}async gzipCompress(e){if(!(`CompressionStream`in window))return e;let t=new CompressionStream(`gzip`),n=t.writable.getWriter(),r=t.readable.getReader(),i=[];n.write(new TextEncoder().encode(e)),n.close();let a=await r.read();for(;!a.done;)i.push(a.value),a=await r.read();let o=new Uint8Array(i.reduce((e,t)=>e+t.length,0)),s=0;for(let e of i)o.set(e,s),s+=e.length;return btoa(String.fromCharCode(...o))}async gzipDecompress(e){if(!(`DecompressionStream`in window))return e;let t=new DecompressionStream(`gzip`),n=t.writable.getWriter(),r=t.readable.getReader(),i=atob(e),a=new Uint8Array(i.length);for(let e=0;e<i.length;e++)a[e]=i.charCodeAt(e);n.write(a),n.close();let o=[],s=await r.read();for(;!s.done;)o.push(s.value),s=await r.read();let c=new Uint8Array(o.reduce((e,t)=>e+t.length,0)),l=0;for(let e of o)c.set(e,l),l+=e.length;return new TextDecoder().decode(c)}recordAccessTime(e){this.accessTimes.push(e),this.accessTimes.length>100&&this.accessTimes.shift(),this.stats.averageAccessTime=this.accessTimes.reduce((e,t)=>e+t,0)/this.accessTimes.length}updateHitRate(){let e=this.stats.hits+this.stats.misses;this.stats.hitRate=e>0?this.stats.hits/e*100:0}updateStats(){this.stats.entryCount=this.cache.size;let e=0;for(let t of this.cache.values())e+=t.size;this.stats.memoryUsage=e/(1024*1024),this.updateHitRate()}dispose(){this.gcTimer&&(clearInterval(this.gcTimer),this.gcTimer=void 0),this.clear()}};const Z=new Ue;var We=class{config;userStats=new Map;globalStats={totalUsers:0,activeUsers:0,totalRequests:0,totalCost:0,averageResponseTime:0,errorRate:0,concurrentRequests:0};cleanupTimer;persistenceTimer;requestTimes=[];constructor(e){this.config={analysisPerHour:100,analysisPerDay:500,generationPerHour:20,generationPerDay:50,maxConcurrentRequests:100,maxConcurrentPerUser:3,dailyCostLimit:10,monthlyCostLimit:250,burstAnalysisLimit:10,burstGenerationLimit:5,burstWindow:60*1e3,analysisRecoveryRate:2,generationRecoveryRate:.5,premiumMultiplier:3,proMultiplier:10,...e},this.initializeFromStorage(),this.startCleanupTimer(),this.startPersistenceTimer()}async checkAnalysisLimit(e,t=`free`){let n=this.getUserStats(e,t),r=this.getEffectiveLimits(t);if(this.updateUsageCounts(n),n.concurrentRequests>=r.maxConcurrentPerUser)return{allowed:!1,remainingRequests:0,resetTime:Date.now()+6e4,retryAfter:6e4,reason:`Too many concurrent requests`,costRemaining:this.getRemainingCost(n)};if(this.globalStats.concurrentRequests>=this.config.maxConcurrentRequests)return{allowed:!1,remainingRequests:0,resetTime:Date.now()+3e4,retryAfter:3e4,reason:`System busy, please try again later`,costRemaining:this.getRemainingCost(n)};if(n.burstUsage.analysis>=r.burstAnalysisLimit){let e=Date.now()-n.burstUsage.windowStart;if(e<this.config.burstWindow)return{allowed:!1,remainingRequests:0,resetTime:n.burstUsage.windowStart+this.config.burstWindow,retryAfter:this.config.burstWindow-e,reason:`Burst limit exceeded`,costRemaining:this.getRemainingCost(n)}}return n.analysisCount.hour>=r.analysisPerHour?{allowed:!1,remainingRequests:0,resetTime:n.analysisCount.hourResetTime,retryAfter:n.analysisCount.hourResetTime-Date.now(),reason:`Hourly limit exceeded`,costRemaining:this.getRemainingCost(n)}:n.analysisCount.day>=r.analysisPerDay?{allowed:!1,remainingRequests:0,resetTime:n.analysisCount.dayResetTime,retryAfter:n.analysisCount.dayResetTime-Date.now(),reason:`Daily limit exceeded`,costRemaining:this.getRemainingCost(n)}:{allowed:!0,remainingRequests:Math.min(r.analysisPerHour-n.analysisCount.hour,r.analysisPerDay-n.analysisCount.day),resetTime:n.analysisCount.hourResetTime,costRemaining:this.getRemainingCost(n)}}async checkGenerationLimit(e,t,n=`free`){let r=this.getUserStats(e,n),i=this.getEffectiveLimits(n);if(this.updateUsageCounts(r),r.costUsage.daily+t>i.dailyCostLimit)return{allowed:!1,remainingRequests:0,resetTime:r.costUsage.dailyResetTime,retryAfter:r.costUsage.dailyResetTime-Date.now(),reason:`Daily cost limit exceeded`,costRemaining:Math.max(0,i.dailyCostLimit-r.costUsage.daily)};if(r.costUsage.monthly+t>i.monthlyCostLimit)return{allowed:!1,remainingRequests:0,resetTime:r.costUsage.monthlyResetTime,retryAfter:r.costUsage.monthlyResetTime-Date.now(),reason:`Monthly cost limit exceeded`,costRemaining:Math.max(0,i.monthlyCostLimit-r.costUsage.monthly)};if(r.concurrentRequests>=i.maxConcurrentPerUser)return{allowed:!1,remainingRequests:0,resetTime:Date.now()+6e4,retryAfter:6e4,reason:`Too many concurrent requests`,costRemaining:this.getRemainingCost(r)};if(r.burstUsage.generation>=i.burstGenerationLimit){let e=Date.now()-r.burstUsage.windowStart;if(e<this.config.burstWindow)return{allowed:!1,remainingRequests:0,resetTime:r.burstUsage.windowStart+this.config.burstWindow,retryAfter:this.config.burstWindow-e,reason:`Burst limit exceeded`,costRemaining:this.getRemainingCost(r)}}return r.generationCount.hour>=i.generationPerHour?{allowed:!1,remainingRequests:0,resetTime:r.generationCount.hourResetTime,retryAfter:r.generationCount.hourResetTime-Date.now(),reason:`Hourly limit exceeded`,costRemaining:this.getRemainingCost(r)}:r.generationCount.day>=i.generationPerDay?{allowed:!1,remainingRequests:0,resetTime:r.generationCount.dayResetTime,retryAfter:r.generationCount.dayResetTime-Date.now(),reason:`Daily limit exceeded`,costRemaining:this.getRemainingCost(r)}:{allowed:!0,remainingRequests:Math.min(i.generationPerHour-r.generationCount.hour,i.generationPerDay-r.generationCount.day),resetTime:r.generationCount.hourResetTime,costRemaining:this.getRemainingCost(r)}}recordAnalysisRequest(e,t){let n=this.getUserStats(e);n.analysisCount.hour++,n.analysisCount.day++,n.concurrentRequests++;let r=Date.now();r-n.burstUsage.windowStart>this.config.burstWindow?(n.burstUsage.windowStart=r,n.burstUsage.analysis=1):n.burstUsage.analysis++,this.globalStats.totalRequests++,this.globalStats.concurrentRequests++,this.recordResponseTime(t),setTimeout(()=>{n.concurrentRequests=Math.max(0,n.concurrentRequests-1),this.globalStats.concurrentRequests=Math.max(0,this.globalStats.concurrentRequests-1)},1e3)}recordGenerationRequest(e,t,n){let r=this.getUserStats(e);r.generationCount.hour++,r.generationCount.day++,r.concurrentRequests++,r.costUsage.daily+=t,r.costUsage.monthly+=t;let i=Date.now();i-r.burstUsage.windowStart>this.config.burstWindow?(r.burstUsage.windowStart=i,r.burstUsage.generation=1):r.burstUsage.generation++,this.globalStats.totalRequests++,this.globalStats.totalCost+=t,this.globalStats.concurrentRequests++,this.recordResponseTime(n),setTimeout(()=>{r.concurrentRequests=Math.max(0,r.concurrentRequests-1),this.globalStats.concurrentRequests=Math.max(0,this.globalStats.concurrentRequests-1)},n)}recordFailedRequest(e,t){let n=this.getUserStats(e);n.violations.push({count:1,lastViolation:Date.now(),type:t}),n.violations.length>10&&n.violations.shift(),this.globalStats.totalRequests++,this.updateErrorRate()}getUserUsageStats(e){let t=this.getUserStats(e),n=this.getEffectiveLimits(t.userTier);return{userId:e,analysisCount:t.analysisCount.hour+t.analysisCount.day,generationCount:t.generationCount.hour+t.generationCount.day,totalCost:t.costUsage.daily+t.costUsage.monthly,lastUsed:new Date,dailyLimits:{analysis:n.analysisPerDay,generation:n.generationPerDay,cost:n.dailyCostLimit}}}getGlobalStats(){return{...this.globalStats}}resetUserLimits(e){let t=this.getUserStats(e),n=Date.now();t.analysisCount={hour:0,day:0,hourResetTime:n+60*60*1e3,dayResetTime:n+24*60*60*1e3},t.generationCount={hour:0,day:0,hourResetTime:n+60*60*1e3,dayResetTime:n+24*60*60*1e3},t.costUsage={daily:0,monthly:0,dailyResetTime:n+24*60*60*1e3,monthlyResetTime:n+30*24*60*60*1e3},t.burstUsage={analysis:0,generation:0,windowStart:n},t.concurrentRequests=0,t.violations=[]}updateConfig(e){this.config={...this.config,...e}}getEffectiveLimits(e){let t=e===`pro`?this.config.proMultiplier:e===`premium`?this.config.premiumMultiplier:1;return{...this.config,analysisPerHour:Math.floor(this.config.analysisPerHour*t),analysisPerDay:Math.floor(this.config.analysisPerDay*t),generationPerHour:Math.floor(this.config.generationPerHour*t),generationPerDay:Math.floor(this.config.generationPerDay*t),dailyCostLimit:this.config.dailyCostLimit*t,monthlyCostLimit:this.config.monthlyCostLimit*t,burstAnalysisLimit:Math.floor(this.config.burstAnalysisLimit*t),burstGenerationLimit:Math.floor(this.config.burstGenerationLimit*t),maxConcurrentPerUser:Math.floor(this.config.maxConcurrentPerUser*t)}}getUserStats(e,t=`free`){let n=this.userStats.get(e);if(!n){let r=Date.now();n={userId:e,userTier:t,analysisCount:{hour:0,day:0,hourResetTime:r+60*60*1e3,dayResetTime:r+24*60*60*1e3},generationCount:{hour:0,day:0,hourResetTime:r+60*60*1e3,dayResetTime:r+24*60*60*1e3},costUsage:{daily:0,monthly:0,dailyResetTime:r+24*60*60*1e3,monthlyResetTime:r+30*24*60*60*1e3},burstUsage:{analysis:0,generation:0,windowStart:r},concurrentRequests:0,violations:[]},this.userStats.set(e,n),this.globalStats.totalUsers++}return n}updateUsageCounts(e){let t=Date.now();t>e.analysisCount.hourResetTime&&(e.analysisCount.hour=0,e.analysisCount.hourResetTime=t+60*60*1e3),t>e.generationCount.hourResetTime&&(e.generationCount.hour=0,e.generationCount.hourResetTime=t+60*60*1e3),t>e.analysisCount.dayResetTime&&(e.analysisCount.day=0,e.analysisCount.dayResetTime=t+24*60*60*1e3),t>e.generationCount.dayResetTime&&(e.generationCount.day=0,e.generationCount.dayResetTime=t+24*60*60*1e3),t>e.costUsage.dailyResetTime&&(e.costUsage.daily=0,e.costUsage.dailyResetTime=t+24*60*60*1e3),t>e.costUsage.monthlyResetTime&&(e.costUsage.monthly=0,e.costUsage.monthlyResetTime=t+30*24*60*60*1e3)}getRemainingCost(e){let t=this.getEffectiveLimits(e.userTier);return Math.max(0,t.dailyCostLimit-e.costUsage.daily)}recordResponseTime(e){this.requestTimes.push(e),this.requestTimes.length>100&&this.requestTimes.shift(),this.globalStats.averageResponseTime=this.requestTimes.reduce((e,t)=>e+t,0)/this.requestTimes.length}updateErrorRate(){let e=Array.from(this.userStats.values()).reduce((e,t)=>e+t.violations.length,0);this.globalStats.errorRate=this.globalStats.totalRequests>0?e/this.globalStats.totalRequests*100:0}initializeFromStorage(){try{let e=localStorage.getItem(`tinderop-rate-limiter`);if(e){let t=JSON.parse(e);for(let[e,n]of Object.entries(t.userStats||{}))this.userStats.set(e,n);t.globalStats&&(this.globalStats={...this.globalStats,...t.globalStats})}}catch(e){console.warn(`Failed to initialize rate limiter from storage:`,e)}}persistToStorage(){try{let e={userStats:Object.fromEntries(this.userStats.entries()),globalStats:this.globalStats};localStorage.setItem(`tinderop-rate-limiter`,JSON.stringify(e))}catch(e){console.warn(`Failed to persist rate limiter to storage:`,e)}}startCleanupTimer(){this.cleanupTimer=window.setInterval(()=>{this.cleanupExpiredStats()},60*60*1e3)}startPersistenceTimer(){this.persistenceTimer=window.setInterval(()=>{this.persistToStorage()},5*60*1e3)}cleanupExpiredStats(){let e=Date.now(),t=[];for(let[n,r]of this.userStats.entries()){let i=Math.max(r.analysisCount.dayResetTime-24*60*60*1e3,r.generationCount.dayResetTime-24*60*60*1e3);e-i>24*60*60*1e3&&t.push(n)}for(let e of t)this.userStats.delete(e),this.globalStats.totalUsers--}dispose(){this.cleanupTimer&&clearInterval(this.cleanupTimer),this.persistenceTimer&&clearInterval(this.persistenceTimer),this.persistToStorage(),this.userStats.clear()}};const Q=new We;var $=class{apiKey;isProcessing=!1;currentUserId;constructor(){if(this.apiKey=`********************************************************************************************************************************************************************`,!this.apiKey)throw console.error(`🔑 VITE_OPENAI_API_KEY not found in environment variables`),Error(`OpenAI API key is required. Please set VITE_OPENAI_API_KEY in your .env file`);console.log(`🔑 OpenAI API key loaded successfully`),console.log(`🔑 API Key preview: ${this.apiKey.substring(0,10)}...`)}async analyzeImageForEditing(e,t={}){if(this.isProcessing)throw Error(`Analysis already in progress`);this.isProcessing=!0;let n=Date.now();try{let r=Z.generateCacheKey(e.imageData,e.preferences),i=await Z.get(r);if(i)return console.log(`🎯 Cache hit for editing analysis`),t.onComplete?.(i),i;let a=await X.validateBase64Image(e.imageData,e.fileName);if(!a.valid)throw Error(`Security validation failed: ${a.errors.join(`, `)}`);let o=this.currentUserId||`anonymous`,s=await Q.checkAnalysisLimit(o);if(!s.allowed)throw Error(`Rate limit exceeded: ${s.reason}`);let c={fileName:e.fileName,originalImage:e.imageData,canEdit:!0,editingRecommendations:[],quickFixes:[],transformativeEdits:[],totalRecommendations:0,estimatedTotalCost:0,processingTime:0,confidence:0,processed:!1},l=await this.executeEditingAnalysis(e,(r,i,a)=>{let o={fileName:e.fileName,currentStep:r,totalSteps:k.length,stepName:i,progress:a,estimatedTimeRemaining:this.estimateTimeRemaining(a,n)};t.onProgress?.(o)}),u=this.categorizeRecommendations(l);return c.editingRecommendations=u.all,c.quickFixes=u.quickFixes,c.transformativeEdits=u.transformative,c.totalRecommendations=l.length,c.estimatedTotalCost=this.calculateTotalCost(l),c.confidence=this.calculateOverallConfidence(l),c.processingTime=Date.now()-n,c.processed=!0,await Z.set(r,c),Q.recordAnalysisRequest(o,c.processingTime),t.onComplete?.(c),c}catch(r){let i=r instanceof Error?r.message:`Unknown error occurred`;return console.error(`❌ Image editing analysis failed:`,r),this.currentUserId&&Q.recordFailedRequest(this.currentUserId,i),t.onError?.(i),{fileName:e.fileName,originalImage:e.imageData,canEdit:!1,editingRecommendations:[],quickFixes:[],transformativeEdits:[],totalRecommendations:0,estimatedTotalCost:0,processingTime:Date.now()-n,confidence:0,processed:!1,error:i}}finally{this.isProcessing=!1}}async generateEditedImage(e,t=`anonymous`){try{let n=Date.now(),r=this.estimateGenerationCost(e),i=await Q.checkGenerationLimit(t,r);if(!i.allowed)throw Error(`Rate limit exceeded: ${i.reason}`);let a=this.generateEditingPrompt(e.recommendation),o=await this.callOpenAIImageAPI(e.originalImage,a,e.quality,e.inputFidelity),s=Date.now()-n,c=this.calculateActualCost(e,s);return Q.recordGenerationRequest(t,c,s),{success:!0,editedImage:o,cost:c,processingTime:s}}catch(e){let n=e instanceof Error?e.message:`Unknown error occurred`;return console.error(`❌ Image generation failed:`,e),Q.recordFailedRequest(t,n),{success:!1,cost:0,processingTime:Date.now()-Date.now(),error:n}}}async executeEditingAnalysis(e,t){let n=[],r=k;for(let i=0;i<r.length;i++){let a=r[i];t?.(a.id,a.name,i/r.length*100);let o=Date.now();try{console.log(`🔍 Starting editing analysis step ${a.id}: ${a.name}`);let s=await this.executeEditingStep(a.id,a.name,e);n.push(...s);let c=Date.now()-o;console.log(`📊 Editing Step ${a.id} (${a.name}):`,{recommendations:s.length,processingTime:`${c}ms`}),t?.(a.id,a.name,(i+1)/r.length*100)}catch(e){console.error(`❌ Error in step ${a.id} (${a.name}):`,e)}}return n}async executeEditingStep(e,t,n){let r=this.getEditingStepPrompt(e,n);console.log(`🤖 Calling OpenAI API for editing step ${e}`);let{text:i}=await _({model:Ve(`gpt-4o`),messages:[{role:`user`,content:[{type:`text`,text:r},{type:`image`,image:n.imageData}]}],maxTokens:2e3,temperature:.3});return this.parseEditingRecommendations(i,e)}getEditingStepPrompt(e,t){let n=`You are an expert photo editor specializing in dating profile optimization. Analyze this image and provide specific, actionable editing recommendations that will improve its dating appeal while maintaining the subject's natural appearance.

CRITICAL REQUIREMENTS:
- Never suggest changing how the subject looks (no facial alterations, body modifications)
- Focus on: lighting, composition, background, clothing, pose, camera angle, colors, style
- Provide specific, actionable editing instructions
- Estimate impact score (1-10) and difficulty level
- Consider cost-effectiveness

Response format (JSON):
{
  "recommendations": [
    {
      "type": "lighting|background|composition|color|style|clothing|expression|posture",
      "category": "quick_fix|advanced_edit|style_enhancement|composition_improvement",
      "title": "Short descriptive title",
      "description": "Detailed description of what to change",
      "impactScore": 1-10,
      "difficulty": "easy|medium|hard",
      "estimatedTime": minutes,
      "instructions": ["Step 1", "Step 2", "Step 3"],
      "toolsRecommended": ["Suggested editing app/tool"],
      "estimatedCost": 0.05-2.00,
      "confidence": 0-100
    }
  ]
}`,r={1:`${n}

STEP 1: TECHNICAL ASSESSMENT & QUICK FIXES
Analyze technical quality and identify immediate improvements:
- Image sharpness and clarity issues
- Basic lighting corrections (exposure, shadows, highlights)
- Color balance and saturation adjustments
- Cropping and framing improvements
- Basic noise reduction or enhancement needs

Focus on: Quick, low-cost fixes that provide immediate visual improvement.`,2:`${n}

STEP 2: LIGHTING OPTIMIZATION
Analyze lighting conditions and improvement opportunities:
- Harsh shadows or unflattering lighting angles
- Exposure issues (overexposed/underexposed areas)
- Color temperature problems (too warm/cool)
- Lighting direction and quality
- Background lighting vs subject lighting balance

Focus on: Lighting adjustments that enhance the subject's appearance without changing facial features.`,3:`${n}

STEP 3: COMPOSITION & FRAMING
Analyze composition and suggest improvements:
- Rule of thirds application
- Background elements and distractions
- Framing and crop optimization
- Angle and perspective improvements
- Visual balance and focal points

Focus on: Repositioning, cropping, and angle adjustments that improve visual appeal.`,4:`${n}

STEP 4: COLOR & STYLE ENHANCEMENT
Analyze color palette and style opportunities:
- Color grading and tone adjustments
- Clothing color optimization
- Background color harmony
- Seasonal and style appropriateness
- Filter and preset applications

Focus on: Color and style improvements that enhance overall aesthetic appeal.`,5:`${n}

STEP 5: ADVANCED EDITING OPPORTUNITIES
Identify sophisticated editing possibilities:
- Background replacement or enhancement
- Advanced lighting techniques
- Professional-grade color grading
- Artistic style applications
- Premium editing techniques

Focus on: High-impact professional edits that justify their cost.`};return r[e]||r[1]}parseEditingRecommendations(e,t){try{let n=e.match(/\{[\s\S]*\}/);if(!n)throw Error(`No JSON found in response`);let r=JSON.parse(n[0]),i=Array.isArray(r.recommendations)?r.recommendations:[];return i.map((e,n)=>({id:`${t}-${n}`,type:e.type||`enhancement`,category:e.category||`quick_fix`,title:e.title||`Photo Enhancement`,description:e.description||`Improve photo quality`,impactScore:Math.max(1,Math.min(10,parseInt(e.impactScore)||5)),difficulty:e.difficulty||`medium`,estimatedTime:parseInt(e.estimatedTime)||5,instructions:Array.isArray(e.instructions)?e.instructions.slice(0,5):[`Apply enhancement`],toolsRecommended:Array.isArray(e.toolsRecommended)?e.toolsRecommended.slice(0,3):[`Photo editing app`],estimatedCost:Math.max(.05,Math.min(2,parseFloat(e.estimatedCost)||.25)),confidence:Math.max(0,Math.min(100,parseInt(e.confidence)||80)),applied:!1,dismissed:!1,createdAt:new Date}))}catch(t){return console.error(`🔧 Failed to parse editing recommendations:`,t),console.log(`📝 Raw response:`,e),[]}}categorizeRecommendations(e){let t=e.filter(e=>e.category===`quick_fix`&&e.estimatedCost<=.5),n=e.filter(e=>e.category===`advanced_edit`&&e.impactScore>=7);return{all:e,quickFixes:t,transformative:n}}calculateTotalCost(e){return e.reduce((e,t)=>e+t.estimatedCost,0)}calculateOverallConfidence(e){if(e.length===0)return 0;let t=e.reduce((e,t)=>e+t.confidence,0);return Math.round(t/e.length)}generateEditingPrompt(e){let t=`Apply professional photo editing to improve this dating profile image. `,n={lighting:`Enhance lighting to be more flattering and natural. ${e.description}`,background:`Improve or replace the background. ${e.description}`,composition:`Optimize composition and framing. ${e.description}`,color:`Adjust colors for better aesthetic appeal. ${e.description}`,style:`Apply style enhancements. ${e.description}`,clothing:`Optimize clothing appearance. ${e.description}`,expression:`Enhance expression and mood. ${e.description}`,posture:`Improve posture and body language. ${e.description}`,enhancement:`Apply general enhancements. ${e.description}`,object_removal:`Remove unwanted objects or distractions. ${e.description}`},r=n[e.type]||n.enhancement;return`${t}${r} Maintain the subject's natural appearance while improving the overall quality and appeal of the image.`}async callOpenAIImageAPI(e,t,n,r){return console.log(`🎨 Calling OpenAI GPT-Image-1 API`,{prompt:t.substring(0,100)+`...`,quality:n,inputFidelity:r}),await new Promise(e=>setTimeout(e,2e3)),e}estimateGenerationCost(e){let t={standard:.04,high:.17,ultra:.34},n={standard:0,high:.062};return t[e.quality]+n[e.inputFidelity]}calculateActualCost(e,t){let n=this.estimateGenerationCost(e),r=Math.max(1,t/1e4);return Math.round(n*r*100)/100}estimateTimeRemaining(e,t){if(e===0)return 0;let n=Date.now()-t,r=n/(e/100),i=r-n;return Math.max(0,Math.round(i/1e3))}setUserId(e){this.currentUserId=e}isCurrentlyProcessing(){return this.isProcessing}getServiceStats(){return{cache:Z.getStats(),rateLimiter:Q.getGlobalStats(),isProcessing:this.isProcessing,apiKeyConfigured:!!this.apiKey}}};const Ge=new $;export{$ as ImageEditingService,Ge as imageEditingService,X as securityService};