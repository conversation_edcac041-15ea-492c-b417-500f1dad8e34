import{Link as e,<PERSON><PERSON><PERSON><PERSON><PERSON> as t,__toESM as n,require_jsx_runtime as r,useUser as i}from"./main-B4G73TvM.js";import{Card as a,CardContent as o,CardDescription as s,CardHeader as c,<PERSON><PERSON><PERSON>le as l}from"./card-Bzfaa5B9.js";import{Button as u,createLucideIcon as d}from"./createLucideIcon-JB7IMeGf.js";import{Badge as f}from"./badge-B_rsQKkv.js";import{Camera as p}from"./camera-P6IreDGA.js";import{Heart as m,Star as h}from"./star-Dm2nqXPr.js";import{Settings as g}from"./settings-Tged_CGs.js";import{TrendingUp as _}from"./trending-up-BvkHvnfP.js";import{ProtectedRoute as v}from"./ProtectedRoute-DIpB4JWB.js";const y=d(`ChartColumn`,[[`path`,{d:`M3 3v16a2 2 0 0 0 2 2h16`,key:`c24i48`}],[`path`,{d:`M18 17V9`,key:`2bz60n`}],[`path`,{d:`M13 17V5`,key:`1frdt8`}],[`path`,{d:`M8 17v-3`,key:`17ska0`}]]),b=d(`FileText`,[[`path`,{d:`M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z`,key:`1rqfz7`}],[`path`,{d:`M14 2v4a2 2 0 0 0 2 2h4`,key:`tnqrlb`}],[`path`,{d:`M10 9H8`,key:`b1mrlr`}],[`path`,{d:`M16 13H8`,key:`t4e002`}],[`path`,{d:`M16 17H8`,key:`z1uh3a`}]]),x=d(`MessageCircle`,[[`path`,{d:`M7.9 20A9 9 0 1 0 4 16.1L2 22Z`,key:`vv11sd`}]]);var S=n(r());function C(){let{user:n}=i();return(0,S.jsx)(`div`,{className:`min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6`,children:(0,S.jsxs)(`div`,{className:`max-w-7xl mx-auto`,children:[(0,S.jsxs)(`div`,{className:`flex items-center justify-between mb-8`,children:[(0,S.jsxs)(`div`,{children:[(0,S.jsxs)(`h1`,{className:`text-3xl font-bold text-slate-900`,children:[`Welcome back, `,n?.firstName||`User`,`!`]}),(0,S.jsx)(`p`,{className:`text-slate-600 mt-1`,children:`Track your dating profile performance and optimize for better matches`})]}),(0,S.jsx)(t,{}),(0,S.jsxs)(u,{variant:`secondary`,size:`sm`,children:[(0,S.jsx)(g,{className:`h-4 w-4 mr-2`}),`Settings`]})]}),(0,S.jsxs)(`div`,{className:`grid grid-cols-1 md:grid-cols-4 gap-6 mb-8`,children:[(0,S.jsxs)(a,{children:[(0,S.jsxs)(c,{className:`flex flex-row items-center justify-between space-y-0 pb-2`,children:[(0,S.jsx)(l,{className:`text-sm font-medium`,children:`Profile Score`}),(0,S.jsx)(h,{className:`h-4 w-4 text-amber-500`})]}),(0,S.jsxs)(o,{children:[(0,S.jsx)(`div`,{className:`text-2xl font-bold`,children:`8.2`}),(0,S.jsx)(`p`,{className:`text-xs text-muted-foreground`,children:`+0.5 from last week`})]})]}),(0,S.jsxs)(a,{children:[(0,S.jsxs)(c,{className:`flex flex-row items-center justify-between space-y-0 pb-2`,children:[(0,S.jsx)(l,{className:`text-sm font-medium`,children:`Matches`}),(0,S.jsx)(m,{className:`h-4 w-4 text-red-500`})]}),(0,S.jsxs)(o,{children:[(0,S.jsx)(`div`,{className:`text-2xl font-bold`,children:`24`}),(0,S.jsx)(`p`,{className:`text-xs text-muted-foreground`,children:`+12% from last week`})]})]}),(0,S.jsxs)(a,{children:[(0,S.jsxs)(c,{className:`flex flex-row items-center justify-between space-y-0 pb-2`,children:[(0,S.jsx)(l,{className:`text-sm font-medium`,children:`Messages`}),(0,S.jsx)(x,{className:`h-4 w-4 text-blue-500`})]}),(0,S.jsxs)(o,{children:[(0,S.jsx)(`div`,{className:`text-2xl font-bold`,children:`156`}),(0,S.jsx)(`p`,{className:`text-xs text-muted-foreground`,children:`+8% from last week`})]})]}),(0,S.jsxs)(a,{children:[(0,S.jsxs)(c,{className:`flex flex-row items-center justify-between space-y-0 pb-2`,children:[(0,S.jsx)(l,{className:`text-sm font-medium`,children:`Profile Views`}),(0,S.jsx)(_,{className:`h-4 w-4 text-green-500`})]}),(0,S.jsxs)(o,{children:[(0,S.jsx)(`div`,{className:`text-2xl font-bold`,children:`342`}),(0,S.jsx)(`p`,{className:`text-xs text-muted-foreground`,children:`+25% from last week`})]})]})]}),(0,S.jsxs)(`div`,{className:`grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8`,children:[(0,S.jsxs)(a,{children:[(0,S.jsxs)(c,{children:[(0,S.jsxs)(l,{className:`flex items-center gap-2`,children:[(0,S.jsx)(p,{className:`h-5 w-5 text-flame-red`}),`Photo Analysis`]}),(0,S.jsx)(s,{children:`Optimize your photos for maximum appeal`})]}),(0,S.jsxs)(o,{className:`space-y-4`,children:[(0,S.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,S.jsx)(`span`,{className:`text-sm`,children:`Last analysis`}),(0,S.jsx)(f,{variant:`secondary`,children:`3 days ago`})]}),(0,S.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,S.jsx)(`span`,{className:`text-sm`,children:`Photos analyzed`}),(0,S.jsx)(`span`,{className:`text-sm font-medium`,children:`8/10`})]}),(0,S.jsx)(u,{asChild:!0,className:`w-full bg-gradient-to-r from-flame-red to-sparks-pink hover:opacity-90`,children:(0,S.jsx)(e,{to:`/image-analyzer`,children:`Analyze New Photos`})})]})]}),(0,S.jsxs)(a,{children:[(0,S.jsxs)(c,{children:[(0,S.jsxs)(l,{className:`flex items-center gap-2`,children:[(0,S.jsx)(b,{className:`h-5 w-5 text-flame-red`}),`Bio Optimization`]}),(0,S.jsx)(s,{children:`Craft the perfect bio to attract matches`})]}),(0,S.jsxs)(o,{className:`space-y-4`,children:[(0,S.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,S.jsx)(`span`,{className:`text-sm`,children:`Bio score`}),(0,S.jsx)(f,{variant:`secondary`,children:`7.8/10`})]}),(0,S.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,S.jsx)(`span`,{className:`text-sm`,children:`Last updated`}),(0,S.jsx)(`span`,{className:`text-sm font-medium`,children:`1 week ago`})]}),(0,S.jsx)(u,{asChild:!0,className:`w-full bg-gradient-to-r from-flame-red to-sparks-pink hover:opacity-90`,children:(0,S.jsx)(e,{to:`/bio-analyzer`,children:`Optimize Bio`})})]})]})]}),(0,S.jsxs)(a,{children:[(0,S.jsxs)(c,{children:[(0,S.jsxs)(l,{className:`flex items-center gap-2`,children:[(0,S.jsx)(y,{className:`h-5 w-5`}),`Recent Activity`]}),(0,S.jsx)(s,{children:`Your latest profile improvements and results`})]}),(0,S.jsx)(o,{children:(0,S.jsxs)(`div`,{className:`space-y-4`,children:[(0,S.jsxs)(`div`,{className:`flex items-center justify-between border-b pb-2`,children:[(0,S.jsxs)(`div`,{className:`flex items-center gap-3`,children:[(0,S.jsx)(`div`,{className:`h-2 w-2 bg-green-500 rounded-full`}),(0,S.jsx)(`span`,{className:`text-sm`,children:`Photo analysis completed`})]}),(0,S.jsx)(`span`,{className:`text-xs text-muted-foreground`,children:`2 hours ago`})]}),(0,S.jsxs)(`div`,{className:`flex items-center justify-between border-b pb-2`,children:[(0,S.jsxs)(`div`,{className:`flex items-center gap-3`,children:[(0,S.jsx)(`div`,{className:`h-2 w-2 bg-blue-500 rounded-full`}),(0,S.jsx)(`span`,{className:`text-sm`,children:`Bio updated with AI suggestions`})]}),(0,S.jsx)(`span`,{className:`text-xs text-muted-foreground`,children:`1 day ago`})]}),(0,S.jsxs)(`div`,{className:`flex items-center justify-between border-b pb-2`,children:[(0,S.jsxs)(`div`,{className:`flex items-center gap-3`,children:[(0,S.jsx)(`div`,{className:`h-2 w-2 bg-amber-500 rounded-full`}),(0,S.jsx)(`span`,{className:`text-sm`,children:`New match milestone reached`})]}),(0,S.jsx)(`span`,{className:`text-xs text-muted-foreground`,children:`3 days ago`})]}),(0,S.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,S.jsxs)(`div`,{className:`flex items-center gap-3`,children:[(0,S.jsx)(`div`,{className:`h-2 w-2 bg-purple-500 rounded-full`}),(0,S.jsx)(`span`,{className:`text-sm`,children:`Profile optimization suggestions received`})]}),(0,S.jsx)(`span`,{className:`text-xs text-muted-foreground`,children:`1 week ago`})]})]})})]}),(0,S.jsx)(`div`,{className:`mt-8 flex justify-center`,children:(0,S.jsx)(u,{asChild:!0,variant:`secondary`,className:`flex items-center gap-2`,children:(0,S.jsxs)(e,{to:`/account-settings`,children:[(0,S.jsx)(g,{className:`h-4 w-4`}),`Account Settings`]})})})]})})}const w=function(){return(0,S.jsx)(v,{children:(0,S.jsx)(C,{})})};export{w as component};