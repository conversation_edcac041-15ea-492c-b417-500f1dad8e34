import{UserProfile as e}from"./uiComponents-BgWMuAlY.js";import{Link as t,__toESM as n,cn as r,require_jsx_runtime as i,require_react as a,useUser as o}from"./main-B4G73TvM.js";import{Card as s,CardContent as c,CardDescription as l,CardHeader as u,CardTitle as d}from"./card-Bzfaa5B9.js";import{Button as f,createLucideIcon as p}from"./createLucideIcon-JB7IMeGf.js";import{Badge as m}from"./badge-B_rsQKkv.js";import{Primitive as h,Trash2 as g}from"./trash-2-CkLpzZy2.js";import{ArrowLeft as _}from"./arrow-left-CrPLcqAC.js";import{Download as v}from"./download-D5v-g3Ed.js";import{Shield as y}from"./shield-Ct2yHJj1.js";import{ProtectedRoute as b}from"./ProtectedRoute-DIpB4JWB.js";var x=n(a(),1),S=n(i(),1),C=`Separator`,w=`horizontal`,T=[`horizontal`,`vertical`],E=x.forwardRef((e,t)=>{let{decorative:n,orientation:r=w,...i}=e,a=D(r)?r:w,o=a===`vertical`?a:void 0,s=n?{role:`none`}:{"aria-orientation":o,role:`separator`};return(0,S.jsx)(h.div,{"data-orientation":a,...s,...i,ref:t})});E.displayName=C;function D(e){return T.includes(e)}var O=E;const k=x.forwardRef(({className:e,orientation:t=`horizontal`,decorative:n=!0,...i},a)=>(0,S.jsx)(O,{ref:a,decorative:n,orientation:t,className:r(`shrink-0 bg-border`,t===`horizontal`?`h-[1px] w-full`:`h-full w-[1px]`,e),...i}));k.displayName=O.displayName;const A=p(`Bell`,[[`path`,{d:`M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9`,key:`1qo2s2`}],[`path`,{d:`M10.3 21a1.94 1.94 0 0 0 3.4 0`,key:`qgo35s`}]]),j=p(`CreditCard`,[[`rect`,{width:`20`,height:`14`,x:`2`,y:`5`,rx:`2`,key:`ynyp8z`}],[`line`,{x1:`2`,x2:`22`,y1:`10`,y2:`10`,key:`1b3vmo`}]]),M=p(`TriangleAlert`,[[`path`,{d:`m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3`,key:`wmoenq`}],[`path`,{d:`M12 9v4`,key:`juzpu7`}],[`path`,{d:`M12 17h.01`,key:`p32p05`}]]);function N(){let{user:n}=o();return(0,S.jsx)(`div`,{className:`min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6`,children:(0,S.jsxs)(`div`,{className:`max-w-4xl mx-auto`,children:[(0,S.jsxs)(`div`,{className:`flex items-center gap-4 mb-8`,children:[(0,S.jsx)(f,{asChild:!0,variant:`ghost`,size:`sm`,children:(0,S.jsxs)(t,{to:`/dashboard`,className:`flex items-center gap-2`,children:[(0,S.jsx)(_,{className:`h-4 w-4`}),`Back to Dashboard`]})}),(0,S.jsxs)(`div`,{children:[(0,S.jsx)(`h1`,{className:`text-3xl font-bold text-slate-900`,children:`Account Settings`}),(0,S.jsx)(`p`,{className:`text-slate-600 mt-1`,children:`Manage your account preferences and security settings`})]})]}),(0,S.jsxs)(`div`,{className:`grid grid-cols-1 lg:grid-cols-3 gap-8`,children:[(0,S.jsxs)(`div`,{className:`lg:col-span-2 space-y-6`,children:[(0,S.jsxs)(s,{children:[(0,S.jsxs)(u,{children:[(0,S.jsx)(d,{children:`Profile Information`}),(0,S.jsx)(l,{children:`Update your personal information and profile details`})]}),(0,S.jsxs)(c,{className:`p-6`,children:[(0,S.jsx)(e,{appearance:{elements:{rootBox:`w-full max-w-full`,card:`shadow-none border-none rounded-none w-full max-w-full`,navbarMobileMenuButton:`hidden`}}}),(0,S.jsx)(`div`,{className:`space-y-4`,children:(0,S.jsxs)(`div`,{className:`text-center py-8`,children:[(0,S.jsx)(`p`,{className:`text-muted-foreground`,children:`User profile management temporarily disabled.`}),(0,S.jsx)(`p`,{className:`text-sm text-muted-foreground mt-2`,children:`Authentication system is currently commented out for development.`})]})})]})]}),(0,S.jsxs)(s,{children:[(0,S.jsxs)(u,{children:[(0,S.jsxs)(d,{className:`flex items-center gap-2`,children:[(0,S.jsx)(y,{className:`h-5 w-5 text-green-600`}),`Privacy & Security`]}),(0,S.jsx)(l,{children:`Control your privacy settings and security preferences`})]}),(0,S.jsxs)(c,{className:`space-y-4`,children:[(0,S.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,S.jsxs)(`div`,{children:[(0,S.jsx)(`p`,{className:`font-medium`,children:`Two-Factor Authentication`}),(0,S.jsx)(`p`,{className:`text-sm text-muted-foreground`,children:`Add an extra layer of security`})]}),(0,S.jsx)(m,{variant:`secondary`,children:`Enabled`})]}),(0,S.jsx)(k,{}),(0,S.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,S.jsxs)(`div`,{children:[(0,S.jsx)(`p`,{className:`font-medium`,children:`Data Privacy`}),(0,S.jsx)(`p`,{className:`text-sm text-muted-foreground`,children:`Control how your data is used`})]}),(0,S.jsx)(f,{variant:`secondary`,size:`sm`,children:`Manage`})]}),(0,S.jsx)(k,{}),(0,S.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,S.jsxs)(`div`,{children:[(0,S.jsx)(`p`,{className:`font-medium`,children:`Active Sessions`}),(0,S.jsx)(`p`,{className:`text-sm text-muted-foreground`,children:`See where you're logged in`})]}),(0,S.jsx)(f,{variant:`secondary`,size:`sm`,children:`View All`})]})]})]}),(0,S.jsxs)(s,{children:[(0,S.jsxs)(u,{children:[(0,S.jsxs)(d,{className:`flex items-center gap-2`,children:[(0,S.jsx)(A,{className:`h-5 w-5 text-blue-600`}),`Notification Preferences`]}),(0,S.jsx)(l,{children:`Choose what notifications you want to receive`})]}),(0,S.jsxs)(c,{className:`space-y-4`,children:[(0,S.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,S.jsxs)(`div`,{children:[(0,S.jsx)(`p`,{className:`font-medium`,children:`Email Notifications`}),(0,S.jsx)(`p`,{className:`text-sm text-muted-foreground`,children:`Get updates via email`})]}),(0,S.jsx)(m,{children:`Enabled`})]}),(0,S.jsx)(k,{}),(0,S.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,S.jsxs)(`div`,{children:[(0,S.jsx)(`p`,{className:`font-medium`,children:`Match Alerts`}),(0,S.jsx)(`p`,{className:`text-sm text-muted-foreground`,children:`Notifications for new matches`})]}),(0,S.jsx)(m,{children:`Enabled`})]}),(0,S.jsx)(k,{}),(0,S.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,S.jsxs)(`div`,{children:[(0,S.jsx)(`p`,{className:`font-medium`,children:`Analysis Completed`}),(0,S.jsx)(`p`,{className:`text-sm text-muted-foreground`,children:`When photo/bio analysis is done`})]}),(0,S.jsx)(m,{variant:`secondary`,children:`Disabled`})]}),(0,S.jsx)(k,{}),(0,S.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,S.jsxs)(`div`,{children:[(0,S.jsx)(`p`,{className:`font-medium`,children:`Weekly Reports`}),(0,S.jsx)(`p`,{className:`text-sm text-muted-foreground`,children:`Performance summaries`})]}),(0,S.jsx)(m,{children:`Enabled`})]})]})]})]}),(0,S.jsxs)(`div`,{className:`space-y-6`,children:[(0,S.jsxs)(s,{children:[(0,S.jsx)(u,{children:(0,S.jsx)(d,{children:`Account Status`})}),(0,S.jsxs)(c,{className:`space-y-4`,children:[(0,S.jsxs)(`div`,{className:`text-center`,children:[(0,S.jsx)(`div`,{className:`w-16 h-16 bg-gradient-to-r from-flame-red to-sparks-pink rounded-full mx-auto mb-3 flex items-center justify-center`,children:(0,S.jsx)(`span`,{className:`text-white font-bold text-lg`,children:n?.firstName?.[0]||`U`})}),(0,S.jsx)(`p`,{className:`font-medium`,children:n?.fullName||`User`}),(0,S.jsx)(`p`,{className:`text-sm text-muted-foreground`,children:n?.primaryEmailAddress?.emailAddress})]}),(0,S.jsx)(k,{}),(0,S.jsxs)(`div`,{className:`space-y-2`,children:[(0,S.jsxs)(`div`,{className:`flex justify-between text-sm`,children:[(0,S.jsx)(`span`,{children:`Account Type`}),(0,S.jsx)(m,{children:`Free`})]}),(0,S.jsxs)(`div`,{className:`flex justify-between text-sm`,children:[(0,S.jsx)(`span`,{children:`Member Since`}),(0,S.jsx)(`span`,{children:new Date(n?.createdAt||Date.now()).toLocaleDateString()})]})]})]})]}),(0,S.jsxs)(s,{children:[(0,S.jsx)(u,{children:(0,S.jsxs)(d,{className:`flex items-center gap-2`,children:[(0,S.jsx)(j,{className:`h-5 w-5 text-purple-600`}),`Subscription`]})}),(0,S.jsx)(c,{className:`space-y-4`,children:(0,S.jsxs)(`div`,{className:`text-center`,children:[(0,S.jsx)(m,{variant:`secondary`,className:`mb-2`,children:`Free Plan`}),(0,S.jsx)(`p`,{className:`text-sm text-muted-foreground mb-4`,children:`Upgrade for unlimited analyses and premium features`}),(0,S.jsx)(f,{className:`w-full bg-gradient-to-r from-flame-red to-sparks-pink hover:opacity-90`,children:`Upgrade to Pro`})]})})]}),(0,S.jsxs)(s,{children:[(0,S.jsx)(u,{children:(0,S.jsx)(d,{children:`Data Management`})}),(0,S.jsxs)(c,{className:`space-y-3`,children:[(0,S.jsxs)(f,{variant:`secondary`,className:`w-full justify-start`,size:`sm`,children:[(0,S.jsx)(v,{className:`h-4 w-4 mr-2`}),`Export Data`]}),(0,S.jsxs)(f,{variant:`secondary`,className:`w-full justify-start text-red-600 hover:text-red-700`,size:`sm`,children:[(0,S.jsx)(g,{className:`h-4 w-4 mr-2`}),`Delete Account`]})]})]}),(0,S.jsxs)(s,{children:[(0,S.jsx)(u,{children:(0,S.jsx)(d,{children:`Need Help?`})}),(0,S.jsxs)(c,{className:`space-y-3`,children:[(0,S.jsx)(f,{variant:`secondary`,className:`w-full`,size:`sm`,children:`Contact Support`}),(0,S.jsx)(f,{variant:`secondary`,className:`w-full`,size:`sm`,children:`View Documentation`}),(0,S.jsxs)(`div`,{className:`flex items-start gap-2 p-3 bg-amber-50 rounded-lg`,children:[(0,S.jsx)(M,{className:`h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0`}),(0,S.jsxs)(`div`,{className:`text-sm`,children:[(0,S.jsx)(`p`,{className:`font-medium text-amber-800`,children:`Having issues?`}),(0,S.jsx)(`p`,{className:`text-amber-700`,children:`Check our FAQ or contact support for help.`})]})]})]})]})]})]})]})})}const P=()=>(0,S.jsx)(b,{children:(0,S.jsx)(N,{})});export{P as component};