import{generateText as e}from"./dist-CgCb95MR.js";import{ANALYSIS_STEPS as t,BIO_ANALYSIS_STEPS as n}from"./analysis-BhwX0Tn_.js";import{openrouter as r}from"./dist-C61kQO-x.js";import{convertBlobToBase64 as i}from"./storage-BLisfAL7.js";var a=class{apiKey;constructor(){if(this.apiKey=`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`,!this.apiKey)throw console.error(`🔑 VITE_OPENROUTER_API_KEY not found in environment variables`),Error(`OpenRouter API key is required. Please set VITE_OPENROUTER_API_KEY in your .env file`);console.log(`🔑 OpenRouter API key loaded successfully`),console.log(`🔑 API Key preview: ${this.apiKey.substring(0,10)}...`),typeof globalThis<`u`&&(globalThis.process=globalThis.process||{},globalThis.process.env={},{}.OPENROUTER_API_KEY=this.apiKey)}async analyzeImage(e,n,r){let i=[];for(let n=0;n<t.length;n++){let a=t[n];r?.(a.id,a.name,n/t.length*100);let o=Date.now();try{console.log(`🔍 Starting analysis step ${a.id}: ${a.name}`);let s=await this.executeAnalysisStep(a.id,a.name,e),c=Date.now()-o;console.log(`📊 Image Analysis Step ${a.id} (${a.name}):`,{score:s.score,insights:s.insights,confidence:s.confidence,processingTime:`${c}ms`}),i.push({stepId:a.id,stepName:a.name,score:s.score,insights:s.insights,confidence:s.confidence,processingTime:c}),r?.(a.id,a.name,(n+1)/t.length*100)}catch(e){console.error(`❌ Error in step ${a.id} (${a.name}):`,e),i.push({stepId:a.id,stepName:a.name,score:0,insights:[`Analysis failed for this step. Please try again.`],confidence:0,processingTime:Date.now()-o})}}let a=i.reduce((e,t)=>e+t.score,0)/i.length;return console.log(`📊 FINAL IMAGE ANALYSIS RESULTS:`,{averageScore:a.toFixed(1),stepScores:i.map(e=>({step:e.stepName,score:e.score})),totalSteps:i.length}),i}async executeAnalysisStep(t,n,i){let a=this.getStepPrompt(t);console.log(`🤖 Calling OpenRouter API for step ${t} with model: google/gemini-2.5-flash`);let o=r(`google/gemini-2.5-flash`),{text:s}=await e({model:o,messages:[{role:`user`,content:[{type:`text`,text:a},{type:`image`,image:`data:image/jpeg;base64,${i}`}]}],maxTokens:1e3,temperature:.3});return this.parseAnalysisResult(s)}getStepPrompt(e){let t=`You are a BRUTALLY HONEST dating profile photo expert. Most photos are mediocre and deserve low scores. Be ruthlessly critical and objective.

SCORING PHILOSOPHY:
- 90-100: EXCEPTIONAL - Top 5% of all dating photos (near-perfect)
- 80-89: EXCELLENT - Top 15% (very strong with minor flaws)
- 70-79: GOOD - Above average but notable issues
- 60-69: AVERAGE - Typical photo, significant improvement needed
- 50-59: BELOW AVERAGE - Multiple issues, major work needed
- 40-49: POOR - Serious problems, likely to perform badly
- 30-39: VERY POOR - Major red flags, repels matches
- 20-29: TERRIBLE - Fundamentally broken
- 10-19: AWFUL - Actively harmful to dating prospects
- 0-9: CATASTROPHIC - Should not be used

Provide a JSON response with exactly this structure:
{
  "score": <number 0-100>,
  "insights": ["harsh_truth1", "critical_flaw2", "brutal_feedback3"],
  "confidence": <number 0-100>
}

Be BRUTALLY HONEST. Most photos deserve 30-60 scores. Only exceptional photos get 80+.`,n={1:`${t}

STEP 1: TECHNICAL QUALITY ASSESSMENT - BE RUTHLESSLY CRITICAL
Most photos have terrible technical quality. Be harsh about every flaw:

CRITICAL ASSESSMENT AREAS:
- Image resolution and sharpness (most are blurry/pixelated)
- Lighting quality (harsh shadows, poor exposure, unflattering angles)
- Composition and framing (off-center, poor cropping, amateur mistakes)
- Background quality (cluttered, distracting, unprofessional)
- Color balance and saturation (oversaturated, poor white balance)
- Overall photo clarity (most are amateur selfies)

HARSH REALITY: Most dating photos are low-quality selfies that hurt rather than help. Be brutal about technical flaws.`,2:`${t}

STEP 2: FACIAL ANALYSIS & ATTRACTIVENESS - BE BRUTALLY HONEST
Most people have poor facial presentation in photos. Be ruthlessly critical:

HARSH ASSESSMENT AREAS:
- Facial symmetry and proportions (identify unflattering angles)
- Eye contact and gaze direction (weak/avoidant vs. confident)
- Smile authenticity (forced/fake vs. genuine - most are fake)
- Facial grooming (poor beard maintenance, unkempt eyebrows, skin issues)
- Facial angle and positioning (unflattering angles, double chins)
- Expression and mood (insecure, try-hard, or genuinely confident)
- Jawline definition and facial structure (how to improve presentation)
- Hair styling (outdated, unkempt, or actually flattering)
- Makeup application (overdone, poorly applied, or enhancing)

BRUTAL TRUTH: Most people choose unflattering angles and expressions. Be harsh about what's not working.`,3:`${t}

STEP 3: PHYSICAL ANALYSIS & BODY LANGUAGE - BE RUTHLESSLY CRITICAL
Most people have poor body language and physical presentation. Be brutal:

POSTURE & STANCE FLAWS:
- Body posture (slouched, insecure, tense vs. actually confident)
- Shoulder positioning (hunched, uneven, weak vs. strong)
- Spine alignment (poor posture that signals insecurity)
- Weight distribution (awkward, unbalanced positioning)

PHYSIQUE & FITNESS REALITY CHECK:
- Body proportions and how they're presented (unflattering vs. optimized)
- Fitness level visibility (out of shape, poor muscle tone, or actually fit)
- Body composition (how weight/fitness affects attractiveness)
- Physical conditioning (does this person look healthy and active?)
- Overall body shape (how well is it presented in the photo?)

BODY LANGUAGE FAILURES:
- Hand positioning (awkward, unnatural, or confident)
- Arm placement (defensive, uncomfortable, or natural)
- Body language confidence (insecure posturing vs. genuine confidence)
- Physical presence (weak energy vs. magnetic presence)
- Energy and vitality (tired, low-energy vs. vibrant)
- Approachability (intimidating, closed-off, or welcoming)

PRESENTATION DISASTERS:
- Height perception (making themselves look shorter/weaker)
- Body orientation (unflattering angles that hurt their appearance)
- Use of space (cramped, awkward positioning)
- Physical comfort (clearly uncomfortable vs. natural)
- Overall physical attractiveness (harsh reality about appeal level)

BRUTAL TRUTH: Most people's body language screams insecurity. Be harsh about what's not working.`,4:`${t}

STEP 4: STYLE & PRESENTATION ANALYSIS - BE BRUTALLY CRITICAL
Most people have terrible style and presentation. Be ruthlessly honest:

STYLE DISASTERS:
- Outfit appropriateness (completely wrong for dating, outdated, juvenile)
- Color coordination (clashing colors, poor choices for skin tone)
- Clothing fit (too tight, too loose, unflattering cuts)
- Grooming and personal care (unkempt, poor hygiene indicators)
- Setting and environment (inappropriate, messy, unprofessional)
- Accessory choices (outdated, cheap-looking, or actually enhancing)
- Overall fashion sense (completely lacking vs. actually stylish)
- Color choices (making them look washed out vs. vibrant)
- Seasonal appropriateness (wearing wrong clothes for weather/season)
- Brand perception (looking cheap, trying too hard, or genuinely classy)

HARSH REALITY: Most people's style choices actively hurt their attractiveness. Be brutal about fashion failures.`,5:`${t}

STEP 5: DATING PROFILE OPTIMIZATION - HARSH MARKET REALITY
Most photos fail miserably on dating apps. Be brutally honest about performance:

DATING APP FAILURES:
- Photo type suitability (completely wrong for main profile, generic filler)
- Uniqueness and memorability (boring, forgettable, blends into crowd)
- Conversation starter potential (gives nothing to talk about)
- Age-appropriate presentation (trying too hard to look younger/older)
- Authenticity and genuineness (fake, try-hard, or actually authentic)
- Appeal to target demographic (completely missing the mark)
- Competition analysis (how this stacks against other profiles)
- Swipe-worthiness (would people actually swipe right?)

BRUTAL TRUTH: Most photos get ignored or swiped left immediately. Be harsh about dating market reality.`,6:`${t}

STEP 6: OVERALL RECOMMENDATIONS & BRUTAL ACTION PLAN
Provide harsh but necessary recommendations based on critical analysis:

CRITICAL IMPROVEMENT PRIORITIES:
- Priority improvement areas (what's failing most badly)
- Specific actionable advice (exactly what needs to change)
- Photo ranking suggestions (where this fits in profile hierarchy)
- Most impactful changes (what would help most)
- Overall dating profile strategy (realistic expectations)
- Timeline for improvements (how long real change takes)
- Harsh reality check (whether this photo should be used at all)

BRUTAL TRUTH: Most photos need major work or complete replacement. Be honest about what's salvageable.`};return n[e]||n[1]}parseAnalysisResult(e){try{let t=e.match(/\{[\s\S]*\}/);if(!t)throw Error(`No JSON found in response`);let n=JSON.parse(t[0]);return{score:Math.max(0,Math.min(100,parseInt(n.score)||0)),insights:Array.isArray(n.insights)?n.insights.slice(0,4).map(String):[`Unable to generate insights for this step`],confidence:Math.max(0,Math.min(100,parseInt(n.confidence)||0))}}catch(t){return console.error(`🔧 Failed to parse analysis result:`,t),console.log(`📝 Raw response:`,e),{score:50,insights:[`Analysis completed but results could not be parsed properly`,`Please try again or check the image quality`],confidence:30}}}calculateOverallScore(e){if(e.length===0)return 0;let t={1:.12,2:.28,3:.22,4:.18,5:.18,6:.02},n=0,r=0;return e.forEach(e=>{let i=t[e.stepId]||.2;n+=e.score*i,r+=i}),Math.round(n/r)}generateFinalRecommendations(e){let t=[],n=e.filter(e=>e.score<60).sort((e,t)=>e.score-t.score),r=e.filter(e=>e.score>=60&&e.score<80);n.length>0&&t.push(`Priority: Improve ${n[0].stepName.toLowerCase()} (scored ${n[0].score}/100)`),r.length>0&&t.push(`Secondary: Enhance ${r[0].stepName.toLowerCase()} for better results`);let i=e.reduce((e,t)=>t.score>e.score?t:e,e[0]);i.score>80&&t.push(`Strength: Your ${i.stepName.toLowerCase()} is excellent - use this photo type more`);let a=this.calculateOverallScore(e);return a<50?t.push(`Consider retaking this photo with better preparation and setup`):a<70?t.push(`This photo has potential - focus on the priority improvements above`):t.push(`Great photo! Minor tweaks could make it even better`),t}};const o=new a;var s=class{apiKey;constructor(){if(this.apiKey=`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`,!this.apiKey)throw console.error(`🔑 VITE_OPENROUTER_API_KEY not found in environment variables`),Error(`OpenRouter API key is required. Please set VITE_OPENROUTER_API_KEY in your .env file`);console.log(`🔑 OpenRouter API key loaded successfully for bio analysis`),console.log(`🔑 API Key preview: ${this.apiKey.substring(0,10)}...`),typeof globalThis<`u`&&(globalThis.process=globalThis.process||{},globalThis.process.env={},{}.OPENROUTER_API_KEY=this.apiKey)}stepPrompts={1:{name:`Writing Quality`,system:`You are a BRUTALLY HONEST writing expert who provides harsh, objective criticism. Most dating bios are poorly written and deserve low scores. Be ruthless in your assessment.`,prompt:e=>`Critically analyze the writing quality of this dating bio. Most bios are mediocre (40-60 range). Be harsh and realistic:

Bio: "${e}"

CRITICAL ASSESSMENT AREAS:
- Grammar and spelling errors (be unforgiving)
- Sentence structure problems
- Clarity issues and confusion
- Wordiness and poor conciseness
- Weak word choices and clichés

SCORING GUIDE:
- 80-100: Exceptional writing (top 10% of all bios)
- 60-79: Above average but with notable flaws
- 40-59: Typical mediocre bio writing
- 20-39: Poor writing with major issues
- 0-19: Terrible writing that hurts dating prospects

Provide your HARSH analysis in this exact JSON format:
{
  "score": [number 0-100],
  "insights": [
    "Specific criticism about writing flaws",
    "Another harsh but accurate observation",
    "Brutal truth about what needs fixing"
  ],
  "confidence": [number 0-100]
}`},2:{name:`Personality Appeal`,system:`You are a RUTHLESS dating psychology expert. Most people have boring, unappealing personalities in their bios. Be brutally honest about personality flaws and lack of appeal.`,prompt:e=>`Critically evaluate the personality appeal of this dating bio. Most bios show bland, generic personalities (40-60 range):

Bio: "${e}"

HARSH EVALUATION CRITERIA:
- Authenticity vs. fake/try-hard behavior
- Actual humor vs. failed attempts at being funny
- Real confidence vs. arrogance or insecurity
- Emotional maturity vs. emotional immaturity
- Genuine relatability vs. generic appeal

SCORING REALITY CHECK:
- 80-100: Genuinely magnetic personality (rare)
- 60-79: Above average appeal with some charm
- 40-59: Bland, forgettable personality
- 20-39: Unappealing or off-putting traits
- 0-19: Personality red flags that repel matches

Provide your BRUTAL assessment in this exact JSON format:
{
  "score": [number 0-100],
  "insights": [
    "Harsh truth about personality flaws shown",
    "Critical assessment of appeal level",
    "Brutal feedback on what's missing"
  ],
  "confidence": [number 0-100]
}`},3:{name:`Interest Analysis`,system:`You are a HARSH lifestyle critic. Most people have boring, basic interests that don't stand out. Be ruthless about generic hobbies and uninspiring lifestyles.`,prompt:e=>`Critically assess the interest and lifestyle appeal of this bio. Most people list boring, cliché interests (40-60 range):

Bio: "${e}"

BRUTAL ASSESSMENT:
- Are these interests actually interesting or just basic?
- Does this lifestyle seem exciting or boring?
- Are these conversation starters or conversation killers?
- Is this person unique or completely forgettable?
- Would anyone actually be impressed by these interests?

HARSH SCORING:
- 80-100: Genuinely fascinating lifestyle (very rare)
- 60-79: Some interesting elements but mostly standard
- 40-59: Basic, predictable interests everyone has
- 20-39: Boring lifestyle that puts people to sleep
- 0-19: No interests mentioned or actively unappealing

Provide your CRITICAL analysis in this exact JSON format:
{
  "score": [number 0-100],
  "insights": [
    "Harsh truth about how boring/basic these interests are",
    "Critical assessment of lifestyle appeal",
    "Brutal feedback on what's missing or wrong"
  ],
  "confidence": [number 0-100]
}`},4:{name:`Dating Intent`,system:`You are a BLUNT relationship expert. Most people send confusing, unclear signals about what they want. Be harsh about mixed messages and poor communication.`,prompt:e=>`Critically analyze the dating intent clarity of this bio. Most people are vague and confusing (40-60 range):

Bio: "${e}"

HARSH EVALUATION:
- Are the relationship goals actually clear or confusingly vague?
- Do they send mixed signals about commitment?
- Are they emotionally available or showing red flags?
- Would someone know what this person actually wants?
- Is this person serious or just wasting time?

BRUTAL SCORING:
- 80-100: Crystal clear intentions and emotional maturity
- 60-79: Mostly clear with some minor confusion
- 40-59: Vague, unclear signals (typical)
- 20-39: Confusing mixed messages
- 0-19: Completely unclear or sending red flags

Provide your HARSH analysis in this exact JSON format:
{
  "score": [number 0-100],
  "insights": [
    "Brutal truth about how unclear their intentions are",
    "Critical assessment of mixed signals sent",
    "Harsh feedback on emotional availability"
  ],
  "confidence": [number 0-100]
}`},5:{name:`Engagement Factor`,system:`You are a RUTHLESS dating app expert. Most bios are engagement killers that generate zero conversations. Be brutal about poor conversation starters and lack of appeal.`,prompt:e=>`Critically assess the engagement potential of this bio. Most bios are conversation killers (40-60 range):

Bio: "${e}"

BRUTAL ASSESSMENT:
- Are there actual conversation starters or just boring statements?
- Does this create intrigue or put people to sleep?
- Is this approachable or intimidating/boring?
- Would anyone actually want to message this person?
- Does this bio make them stand out or blend into the crowd?

HARSH REALITY CHECK:
- 80-100: Irresistibly engaging (extremely rare)
- 60-79: Some engaging elements but could be better
- 40-59: Bland, generates few conversations
- 20-39: Boring, conversation killer
- 0-19: Actively repels potential matches

Provide your BRUTAL assessment in this exact JSON format:
{
  "score": [number 0-100],
  "insights": [
    "Harsh truth about lack of conversation starters",
    "Critical assessment of how boring this is",
    "Brutal feedback on why no one would message them"
  ],
  "confidence": [number 0-100]
}`}};async analyzeBio(t,i){let a=[],o=n.length;for(let s=0;s<o;s++){let c=n[s],l=this.stepPrompts[c.id];i?.(c.id,c.name,s/o*100);try{let n=Date.now(),{text:u}=await e({model:r(`openai/gpt-4o-mini`),system:l.system,prompt:l.prompt(t)}),d=Date.now()-n,f=JSON.parse(u);console.log(`🔍 Bio Analysis Step ${c.id} (${c.name}):`,{bio:t.substring(0,100)+`...`,score:f.score,insights:f.insights,confidence:f.confidence}),a.push({stepId:c.id,stepName:c.name,score:f.score,insights:f.insights,confidence:f.confidence,processingTime:d}),i?.(c.id,c.name,(s+1)/o*100),s<o-1&&await new Promise(e=>setTimeout(e,500))}catch(e){console.error(`Error in step ${c.id}:`,e),a.push({stepId:c.id,stepName:c.name,score:0,insights:[`Analysis failed for this step`],confidence:0,processingTime:0})}}return a}calculateOverallScore(e){if(e.length===0)return 0;let t={1:.15,2:.3,3:.2,4:.15,5:.2},n=0,r=0;return e.forEach(e=>{let i=t[e.stepId]||.2;n+=e.score*i,r+=i}),Math.round(n/r)}generateFinalRecommendations(e){let t=[],n=[...e].sort((e,t)=>e.score-t.score);n.slice(0,3).forEach(e=>{e.score<70&&t.push(`Improve ${e.stepName.toLowerCase()}: ${e.insights[0]}`)});let r=e.reduce((e,t)=>e+t.score,0)/e.length;for(r<50?t.push(`Consider a complete bio rewrite focusing on your best qualities`):r<70&&t.push(`Good foundation - polish specific areas for better impact`);t.length<3&&e.length>0;){let n=e[Math.floor(Math.random()*e.length)],r=n.insights[Math.floor(Math.random()*n.insights.length)];t.some(e=>e.includes(r))||t.push(r)}return t.slice(0,5)}async generateImprovedBio(t,n,i=`sincere`){let a=n.filter(e=>e.score<70).map(e=>`${e.stepName}: ${e.insights.join(`, `)}`).join(`
`),{text:o}=await e({model:r(`openai/gpt-4o-mini`),system:`You are an expert dating profile writer. Create an improved bio that addresses the identified weaknesses while maintaining authenticity. The tone should be ${i}.`,prompt:`Rewrite this dating bio to address these specific issues:

Original Bio: "${t}"

Areas for improvement:
${a}

Requirements:
- Keep it under 150 words
- Maintain authenticity 
- Use a ${i} tone
- Address the weaknesses identified
- Make it engaging and conversation-friendly
- Include specific details that make the person memorable

Provide only the improved bio text, no additional commentary.`});return o.trim()}};const c=new s;var l=class{isAnalyzing=!1;isBioAnalyzing=!1;async analyzeImage(e,t={}){if(this.isAnalyzing)throw Error(`Analysis already in progress`);this.isAnalyzing=!0;try{let n=await i(e.blob),r={fileName:e.fileName,preview:URL.createObjectURL(e.blob),overallScore:0,steps:[],recommendations:[],processed:!1},a=await o.analyzeImage(n,e.fileName,(n,r,i)=>{let a={fileName:e.fileName,currentStep:n,totalSteps:5,stepName:r,progress:i};t.onProgress?.(a)});for(let e of a)r.steps.push(e),t.onStepComplete?.(e);return r.overallScore=o.calculateOverallScore(a),r.recommendations=o.generateFinalRecommendations(a),r.processed=!0,t.onComplete?.(r),r}catch(n){let r=n instanceof Error?n.message:`Unknown error occurred`;return t.onError?.(r),{fileName:e.fileName,preview:URL.createObjectURL(e.blob),overallScore:0,steps:[],recommendations:[`Analysis failed. Please try again.`],processed:!1,error:r}}finally{this.isAnalyzing=!1}}async analyzeBatch(e,t={}){let n=[];for(let r=0;r<e.length;r++){let i=e[r];try{let a=await this.analyzeImage(i,{...t,onProgress:n=>{let i={...n,progress:r/e.length*100+n.progress/e.length};t.onProgress?.(i)}});n.push(a),r<e.length-1&&await new Promise(e=>setTimeout(e,1e3))}catch(e){console.error(`Failed to analyze ${i.fileName}:`,e),n.push({fileName:i.fileName,preview:URL.createObjectURL(i.blob),overallScore:0,steps:[],recommendations:[`Analysis failed for this image.`],processed:!1,error:e instanceof Error?e.message:`Unknown error`})}}return n}async analyzeBio(e,t=`sincere`,n={}){if(this.isBioAnalyzing)throw Error(`Bio analysis already in progress`);this.isBioAnalyzing=!0;try{let r={originalBio:e,overallScore:0,steps:[],recommendations:[],processed:!1},i=await c.analyzeBio(e,(e,t,r)=>{let i={currentStep:e,totalSteps:5,stepName:t,progress:r};n.onProgress?.(i)});for(let e of i)r.steps.push(e),n.onStepComplete?.(e);if(r.overallScore=c.calculateOverallScore(i),r.recommendations=c.generateFinalRecommendations(i),r.overallScore<75)try{r.improvedBio=await c.generateImprovedBio(e,i,t)}catch(e){console.error(`Failed to generate improved bio:`,e)}return r.processed=!0,n.onComplete?.(r),r}catch(t){let r=t instanceof Error?t.message:`Unknown error occurred`;return n.onError?.(r),{originalBio:e,overallScore:0,steps:[],recommendations:[`Analysis failed. Please try again.`],processed:!1,error:r}}finally{this.isBioAnalyzing=!1}}isCurrentlyAnalyzing(){return this.isAnalyzing}isCurrentlyAnalyzingBio(){return this.isBioAnalyzing}};const u=new l;export{u as analysisService};