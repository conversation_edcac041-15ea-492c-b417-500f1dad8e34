import{__toESM as e,cn as t,require_jsx_runtime as n}from"./main-B4G73TvM.js";import{cva as r}from"./createLucideIcon-JB7IMeGf.js";var i=e(n());const a=r(`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2`,{variants:{variant:{default:`border-transparent bg-primary text-primary-foreground hover:bg-primary/80`,secondary:`border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80`,destructive:`border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80`,outline:`text-foreground`}},defaultVariants:{variant:`default`}});function o({className:e,variant:n,...r}){return(0,i.jsx)(`div`,{className:t(a({variant:n}),e),...r})}export{o as Badge};