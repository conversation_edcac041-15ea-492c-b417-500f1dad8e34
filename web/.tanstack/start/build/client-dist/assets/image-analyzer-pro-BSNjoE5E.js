import{Link as e,__toESM as t,require_jsx_runtime as n,require_react as r}from"./main-B4G73TvM.js";import{generateText as i}from"./dist-CgCb95MR.js";import{Card as a,CardContent as o,CardDescription as s,CardHeader as c,<PERSON><PERSON>itle as l}from"./card-Bzfaa5B9.js";import{Button as u}from"./createLucideIcon-JB7IMeGf.js";import{Badge as d}from"./badge-B_rsQKkv.js";import"./trash-2-CkLpzZy2.js";import{ArrowLeft as f}from"./arrow-left-CrPLcqAC.js";import{Brain as p}from"./brain-wUBLYMm0.js";import{Camera as m}from"./camera-P6IreDGA.js";import{Crown as h}from"./crown-BUU0WoJh.js";import{FileUp as g,PrivacyNotice as _,useDropzone as v}from"./PrivacyNotice-C9kEPnp4.js";import{LoaderCircle as y,openrouter as b}from"./dist-C61kQO-x.js";import"./shield-Ct2yHJj1.js";import{Sparkles as x}from"./sparkles-BbQK6jEu.js";import{TrendingUp as S}from"./trending-up-BvkHvnfP.js";import{X as C}from"./storage-BLisfAL7.js";import{Progress as w}from"./progress-BIjJajwn.js";import{Tabs as T,TabsContent as E,TabsList as D,TabsTrigger as O}from"./tabs-CvPwdQTd.js";import{AdvancedScoringEngine as k,DEFAULT_IMAGE_WEIGHTS as A,IMAGE_CATEGORY_WEIGHTS as j,getAllExpertTypes as M,getExpertCredentials as N,getExpertPersona as P}from"./advanced-scoring-CKP2w-nj.js";import"./dist-DGuqR-4V.js";var F=class{generateExpertPrompt(e,t){let n=P(e),r=this.buildSystemPrompt(n),i=this.buildUserPrompt(n,t),a=this.buildChainOfThoughtStructure(n);return{systemPrompt:r,userPrompt:i,chainOfThoughtStructure:a,examples:this.getExamples(e)}}buildSystemPrompt(e){return`You are ${e.name}, ${e.credentials}.

BACKGROUND & EXPERTISE:
${e.background}

Your core expertise includes:
${e.expertise.map(e=>`• ${e}`).join(`
`)}

ANALYSIS APPROACH:
${e.analysisApproach}

CRITICAL ANALYSIS REQUIREMENTS:
You are a BRUTALLY HONEST expert who provides OBJECTIVE, UNFORGIVING analysis. Your reputation depends on accuracy, not kindness.

SCORING PHILOSOPHY:
- 90-100: EXCEPTIONAL - Top 5% of all dating profile images, near-perfect execution
- 80-89: EXCELLENT - Top 15% of images, very strong with minor flaws
- 70-79: GOOD - Above average, solid but with notable improvement areas
- 60-69: AVERAGE - Typical image, significant room for improvement
- 50-59: BELOW AVERAGE - Multiple issues, needs major work
- 40-49: POOR - Serious problems, likely to perform badly
- 30-39: VERY POOR - Major red flags, would repel most matches
- 20-29: TERRIBLE - Fundamentally broken, needs complete reshoot
- 10-19: AWFUL - Actively harmful to dating prospects
- 0-9: CATASTROPHIC - Should not be used under any circumstances

ANALYSIS REQUIREMENTS:
1. BE RUTHLESSLY CRITICAL - Most images are mediocre and deserve low scores
2. IDENTIFY EVERY FLAW - No matter how small, call out problems
3. DEMAND EXCELLENCE - Only exceptional images deserve high scores
4. PROVIDE HARSH TRUTH - Your job is accuracy, not making people feel good
5. USE REAL-WORLD STANDARDS - Compare to actual successful dating profiles

RESPONSE REQUIREMENTS:
- Be uncompromisingly honest about weaknesses
- Support every criticism with specific visual evidence
- Score based on real dating market performance
- Prioritize brutal honesty over politeness
- Remember: Average images get average results (poor performance)

Your analysis will be combined with other experts to provide comprehensive feedback.`}buildUserPrompt(e,t){let n=t?this.buildContextInfo(t):``;return`${n}

Please analyze this dating profile image using your expertise as a ${e.type} expert.

ANALYSIS FRAMEWORK:
Follow this systematic approach:

1. INITIAL OBSERVATION PHASE:
   - What are the key visual elements I observe?
   - What stands out immediately from my expert perspective?
   - What technical/aesthetic/psychological factors are present?

2. DETAILED EXPERT ANALYSIS:
   - What specific improvements are needed from my expert perspective?
   - What concrete changes would optimize this for dating success?
   - How can this be enhanced to follow best practices in my field?
   - Focus on ACTIONABLE IMPROVEMENTS, not just observations

3. CRITICAL SCORING RATIONALE:
   - What score (0-100) does this image ACTUALLY deserve? (Be harsh - most images are 40-60)
   - What SPECIFIC FLAWS and weaknesses lower this score?
   - Why would this image FAIL in the competitive dating market?
   - What evidence PROVES this score is accurate?

4. CONFIDENCE ASSESSMENT:
   - How confident am I in this analysis (0-100)?
   - What factors increase or decrease my confidence?
   - What additional information would improve my assessment?

5. ACTIONABLE RECOMMENDATIONS:
   - What are the top 3-5 most impactful improvements needed?
   - Which changes would have the highest impact on dating success?
   - What is the effort level required for each improvement?
   - Be specific about HOW to implement each improvement

RESPONSE FORMAT:
Provide your analysis in this exact JSON structure:

{
  "observation_phase": {
    "key_elements": ["element1", "element2", "element3"],
    "immediate_impressions": "Your first impressions as an expert",
    "technical_factors": ["factor1", "factor2"]
  },
  "expert_evaluation": {
    "expert_specific_analysis": "Focus on SPECIFIC IMPROVEMENTS needed, not just observations. What exactly should be changed, improved, or optimized? Provide actionable insights.",
    "strengths": ["strength1", "strength2"],
    "weaknesses": ["weakness1", "weakness2"]
  },
  "scoring_methodology": {
    "score": 45,
    "harsh_reality_check": "Why this image would struggle in the real dating market",
    "critical_flaws": ["major_visual_flaw1", "major_flaw2", "major_flaw3"],
    "evidence_for_low_score": ["specific_visual_evidence1", "specific_evidence2"],
    "market_performance_prediction": "How this would actually perform (be realistic)"
  },
  "confidence_evaluation": {
    "confidence": 92,
    "supporting_factors": ["factor1", "factor2"],
    "limiting_factors": ["area1", "area2"]
  },
  "strategic_recommendations": [
    {
      "recommendation": "Specific actionable improvement advice",
      "impact_level": "high",
      "implementation_difficulty": "easy",
      "reasoning": "Why this improvement matters and how it helps"
    },
    {
      "recommendation": "Another specific improvement",
      "impact_level": "medium",
      "implementation_difficulty": "moderate",
      "reasoning": "Detailed reasoning for this improvement"
    }
  ]
}

Remember: Your analysis should reflect your specific expertise while being constructive and actionable.`}buildChainOfThoughtStructure(e){return`
CHAIN-OF-THOUGHT REASONING STRUCTURE:

Step 1: Initial Expert Observation
- "As a ${e.type} expert, I immediately notice..."
- "The key elements that stand out to me are..."
- "From my professional perspective, this image shows..."

Step 2: Technical/Professional Analysis
- "Applying my expertise in ${e.expertise[0]}, I can see..."
- "The technical quality/psychological factors/style elements indicate..."
- "Based on my experience with ${e.specializations[0]}, this demonstrates..."

Step 3: Comparative Assessment
- "Compared to successful profiles I've analyzed..."
- "This ranks in the [percentile] of images I've evaluated because..."
- "The market positioning would be..."

Step 4: Evidence-Based Scoring
- "I'm scoring this [X]/100 because..."
- "The evidence supporting this score includes..."
- "The main factors influencing this score are..."

Step 5: Strategic Recommendations
- "The highest impact improvement would be..."
- "Based on my expertise, I recommend..."
- "The priority order for improvements should be..."
`}buildContextInfo(e){let t=`ANALYSIS CONTEXT:
`;return e.targetDemographic&&(t+=`• Target Demographic: ${e.targetDemographic}\n`),e.platform&&(t+=`• Platform: ${e.platform}\n`),e.analysisDepth&&(t+=`• Analysis Depth: ${e.analysisDepth}\n`),t+`
`}getExamples(e){let t={photography:[`Example: "As a photography expert, I immediately notice the harsh overhead lighting creating unflattering shadows under the eyes and nose. The composition is off-center without artistic intent, and the background is cluttered with distracting elements. Score: 23/100 because this amateur photography actively hurts the subject's appeal and screams 'low effort selfie.'"`,`Example: "This image demonstrates decent use of natural window light and acceptable composition. However, the lighting could be more flattering, the background lacks visual interest, and the pose appears stiff. Score: 67/100 - while technically competent, it lacks the polish needed to stand out in today's competitive dating market."`],psychology:[`Example: "From a psychological perspective, while the subject attempts a smile, it lacks genuine Duchenne markers and appears practiced rather than spontaneous. The eye contact is adequate but not particularly engaging. Body language shows some tension. Score: 54/100 because these psychological cues suggest someone trying to appear confident rather than naturally being so."`,`Example: "The facial expression is clearly forced, with visible tension in the jaw and a smile that screams 'fake.' The averted gaze signals insecurity and discomfort. Body language appears defensive. Score: 19/100 because these psychological red flags would actively repel potential matches who can sense inauthenticity."`],fashion:[`Example: "The styling shows basic understanding of color coordination with the navy shirt working adequately with the subject's skin tone. However, the fit is standard rather than tailored, and the overall look lacks sophistication or personality. Score: 58/100 - while not offensive, it's forgettable and doesn't create any visual impact."`,`Example: "The outfit choice is a complete disaster - the oversized graphic tee looks juvenile and sloppy, the colors clash horribly with the subject's complexion, and the overall styling suggests someone who doesn't understand basic fashion principles. Score: 14/100 because this actively damages the subject's attractiveness and signals poor judgment."`],data_science:[`Example: "Based on platform analytics, this image type shows moderate performance indicators but lacks the standout elements that drive top-tier engagement. While technically adequate, it falls into the oversaturated 'decent but forgettable' category. Score: 52/100 based on competitive analysis showing this style performs below median in current market conditions."`,`Example: "This image style (poor lighting, forced expression, cluttered background) correlates with bottom 15% performance metrics. Platform algorithms actively deprioritize such images, leading to 73% fewer profile views. Score: 11/100 based on conversion rate analysis showing this type generates virtually zero quality matches."`],dating_coach:[`Example: "This image shows decent approachability but lacks the magnetic confidence needed for standout dating success. The expression is pleasant but forgettable, and the setting provides minimal conversation value. Score: 59/100 because while it won't actively hurt, it won't generate the excitement needed in today's competitive dating market."`,`Example: "While the subject may be physically attractive, this image is a dating disaster. The bland expression, generic setting, and complete lack of personality make it instantly forgettable. Score: 31/100 because it would generate few matches and even fewer meaningful conversations - a waste of potential."`]};return t[e]||[]}},I=class{apiKey;promptGenerator;scoringEngine;constructor(){if(this.apiKey=`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`,!this.apiKey)throw console.error(`🔑 VITE_OPENROUTER_API_KEY not found in environment variables`),Error(`OpenRouter API key is required. Please set VITE_OPENROUTER_API_KEY in your .env file`);console.log(`🔑 OpenRouter API key loaded successfully for advanced analysis`),console.log(`🔑 API Key preview: ${this.apiKey.substring(0,10)}...`),typeof globalThis<`u`&&(globalThis.process=globalThis.process||{},globalThis.process.env={},{}.OPENROUTER_API_KEY=this.apiKey),this.promptGenerator=new F,this.scoringEngine=new k}async analyzeImage(e,t,n={},r){let i=Date.now(),a=`img_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;console.log(`🚀 Starting advanced image analysis for ${t} with o3`);try{r?.({phase:`pre_analysis`,progress:5,message:`Performing pre-analysis assessment...`});let o=await this.performPreAnalysis(e,n);r?.({phase:`expert_analysis`,progress:15,message:`Conducting multi-expert analysis...`});let s=await this.conductExpertAnalyses(e,n,r);r?.({phase:`scoring`,progress:70,message:`Calculating advanced scores and rankings...`});let c=this.scoringEngine.calculateDetailedScoring(s,j,A);r?.({phase:`insights`,progress:85,message:`Generating actionable insights...`});let l=await this.generateActionableInsights(s,c),{quickWins:u,longTermImprovements:d}=this.categorizeRecommendations(l);r?.({phase:`comparison`,progress:95,message:`Performing comparative market analysis...`});let f=this.scoringEngine.generateComparativeAnalysis(c.overallScore,c,s);r?.({phase:`finalization`,progress:100,message:`Finalizing advanced analysis...`});let p=this.calculateConfidenceMetrics(s,o),m=await this.generateDemographicInsights(e,s),h=Date.now()-i,g={id:a,fileName:t,preview:`data:image/jpeg;base64,${e}`,timestamp:Date.now(),overallScore:c.overallScore,percentileRank:c.percentileRank,improvementPotential:c.improvementPotential,marketCompetitiveness:c.marketCompetitiveness,expertAnalyses:s,detailedScoring:c,actionableInsights:l,quickWins:u,longTermImprovements:d,comparativeAnalysis:f,confidenceMetrics:p,demographicInsights:m,processingTime:h,modelUsed:`openai/o3`,analysisVersion:`1.0.0`};return console.log(`✅ Advanced analysis completed in ${h}ms - Overall Score: ${g.overallScore}/100`),g}catch(e){throw console.error(`❌ Advanced image analysis failed:`,e),Error(`Advanced analysis failed: ${e instanceof Error?e.message:`Unknown error`}`)}}async performPreAnalysis(e,t){return console.log(`🔍 Performing pre-analysis assessment...`),{imageQuality:`high`,detectedContext:`outdoor`,estimatedAge:`25-35`,technicalIssues:[]}}async conductExpertAnalyses(e,t,n){let r=M(),i=[];for(let a=0;a<r.length;a++){let o=r[a],s=15+a/r.length*50;n?.({phase:`expert_analysis`,currentExpert:N(o),progress:s,message:`Analyzing with ${o} expert...`});try{let n=await this.conductSingleExpertAnalysis(o,e,t);i.push(n),console.log(`✅ ${o} analysis completed - Score: ${n.score}/100`),a<r.length-1&&await new Promise(e=>setTimeout(e,1e3))}catch(e){console.error(`❌ ${o} analysis failed:`,e)}}return i}async conductSingleExpertAnalysis(e,t,n){let r=this.promptGenerator.generateExpertPrompt(e,n);console.log(`🤖 Calling OpenRouter with o3 for ${e} expert analysis`);let{text:a}=await i({model:b(`openai/o3`),messages:[{role:`system`,content:r.systemPrompt},{role:`user`,content:[{type:`text`,text:r.userPrompt},{type:`image`,image:`data:image/jpeg;base64,${t}`}]}],maxTokens:2e3,temperature:.1}),o=this.parseAdvancedAnalysisResult(a);return{expertType:e,expertName:N(e),credentials:N(e),analysis:o.expert_evaluation?.expert_specific_analysis||`Analysis completed`,score:o.scoring_methodology?.score||75,confidence:o.confidence_evaluation?.confidence||85,keyObservations:o.observation_phase?.key_elements||[],recommendations:this.parseRecommendations(o.strategic_recommendations||[])}}parseAdvancedAnalysisResult(e){try{console.log(`🔍 Parsing advanced analysis response...`);let t=e.match(/\{[\s\S]*\}/);if(!t)return console.warn(`⚠️ No JSON found in response, using fallback`),this.getFallbackAnalysisData();let n=JSON.parse(t[0]);return console.log(`✅ Successfully parsed advanced analysis JSON`),{observation_phase:{key_elements:Array.isArray(n.observation_phase?.key_elements)?n.observation_phase.key_elements:[`Image analysis completed`],immediate_impressions:n.observation_phase?.immediate_impressions||`Professional analysis completed`,technical_factors:Array.isArray(n.observation_phase?.technical_factors)?n.observation_phase.technical_factors:[`Technical assessment completed`]},expert_evaluation:{expert_specific_analysis:n.expert_evaluation?.expert_specific_analysis||`Expert analysis completed successfully`,strengths:Array.isArray(n.expert_evaluation?.strengths)?n.expert_evaluation.strengths:[`Analysis completed`],weaknesses:Array.isArray(n.expert_evaluation?.weaknesses)?n.expert_evaluation.weaknesses:[`Areas for improvement identified`]},scoring_methodology:{score:Math.max(0,Math.min(100,parseInt(n.scoring_methodology?.score)||75)),evidence:Array.isArray(n.scoring_methodology?.evidence)?n.scoring_methodology.evidence:[`Evidence-based scoring completed`],key_factors:Array.isArray(n.scoring_methodology?.key_factors)?n.scoring_methodology.key_factors:[`Multiple factors considered`]},confidence_evaluation:{confidence:Math.max(0,Math.min(100,parseInt(n.confidence_evaluation?.confidence)||85)),supporting_factors:Array.isArray(n.confidence_evaluation?.supporting_factors)?n.confidence_evaluation.supporting_factors:[`Professional analysis methodology`],limiting_factors:Array.isArray(n.confidence_evaluation?.limiting_factors)?n.confidence_evaluation.limiting_factors:[]},strategic_recommendations:Array.isArray(n.strategic_recommendations)?n.strategic_recommendations:[]}}catch(t){return console.error(`❌ Failed to parse advanced analysis result:`,t),console.log(`📝 Raw response:`,e),this.getFallbackAnalysisData()}}getFallbackAnalysisData(){return{observation_phase:{key_elements:[`Image analysis completed`,`Professional assessment performed`],immediate_impressions:`Analysis completed successfully`,technical_factors:[`Technical quality assessed`]},expert_evaluation:{expert_specific_analysis:`Professional analysis completed. Please try again for more detailed insights.`,strengths:[`Image uploaded successfully`,`Analysis framework applied`],weaknesses:[`Detailed analysis temporarily unavailable`]},scoring_methodology:{score:75,evidence:[`Analysis methodology applied`,`Professional standards used`],key_factors:[`Multiple assessment criteria`,`Expert evaluation framework`]},confidence_evaluation:{confidence:85,supporting_factors:[`Professional analysis system`,`Established methodology`],limiting_factors:[`Response parsing issue - please retry`]},strategic_recommendations:[{recommendation:`Try the analysis again for more detailed insights`,impact_level:`medium`,implementation_difficulty:`easy`,reasoning:`System temporarily unable to provide detailed analysis`}]}}parseRecommendations(e){if(console.log(`🔧 Parsing recommendations:`,e),!Array.isArray(e)||e.length===0)return console.warn(`⚠️ No recommendations provided, creating fallback recommendations`),[{recommendation:`Improve lighting and image quality for better visual appeal`,priority:`high`,impactScore:85,effortRequired:`low`,category:`technical`,reasoning:`Better lighting significantly improves photo attractiveness`},{recommendation:`Consider professional photo editing or retouching`,priority:`medium`,impactScore:70,effortRequired:`medium`,category:`enhancement`,reasoning:`Professional editing can enhance natural features`},{recommendation:`Experiment with different angles and poses`,priority:`medium`,impactScore:75,effortRequired:`low`,category:`composition`,reasoning:`Varied poses show personality and confidence`}];let t=e.map(e=>({recommendation:e.recommendation||`No recommendation provided`,priority:e.impact_level===`high`?`high`:e.impact_level===`medium`?`medium`:`low`,impactScore:this.mapImpactToScore(e.impact_level),effortRequired:e.implementation_difficulty===`easy`?`low`:e.implementation_difficulty===`moderate`?`medium`:`high`,category:`general`,reasoning:e.reasoning||`Expert recommendation`}));return console.log(`✅ Parsed recommendations:`,t),t}mapImpactToScore(e){switch(e){case`high`:return 85;case`medium`:return 65;case`low`:return 40;default:return 50}}async generateActionableInsights(e,t){console.log(`🎯 Generating actionable insights from expert analyses...`),console.log(`📊 Expert analyses count:`,e.length);let n=[];for(let t of e)console.log(`📝 Expert ${t.expertType} recommendations:`,t.recommendations.length),n.push(...t.recommendations);if(console.log(`🔗 Total combined recommendations:`,n.length),n.length===0)return console.warn(`⚠️ No recommendations found from experts, generating fallback insights`),[{recommendation:`Improve overall image composition and framing`,priority:`high`,impactScore:80,effortRequired:`medium`,category:`composition`,reasoning:`Better composition significantly improves photo appeal`},{recommendation:`Enhance lighting conditions for more flattering results`,priority:`high`,impactScore:85,effortRequired:`low`,category:`technical`,reasoning:`Good lighting is crucial for attractive photos`},{recommendation:`Consider wardrobe and styling improvements`,priority:`medium`,impactScore:70,effortRequired:`medium`,category:`styling`,reasoning:`Appropriate styling enhances overall attractiveness`}];let r=n.sort((e,t)=>t.impactScore-e.impactScore).slice(0,10);return console.log(`✅ Final actionable insights:`,r.length),r}categorizeRecommendations(e){let t=e.filter(e=>e.effortRequired===`low`&&e.impactScore>=60),n=e.filter(e=>e.effortRequired===`high`&&e.impactScore>=70);return{quickWins:t,longTermImprovements:n}}calculateConfidenceMetrics(e,t){let n=e.map(e=>e.confidence),r=n.reduce((e,t)=>e+t,0)/n.length;return{overallConfidence:Math.round(r),confidenceFactors:{image_quality:t.imageQuality===`high`?90:70,analysis_complexity:85,expert_consensus:this.calculateExpertConsensus(e),data_availability:80},uncertaintyAreas:this.identifyUncertaintyAreas(e),confidenceReasons:[`Multiple expert validation`,`High-quality image analysis`,`Comprehensive scoring methodology`]}}calculateExpertConsensus(e){let t=e.map(e=>e.score),n=t.reduce((e,t)=>e+t,0)/t.length,r=t.reduce((e,t)=>e+(t-n)**2,0)/t.length,i=Math.sqrt(r);return Math.max(0,Math.round(100-i*2))}identifyUncertaintyAreas(e){let t=[],n=e.reduce((e,t)=>(e[t.expertType]=t.score,e),{});return Math.abs(n.photography-n.psychology)>20&&t.push(`Technical vs. psychological appeal assessment`),t}async generateDemographicInsights(e,t){return{estimatedAge:`25-35`,targetAudience:[`young professionals`,`active lifestyle`],platformOptimization:{tinder:75,bumble:82,hinge:78}}}},L=t(r()),R=t(n());function z(){let[t,n]=(0,L.useState)([]),[r,i]=(0,L.useState)(`idle`),[b,k]=(0,L.useState)([]),[A,j]=(0,L.useState)(null),[M]=(0,L.useState)(()=>new I),N=(0,L.useCallback)(e=>{let t=e.map(e=>({id:Math.random().toString(36).substr(2,9),fileName:e.name,preview:URL.createObjectURL(e),file:e}));n(e=>[...e,...t])},[]),{getRootProps:P,getInputProps:F,isDragActive:z}=v({onDrop:N,accept:{"image/*":[`.jpeg`,`.jpg`,`.png`,`.webp`]},maxFiles:5}),B=e=>{n(t=>{let n=t.filter(t=>t.id!==e),r=t.find(t=>t.id===e);return r&&URL.revokeObjectURL(r.preview),n})},V=async()=>{if(t.length!==0){i(`processing`),k([]),j(null);try{for(let e of t){let t=document.createElement(`canvas`),n=t.getContext(`2d`),r=new Image;await new Promise(t=>{r.onload=t,r.src=e.preview}),t.width=r.width,t.height=r.height,n?.drawImage(r,0,0);let i=t.toDataURL(`image/jpeg`,.8).split(`,`)[1],a=await M.analyzeImage(i,e.fileName,{analysisDepth:`comprehensive`,includeComparative:!0,generateImprovements:!0},e=>{j(e)});k(e=>[...e,a])}i(`done`)}catch(e){console.error(`Advanced analysis failed:`,e),i(`idle`)}}};return(0,L.useEffect)(()=>()=>{t.forEach(e=>URL.revokeObjectURL(e.preview))},[t]),(0,R.jsxs)(`div`,{className:`min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50`,children:[(0,R.jsx)(`header`,{className:`border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50`,children:(0,R.jsx)(`div`,{className:`container mx-auto px-4 py-4`,children:(0,R.jsx)(`div`,{className:`flex items-center justify-between`,children:(0,R.jsxs)(`div`,{className:`flex items-center space-x-4`,children:[(0,R.jsx)(u,{variant:`ghost`,size:`sm`,asChild:!0,children:(0,R.jsxs)(e,{to:`/image-analyzer`,children:[(0,R.jsx)(f,{className:`h-4 w-4 mr-2`}),`Back to Basic`]})}),(0,R.jsxs)(`div`,{className:`flex items-center space-x-2`,children:[(0,R.jsx)(h,{className:`h-6 w-6 text-purple-600`}),(0,R.jsx)(`h1`,{className:`text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent`,children:`Advanced Image Analyzer`}),(0,R.jsx)(d,{variant:`secondary`,className:`bg-purple-100 text-purple-700`,children:`Powered by OpenRouter o3`})]})]})})})}),(0,R.jsxs)(`main`,{className:`container mx-auto px-4 py-8`,children:[r===`idle`&&(0,R.jsxs)(`div`,{className:`max-w-4xl mx-auto`,children:[(0,R.jsxs)(`div`,{className:`text-center mb-8`,children:[(0,R.jsx)(`h2`,{className:`text-3xl font-bold text-gray-900 mb-4`,children:`Professional-Grade Photo Analysis`}),(0,R.jsx)(`p`,{className:`text-lg text-gray-600 mb-6`,children:`Get expert-level insights from our advanced AI system powered by OpenRouter's o3 model`}),(0,R.jsxs)(`div`,{className:`grid grid-cols-1 md:grid-cols-3 gap-6 mb-8`,children:[(0,R.jsxs)(a,{children:[(0,R.jsxs)(c,{className:`text-center`,children:[(0,R.jsx)(p,{className:`h-8 w-8 text-purple-600 mx-auto mb-2`}),(0,R.jsx)(l,{className:`text-lg`,children:`Multi-Expert Analysis`})]}),(0,R.jsx)(o,{children:(0,R.jsx)(`p`,{className:`text-sm text-gray-600`,children:`Photography, psychology, fashion, data science, and dating coach experts`})})]}),(0,R.jsxs)(a,{children:[(0,R.jsxs)(c,{className:`text-center`,children:[(0,R.jsx)(S,{className:`h-8 w-8 text-blue-600 mx-auto mb-2`}),(0,R.jsx)(l,{className:`text-lg`,children:`Advanced Scoring`})]}),(0,R.jsx)(o,{children:(0,R.jsx)(`p`,{className:`text-sm text-gray-600`,children:`Percentile rankings, improvement potential, and market competitiveness`})})]}),(0,R.jsxs)(a,{children:[(0,R.jsxs)(c,{className:`text-center`,children:[(0,R.jsx)(x,{className:`h-8 w-8 text-green-600 mx-auto mb-2`}),(0,R.jsx)(l,{className:`text-lg`,children:`Actionable Insights`})]}),(0,R.jsx)(o,{children:(0,R.jsx)(`p`,{className:`text-sm text-gray-600`,children:`Prioritized recommendations with impact scores and effort estimates`})})]})]})]}),(0,R.jsxs)(`div`,{...P(),className:`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${z?`border-purple-400 bg-purple-50`:`border-gray-300 hover:border-purple-400`}`,children:[(0,R.jsx)(`input`,{...F()}),(0,R.jsx)(g,{className:`h-12 w-12 text-gray-400 mx-auto mb-4`}),(0,R.jsx)(`h3`,{className:`text-xl font-semibold text-gray-900 mb-2`,children:`Upload Your Photos`}),(0,R.jsx)(`p`,{className:`text-gray-600 mb-4`,children:`Drag and drop your images here, or click to select files`}),(0,R.jsx)(`p`,{className:`text-sm text-gray-500`,children:`Supports JPEG, PNG, WebP • Max 5 files • Professional analysis`})]}),t.length>0&&(0,R.jsxs)(`div`,{className:`mt-8`,children:[(0,R.jsxs)(`h3`,{className:`text-lg font-semibold mb-4`,children:[`Selected Images (`,t.length,`)`]}),(0,R.jsx)(`div`,{className:`grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6`,children:t.map(e=>(0,R.jsxs)(`div`,{className:`relative group`,children:[(0,R.jsx)(`img`,{src:e.preview,alt:e.fileName,className:`w-full h-32 object-cover rounded-lg border`}),(0,R.jsx)(`button`,{onClick:()=>B(e.id),className:`absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity`,children:(0,R.jsx)(C,{className:`h-4 w-4`})}),(0,R.jsx)(`p`,{className:`text-xs text-gray-600 mt-1 truncate`,children:e.fileName})]},e.id))}),(0,R.jsx)(`div`,{className:`text-center`,children:(0,R.jsxs)(u,{onClick:V,size:`lg`,className:`bg-gradient-to-r from-purple-600 to-blue-600`,children:[(0,R.jsx)(m,{className:`mr-2 h-5 w-5`}),`Start Advanced Analysis`]})})]}),(0,R.jsx)(_,{})]}),r===`processing`&&(0,R.jsxs)(`div`,{className:`max-w-2xl mx-auto text-center`,children:[(0,R.jsxs)(`div`,{className:`mb-8`,children:[(0,R.jsx)(h,{className:`h-16 w-16 text-purple-600 mx-auto mb-4 animate-pulse`}),(0,R.jsx)(`h2`,{className:`text-2xl font-bold text-gray-900 mb-2`,children:`Advanced Analysis in Progress`}),(0,R.jsx)(`p`,{className:`text-gray-600`,children:`Our expert AI system is analyzing your photos with professional-grade precision`})]}),A&&(0,R.jsxs)(`div`,{className:`space-y-4`,children:[(0,R.jsxs)(`div`,{className:`bg-white rounded-lg p-6 shadow-sm border`,children:[(0,R.jsxs)(`div`,{className:`flex items-center justify-between mb-2`,children:[(0,R.jsx)(`span`,{className:`font-medium text-gray-900`,children:A.message}),(0,R.jsxs)(`span`,{className:`text-sm text-gray-500`,children:[Math.round(A.progress),`%`]})]}),(0,R.jsx)(w,{value:A.progress,className:`h-2`}),A.currentExpert&&(0,R.jsxs)(`p`,{className:`text-sm text-gray-600 mt-2`,children:[`Current Expert: `,A.currentExpert]}),A.estimatedTimeRemaining&&(0,R.jsxs)(`p`,{className:`text-xs text-gray-500 mt-1`,children:[`Estimated time remaining: `,A.estimatedTimeRemaining,`s`]})]}),(0,R.jsxs)(`div`,{className:`flex items-center justify-center space-x-2 text-sm text-gray-500`,children:[(0,R.jsx)(y,{className:`h-4 w-4 animate-spin`}),(0,R.jsx)(`span`,{children:`Processing with OpenRouter o3 model...`})]})]})]}),r===`done`&&b.length>0&&(0,R.jsxs)(`div`,{className:`max-w-6xl mx-auto`,children:[(0,R.jsxs)(`div`,{className:`text-center mb-8`,children:[(0,R.jsx)(`h2`,{className:`text-3xl font-bold text-gray-900 mb-2`,children:`Advanced Analysis Complete`}),(0,R.jsx)(`p`,{className:`text-gray-600`,children:`Professional insights from our expert AI system`})]}),(0,R.jsx)(`div`,{className:`space-y-8`,children:b.map((e,t)=>(0,R.jsxs)(a,{className:`overflow-hidden`,children:[(0,R.jsx)(c,{children:(0,R.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,R.jsxs)(l,{className:`flex items-center space-x-2`,children:[(0,R.jsx)(`img`,{src:e.preview,alt:e.fileName,className:`w-12 h-12 object-cover rounded-lg`}),(0,R.jsx)(`span`,{children:e.fileName})]}),(0,R.jsxs)(`div`,{className:`flex items-center space-x-4`,children:[(0,R.jsxs)(d,{variant:`outline`,className:`text-lg font-bold`,children:[e.overallScore,`/100`]}),(0,R.jsxs)(d,{variant:`secondary`,children:[e.percentileRank,`th percentile`]})]})]})}),(0,R.jsx)(o,{children:(0,R.jsxs)(T,{defaultValue:`overview`,className:`w-full`,children:[(0,R.jsxs)(D,{className:`grid w-full grid-cols-5`,children:[(0,R.jsx)(O,{value:`overview`,children:`Overview`}),(0,R.jsx)(O,{value:`experts`,children:`Expert Analysis`}),(0,R.jsx)(O,{value:`insights`,children:`Insights`}),(0,R.jsx)(O,{value:`demographics`,children:`Demographics`}),(0,R.jsx)(O,{value:`comparison`,children:`Comparison`})]}),(0,R.jsxs)(E,{value:`overview`,className:`space-y-6`,children:[(0,R.jsxs)(`div`,{className:`grid grid-cols-1 md:grid-cols-4 gap-4`,children:[(0,R.jsxs)(a,{children:[(0,R.jsx)(c,{className:`pb-2`,children:(0,R.jsx)(l,{className:`text-sm`,children:`Overall Score`})}),(0,R.jsx)(o,{children:(0,R.jsxs)(`div`,{className:`text-2xl font-bold text-purple-600`,children:[e.overallScore,`/100`]})})]}),(0,R.jsxs)(a,{children:[(0,R.jsx)(c,{className:`pb-2`,children:(0,R.jsx)(l,{className:`text-sm`,children:`Percentile Rank`})}),(0,R.jsx)(o,{children:(0,R.jsxs)(`div`,{className:`text-2xl font-bold text-blue-600`,children:[e.percentileRank,`th`]})})]}),(0,R.jsxs)(a,{children:[(0,R.jsx)(c,{className:`pb-2`,children:(0,R.jsx)(l,{className:`text-sm`,children:`Improvement Potential`})}),(0,R.jsx)(o,{children:(0,R.jsxs)(`div`,{className:`text-2xl font-bold text-green-600`,children:[e.improvementPotential,`%`]})})]}),(0,R.jsxs)(a,{children:[(0,R.jsx)(c,{className:`pb-2`,children:(0,R.jsx)(l,{className:`text-sm`,children:`Market Competitiveness`})}),(0,R.jsx)(o,{children:(0,R.jsxs)(`div`,{className:`text-2xl font-bold text-orange-600`,children:[e.marketCompetitiveness,`/100`]})})]})]}),(0,R.jsxs)(`div`,{className:`grid grid-cols-1 md:grid-cols-2 gap-6`,children:[(0,R.jsxs)(a,{children:[(0,R.jsxs)(c,{children:[(0,R.jsx)(l,{className:`text-lg`,children:`Quick Wins`}),(0,R.jsx)(s,{children:`Easy improvements with high impact`})]}),(0,R.jsx)(o,{children:(0,R.jsx)(`div`,{className:`space-y-3`,children:e.quickWins.slice(0,3).map((e,t)=>(0,R.jsxs)(`div`,{className:`flex items-start space-x-3`,children:[(0,R.jsx)(d,{variant:`outline`,className:`text-xs`,children:e.impactScore}),(0,R.jsx)(`p`,{className:`text-sm`,children:e.recommendation})]},t))})})]}),(0,R.jsxs)(a,{children:[(0,R.jsxs)(c,{children:[(0,R.jsx)(l,{className:`text-lg`,children:`Long-term Improvements`}),(0,R.jsx)(s,{children:`Strategic changes for maximum impact`})]}),(0,R.jsx)(o,{children:(0,R.jsx)(`div`,{className:`space-y-3`,children:e.longTermImprovements.slice(0,3).map((e,t)=>(0,R.jsxs)(`div`,{className:`flex items-start space-x-3`,children:[(0,R.jsx)(d,{variant:`outline`,className:`text-xs`,children:e.impactScore}),(0,R.jsx)(`p`,{className:`text-sm`,children:e.recommendation})]},t))})})]})]})]}),(0,R.jsx)(E,{value:`experts`,className:`space-y-4`,children:e.expertAnalyses.map((e,t)=>(0,R.jsxs)(a,{children:[(0,R.jsxs)(c,{children:[(0,R.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,R.jsxs)(l,{className:`text-lg capitalize`,children:[e.expertType.replace(`_`,` `),` Expert`]}),(0,R.jsxs)(`div`,{className:`flex items-center space-x-2`,children:[(0,R.jsxs)(d,{variant:`outline`,children:[e.score,`/100`]}),(0,R.jsxs)(d,{variant:`secondary`,children:[e.confidence,`% confident`]})]})]}),(0,R.jsx)(s,{children:e.credentials})]}),(0,R.jsxs)(o,{children:[(0,R.jsx)(`p`,{className:`text-sm text-gray-700 mb-4`,children:e.analysis}),(0,R.jsxs)(`div`,{className:`space-y-2`,children:[(0,R.jsx)(`h4`,{className:`font-medium text-sm`,children:`Key Observations:`}),(0,R.jsx)(`ul`,{className:`text-sm text-gray-600 space-y-1`,children:e.keyObservations.map((e,t)=>(0,R.jsxs)(`li`,{className:`flex items-start space-x-2`,children:[(0,R.jsx)(`span`,{className:`text-purple-500`,children:`•`}),(0,R.jsx)(`span`,{children:e})]},t))})]})]})]},t))}),(0,R.jsx)(E,{value:`insights`,className:`space-y-4`,children:(0,R.jsxs)(`div`,{className:`grid grid-cols-1 md:grid-cols-2 gap-6`,children:[(0,R.jsxs)(a,{children:[(0,R.jsxs)(c,{children:[(0,R.jsx)(l,{children:`All Recommendations`}),(0,R.jsx)(s,{children:`Prioritized by impact potential`})]}),(0,R.jsx)(o,{children:(0,R.jsx)(`div`,{className:`space-y-4`,children:e.actionableInsights.map((e,t)=>(0,R.jsxs)(`div`,{className:`border-l-4 border-purple-200 pl-4`,children:[(0,R.jsxs)(`div`,{className:`flex items-center justify-between mb-2`,children:[(0,R.jsxs)(d,{variant:e.priority===`high`?`default`:`secondary`,className:`text-xs`,children:[e.priority,` priority`]}),(0,R.jsxs)(`span`,{className:`text-xs text-gray-500`,children:[`Impact: `,e.impactScore,`/100`]})]}),(0,R.jsx)(`p`,{className:`text-sm font-medium`,children:e.recommendation}),(0,R.jsx)(`p`,{className:`text-xs text-gray-600 mt-1`,children:e.reasoning})]},t))})})]}),(0,R.jsxs)(a,{children:[(0,R.jsxs)(c,{children:[(0,R.jsx)(l,{children:`Confidence Analysis`}),(0,R.jsx)(s,{children:`Analysis reliability metrics`})]}),(0,R.jsx)(o,{children:(0,R.jsxs)(`div`,{className:`space-y-4`,children:[(0,R.jsxs)(`div`,{children:[(0,R.jsxs)(`div`,{className:`flex justify-between text-sm mb-1`,children:[(0,R.jsx)(`span`,{children:`Overall Confidence`}),(0,R.jsxs)(`span`,{children:[e.confidenceMetrics.overallConfidence,`%`]})]}),(0,R.jsx)(w,{value:e.confidenceMetrics.overallConfidence})]}),Object.entries(e.confidenceMetrics.confidenceFactors).map(([e,t])=>(0,R.jsxs)(`div`,{children:[(0,R.jsxs)(`div`,{className:`flex justify-between text-sm mb-1`,children:[(0,R.jsx)(`span`,{className:`capitalize`,children:e.replace(`_`,` `)}),(0,R.jsxs)(`span`,{children:[t,`%`]})]}),(0,R.jsx)(w,{value:t,className:`h-2`})]},e)),(0,R.jsxs)(`div`,{className:`mt-4`,children:[(0,R.jsx)(`h4`,{className:`font-medium text-sm mb-2`,children:`Confidence Reasons:`}),(0,R.jsx)(`ul`,{className:`text-xs text-gray-600 space-y-1`,children:e.confidenceMetrics.confidenceReasons.map((e,t)=>(0,R.jsxs)(`li`,{className:`flex items-start space-x-2`,children:[(0,R.jsx)(`span`,{className:`text-green-500`,children:`✓`}),(0,R.jsx)(`span`,{children:e})]},t))})]})]})})]})]})}),(0,R.jsx)(E,{value:`demographics`,className:`space-y-4`,children:(0,R.jsxs)(`div`,{className:`grid grid-cols-1 md:grid-cols-2 gap-6`,children:[(0,R.jsxs)(a,{children:[(0,R.jsxs)(c,{children:[(0,R.jsx)(l,{children:`Demographic Insights`}),(0,R.jsx)(s,{children:`Target audience analysis`})]}),(0,R.jsx)(o,{children:(0,R.jsxs)(`div`,{className:`space-y-4`,children:[(0,R.jsxs)(`div`,{children:[(0,R.jsx)(`h4`,{className:`font-medium text-sm mb-2`,children:`Estimated Age Range:`}),(0,R.jsx)(`p`,{className:`text-lg font-semibold text-purple-600`,children:e.demographicInsights.estimatedAge})]}),(0,R.jsxs)(`div`,{children:[(0,R.jsx)(`h4`,{className:`font-medium text-sm mb-2`,children:`Target Audience:`}),(0,R.jsx)(`div`,{className:`flex flex-wrap gap-2`,children:e.demographicInsights.targetAudience.map((e,t)=>(0,R.jsx)(d,{variant:`outline`,className:`text-xs`,children:e},t))})]})]})})]}),(0,R.jsxs)(a,{children:[(0,R.jsxs)(c,{children:[(0,R.jsx)(l,{children:`Platform Optimization`}),(0,R.jsx)(s,{children:`Performance by dating platform`})]}),(0,R.jsx)(o,{children:(0,R.jsx)(`div`,{className:`space-y-4`,children:Object.entries(e.demographicInsights.platformOptimization).map(([e,t])=>(0,R.jsxs)(`div`,{children:[(0,R.jsxs)(`div`,{className:`flex justify-between text-sm mb-1`,children:[(0,R.jsx)(`span`,{className:`capitalize font-medium`,children:e}),(0,R.jsxs)(`span`,{children:[t,`/100`]})]}),(0,R.jsx)(w,{value:t,className:`h-2`})]},e))})})]})]})}),(0,R.jsx)(E,{value:`comparison`,className:`space-y-4`,children:(0,R.jsxs)(a,{children:[(0,R.jsxs)(c,{children:[(0,R.jsx)(l,{children:`Market Comparison`}),(0,R.jsx)(s,{children:`How you compare to other profiles`})]}),(0,R.jsx)(o,{children:(0,R.jsxs)(`div`,{className:`grid grid-cols-1 md:grid-cols-2 gap-6`,children:[(0,R.jsxs)(`div`,{children:[(0,R.jsx)(`h4`,{className:`font-medium text-sm mb-4`,children:`Market Position`}),(0,R.jsxs)(`div`,{className:`text-center p-6 bg-gray-50 rounded-lg`,children:[(0,R.jsx)(`div`,{className:`text-3xl font-bold text-purple-600 mb-2`,children:e.comparativeAnalysis.marketPosition.replace(`_`,` `).toUpperCase()}),(0,R.jsxs)(`p`,{className:`text-sm text-gray-600`,children:[`Top `,e.comparativeAnalysis.topPercentile,`% of profiles`]})]})]}),(0,R.jsxs)(`div`,{children:[(0,R.jsx)(`h4`,{className:`font-medium text-sm mb-4`,children:`Competitive Advantages`}),(0,R.jsx)(`div`,{className:`space-y-2`,children:e.comparativeAnalysis.competitiveAdvantages.map((e,t)=>(0,R.jsxs)(`div`,{className:`flex items-center space-x-2`,children:[(0,R.jsx)(`span`,{className:`text-green-500`,children:`✓`}),(0,R.jsx)(`span`,{className:`text-sm`,children:e})]},t))}),(0,R.jsx)(`h4`,{className:`font-medium text-sm mb-2 mt-4`,children:`Areas for Improvement`}),(0,R.jsx)(`div`,{className:`space-y-2`,children:e.comparativeAnalysis.areasForImprovement.map((e,t)=>(0,R.jsxs)(`div`,{className:`flex items-center space-x-2`,children:[(0,R.jsx)(`span`,{className:`text-orange-500`,children:`→`}),(0,R.jsx)(`span`,{className:`text-sm`,children:e})]},t))})]})]})})]})})]})})]},e.id))}),(0,R.jsxs)(`div`,{className:`text-center mt-8`,children:[(0,R.jsx)(u,{asChild:!0,size:`lg`,className:`mr-4`,children:(0,R.jsxs)(e,{to:`/bio-analyzer-pro`,children:[`Analyze Bio with Advanced AI `,(0,R.jsx)(x,{className:`ml-2 h-5 w-5`})]})}),(0,R.jsx)(u,{variant:`outline`,onClick:()=>{i(`idle`),k([]),n([])},children:`Analyze More Photos`})]})]})]})]})}const B=z;export{B as component};