import{__toESM as e,require_jsx_runtime as t}from"./main-B4G73TvM.js";var n=e(t());const r=function(){return(0,n.jsxs)(`div`,{className:`flex flex-col items-center justify-center h-screen`,children:[(0,n.jsx)(`img`,{src:`/Selection_405.png`,alt:`Not Found`,className:`w-1/2`}),(0,n.jsx)(`h1`,{className:`text-4xl font-bold`,children:`Page Not Found`}),(0,n.jsx)(`p`,{className:`text-lg`,children:`The page you are looking for does not exist.`})]})};export{r as component};