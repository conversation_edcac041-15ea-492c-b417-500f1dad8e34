import{__toESM as e,require_jsx_runtime as t,require_react as n}from"./main-B4G73TvM.js";function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),n===!1||!r.defaultPrevented)return t?.(r)}}var i=e(n(),1),a=e(t(),1);function o(e,t){let n=i.createContext(t),r=e=>{let{children:t,...r}=e,o=i.useMemo(()=>r,Object.values(r));return(0,a.jsx)(n.Provider,{value:o,children:t})};r.displayName=e+`Provider`;function o(r){let a=i.useContext(n);if(a)return a;if(t!==void 0)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}return[r,o]}function s(e,t=[]){let n=[];function r(t,r){let o=i.createContext(r),s=n.length;n=[...n,r];let c=t=>{let{scope:n,children:r,...c}=t,l=n?.[e]?.[s]||o,u=i.useMemo(()=>c,Object.values(c));return(0,a.jsx)(l.Provider,{value:u,children:r})};c.displayName=t+`Provider`;function l(n,a){let c=a?.[e]?.[s]||o,l=i.useContext(c);if(l)return l;if(r!==void 0)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}return[c,l]}let o=()=>{let t=n.map(e=>i.createContext(e));return function(n){let r=n?.[e]||t;return i.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return o.scopeName=e,[r,c(o,...t)]}function c(...e){let t=e[0];if(e.length===1)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e),a=i[`__scope${r}`];return{...t,...a}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}var l=globalThis?.document?i.useLayoutEffect:()=>{};function u(e){let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...e)=>t.current?.(...e),[])}function d({prop:e,defaultProp:t,onChange:n=()=>{}}){let[r,a]=f({defaultProp:t,onChange:n}),o=e!==void 0,s=o?e:r,c=u(n),l=i.useCallback(t=>{if(o){let n=t,r=typeof t==`function`?n(e):t;r!==e&&c(r)}else a(t)},[o,e,a,c]);return[s,l]}function f({defaultProp:e,onChange:t}){let n=i.useState(e),[r]=n,a=i.useRef(r),o=u(t);return i.useEffect(()=>{a.current!==r&&(o(r),a.current=r)},[r,a,o]),n}export{r as composeEventHandlers,o as createContext2,s as createContextScope,u as useCallbackRef,d as useControllableState,l as useLayoutEffect2};