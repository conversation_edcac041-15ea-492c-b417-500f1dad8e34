import{OrganizationList as e,OrganizationProfile as t,SignIn as n,SignUp as r,UserProfile as i,__toESM as a,errorThrower as o,incompatibleRoutingWithPathProvidedError as s,noPathProvidedError as c,require_jsx_runtime as l,useLocation as u,useParams as d}from"./main-B4G73TvM.js";function f(e,t,n){let r=t.path||n?.path,i=t.routing||n?.routing||`path`;return i===`path`?r?{...n,...t,routing:`path`}:o.throw(c(e)):t.path?o.throw(s(e)):{...n,...t,path:void 0}}var p=a(l()),m=()=>{let{_splat:e}=d({strict:!1}),{pathname:t}=u(),n=e||``,r=t.replace(n,``).replace(/\/$/,``).replace(/^\//,``).trim();return`/${r}`},h=Object.assign(e=>{let t=m();return(0,p.jsx)(i,{...f(`UserProfile`,e,{path:t})})},{...i}),g=Object.assign(e=>{let n=m();return(0,p.jsx)(t,{...f(`OrganizationProfile`,e,{path:n})})},{...t}),_=Object.assign(t=>{let n=m();return(0,p.jsx)(e,{...f(`OrganizationList`,t,{path:n})})},{...e}),v=e=>{let t=m();return(0,p.jsx)(n,{...f(`SignIn`,e,{path:t})})},y=e=>{let t=m();return(0,p.jsx)(r,{...f(`SignUp`,e,{path:t})})};export{v as SignIn,y as SignUp,h as UserProfile};