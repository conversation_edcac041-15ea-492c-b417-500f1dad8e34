import{Link as e,__esmMin as t,__export as n,__toCommonJS as r,__toESM as i,cn as a,require_jsx_runtime as o,require_react as s}from"./main-B4G73TvM.js";import{Button as c,createLucideIcon as l}from"./createLucideIcon-JB7IMeGf.js";import{Heart as u,Star as d}from"./star-Dm2nqXPr.js";import{Sparkles as f}from"./sparkles-BbQK6jEu.js";import{TrendingUp as p}from"./trending-up-BvkHvnfP.js";const m=l(`CloudUpload`,[[`path`,{d:`M12 13v8`,key:`1l5pq0`}],[`path`,{d:`M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242`,key:`1pljnt`}],[`path`,{d:`m8 17 4-4 4 4`,key:`1quai1`}]]),h=l(`<PERSON>an<PERSON>earch`,[[`path`,{d:`M3 7V5a2 2 0 0 1 2-2h2`,key:`aa7l1z`}],[`path`,{d:`M17 3h2a2 2 0 0 1 2 2v2`,key:`4qcy5o`}],[`path`,{d:`M21 17v2a2 2 0 0 1-2 2h-2`,key:`6vwrx8`}],[`path`,{d:`M7 21H5a2 2 0 0 1-2-2v-2`,key:`ioqczr`}],[`circle`,{cx:`12`,cy:`12`,r:`3`,key:`1v7zrd`}],[`path`,{d:`m16 16-1.9-1.9`,key:`1dq9hf`}]]);var g=i(s(),1);const _=(0,g.createContext)({});function v(e){let t=(0,g.useRef)(null);return t.current===null&&(t.current=e()),t.current}const y=typeof window<`u`,b=y?g.useLayoutEffect:g.useEffect,x=(0,g.createContext)(null);function S(e,t){e.indexOf(t)===-1&&e.push(t)}function C(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}const w=(e,t,n)=>n>t?t:n<e?e:n;let ee=()=>{},te=()=>{};const T={},ne=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e);function re(e){return typeof e==`object`&&!!e}const ie=e=>/^0[^.\s]+$/u.test(e);function ae(e){let t;return()=>(t===void 0&&(t=e()),t)}const E=e=>e,oe=(e,t)=>n=>t(e(n)),se=(...e)=>e.reduce(oe),ce=(e,t,n)=>{let r=t-e;return r===0?1:(n-e)/r};var le=class{constructor(){this.subscriptions=[]}add(e){return S(this.subscriptions,e),()=>C(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r)if(r===1)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}};const D=e=>e*1e3,O=e=>e/1e3;function ue(e,t){return t?e*(1e3/t):0}const de=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,fe=1e-7,pe=12;function me(e,t,n,r,i){let a,o,s=0;do o=t+(n-t)/2,a=de(o,r,i)-e,a>0?n=o:t=o;while(Math.abs(a)>fe&&++s<pe);return o}function he(e,t,n,r){if(e===t&&n===r)return E;let i=t=>me(t,0,1,e,n);return e=>e===0||e===1?e:de(i(e),t,r)}const ge=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,_e=e=>t=>1-e(1-t),ve=he(.33,1.53,.69,.99),ye=_e(ve),be=ge(ye),xe=e=>(e*=2)<1?.5*ye(e):.5*(2-2**(-10*(e-1))),Se=e=>1-Math.sin(Math.acos(e)),Ce=_e(Se),we=ge(Se),Te=he(.42,0,1,1),Ee=he(0,0,.58,1),De=he(.42,0,.58,1),Oe=e=>Array.isArray(e)&&typeof e[0]!=`number`,ke=e=>Array.isArray(e)&&typeof e[0]==`number`,Ae={linear:E,easeIn:Te,easeInOut:De,easeOut:Ee,circIn:Se,circInOut:we,circOut:Ce,backIn:ye,backInOut:be,backOut:ve,anticipate:xe},je=e=>typeof e==`string`,Me=e=>{if(ke(e)){te(e.length===4,`Cubic bezier arrays must contain four numerical values.`);let[t,n,r,i]=e;return he(t,n,r,i)}else if(je(e))return te(Ae[e]!==void 0,`Invalid easing type '${e}'`),Ae[e];return e},Ne=[`setup`,`read`,`resolveKeyframes`,`preUpdate`,`update`,`preRender`,`render`,`postRender`],k={value:null,addProjectionMetrics:null};function Pe(e,t){let n=new Set,r=new Set,i=!1,a=!1,o=new WeakSet,s={delta:0,timestamp:0,isProcessing:!1},c=0;function l(t){o.has(t)&&(u.schedule(t),e()),c++,t(s)}let u={schedule:(e,t=!1,a=!1)=>{let s=a&&i,c=s?n:r;return t&&o.add(e),c.has(e)||c.add(e),e},cancel:e=>{r.delete(e),o.delete(e)},process:e=>{if(s=e,i){a=!0;return}i=!0,[n,r]=[r,n],n.forEach(l),t&&k.value&&k.value.frameloop[t].push(c),c=0,n.clear(),i=!1,a&&(a=!1,u.process(e))}};return u}const Fe=40;function Ie(e,t){let n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},a=()=>n=!0,o=Ne.reduce((e,n)=>(e[n]=Pe(a,t?n:void 0),e),{}),{setup:s,read:c,resolveKeyframes:l,preUpdate:u,update:d,preRender:f,render:p,postRender:m}=o,h=()=>{let a=T.useManualTiming?i.timestamp:performance.now();n=!1,T.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(a-i.timestamp,Fe),1)),i.timestamp=a,i.isProcessing=!0,s.process(i),c.process(i),l.process(i),u.process(i),d.process(i),f.process(i),p.process(i),m.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(h))},g=()=>{n=!0,r=!0,i.isProcessing||e(h)},_=Ne.reduce((e,t)=>{let r=o[t];return e[t]=(e,t=!1,i=!1)=>(n||g(),r.schedule(e,t,i)),e},{}),v=e=>{for(let t=0;t<Ne.length;t++)o[Ne[t]].cancel(e)};return{schedule:_,cancel:v,state:i,steps:o}}const{schedule:A,cancel:j,state:M,steps:Le}=Ie(typeof requestAnimationFrame<`u`?requestAnimationFrame:E,!0);let Re;function ze(){Re=void 0}const N={now:()=>(Re===void 0&&N.set(M.isProcessing||T.useManualTiming?M.timestamp:performance.now()),Re),set:e=>{Re=e,queueMicrotask(ze)}},P={layout:0,mainThread:0,waapi:0},Be=e=>t=>typeof t==`string`&&t.startsWith(e),Ve=Be(`--`),He=Be(`var(--`),Ue=e=>{let t=He(e);return t?We.test(e.split(`/*`)[0].trim()):!1},We=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Ge={test:e=>typeof e==`number`,parse:parseFloat,transform:e=>e},Ke={...Ge,transform:e=>w(0,1,e)},qe={...Ge,default:1},Je=e=>Math.round(e*1e5)/1e5,Ye=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Xe(e){return e==null}const Ze=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Qe=(e,t)=>n=>!!(typeof n==`string`&&Ze.test(n)&&n.startsWith(e)||t&&!Xe(n)&&Object.prototype.hasOwnProperty.call(n,t)),$e=(e,t,n)=>r=>{if(typeof r!=`string`)return r;let[i,a,o,s]=r.match(Ye);return{[e]:parseFloat(i),[t]:parseFloat(a),[n]:parseFloat(o),alpha:s===void 0?1:parseFloat(s)}},et=e=>w(0,255,e),tt={...Ge,transform:e=>Math.round(et(e))},F={test:Qe(`rgb`,`red`),parse:$e(`red`,`green`,`blue`),transform:({red:e,green:t,blue:n,alpha:r=1})=>`rgba(`+tt.transform(e)+`, `+tt.transform(t)+`, `+tt.transform(n)+`, `+Je(Ke.transform(r))+`)`};function nt(e){let t=``,n=``,r=``,i=``;return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const rt={test:Qe(`#`),parse:nt,transform:F.transform},it=e=>({test:t=>typeof t==`string`&&t.endsWith(e)&&t.split(` `).length===1,parse:parseFloat,transform:t=>`${t}${e}`}),I=it(`deg`),L=it(`%`),R=it(`px`),at=it(`vh`),ot=it(`vw`),st=(()=>({...L,parse:e=>L.parse(e)/100,transform:e=>L.transform(e*100)}))(),ct={test:Qe(`hsl`,`hue`),parse:$e(`hue`,`saturation`,`lightness`),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>`hsla(`+Math.round(e)+`, `+L.transform(Je(t))+`, `+L.transform(Je(n))+`, `+Je(Ke.transform(r))+`)`},z={test:e=>F.test(e)||rt.test(e)||ct.test(e),parse:e=>F.test(e)?F.parse(e):ct.test(e)?ct.parse(e):rt.parse(e),transform:e=>typeof e==`string`?e:e.hasOwnProperty(`red`)?F.transform(e):ct.transform(e),getAnimatableNone:e=>{let t=z.parse(e);return t.alpha=0,z.transform(t)}},lt=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function ut(e){return isNaN(e)&&typeof e==`string`&&(e.match(Ye)?.length||0)+(e.match(lt)?.length||0)>0}const dt=`number`,ft=`color`,pt=`var`,mt=`var(`,ht="${}",gt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function _t(e){let t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],a=0,o=t.replace(gt,e=>(z.test(e)?(r.color.push(a),i.push(ft),n.push(z.parse(e))):e.startsWith(mt)?(r.var.push(a),i.push(pt),n.push(e)):(r.number.push(a),i.push(dt),n.push(parseFloat(e))),++a,ht)),s=o.split(ht);return{values:n,split:s,indexes:r,types:i}}function vt(e){return _t(e).values}function yt(e){let{split:t,types:n}=_t(e),r=t.length;return e=>{let i=``;for(let a=0;a<r;a++)if(i+=t[a],e[a]!==void 0){let t=n[a];t===dt?i+=Je(e[a]):t===ft?i+=z.transform(e[a]):i+=e[a]}return i}}const bt=e=>typeof e==`number`?0:z.test(e)?z.getAnimatableNone(e):e;function xt(e){let t=vt(e),n=yt(e);return n(t.map(bt))}const B={test:ut,parse:vt,createTransformer:yt,getAnimatableNone:xt};function St(e,t,n){return n<0&&(n+=1),n>1&&--n,n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Ct({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,a=0,o=0;if(!t)i=a=o=n;else{let r=n<.5?n*(1+t):n+t-n*t,s=2*n-r;i=St(s,r,e+1/3),a=St(s,r,e),o=St(s,r,e-1/3)}return{red:Math.round(i*255),green:Math.round(a*255),blue:Math.round(o*255),alpha:r}}function wt(e,t){return n=>n>0?t:e}const V=(e,t,n)=>e+(t-e)*n,Tt=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},Et=[rt,F,ct],Dt=e=>Et.find(t=>t.test(e));function Ot(e){let t=Dt(e);if(ee(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let n=t.parse(e);return t===ct&&(n=Ct(n)),n}const kt=(e,t)=>{let n=Ot(e),r=Ot(t);if(!n||!r)return wt(e,t);let i={...n};return e=>(i.red=Tt(n.red,r.red,e),i.green=Tt(n.green,r.green,e),i.blue=Tt(n.blue,r.blue,e),i.alpha=V(n.alpha,r.alpha,e),F.transform(i))},At=new Set([`none`,`hidden`]);function jt(e,t){return At.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function Mt(e,t){return n=>V(e,t,n)}function Nt(e){return typeof e==`number`?Mt:typeof e==`string`?Ue(e)?wt:z.test(e)?kt:Lt:Array.isArray(e)?Pt:typeof e==`object`?z.test(e)?kt:Ft:wt}function Pt(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>Nt(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function Ft(e,t){let n={...e,...t},r={};for(let i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=Nt(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}function It(e,t){let n=[],r={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let a=t.types[i],o=e.indexes[a][r[a]],s=e.values[o]??0;n[i]=s,r[a]++}return n}const Lt=(e,t)=>{let n=B.createTransformer(t),r=_t(e),i=_t(t),a=r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length;return a?At.has(e)&&!i.values.length||At.has(t)&&!r.values.length?jt(e,t):se(Pt(It(r,i),i.values),n):(ee(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),wt(e,t))};function Rt(e,t,n){if(typeof e==`number`&&typeof t==`number`&&typeof n==`number`)return V(e,t,n);let r=Nt(e);return r(e,t)}const zt=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>A.update(t,e),stop:()=>j(t),now:()=>M.isProcessing?M.timestamp:N.now()}},Bt=(e,t,n=10)=>{let r=``,i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=Math.round(e(t/(i-1))*1e4)/1e4+`, `;return`linear(${r.substring(0,r.length-2)})`},Vt=2e4;function Ht(e){let t=0,n=50,r=e.next(t);for(;!r.done&&t<Vt;)t+=n,r=e.next(t);return t>=Vt?1/0:t}function Ut(e,t=100,n){let r=n({...e,keyframes:[0,t]}),i=Math.min(Ht(r),Vt);return{type:`keyframes`,ease:e=>r.next(i*e).value/t,duration:O(i)}}const Wt=5;function Gt(e,t,n){let r=Math.max(t-Wt,0);return ue(n-e(r),t-r)}const H={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Kt=.001;function qt({duration:e=H.duration,bounce:t=H.bounce,velocity:n=H.velocity,mass:r=H.mass}){let i,a;ee(e<=D(H.maxDuration),`Spring duration must be 10 seconds or less`);let o=1-t;o=w(H.minDamping,H.maxDamping,o),e=w(H.minDuration,H.maxDuration,O(e)),o<1?(i=t=>{let r=t*o,i=r*e,a=r-n,s=Xt(t,o),c=Math.exp(-i);return Kt-a/s*c},a=t=>{let r=t*o,a=r*e,s=a*n+n,c=o**2*t**2*e,l=Math.exp(-a),u=Xt(t**2,o),d=-i(t)+Kt>0?-1:1;return d*((s-c)*l)/u}):(i=t=>{let r=Math.exp(-t*e),i=(t-n)*e+1;return-Kt+r*i},a=t=>{let r=Math.exp(-t*e),i=(n-t)*(e*e);return r*i});let s=5/e,c=Yt(i,a,s);if(e=D(e),isNaN(c))return{stiffness:H.stiffness,damping:H.damping,duration:e};{let t=c**2*r;return{stiffness:t,damping:o*2*Math.sqrt(r*t),duration:e}}}const Jt=12;function Yt(e,t,n){let r=n;for(let n=1;n<Jt;n++)r-=e(r)/t(r);return r}function Xt(e,t){return e*Math.sqrt(1-t*t)}const Zt=[`duration`,`bounce`],Qt=[`stiffness`,`damping`,`mass`];function $t(e,t){return t.some(t=>e[t]!==void 0)}function en(e){let t={velocity:H.velocity,stiffness:H.stiffness,damping:H.damping,mass:H.mass,isResolvedFromDuration:!1,...e};if(!$t(e,Qt)&&$t(e,Zt))if(e.visualDuration){let n=e.visualDuration,r=2*Math.PI/(n*1.2),i=r*r,a=2*w(.05,1,1-(e.bounce||0))*Math.sqrt(i);t={...t,mass:H.mass,stiffness:i,damping:a}}else{let n=qt(e);t={...t,...n,mass:H.mass},t.isResolvedFromDuration=!0}return t}function tn(e=H.visualDuration,t=H.bounce){let n=typeof e==`object`?e:{visualDuration:e,keyframes:[0,1],bounce:t},{restSpeed:r,restDelta:i}=n,a=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],s={done:!1,value:a},{stiffness:c,damping:l,mass:u,duration:d,velocity:f,isResolvedFromDuration:p}=en({...n,velocity:-O(n.velocity||0)}),m=f||0,h=l/(2*Math.sqrt(c*u)),g=o-a,_=O(Math.sqrt(c/u)),v=Math.abs(g)<5;r||=v?H.restSpeed.granular:H.restSpeed.default,i||=v?H.restDelta.granular:H.restDelta.default;let y;if(h<1){let e=Xt(_,h);y=t=>{let n=Math.exp(-h*_*t);return o-n*((m+h*_*g)/e*Math.sin(e*t)+g*Math.cos(e*t))}}else if(h===1)y=e=>o-Math.exp(-_*e)*(g+(m+_*g)*e);else{let e=_*Math.sqrt(h*h-1);y=t=>{let n=Math.exp(-h*_*t),r=Math.min(e*t,300);return o-n*((m+h*_*g)*Math.sinh(r)+e*g*Math.cosh(r))/e}}let b={calculatedDuration:p&&d||null,next:e=>{let t=y(e);if(p)s.done=e>=d;else{let n=e===0?m:0;h<1&&(n=e===0?D(m):Gt(y,e,t));let a=Math.abs(n)<=r,c=Math.abs(o-t)<=i;s.done=a&&c}return s.value=s.done?o:t,s},toString:()=>{let e=Math.min(Ht(b),Vt),t=Bt(t=>b.next(e*t).value,e,30);return e+`ms `+t},toTransition:()=>{}};return b}tn.applyToOptions=e=>{let t=Ut(e,100,tn);return e.ease=t.ease,e.duration=D(t.duration),e.type=`keyframes`,e};function nn({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:a=500,modifyTarget:o,min:s,max:c,restDelta:l=.5,restSpeed:u}){let d=e[0],f={done:!1,value:d},p=e=>s!==void 0&&e<s||c!==void 0&&e>c,m=e=>s===void 0?c:c===void 0||Math.abs(s-e)<Math.abs(c-e)?s:c,h=n*t,g=d+h,_=o===void 0?g:o(g);_!==g&&(h=_-d);let v=e=>-h*Math.exp(-e/r),y=e=>_+v(e),b=e=>{let t=v(e),n=y(e);f.done=Math.abs(t)<=l,f.value=f.done?_:n},x,S,C=e=>{p(f.value)&&(x=e,S=tn({keyframes:[f.value,m(f.value)],velocity:Gt(y,e,f.value),damping:i,stiffness:a,restDelta:l,restSpeed:u}))};return C(0),{calculatedDuration:null,next:e=>{let t=!1;return!S&&x===void 0&&(t=!0,b(e),C(e)),x!==void 0&&e>=x?S.next(e-x):(!t&&b(e),f)}}}function rn(e,t,n){let r=[],i=n||T.mix||Rt,a=e.length-1;for(let n=0;n<a;n++){let a=i(e[n],e[n+1]);if(t){let e=Array.isArray(t)?t[n]||E:t;a=se(e,a)}r.push(a)}return r}function an(e,t,{clamp:n=!0,ease:r,mixer:i}={}){let a=e.length;if(te(a===t.length,`Both input and output ranges must be the same length`),a===1)return()=>t[0];if(a===2&&t[0]===t[1])return()=>t[1];let o=e[0]===e[1];e[0]>e[a-1]&&(e=[...e].reverse(),t=[...t].reverse());let s=rn(t,r,i),c=s.length,l=n=>{if(o&&n<e[0])return t[0];let r=0;if(c>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=ce(e[r],e[r+1],n);return s[r](i)};return n?t=>l(w(e[0],e[a-1],t)):l}function on(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=ce(0,t,r);e.push(V(n,1,i))}}function sn(e){let t=[0];return on(t,e.length-1),t}function cn(e,t){return e.map(e=>e*t)}function ln(e,t){return e.map(()=>t||De).splice(0,e.length-1)}function un({duration:e=300,keyframes:t,times:n,ease:r=`easeInOut`}){let i=Oe(r)?r.map(Me):Me(r),a={done:!1,value:t[0]},o=cn(n&&n.length===t.length?n:sn(t),e),s=an(o,t,{ease:Array.isArray(i)?i:ln(t,i)});return{calculatedDuration:e,next:t=>(a.value=s(t),a.done=t>=e,a)}}const dn=e=>e!==null;function fn(e,{repeat:t,repeatType:n=`loop`},r,i=1){let a=e.filter(dn),o=i<0||t&&n!==`loop`&&t%2==1,s=o?0:a.length-1;return!s||r===void 0?a[s]:r}const pn={decay:nn,inertia:nn,tween:un,keyframes:un,spring:tn};function mn(e){typeof e.type==`string`&&(e.type=pn[e.type])}var hn=class{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}};const gn=e=>e/100;var _n=class extends hn{constructor(e){super(),this.state=`idle`,this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==N.now()&&this.tick(N.now()),this.isStopped=!0,this.state!==`idle`&&(this.teardown(),this.options.onStop?.())},P.mainThread++,this.options=e,this.initAnimation(),this.play(),e.autoplay===!1&&this.pause()}initAnimation(){let{options:e}=this;mn(e);let{type:t=un,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:a=0}=e,{keyframes:o}=e,s=t||un;s!==un&&typeof o[0]!=`number`&&(this.mixKeyframes=se(gn,Rt(o[0],o[1])),o=[0,100]);let c=s({...e,keyframes:o});i===`mirror`&&(this.mirroredGenerator=s({...e,keyframes:[...o].reverse(),velocity:-a})),c.calculatedDuration===null&&(c.calculatedDuration=Ht(c));let{calculatedDuration:l}=c;this.calculatedDuration=l,this.resolvedDuration=l+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=c}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;this.holdTime===null?this.currentTime=t:this.currentTime=this.holdTime}tick(e,t=!1){let{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:a,resolvedDuration:o,calculatedDuration:s}=this;if(this.startTime===null)return n.next(0);let{delay:c=0,keyframes:l,repeat:u,repeatType:d,repeatDelay:f,type:p,onUpdate:m,finalKeyframe:h}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-c*(this.playbackSpeed>=0?1:-1),_=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),this.state===`finished`&&this.holdTime===null&&(this.currentTime=r);let v=this.currentTime,y=n;if(u){let e=Math.min(this.currentTime,r)/o,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),n===1&&t--,t=Math.min(t,u+1);let i=!!(t%2);i&&(d===`reverse`?(n=1-n,f&&(n-=f/o)):d===`mirror`&&(y=a)),v=w(0,1,n)*o}let b=_?{done:!1,value:l[0]}:y.next(v);i&&(b.value=i(b.value));let{done:x}=b;!_&&s!==null&&(x=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let S=this.holdTime===null&&(this.state===`finished`||this.state===`running`&&x);return S&&p!==nn&&(b.value=fn(l,this.options,h,this.speed)),m&&m(b.value),S&&this.finish(),b}then(e,t){return this.finished.then(e,t)}get duration(){return O(this.calculatedDuration)}get time(){return O(this.currentTime)}set time(e){e=D(e),this.currentTime=e,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(N.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=O(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=zt,startTime:t}=this.options;this.driver||=e(e=>this.tick(e)),this.options.onPlay?.();let n=this.driver.now();this.state===`finished`?(this.updateFinished(),this.startTime=n):this.holdTime===null?this.startTime||=t??n:this.startTime=n-this.holdTime,this.state===`finished`&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state=`running`,this.driver.start()}pause(){this.state=`paused`,this.updateTime(N.now()),this.holdTime=this.currentTime}complete(){this.state!==`running`&&this.play(),this.state=`finished`,this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state=`finished`,this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state=`idle`,this.stopDriver(),this.startTime=this.holdTime=null,P.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type=`keyframes`,this.options.ease=`linear`,this.initAnimation()),this.driver?.stop(),e.observe(this)}};function vn(e){for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}const U=e=>e*180/Math.PI,yn=e=>{let t=U(Math.atan2(e[1],e[0]));return xn(t)},bn={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:yn,rotateZ:yn,skewX:e=>U(Math.atan(e[1])),skewY:e=>U(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},xn=e=>(e%=360,e<0&&(e+=360),e),Sn=yn,Cn=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),wn=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),Tn={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Cn,scaleY:wn,scale:e=>(Cn(e)+wn(e))/2,rotateX:e=>xn(U(Math.atan2(e[6],e[5]))),rotateY:e=>xn(U(Math.atan2(-e[2],e[0]))),rotateZ:Sn,rotate:Sn,skewX:e=>U(Math.atan(e[4])),skewY:e=>U(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function En(e){return e.includes(`scale`)?1:0}function Dn(e,t){if(!e||e===`none`)return En(t);let n=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u),r,i;if(n)r=Tn,i=n;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=bn,i=t}if(!i)return En(t);let a=r[t],o=i[1].split(`,`).map(kn);return typeof a==`function`?a(o):o[a]}const On=(e,t)=>{let{transform:n=`none`}=getComputedStyle(e);return Dn(n,t)};function kn(e){return parseFloat(e.trim())}const An=[`transformPerspective`,`x`,`y`,`z`,`translateX`,`translateY`,`translateZ`,`scale`,`scaleX`,`scaleY`,`rotate`,`rotateX`,`rotateY`,`rotateZ`,`skew`,`skewX`,`skewY`],jn=(()=>new Set(An))(),Mn=e=>e===Ge||e===R,Nn=new Set([`x`,`y`,`z`]),Pn=An.filter(e=>!Nn.has(e));function Fn(e){let t=[];return Pn.forEach(n=>{let r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith(`scale`)?1:0))}),t}const W={width:({x:e},{paddingLeft:t=`0`,paddingRight:n=`0`})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t=`0`,paddingBottom:n=`0`})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>Dn(t,`x`),y:(e,{transform:t})=>Dn(t,`y`)};W.translateX=W.x,W.translateY=W.y;const G=new Set;let In=!1,Ln=!1,Rn=!1;function zn(){if(Ln){let e=Array.from(G).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=Fn(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{e.getValue(t)?.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{e.suspendedScrollY!==void 0&&window.scrollTo(0,e.suspendedScrollY)})}Ln=!1,In=!1,G.forEach(e=>e.complete(Rn)),G.clear()}function Bn(){G.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Ln=!0)})}function Vn(){Rn=!0,Bn(),zn(),Rn=!1}var Hn=class{constructor(e,t,n,r,i,a=!1){this.state=`pending`,this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=a}scheduleResolve(){this.state=`scheduled`,this.isAsync?(G.add(this),In||(In=!0,A.read(Bn),A.resolveKeyframes(zn))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;if(e[0]===null){let i=r?.get(),a=e[e.length-1];if(i!==void 0)e[0]=i;else if(n&&t){let r=n.readValue(t,a);r!=null&&(e[0]=r)}e[0]===void 0&&(e[0]=a),r&&i===void 0&&r.set(e[0])}vn(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state=`complete`,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),G.delete(this)}cancel(){this.state===`scheduled`&&(G.delete(this),this.state=`pending`)}resume(){this.state===`pending`&&this.scheduleResolve()}};const Un=e=>e.startsWith(`--`);function Wn(e,t,n){Un(t)?e.style.setProperty(t,n):e.style[t]=n}const Gn=ae(()=>window.ScrollTimeline!==void 0),Kn={};function qn(e,t){let n=ae(e);return()=>Kn[t]??n()}const Jn=qn(()=>{try{document.createElement(`div`).animate({opacity:0},{easing:`linear(0, 1)`})}catch{return!1}return!0},`linearEasing`),Yn=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Xn={linear:`linear`,ease:`ease`,easeIn:`ease-in`,easeOut:`ease-out`,easeInOut:`ease-in-out`,circIn:Yn([0,.65,.55,1]),circOut:Yn([.55,0,1,.45]),backIn:Yn([.31,.01,.66,-.59]),backOut:Yn([.33,1.53,.69,.99])};function Zn(e,t){if(e)return typeof e==`function`?Jn()?Bt(e,t):`ease-out`:ke(e)?Yn(e):Array.isArray(e)?e.map(e=>Zn(e,t)||Xn.easeOut):Xn[e]}function Qn(e,t,n,{delay:r=0,duration:i=300,repeat:a=0,repeatType:o=`loop`,ease:s=`easeOut`,times:c}={},l=void 0){let u={[t]:n};c&&(u.offset=c);let d=Zn(s,i);Array.isArray(d)&&(u.easing=d),k.value&&P.waapi++;let f={delay:r,duration:i,easing:Array.isArray(d)?`linear`:d,fill:`both`,iterations:a+1,direction:o===`reverse`?`alternate`:`normal`};l&&(f.pseudoElement=l);let p=e.animate(u,f);return k.value&&p.finished.finally(()=>{P.waapi--}),p}function $n(e){return typeof e==`function`&&`applyToOptions`in e}function er({type:e,...t}){return $n(e)&&Jn()?e.applyToOptions(t):(t.duration??=300,t.ease??=`easeOut`,t)}var tr=class extends hn{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:n,keyframes:r,pseudoElement:i,allowFlatten:a=!1,finalKeyframe:o,onComplete:s}=e;this.isPseudoElement=!!i,this.allowFlatten=a,this.options=e,te(typeof e.type!=`string`,`animateMini doesn't support "type" as a string. Did you mean to import { spring } from "motion"?`);let c=er(e);this.animation=Qn(t,n,r,c,i),c.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=fn(r,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(e):Wn(t,n,e),this.animation.cancel()}s?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state===`finished`&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;e===`idle`||e===`finished`||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let e=this.animation.effect?.getComputedTiming?.().duration||0;return O(Number(e))}get time(){return O(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=D(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return this.finishedTime===null?this.animation.playState:`finished`}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:`linear`}),this.animation.onfinish=null,e&&Gn()?(this.animation.timeline=e,E):t(this)}};const nr={anticipate:xe,backInOut:be,circInOut:we};function rr(e){return e in nr}function ir(e){typeof e.ease==`string`&&rr(e.ease)&&(e.ease=nr[e.ease])}const ar=10;var or=class extends tr{constructor(e){ir(e),mn(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:n,onComplete:r,element:i,...a}=this.options;if(!t)return;if(e!==void 0){t.set(e);return}let o=new _n({...a,autoplay:!1}),s=D(this.finishedTime??this.time);t.setWithVelocity(o.sample(s-ar).value,o.sample(s).value,ar),o.stop()}};const sr=(e,t)=>t===`zIndex`?!1:!!(typeof e==`number`||Array.isArray(e)||typeof e==`string`&&(B.test(e)||e===`0`)&&!e.startsWith(`url(`));function cr(e){let t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function lr(e,t,n,r){let i=e[0];if(i===null)return!1;if(t===`display`||t===`visibility`)return!0;let a=e[e.length-1],o=sr(i,t),s=sr(a,t);return ee(o===s,`You are trying to animate ${t} from "${i}" to "${a}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${a} via the \`style\` property.`),!o||!s?!1:cr(e)||(n===`spring`||$n(n))&&r}function ur(e){return re(e)&&`offsetHeight`in e}const dr=new Set([`opacity`,`clipPath`,`filter`,`transform`]),fr=ae(()=>Object.hasOwnProperty.call(Element.prototype,`animate`));function pr(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:a,type:o}=e;if(!ur(t?.owner?.current))return!1;let{onUpdate:s,transformTemplate:c}=t.owner.getProps();return fr()&&n&&dr.has(n)&&(n!==`transform`||!c)&&!s&&!r&&i!==`mirror`&&a!==0&&o!==`inertia`}const mr=40;var hr=class extends hn{constructor({autoplay:e=!0,delay:t=0,type:n=`keyframes`,repeat:r=0,repeatDelay:i=0,repeatType:a=`loop`,keyframes:o,name:s,motionValue:c,element:l,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=N.now();let d={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:a,name:s,motionValue:c,element:l,...u},f=l?.KeyframeResolver||Hn;this.keyframeResolver=new f(o,(e,t,n)=>this.onKeyframesResolved(e,t,d,!n),s,c,l),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,n,r){this.keyframeResolver=void 0;let{name:i,type:a,velocity:o,delay:s,isHandoff:c,onUpdate:l}=n;this.resolvedAt=N.now(),lr(e,i,a,o)||((T.instantAnimations||!s)&&l?.(fn(e,n,t)),e[0]=e[e.length-1],n.duration=0,n.repeat=0);let u=r?this.resolvedAt&&this.resolvedAt-this.createdAt>mr?this.resolvedAt:this.createdAt:void 0,d={startTime:u,finalKeyframe:t,...n,keyframes:e},f=!c&&pr(d)?new or({...d,element:d.motionValue.owner.current}):new _n(d);f.finished.then(()=>this.notifyFinished()).catch(E),this.pendingTimeline&&(this.stopTimeline=f.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=f}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),Vn()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}};const gr=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function _r(e){let t=gr.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${n??r}`,i]}const vr=4;function yr(e,t,n=1){te(n<=vr,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,i]=_r(e);if(!r)return;let a=window.getComputedStyle(t).getPropertyValue(r);if(a){let e=a.trim();return ne(e)?parseFloat(e):e}return Ue(i)?yr(i,t,n+1):i}function br(e,t){return e?.[t]??e?.default??e}const xr=new Set([`width`,`height`,`top`,`left`,`right`,`bottom`,...An]),Sr={test:e=>e===`auto`,parse:e=>e},Cr=e=>t=>t.test(e),wr=[Ge,R,L,I,ot,at,Sr],Tr=e=>wr.find(Cr(e));function Er(e){return typeof e==`number`?e===0:e===null?!0:e===`none`||e===`0`||ie(e)}const Dr=new Set([`brightness`,`contrast`,`saturate`,`opacity`]);function Or(e){let[t,n]=e.slice(0,-1).split(`(`);if(t===`drop-shadow`)return e;let[r]=n.match(Ye)||[];if(!r)return e;let i=n.replace(r,``),a=Dr.has(t)?1:0;return r!==n&&(a*=100),t+`(`+a+i+`)`}const kr=/\b([a-z-]*)\(.*?\)/gu,Ar={...B,getAnimatableNone:e=>{let t=e.match(kr);return t?t.map(Or).join(` `):e}},jr={...Ge,transform:Math.round},Mr={rotate:I,rotateX:I,rotateY:I,rotateZ:I,scale:qe,scaleX:qe,scaleY:qe,scaleZ:qe,skew:I,skewX:I,skewY:I,distance:R,translateX:R,translateY:R,translateZ:R,x:R,y:R,z:R,perspective:R,transformPerspective:R,opacity:Ke,originX:st,originY:st,originZ:R},Nr={borderWidth:R,borderTopWidth:R,borderRightWidth:R,borderBottomWidth:R,borderLeftWidth:R,borderRadius:R,radius:R,borderTopLeftRadius:R,borderTopRightRadius:R,borderBottomRightRadius:R,borderBottomLeftRadius:R,width:R,maxWidth:R,height:R,maxHeight:R,top:R,right:R,bottom:R,left:R,padding:R,paddingTop:R,paddingRight:R,paddingBottom:R,paddingLeft:R,margin:R,marginTop:R,marginRight:R,marginBottom:R,marginLeft:R,backgroundPositionX:R,backgroundPositionY:R,...Mr,zIndex:jr,fillOpacity:Ke,strokeOpacity:Ke,numOctaves:jr},Pr={...Nr,color:z,backgroundColor:z,outlineColor:z,fill:z,stroke:z,borderColor:z,borderTopColor:z,borderRightColor:z,borderBottomColor:z,borderLeftColor:z,filter:Ar,WebkitFilter:Ar},Fr=e=>Pr[e];function Ir(e,t){let n=Fr(e);return n!==Ar&&(n=B),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Lr=new Set([`auto`,`none`,`0`]);function Rr(e,t,n){let r=0,i;for(;r<e.length&&!i;){let t=e[r];typeof t==`string`&&!Lr.has(t)&&_t(t).values.length&&(i=e[r]),r++}if(i&&n)for(let r of t)e[r]=Ir(n,i)}var zr=class extends Hn{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if(typeof r==`string`&&(r=r.trim(),Ue(r))){let i=yr(r,t.current);i!==void 0&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!xr.has(n)||e.length!==2)return;let[r,i]=e,a=Tr(r),o=Tr(i);if(a!==o)if(Mn(a)&&Mn(o))for(let t=0;t<e.length;t++){let n=e[t];typeof n==`string`&&(e[t]=parseFloat(n))}else W[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++)(e[t]===null||Er(e[t]))&&n.push(t);n.length&&Rr(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;n===`height`&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=W[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];r!==void 0&&e.getValue(n,r).jump(r,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(t);r&&r.jump(this.measuredOrigin,!1);let i=n.length-1,a=n[i];n[i]=W[t](e.measureViewportBox(),window.getComputedStyle(e.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}};function Br(e,t,n){if(e instanceof EventTarget)return[e];if(typeof e==`string`){let r=document;t&&(r=t.current);let i=n?.[e]??r.querySelectorAll(e);return i?Array.from(i):[]}return Array.from(e)}const Vr=(e,t)=>t&&typeof e==`number`?t.transform(e):e,Hr=30,Ur=e=>!isNaN(parseFloat(e)),Wr={current:void 0};var Gr=class{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let n=N.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=N.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=Ur(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on(`change`,e)}on(e,t){this.events[e]||(this.events[e]=new le);let n=this.events[e].add(t);return e===`change`?()=>{n(),A.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){!t||!this.passiveEffect?this.updateAndNotify(e,t):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||=new Set,this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return Wr.current&&Wr.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let e=N.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>Hr)return 0;let t=Math.min(this.updatedAt-this.prevUpdatedAt,Hr);return ue(parseFloat(this.current)-parseFloat(this.prevFrameValue),t)}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}};function Kr(e,t){return new Gr(e,t)}const{schedule:qr,cancel:Jr}=Ie(queueMicrotask,!1),K={x:!1,y:!1};function Yr(){return K.x||K.y}function Xr(e){return e===`x`||e===`y`?K[e]?null:(K[e]=!0,()=>{K[e]=!1}):K.x||K.y?null:(K.x=K.y=!0,()=>{K.x=K.y=!1})}function Zr(e,t){let n=Br(e),r=new AbortController,i={passive:!0,...t,signal:r.signal},a=()=>r.abort();return[n,i,a]}function Qr(e){return!(e.pointerType===`touch`||Yr())}function $r(e,t,n={}){let[r,i,a]=Zr(e,n),o=e=>{if(!Qr(e))return;let{target:n}=e,r=t(n,e);if(typeof r!=`function`||!n)return;let a=e=>{Qr(e)&&(r(e),n.removeEventListener(`pointerleave`,a))};n.addEventListener(`pointerleave`,a,i)};return r.forEach(e=>{e.addEventListener(`pointerenter`,o,i)}),a}const ei=(e,t)=>t?e===t?!0:ei(e,t.parentElement):!1,ti=e=>e.pointerType===`mouse`?typeof e.button!=`number`||e.button<=0:e.isPrimary!==!1,ni=new Set([`BUTTON`,`INPUT`,`SELECT`,`TEXTAREA`,`A`]);function ri(e){return ni.has(e.tagName)||e.tabIndex!==-1}const ii=new WeakSet;function ai(e){return t=>{t.key===`Enter`&&e(t)}}function oi(e,t){e.dispatchEvent(new PointerEvent(`pointer`+t,{isPrimary:!0,bubbles:!0}))}const si=(e,t)=>{let n=e.currentTarget;if(!n)return;let r=ai(()=>{if(ii.has(n))return;oi(n,`down`);let e=ai(()=>{oi(n,`up`)}),r=()=>oi(n,`cancel`);n.addEventListener(`keyup`,e,t),n.addEventListener(`blur`,r,t)});n.addEventListener(`keydown`,r,t),n.addEventListener(`blur`,()=>n.removeEventListener(`keydown`,r),t)};function ci(e){return ti(e)&&!Yr()}function li(e,t,n={}){let[r,i,a]=Zr(e,n),o=e=>{let r=e.currentTarget;if(!ci(e))return;ii.add(r);let a=t(r,e),o=(e,t)=>{window.removeEventListener(`pointerup`,s),window.removeEventListener(`pointercancel`,c),ii.has(r)&&ii.delete(r),ci(e)&&typeof a==`function`&&a(e,{success:t})},s=e=>{o(e,r===window||r===document||n.useGlobalTarget||ei(r,e.target))},c=e=>{o(e,!1)};window.addEventListener(`pointerup`,s,i),window.addEventListener(`pointercancel`,c,i)};return r.forEach(e=>{let t=n.useGlobalTarget?window:e;t.addEventListener(`pointerdown`,o,i),ur(e)&&(e.addEventListener(`focus`,e=>si(e,i)),!ri(e)&&!e.hasAttribute(`tabindex`)&&(e.tabIndex=0))}),a}function ui(e){return re(e)&&`ownerSVGElement`in e}function di(e){return ui(e)&&e.tagName===`svg`}const q=e=>!!(e&&e.getVelocity),fi=[...wr,z,B],pi=e=>fi.find(Cr(e)),mi=(0,g.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:`never`});var J=i(o(),1),hi=class extends g.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=ur(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}};function gi({children:e,isPresent:t,anchorX:n}){let r=(0,g.useId)(),i=(0,g.useRef)(null),a=(0,g.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:o}=(0,g.useContext)(mi);return(0,g.useInsertionEffect)(()=>{let{width:e,height:s,top:c,left:l,right:u}=a.current;if(t||!i.current||!e||!s)return;let d=n===`left`?`left: ${l}`:`right: ${u}`;i.current.dataset.motionPopId=r;let f=document.createElement(`style`);return o&&(f.nonce=o),document.head.appendChild(f),f.sheet&&f.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${s}px !important;
            ${d}px !important;
            top: ${c}px !important;
          }
        `),()=>{document.head.contains(f)&&document.head.removeChild(f)}},[t]),(0,J.jsx)(hi,{isPresent:t,childRef:i,sizeRef:a,children:g.cloneElement(e,{ref:i})})}const _i=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:i,presenceAffectsLayout:a,mode:o,anchorX:s})=>{let c=v(vi),l=(0,g.useId)(),u=!0,d=(0,g.useMemo)(()=>(u=!1,{id:l,initial:t,isPresent:n,custom:i,onExitComplete:e=>{c.set(e,!0);for(let e of c.values())if(!e)return;r&&r()},register:e=>(c.set(e,!1),()=>c.delete(e))}),[n,c,r]);return a&&u&&(d={...d}),(0,g.useMemo)(()=>{c.forEach((e,t)=>c.set(t,!1))},[n]),g.useEffect(()=>{!n&&!c.size&&r&&r()},[n]),o===`popLayout`&&(e=(0,J.jsx)(gi,{isPresent:n,anchorX:s,children:e})),(0,J.jsx)(x.Provider,{value:d,children:e})};function vi(){return new Map}function yi(e=!0){let t=(0,g.useContext)(x);if(t===null)return[!0,null];let{isPresent:n,onExitComplete:r,register:i}=t,a=(0,g.useId)();(0,g.useEffect)(()=>{if(e)return i(a)},[e]);let o=(0,g.useCallback)(()=>e&&r&&r(a),[a,r,e]);return!n&&r?[!1,o]:[!0]}const bi=e=>e.key||``;function xi(e){let t=[];return g.Children.forEach(e,e=>{(0,g.isValidElement)(e)&&t.push(e)}),t}const Si=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:i=!0,mode:a=`sync`,propagate:o=!1,anchorX:s=`left`})=>{let[c,l]=yi(o),u=(0,g.useMemo)(()=>xi(e),[e]),d=o&&!c?[]:u.map(bi),f=(0,g.useRef)(!0),p=(0,g.useRef)(u),m=v(()=>new Map),[h,y]=(0,g.useState)(u),[x,S]=(0,g.useState)(u);b(()=>{f.current=!1,p.current=u;for(let e=0;e<x.length;e++){let t=bi(x[e]);d.includes(t)?m.delete(t):m.get(t)!==!0&&m.set(t,!1)}},[x,d.length,d.join(`-`)]);let C=[];if(u!==h){let e=[...u];for(let t=0;t<x.length;t++){let n=x[t],r=bi(n);d.includes(r)||(e.splice(t,0,n),C.push(n))}return a===`wait`&&C.length&&(e=C),S(xi(e)),y(u),null}let{forceRender:w}=(0,g.useContext)(_);return(0,J.jsx)(J.Fragment,{children:x.map(e=>{let h=bi(e),g=o&&!c?!1:u===x||d.includes(h),_=()=>{if(m.has(h))m.set(h,!0);else return;let e=!0;m.forEach(t=>{t||(e=!1)}),e&&(w?.(),S(p.current),o&&l?.(),r&&r())};return(0,J.jsx)(_i,{isPresent:g,initial:!f.current||n?void 0:!1,custom:t,presenceAffectsLayout:i,mode:a,onExitComplete:g?void 0:_,anchorX:s,children:e},h)})})},Ci=(0,g.createContext)({strict:!1}),wi={animation:[`animate`,`variants`,`whileHover`,`whileTap`,`exit`,`whileInView`,`whileFocus`,`whileDrag`],exit:[`exit`],drag:[`drag`,`dragControls`],focus:[`whileFocus`],hover:[`whileHover`,`onHoverStart`,`onHoverEnd`],tap:[`whileTap`,`onTap`,`onTapStart`,`onTapCancel`],pan:[`onPan`,`onPanStart`,`onPanSessionStart`,`onPanEnd`],inView:[`whileInView`,`onViewportEnter`,`onViewportLeave`],layout:[`layout`,`layoutId`]},Ti={};for(let e in wi)Ti[e]={isEnabled:t=>wi[e].some(e=>!!t[e])};function Ei(e){for(let t in e)Ti[t]={...Ti[t],...e[t]}}const Di=new Set(`animate.exit.variants.initial.style.values.variants.transition.transformTemplate.custom.inherit.onBeforeLayoutMeasure.onAnimationStart.onAnimationComplete.onUpdate.onDragStart.onDrag.onDragEnd.onMeasureDragConstraints.onDirectionLock.onDragTransitionEnd._dragX._dragY.onHoverStart.onHoverEnd.onViewportEnter.onViewportLeave.globalTapTarget.ignoreStrict.viewport`.split(`.`));function Oi(e){return e.startsWith(`while`)||e.startsWith(`drag`)&&e!==`draggable`||e.startsWith(`layout`)||e.startsWith(`onTap`)||e.startsWith(`onPan`)||e.startsWith(`onLayout`)||Di.has(e)}function ki(e){var t=Object.create(null);return function(n){return t[n]===void 0&&(t[n]=e(n)),t[n]}}var Ai=t(()=>{}),ji={};n(ji,{default:()=>Ni});var Mi,Ni,Pi=t(()=>{Ai(),Mi=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,Ni=ki(function(e){return Mi.test(e)||e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)<91})});let Fi=e=>!Oi(e);function Ii(e){typeof e==`function`&&(Fi=t=>t.startsWith(`on`)?!Oi(t):e(t))}try{Ii((Pi(),r(ji)).default)}catch{}function Li(e,t,n){let r={};for(let i in e){if(i===`values`&&typeof e.values==`object`)continue;(Fi(i)||n===!0&&Oi(i)||!t&&!Oi(i)||e.draggable&&i.startsWith(`onDrag`))&&(r[i]=e[i])}return r}function Ri(e){if(typeof Proxy>`u`)return e;let t=new Map,n=(...t)=>e(...t);return new Proxy(n,{get:(n,r)=>r===`create`?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}const zi=(0,g.createContext)({});function Bi(e){return typeof e==`object`&&!!e&&typeof e.start==`function`}function Vi(e){return typeof e==`string`||Array.isArray(e)}const Hi=[`animate`,`whileInView`,`whileFocus`,`whileHover`,`whileTap`,`whileDrag`,`exit`],Ui=[`initial`,...Hi];function Wi(e){return Bi(e.animate)||Ui.some(t=>Vi(e[t]))}function Gi(e){return!!(Wi(e)||e.variants)}function Ki(e,t){if(Wi(e)){let{initial:t,animate:n}=e;return{initial:t===!1||Vi(t)?t:void 0,animate:Vi(n)?n:void 0}}return e.inherit===!1?{}:t}function qi(e){let{initial:t,animate:n}=Ki(e,(0,g.useContext)(zi));return(0,g.useMemo)(()=>({initial:t,animate:n}),[Ji(t),Ji(n)])}function Ji(e){return Array.isArray(e)?e.join(` `):e}const Yi=Symbol.for(`motionComponentSymbol`);function Xi(e){return e&&typeof e==`object`&&Object.prototype.hasOwnProperty.call(e,`current`)}function Zi(e,t,n){return(0,g.useCallback)(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n==`function`?n(r):Xi(n)&&(n.current=r))},[t])}const Qi=e=>e.replace(/([a-z])([A-Z])/gu,`$1-$2`).toLowerCase(),$i=`framerAppearId`,ea=`data-`+Qi($i),ta=(0,g.createContext)({});function na(e,t,n,r,i){let{visualElement:a}=(0,g.useContext)(zi),o=(0,g.useContext)(Ci),s=(0,g.useContext)(x),c=(0,g.useContext)(mi).reducedMotion,l=(0,g.useRef)(null);r||=o.renderer,!l.current&&r&&(l.current=r(e,{visualState:t,parent:a,props:n,presenceContext:s,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:c}));let u=l.current,d=(0,g.useContext)(ta);u&&!u.projection&&i&&(u.type===`html`||u.type===`svg`)&&ra(l.current,n,i,d);let f=(0,g.useRef)(!1);(0,g.useInsertionEffect)(()=>{u&&f.current&&u.update(n,s)});let p=n[ea],m=(0,g.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return b(()=>{u&&(f.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),qr.render(u.render),m.current&&u.animationState&&u.animationState.animateChanges())}),(0,g.useEffect)(()=>{u&&(!m.current&&u.animationState&&u.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),u}function ra(e,t,n,r){let{layoutId:i,layout:a,drag:o,dragConstraints:s,layoutScroll:c,layoutRoot:l,layoutCrossfade:u}=t;e.projection=new n(e.latestValues,t[`data-framer-portal-id`]?void 0:ia(e.parent)),e.projection.setOptions({layoutId:i,layout:a,alwaysMeasureLayout:!!o||s&&Xi(s),visualElement:e,animationType:typeof a==`string`?a:`both`,initialPromotionConfig:r,crossfade:u,layoutScroll:c,layoutRoot:l})}function ia(e){if(e)return e.options.allowProjection===!1?ia(e.parent):e.projection}function aa({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&Ei(e);function a(a,o){let s,c={...(0,g.useContext)(mi),...a,layoutId:oa(a)},{isStatic:l}=c,u=qi(a),d=r(a,l);if(!l&&y){sa(c,e);let n=ca(c);s=n.MeasureLayout,u.visualElement=na(i,d,c,t,n.ProjectionNode)}return(0,J.jsxs)(zi.Provider,{value:u,children:[s&&u.visualElement?(0,J.jsx)(s,{visualElement:u.visualElement,...c}):null,n(i,a,Zi(d,u.visualElement,o),d,l,u.visualElement)]})}a.displayName=`motion.${typeof i==`string`?i:`create(${i.displayName??i.name??``})`}`;let o=(0,g.forwardRef)(a);return o[Yi]=i,o}function oa({layoutId:e}){let t=(0,g.useContext)(_).id;return t&&e!==void 0?t+`-`+e:e}function sa(e,t){let n=(0,g.useContext)(Ci).strict}function ca(e){let{drag:t,layout:n}=Ti;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:t?.isEnabled(e)||n?.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const la={};function ua(e){for(let t in e)la[t]=e[t],Ve(t)&&(la[t].isCSSVariable=!0)}function da(e,{layout:t,layoutId:n}){return jn.has(e)||e.startsWith(`origin`)||(t||n!==void 0)&&(!!la[e]||e===`opacity`)}const fa={x:`translateX`,y:`translateY`,z:`translateZ`,transformPerspective:`perspective`},pa=An.length;function ma(e,t,n){let r=``,i=!0;for(let a=0;a<pa;a++){let o=An[a],s=e[o];if(s===void 0)continue;let c=!0;if(c=typeof s==`number`?s===(o.startsWith(`scale`)?1:0):parseFloat(s)===0,!c||n){let e=Vr(s,Nr[o]);if(!c){i=!1;let t=fa[o]||o;r+=`${t}(${e}) `}n&&(t[o]=e)}}return r=r.trim(),n?r=n(t,i?``:r):i&&(r=`none`),r}function ha(e,t,n){let{style:r,vars:i,transformOrigin:a}=e,o=!1,s=!1;for(let e in t){let n=t[e];if(jn.has(e)){o=!0;continue}else if(Ve(e)){i[e]=n;continue}else{let t=Vr(n,Nr[e]);e.startsWith(`origin`)?(s=!0,a[e]=t):r[e]=t}}if(t.transform||(o||n?r.transform=ma(t,e.transform,n):r.transform&&=`none`),s){let{originX:e=`50%`,originY:t=`50%`,originZ:n=0}=a;r.transformOrigin=`${e} ${t} ${n}`}}const ga=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function _a(e,t,n){for(let r in t)!q(t[r])&&!da(r,n)&&(e[r]=t[r])}function va({transformTemplate:e},t){return(0,g.useMemo)(()=>{let n=ga();return ha(n,t,e),Object.assign({},n.vars,n.style)},[t])}function ya(e,t){let n=e.style||{},r={};return _a(r,n,e),Object.assign(r,va(e,t)),r}function ba(e,t){let n={},r=ya(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout=`none`,r.touchAction=e.drag===!0?`none`:`pan-${e.drag===`x`?`y`:`x`}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}const xa={offset:`stroke-dashoffset`,array:`stroke-dasharray`},Sa={offset:`strokeDashoffset`,array:`strokeDasharray`};function Ca(e,t,n=1,r=0,i=!0){e.pathLength=1;let a=i?xa:Sa;e[a.offset]=R.transform(-r);let o=R.transform(t),s=R.transform(n);e[a.array]=`${o} ${s}`}function wa(e,{attrX:t,attrY:n,attrScale:r,pathLength:i,pathSpacing:a=1,pathOffset:o=0,...s},c,l,u){if(ha(e,s,l),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:f}=e;d.transform&&(f.transform=d.transform,delete d.transform),(f.transform||d.transformOrigin)&&(f.transformOrigin=d.transformOrigin??`50% 50%`,delete d.transformOrigin),f.transform&&(f.transformBox=u?.transformBox??`fill-box`,delete d.transformBox),t!==void 0&&(d.x=t),n!==void 0&&(d.y=n),r!==void 0&&(d.scale=r),i!==void 0&&Ca(d,i,a,o,!1)}const Ta=()=>({...ga(),attrs:{}}),Ea=e=>typeof e==`string`&&e.toLowerCase()===`svg`;function Da(e,t,n,r){let i=(0,g.useMemo)(()=>{let n=Ta();return wa(n,t,Ea(r),e.transformTemplate,e.style),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};_a(t,e.style,e),i.style={...t,...i.style}}return i}const Oa=[`animate`,`circle`,`defs`,`desc`,`ellipse`,`g`,`image`,`line`,`filter`,`marker`,`mask`,`metadata`,`path`,`pattern`,`polygon`,`polyline`,`rect`,`stop`,`switch`,`symbol`,`svg`,`text`,`tspan`,`use`,`view`];function ka(e){return typeof e!=`string`||e.includes(`-`)?!1:!!(Oa.indexOf(e)>-1||/[A-Z]/u.test(e))}function Aa(e=!1){let t=(t,n,r,{latestValues:i},a)=>{let o=ka(t)?Da:ba,s=o(n,i,a,t),c=Li(n,typeof t==`string`,e),l=t===g.Fragment?{}:{...c,...s,ref:r},{children:u}=n,d=(0,g.useMemo)(()=>q(u)?u.get():u,[u]);return(0,g.createElement)(t,{...l,children:d})};return t}function ja(e){let t=[{},{}];return e?.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function Ma(e,t,n,r){if(typeof t==`function`){let[i,a]=ja(r);t=t(n===void 0?e.custom:n,i,a)}if(typeof t==`string`&&(t=e.variants&&e.variants[t]),typeof t==`function`){let[i,a]=ja(r);t=t(n===void 0?e.custom:n,i,a)}return t}function Na(e){return q(e)?e.get():e}function Pa({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,i){let a={latestValues:Ia(n,r,i,e),renderState:t()};return a}const Fa=e=>(t,n)=>{let r=(0,g.useContext)(zi),i=(0,g.useContext)(x),a=()=>Pa(e,t,r,i);return n?a():v(a)};function Ia(e,t,n,r){let i={},a=r(e,{});for(let e in a)i[e]=Na(a[e]);let{initial:o,animate:s}=e,c=Wi(e),l=Gi(e);t&&l&&!c&&e.inherit!==!1&&(o===void 0&&(o=t.initial),s===void 0&&(s=t.animate));let u=n?n.initial===!1:!1;u||=o===!1;let d=u?s:o;if(d&&typeof d!=`boolean`&&!Bi(d)){let t=Array.isArray(d)?d:[d];for(let n=0;n<t.length;n++){let r=Ma(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=u?t.length-1:0;t=t[e]}t!==null&&(i[e]=t)}for(let t in e)i[t]=e[t]}}}return i}function La(e,t,n){let{style:r}=e,i={};for(let a in r)(q(r[a])||t.style&&q(t.style[a])||da(a,e)||n?.getValue(a)?.liveStyle!==void 0)&&(i[a]=r[a]);return i}const Ra={useVisualState:Fa({scrapeMotionValuesFromProps:La,createRenderState:ga})};function za(e,t,n){let r=La(e,t,n);for(let n in e)if(q(e[n])||q(t[n])){let t=An.indexOf(n)===-1?n:`attr`+n.charAt(0).toUpperCase()+n.substring(1);r[t]=e[n]}return r}const Ba={useVisualState:Fa({scrapeMotionValuesFromProps:za,createRenderState:Ta})};function Va(e,t){return function(n,{forwardMotionProps:r}={forwardMotionProps:!1}){let i=ka(n)?Ba:Ra,a={...i,preloadedFeatures:e,useRender:Aa(r),createVisualElement:t,Component:n};return aa(a)}}function Ha(e,t,n){let r=e.getProps();return Ma(r,t,n===void 0?r.custom:n,e)}const Ua=e=>Array.isArray(e);function Wa(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Kr(n))}function Ga(e){return Ua(e)?e[e.length-1]||0:e}function Ka(e,t){let n=Ha(e,t),{transitionEnd:r={},transition:i={},...a}=n||{};for(let t in a={...a,...r},a){let n=Ga(a[t]);Wa(e,t,n)}}function qa(e){return!!(q(e)&&e.add)}function Ja(e,t){let n=e.getValue(`willChange`);if(qa(n))return n.add(t);if(!n&&T.WillChange){let n=new T.WillChange(`auto`);e.addValue(`willChange`,n),n.add(t)}}function Ya(e){return e.props[ea]}const Xa=e=>e!==null;function Za(e,{repeat:t,repeatType:n=`loop`},r){let i=e.filter(Xa),a=t&&n!==`loop`&&t%2==1?0:i.length-1;return!a||r===void 0?i[a]:r}const Qa={type:`spring`,stiffness:500,damping:25,restSpeed:10},$a=e=>({type:`spring`,stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),eo={type:`keyframes`,duration:.8},to={type:`keyframes`,ease:[.25,.1,.35,1],duration:.3},no=(e,{keyframes:t})=>t.length>2?eo:jn.has(e)?e.startsWith(`scale`)?$a(t[1]):Qa:to;function ro({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:a,repeatType:o,repeatDelay:s,from:c,elapsed:l,...u}){return!!Object.keys(u).length}const io=(e,t,n,r={},i,a)=>o=>{let s=br(r,e)||{},c=s.delay||r.delay||0,{elapsed:l=0}=r;l-=D(c);let u={keyframes:Array.isArray(n)?n:[null,n],ease:`easeOut`,velocity:t.getVelocity(),...s,delay:-l,onUpdate:e=>{t.set(e),s.onUpdate&&s.onUpdate(e)},onComplete:()=>{o(),s.onComplete&&s.onComplete()},name:e,motionValue:t,element:a?void 0:i};ro(s)||Object.assign(u,no(e,u)),u.duration&&=D(u.duration),u.repeatDelay&&=D(u.repeatDelay),u.from!==void 0&&(u.keyframes[0]=u.from);let d=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(u.duration=0,u.delay===0&&(d=!0)),(T.instantAnimations||T.skipAnimations)&&(d=!0,u.duration=0,u.delay=0),u.allowFlatten=!s.type&&!s.ease,d&&!a&&t.get()!==void 0){let e=Za(u.keyframes,s);if(e!==void 0){A.update(()=>{u.onUpdate(e),u.onComplete()});return}}return s.isSync?new _n(u):new hr(u)};function ao({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function oo(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:a=e.getDefaultTransition(),transitionEnd:o,...s}=t;r&&(a=r);let c=[],l=i&&e.animationState&&e.animationState.getState()[i];for(let t in s){let r=e.getValue(t,e.latestValues[t]??null),i=s[t];if(i===void 0||l&&ao(l,t))continue;let o={delay:n,...br(a||{},t)},u=r.get();if(u!==void 0&&!r.isAnimating&&!Array.isArray(i)&&i===u&&!o.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let n=Ya(e);if(n){let e=window.MotionHandoffAnimation(n,t,A);e!==null&&(o.startTime=e,d=!0)}}Ja(e,t),r.start(io(t,r,i,e.shouldReduceMotion&&xr.has(t)?{type:!1}:o,e,d));let f=r.animation;f&&c.push(f)}return o&&Promise.all(c).then(()=>{A.update(()=>{o&&Ka(e,o)})}),c}function so(e,t,n={}){let r=Ha(e,t,n.type===`exit`?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);let a=r?()=>Promise.all(oo(e,r,n)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:a=0,staggerChildren:o,staggerDirection:s}=i;return co(e,t,a+r,o,s,n)}:()=>Promise.resolve(),{when:s}=i;if(s){let[e,t]=s===`beforeChildren`?[a,o]:[o,a];return e().then(()=>t())}else return Promise.all([a(),o(n.delay)])}function co(e,t,n=0,r=0,i=1,a){let o=[],s=(e.variantChildren.size-1)*r,c=i===1?(e=0)=>e*r:(e=0)=>s-e*r;return Array.from(e.variantChildren).sort(lo).forEach((e,r)=>{e.notify(`AnimationStart`,t),o.push(so(e,t,{...a,delay:n+c(r)}).then(()=>e.notify(`AnimationComplete`,t)))}),Promise.all(o)}function lo(e,t){return e.sortNodePosition(t)}function uo(e,t,n={}){e.notify(`AnimationStart`,t);let r;if(Array.isArray(t)){let i=t.map(t=>so(e,t,n));r=Promise.all(i)}else if(typeof t==`string`)r=so(e,t,n);else{let i=typeof t==`function`?Ha(e,t,n.custom):t;r=Promise.all(oo(e,i,n))}return r.then(()=>{e.notify(`AnimationComplete`,t)})}function fo(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}const po=Ui.length;function mo(e){if(!e)return;if(!e.isControllingVariants){let t=e.parent&&mo(e.parent)||{};return e.props.initial!==void 0&&(t.initial=e.props.initial),t}let t={};for(let n=0;n<po;n++){let r=Ui[n],i=e.props[r];(Vi(i)||i===!1)&&(t[r]=i)}return t}const ho=[...Hi].reverse(),go=Hi.length;function _o(e){return t=>Promise.all(t.map(({animation:t,options:n})=>uo(e,t,n)))}function vo(e){let t=_o(e),n=xo(),r=!0,i=t=>(n,r)=>{let i=Ha(e,r,t===`exit`?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...r}=i;n={...n,...r,...t}}return n};function a(n){t=n(e)}function o(a){let{props:o}=e,s=mo(e.parent)||{},c=[],l=new Set,u={},d=1/0;for(let t=0;t<go;t++){let f=ho[t],p=n[f],m=o[f]===void 0?s[f]:o[f],h=Vi(m),g=f===a?p.isActive:null;g===!1&&(d=t);let _=m===s[f]&&m!==o[f]&&h;if(_&&r&&e.manuallyAnimateOnMount&&(_=!1),p.protectedKeys={...u},!p.isActive&&g===null||!m&&!p.prevProp||Bi(m)||typeof m==`boolean`)continue;let v=yo(p.prevProp,m),y=v||f===a&&p.isActive&&!_&&h||t>d&&h,b=!1,x=Array.isArray(m)?m:[m],S=x.reduce(i(f),{});g===!1&&(S={});let{prevResolvedValues:C={}}=p,w={...C,...S},ee=t=>{y=!0,l.has(t)&&(b=!0,l.delete(t)),p.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in w){let t=S[e],n=C[e];if(u.hasOwnProperty(e))continue;let r=!1;r=Ua(t)&&Ua(n)?!fo(t,n):t!==n,r?t==null?l.add(e):ee(e):t!==void 0&&l.has(e)?ee(e):p.protectedKeys[e]=!0}p.prevProp=m,p.prevResolvedValues=S,p.isActive&&(u={...u,...S}),r&&e.blockInitialAnimation&&(y=!1);let te=_&&v,T=!te||b;y&&T&&c.push(...x.map(e=>({animation:e,options:{type:f}})))}if(l.size){let t={};if(typeof o.initial!=`boolean`){let n=Ha(e,Array.isArray(o.initial)?o.initial[0]:o.initial);n&&n.transition&&(t.transition=n.transition)}l.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=r??null}),c.push({animation:t})}let f=!!c.length;return r&&(o.initial===!1||o.initial===o.animate)&&!e.manuallyAnimateOnMount&&(f=!1),r=!1,f?t(c):Promise.resolve()}function s(t,r){if(n[t].isActive===r)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,r)),n[t].isActive=r;let i=o(t);for(let e in n)n[e].protectedKeys={};return i}return{animateChanges:o,setActive:s,setAnimateFunction:a,getState:()=>n,reset:()=>{n=xo(),r=!0}}}function yo(e,t){return typeof t==`string`?t!==e:Array.isArray(t)?!fo(t,e):!1}function bo(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function xo(){return{animate:bo(!0),whileInView:bo(),whileHover:bo(),whileTap:bo(),whileDrag:bo(),whileFocus:bo(),exit:bo()}}var Y=class{constructor(e){this.isMounted=!1,this.node=e}update(){}},So=class extends Y{constructor(e){super(e),e.animationState||=vo(e)}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();Bi(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}};let Co=0;var wo=class extends Y{constructor(){super(...arguments),this.id=Co++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive(`exit`,!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}};const To={animation:{Feature:So},exit:{Feature:wo}};function Eo(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function Do(e){return{point:{x:e.pageX,y:e.pageY}}}const Oo=e=>t=>ti(t)&&e(t,Do(t));function ko(e,t,n,r){return Eo(e,t,Oo(n),r)}function Ao({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function jo({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function Mo(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}const No=1e-4,Po=1-No,Fo=1+No,Io=.01,Lo=0-Io,Ro=0+Io;function X(e){return e.max-e.min}function zo(e,t,n){return Math.abs(e-t)<=n}function Bo(e,t,n,r=.5){e.origin=r,e.originPoint=V(t.min,t.max,e.origin),e.scale=X(n)/X(t),e.translate=V(n.min,n.max,e.origin)-e.originPoint,(e.scale>=Po&&e.scale<=Fo||isNaN(e.scale))&&(e.scale=1),(e.translate>=Lo&&e.translate<=Ro||isNaN(e.translate))&&(e.translate=0)}function Vo(e,t,n,r){Bo(e.x,t.x,n.x,r?r.originX:void 0),Bo(e.y,t.y,n.y,r?r.originY:void 0)}function Ho(e,t,n){e.min=n.min+t.min,e.max=e.min+X(t)}function Uo(e,t,n){Ho(e.x,t.x,n.x),Ho(e.y,t.y,n.y)}function Wo(e,t,n){e.min=t.min-n.min,e.max=e.min+X(t)}function Go(e,t,n){Wo(e.x,t.x,n.x),Wo(e.y,t.y,n.y)}const Ko=()=>({translate:0,scale:1,origin:0,originPoint:0}),qo=()=>({x:Ko(),y:Ko()}),Jo=()=>({min:0,max:0}),Z=()=>({x:Jo(),y:Jo()});function Q(e){return[e(`x`),e(`y`)]}function Yo(e){return e===void 0||e===1}function Xo({scale:e,scaleX:t,scaleY:n}){return!Yo(e)||!Yo(t)||!Yo(n)}function Zo(e){return Xo(e)||Qo(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function Qo(e){return $o(e.x)||$o(e.y)}function $o(e){return e&&e!==`0%`}function es(e,t,n){let r=e-n,i=t*r;return n+i}function ts(e,t,n,r,i){return i!==void 0&&(e=es(e,i,r)),es(e,n,r)+t}function ns(e,t=0,n=1,r,i){e.min=ts(e.min,t,n,r,i),e.max=ts(e.max,t,n,r,i)}function rs(e,{x:t,y:n}){ns(e.x,t.translate,t.scale,t.originPoint),ns(e.y,n.translate,n.scale,n.originPoint)}const os=.999999999999,ss=1.0000000000001;function cs(e,t,n,r=!1){let i=n.length;if(!i)return;t.x=t.y=1;let a,o;for(let s=0;s<i;s++){a=n[s],o=a.projectionDelta;let{visualElement:i}=a.options;i&&i.props.style&&i.props.style.display===`contents`||(r&&a.options.layoutScroll&&a.scroll&&a!==a.root&&ds(e,{x:-a.scroll.offset.x,y:-a.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,rs(e,o)),r&&Zo(a.latestValues)&&ds(e,a.latestValues))}t.x<ss&&t.x>os&&(t.x=1),t.y<ss&&t.y>os&&(t.y=1)}function ls(e,t){e.min+=t,e.max+=t}function us(e,t,n,r,i=.5){let a=V(e.min,e.max,i);ns(e,t,n,a,r)}function ds(e,t){us(e.x,t.x,t.scaleX,t.scale,t.originX),us(e.y,t.y,t.scaleY,t.scale,t.originY)}function fs(e,t){return Ao(Mo(e.getBoundingClientRect(),t))}function ps(e,t,n){let r=fs(e,n),{scroll:i}=t;return i&&(ls(r.x,i.offset.x),ls(r.y,i.offset.y)),r}const ms=({current:e})=>e?e.ownerDocument.defaultView:null,hs=(e,t)=>Math.abs(e-t);function gs(e,t){let n=hs(e.x,t.x),r=hs(e.y,t.y);return Math.sqrt(n**2+r**2)}var _s=class{constructor(e,t,{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=bs(this.lastMoveEventInfo,this.history),t=this.startEvent!==null,n=gs(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=M;this.history.push({...r,timestamp:i});let{onStart:a,onMove:o}=this.handlers;t||(a&&a(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=vs(t,this.transformPagePoint),A.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=bs(e.type===`pointercancel`?this.lastMoveEventInfo:vs(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,a),r&&r(e,a)},!ti(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.contextWindow=r||window;let a=Do(e),o=vs(a,this.transformPagePoint),{point:s}=o,{timestamp:c}=M;this.history=[{...s,timestamp:c}];let{onSessionStart:l}=t;l&&l(e,bs(o,this.history)),this.removeListeners=se(ko(this.contextWindow,`pointermove`,this.handlePointerMove),ko(this.contextWindow,`pointerup`,this.handlePointerUp),ko(this.contextWindow,`pointercancel`,this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),j(this.updatePoint)}};function vs(e,t){return t?{point:t(e.point)}:e}function ys(e,t){return{x:e.x-t.x,y:e.y-t.y}}function bs({point:e},t){return{point:e,delta:ys(e,Ss(t)),offset:ys(e,xs(t)),velocity:Cs(t,.1)}}function xs(e){return e[0]}function Ss(e){return e[e.length-1]}function Cs(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=Ss(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>D(t)));)n--;if(!r)return{x:0,y:0};let a=O(i.timestamp-r.timestamp);if(a===0)return{x:0,y:0};let o={x:(i.x-r.x)/a,y:(i.y-r.y)/a};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function ws(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?V(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?V(n,e,r.max):Math.min(e,n)),e}function Ts(e,t,n){return{min:t===void 0?void 0:e.min+t,max:n===void 0?void 0:e.max+n-(e.max-e.min)}}function Es(e,{top:t,left:n,bottom:r,right:i}){return{x:Ts(e.x,n,i),y:Ts(e.y,t,r)}}function Ds(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function Os(e,t){return{x:Ds(e.x,t.x),y:Ds(e.y,t.y)}}function ks(e,t){let n=.5,r=X(e),i=X(t);return i>r?n=ce(t.min,t.max-r,e.min):r>i&&(n=ce(e.min,e.max-i,t.min)),w(0,1,n)}function As(e,t){let n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const js=.35;function Ms(e=js){return e===!1?e=0:e===!0&&(e=js),{x:Ns(e,`left`,`right`),y:Ns(e,`top`,`bottom`)}}function Ns(e,t,n){return{min:Ps(e,t),max:Ps(e,n)}}function Ps(e,t){return typeof e==`number`?e:e[t]||0}const Fs=new WeakMap;var Is=class{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Z(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&n.isPresent===!1)return;let r=e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(Do(e).point)},i=(e,t)=>{let{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Xr(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Q(e=>{let t=this.getAxisMotionValue(e).get()||0;if(L.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];if(r){let e=X(r);t=e*(parseFloat(t)/100)}}}this.originPoint[e]=t}),i&&A.postRender(()=>i(e,t)),Ja(this.visualElement,`transform`);let{animationState:a}=this.visualElement;a&&a.setActive(`whileDrag`,!0)},a=(e,t)=>{let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:a}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:o}=t;if(r&&this.currentDirection===null){this.currentDirection=Rs(o),this.currentDirection!==null&&i&&i(this.currentDirection);return}this.updateAxis(`x`,t.point,o),this.updateAxis(`y`,t.point,o),this.visualElement.render(),a&&a(e,t)},o=(e,t)=>this.stop(e,t),s=()=>Q(e=>this.getAnimationState(e)===`paused`&&this.getAxisMotionValue(e).animation?.play()),{dragSnapToOrigin:c}=this.getProps();this.panSession=new _s(e,{onSessionStart:r,onStart:i,onMove:a,onSessionEnd:o,resumeAnimation:s},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:ms(this.visualElement)})}stop(e,t){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:i}=this.getProps();i&&A.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive(`whileDrag`,!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!Ls(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),a=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(a=ws(a,this.constraints[e],this.elastic[e])),i.set(a)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;e&&Xi(e)?this.constraints||=this.resolveRefConstraints():e&&n?this.constraints=Es(n.layoutBox,e):this.constraints=!1,this.elastic=Ms(t),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&Q(e=>{this.constraints!==!1&&this.getAxisMotionValue(e)&&(this.constraints[e]=As(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){let{dragConstraints:e,onMeasureDragConstraints:t}=this.getProps();if(!e||!Xi(e))return!1;let n=e.current;te(n!==null,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let i=ps(n,r.root,this.visualElement.getTransformPagePoint()),a=Os(r.layout.layoutBox,i);if(t){let e=t(jo(a));this.hasMutatedConstraints=!!e,e&&(a=Ao(e))}return a}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:a,onDragTransitionEnd:o}=this.getProps(),s=this.constraints||{},c=Q(o=>{if(!Ls(o,t,this.currentDirection))return;let c=s&&s[o]||{};a&&(c={min:0,max:0});let l=r?200:1e6,u=r?40:1e7,d={type:`inertia`,velocity:n?e[o]:0,bounceStiffness:l,bounceDamping:u,timeConstant:750,restDelta:1,restSpeed:10,...i,...c};return this.startAxisValueAnimation(o,d)});return Promise.all(c).then(o)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return Ja(this.visualElement,e),n.start(io(e,n,0,t,this.visualElement,!1))}stopAnimation(){Q(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){Q(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps(),r=n[t];return r||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){Q(t=>{let{drag:n}=this.getProps();if(!Ls(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:a}=r.layout.layoutBox[t];i.set(e[t]-V(n,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!Xi(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};Q(e=>{let t=this.getAxisMotionValue(e);if(t&&this.constraints!==!1){let n=t.get();r[e]=ks({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},``):`none`,n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Q(t=>{if(!Ls(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:a}=this.constraints[t];n.set(V(i,a,r[t]))})}addListeners(){if(!this.visualElement.current)return;Fs.set(this.visualElement,this);let e=this.visualElement.current,t=ko(e,`pointerdown`,e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),n=()=>{let{dragConstraints:e}=this.getProps();Xi(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,i=r.addEventListener(`measure`,n);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),A.read(n);let a=Eo(window,`resize`,()=>this.scalePositionWithinConstraints()),o=r.addEventListener(`didUpdate`,({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(Q(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{a(),t(),i(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:a=js,dragMomentum:o=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:a,dragMomentum:o}}};function Ls(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function Rs(e,t=10){let n=null;return Math.abs(e.y)>t?n=`y`:Math.abs(e.x)>t&&(n=`x`),n}var zs=class extends Y{constructor(e){super(e),this.removeGroupControls=E,this.removeListeners=E,this.controls=new Is(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||E}unmount(){this.removeGroupControls(),this.removeListeners()}};const Bs=e=>(t,n)=>{e&&A.postRender(()=>e(t,n))};var Vs=class extends Y{constructor(){super(...arguments),this.removePointerDownListener=E}onPointerDown(e){this.session=new _s(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ms(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:Bs(e),onStart:Bs(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&A.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=ko(this.node.current,`pointerdown`,e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}};const Hs={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Us(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const Ws={correct:(e,t)=>{if(!t.target)return e;if(typeof e==`string`)if(R.test(e))e=parseFloat(e);else return e;let n=Us(e,t.target.x),r=Us(e,t.target.y);return`${n}% ${r}%`}},Gs={correct:(e,{treeScale:t,projectionDelta:n})=>{let r=e,i=B.parse(e);if(i.length>5)return r;let a=B.createTransformer(e),o=typeof i[0]==`number`?0:1,s=n.x.scale*t.x,c=n.y.scale*t.y;i[0+o]/=s,i[1+o]/=c;let l=V(s,c,.5);return typeof i[2+o]==`number`&&(i[2+o]/=l),typeof i[3+o]==`number`&&(i[3+o]/=l),a(i)}};var Ks=class extends g.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;ua(Js),i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),i.root.didUpdate(),i.addEventListener(`animationComplete`,()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),Hs.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,{projection:a}=n;return a?(a.isPresent=i,r||e.layoutDependency!==t||t===void 0||e.isPresent!==i?a.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?a.promote():a.relegate()||A.postRender(()=>{let e=a.getStack();(!e||!e.members.length)&&this.safeToRemove()})),null):null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),qr.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}};function qs(e){let[t,n]=yi(),r=(0,g.useContext)(_);return(0,J.jsx)(Ks,{...e,layoutGroup:r,switchLayoutGroup:(0,g.useContext)(ta),isPresent:t,safeToRemove:n})}const Js={borderRadius:{...Ws,applyTo:[`borderTopLeftRadius`,`borderTopRightRadius`,`borderBottomLeftRadius`,`borderBottomRightRadius`]},borderTopLeftRadius:Ws,borderTopRightRadius:Ws,borderBottomLeftRadius:Ws,borderBottomRightRadius:Ws,boxShadow:Gs};function Ys(e,t,n){let r=q(e)?e:Kr(e);return r.start(io(``,r,t,n)),r.animation}const Xs=(e,t)=>e.depth-t.depth;var Zs=class{constructor(){this.children=[],this.isDirty=!1}add(e){S(this.children,e),this.isDirty=!0}remove(e){C(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Xs),this.isDirty=!1,this.children.forEach(e)}};function Qs(e,t){let n=N.now(),r=({timestamp:i})=>{let a=i-n;a>=t&&(j(r),e(a-t))};return A.setup(r,!0),()=>j(r)}const $s=[`TopLeft`,`TopRight`,`BottomLeft`,`BottomRight`],ec=$s.length,tc=e=>typeof e==`string`?parseFloat(e):e,nc=e=>typeof e==`number`||R.test(e);function rc(e,t,n,r,i,a){i?(e.opacity=V(0,n.opacity??1,ac(r)),e.opacityExit=V(t.opacity??1,0,oc(r))):a&&(e.opacity=V(t.opacity??1,n.opacity??1,r));for(let i=0;i<ec;i++){let a=`border${$s[i]}Radius`,o=ic(t,a),s=ic(n,a);if(o===void 0&&s===void 0)continue;o||=0,s||=0;let c=o===0||s===0||nc(o)===nc(s);c?(e[a]=Math.max(V(tc(o),tc(s),r),0),(L.test(s)||L.test(o))&&(e[a]+=`%`)):e[a]=s}(t.rotate||n.rotate)&&(e.rotate=V(t.rotate||0,n.rotate||0,r))}function ic(e,t){return e[t]===void 0?e.borderRadius:e[t]}const ac=sc(0,.5,Ce),oc=sc(.5,.95,E);function sc(e,t,n){return r=>r<e?0:r>t?1:n(ce(e,t,r))}function cc(e,t){e.min=t.min,e.max=t.max}function $(e,t){cc(e.x,t.x),cc(e.y,t.y)}function lc(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function uc(e,t,n,r,i){return e-=t,e=es(e,1/n,r),i!==void 0&&(e=es(e,1/i,r)),e}function dc(e,t=0,n=1,r=.5,i,a=e,o=e){if(L.test(t)){t=parseFloat(t);let e=V(o.min,o.max,t/100);t=e-o.min}if(typeof t!=`number`)return;let s=V(a.min,a.max,r);e===a&&(s-=t),e.min=uc(e.min,t,n,s,i),e.max=uc(e.max,t,n,s,i)}function fc(e,t,[n,r,i],a,o){dc(e,t[n],t[r],t[i],t.scale,a,o)}const pc=[`x`,`scaleX`,`originX`],mc=[`y`,`scaleY`,`originY`];function hc(e,t,n,r){fc(e.x,t,pc,n?n.x:void 0,r?r.x:void 0),fc(e.y,t,mc,n?n.y:void 0,r?r.y:void 0)}function gc(e){return e.translate===0&&e.scale===1}function _c(e){return gc(e.x)&&gc(e.y)}function vc(e,t){return e.min===t.min&&e.max===t.max}function yc(e,t){return vc(e.x,t.x)&&vc(e.y,t.y)}function bc(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function xc(e,t){return bc(e.x,t.x)&&bc(e.y,t.y)}function Sc(e){return X(e.x)/X(e.y)}function Cc(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}var wc=class{constructor(){this.members=[]}add(e){S(this.members,e),e.scheduleRender()}remove(e){if(C(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t=this.members.findIndex(t=>e===t);if(t===0)return!1;let n;for(let e=t;e>=0;e--){let t=this.members[e];if(t.isPresent!==!1){n=t;break}}return n?(this.promote(n),!0):!1}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;r===!1&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}};function Tc(e,t,n){let r=``,i=e.x.translate/t.x,a=e.y.translate/t.y,o=n?.z||0;if((i||a||o)&&(r=`translate3d(${i}px, ${a}px, ${o}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:a,skewX:o,skewY:s}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),a&&(r+=`rotateY(${a}deg) `),o&&(r+=`skewX(${o}deg) `),s&&(r+=`skewY(${s}deg) `)}let s=e.x.scale*t.x,c=e.y.scale*t.y;return(s!==1||c!==1)&&(r+=`scale(${s}, ${c})`),r||`none`}const Ec={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},Dc=[``,`X`,`Y`,`Z`],Oc={visibility:`hidden`},kc=1e3;let Ac=0;function jc(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function Mc(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:t}=e.options;if(!t)return;let n=Ya(t);if(window.MotionHasOptimisedAnimation(n,`transform`)){let{layout:t,layoutId:r}=e.options;window.MotionCancelOptimisedAnimation(n,`transform`,A,!(t||r))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&Mc(r)}function Nc({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=t?.()){this.id=Ac++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,k.value&&(Ec.nodes=Ec.calculatedTargetDeltas=Ec.calculatedProjections=0),this.nodes.forEach(Ic),this.nodes.forEach(Uc),this.nodes.forEach(Wc),this.nodes.forEach(Lc),k.addProjectionMetrics&&k.addProjectionMetrics(Ec)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new Zs)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new le),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=ui(t)&&!di(t),this.instance=t;let{layoutId:n,layout:r,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||n)&&(this.isLayoutDirty=!0),e){let n,r=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=Qs(r,250),Hs.hasAnimatedSinceResize&&(Hs.hasAnimatedSinceResize=!1,this.nodes.forEach(Hc))})}n&&this.root.registerSharedNode(n,this),this.options.animate!==!1&&i&&(n||r)&&this.addEventListener(`didUpdate`,({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let a=this.options.transition||i.getDefaultTransition()||Zc,{onLayoutAnimationStart:o,onLayoutAnimationComplete:s}=i.getProps(),c=!this.targetLayout||!xc(this.targetLayout,r),l=!t&&n;if(this.options.layoutRoot||this.resumeFrom||l||t&&(c||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...br(a,`layout`),onPlay:o,onComplete:s};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,l)}else t||Hc(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),j(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Gc),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Mc(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll(`snapshot`),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(t===void 0&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,``):void 0,this.updateSnapshot(),e&&this.notifyListeners(`willUpdate`)}update(){this.updateScheduled=!1;let e=this.isUpdateBlocked();if(e){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(zc);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(Bc);return}this.isUpdating||this.nodes.forEach(Bc),this.animationCommitId=this.animationId,this.isUpdating=!1,this.nodes.forEach(Vc),this.nodes.forEach(Pc),this.nodes.forEach(Fc),this.clearAllSnapshots();let t=N.now();M.delta=w(0,1e3/60,t-M.timestamp),M.timestamp=t,M.isProcessing=!0,Le.update.process(M),Le.preRender.process(M),Le.render.process(M),M.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,qr.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Rc),this.sharedNodes.forEach(Kc)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,A.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){A.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!X(this.snapshot.measuredBox.x)&&!X(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++){let t=this.path[e];t.updateScroll()}let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Z(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners(`measure`,this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify(`LayoutMeasure`,this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e=`measure`){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!_c(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,``):void 0,a=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||Zo(this.latestValues)||a)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){let t=this.measurePageBox(),n=this.removeElementScroll(t);return e&&(n=this.removeTransform(n)),tl(n),{animationId:this.root.animationId,measuredBox:t,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return Z();let t=e.measureViewportBox(),n=this.scroll?.wasRoot||this.path.some(rl);if(!n){let{scroll:e}=this.root;e&&(ls(t.x,e.offset.x),ls(t.y,e.offset.y))}return t}removeElementScroll(e){let t=Z();if($(t,e),this.scroll?.wasRoot)return t;for(let n=0;n<this.path.length;n++){let r=this.path[n],{scroll:i,options:a}=r;r!==this.root&&i&&a.layoutScroll&&(i.wasRoot&&$(t,e),ls(t.x,i.offset.x),ls(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let n=Z();$(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&ds(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),Zo(r.latestValues)&&ds(n,r.latestValues)}return Zo(this.latestValues)&&ds(n,this.latestValues),n}removeTransform(e){let t=Z();$(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!Zo(n.latestValues))continue;Xo(n.latestValues)&&n.updateSnapshot();let r=Z(),i=n.measurePageBox();$(r,i),hc(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return Zo(this.latestValues)&&hc(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:e.crossfade===void 0?!0:e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==M.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||=t.isProjectionDirty,this.isTransformDirty||=t.isTransformDirty,this.isSharedProjectionDirty||=t.isSharedProjectionDirty;let n=!!this.resumingFrom||this!==t,r=!(e||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize);if(r)return;let{layout:i,layoutId:a}=this.options;if(!(!this.layout||!(i||a))){if(this.resolvedRelativeTargetAt=M.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&this.animationProgress!==1?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Z(),this.relativeTargetOrigin=Z(),Go(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),$(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=Z(),this.targetWithTransforms=Z()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Uo(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):$(this.target,this.layout.layoutBox),rs(this.target,this.targetDelta)):$(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&this.animationProgress!==1?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Z(),this.relativeTargetOrigin=Z(),Go(this.relativeTargetOrigin,this.target,e.target),$(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}k.value&&Ec.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Xo(this.parent.latestValues)||Qo(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===M.timestamp&&(n=!1),n)return;let{layout:r,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||i))return;$(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,o=this.treeScale.y;cs(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=Z());let{target:s}=e;if(!s){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(lc(this.prevProjectionDelta.x,this.projectionDelta.x),lc(this.prevProjectionDelta.y,this.projectionDelta.y)),Vo(this.projectionDelta,this.layoutCorrected,s,this.latestValues),(this.treeScale.x!==a||this.treeScale.y!==o||!Cc(this.projectionDelta.x,this.prevProjectionDelta.x)||!Cc(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners(`projectionUpdate`,s)),k.value&&Ec.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=qo(),this.projectionDelta=qo(),this.projectionDeltaWithTransform=qo()}setAnimationOrigin(e,t=!1){let n=this.snapshot,r=n?n.latestValues:{},i={...this.latestValues},a=qo();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=Z(),s=n?n.source:void 0,c=this.layout?this.layout.source:void 0,l=s!==c,u=this.getStack(),d=!u||u.members.length<=1,f=!!(l&&!d&&this.options.crossfade===!0&&!this.path.some(Xc));this.animationProgress=0;let p;this.mixTargetDelta=t=>{let n=t/1e3;qc(a.x,e.x,n),qc(a.y,e.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Go(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Yc(this.relativeTarget,this.relativeTargetOrigin,o,n),p&&yc(this.relativeTarget,p)&&(this.isProjectionDirty=!1),p||=Z(),$(p,this.relativeTarget)),l&&(this.animationValues=i,rc(i,r,this.latestValues,n,f,d)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners(`animationStart`),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(j(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=A.update(()=>{Hs.hasAnimatedSinceResize=!0,P.layout++,this.motionValue||=Kr(0),this.currentAnimation=Ys(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{P.layout--},onComplete:()=>{P.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners(`animationComplete`)}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(kc),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(!(!t||!n||!r)){if(this!==e&&this.layout&&r&&nl(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||Z();let t=X(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=X(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}$(t,n),ds(t,i),Vo(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new wc);let n=this.sharedNodes.get(e);n.add(t);let r=t.options.initialPromotionConfig;t.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return e?e.lead===this:!0}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return e?e.relegate(this):!1}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&jc(`z`,e,r,this.animationValues);for(let t=0;t<Dc.length;t++)jc(`rotate${Dc[t]}`,e,r,this.animationValues),jc(`skew${Dc[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return Oc;let t={visibility:``},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity=``,t.pointerEvents=Na(e?.pointerEvents)||``,t.transform=n?n(this.latestValues,``):`none`,t;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let t={};return this.options.layoutId&&(t.opacity=this.latestValues.opacity===void 0?1:this.latestValues.opacity,t.pointerEvents=Na(e?.pointerEvents)||``),this.hasProjected&&!Zo(this.latestValues)&&(t.transform=n?n({},``):`none`,this.hasProjected=!1),t}let i=r.animationValues||r.latestValues;this.applyTransformsToTarget(),t.transform=Tc(this.projectionDeltaWithTransform,this.treeScale,i),n&&(t.transform=n(i,t.transform));let{x:a,y:o}=this.projectionDelta;for(let e in t.transformOrigin=`${a.origin*100}% ${o.origin*100}% 0`,r.animationValues?t.opacity=r===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:t.opacity=r===this?i.opacity===void 0?``:i.opacity:i.opacityExit===void 0?0:i.opacityExit,la){if(i[e]===void 0)continue;let{correct:n,applyTo:a,isCSSVariable:o}=la[e],s=t.transform===`none`?i[e]:n(i[e],r);if(a){let e=a.length;for(let n=0;n<e;n++)t[a[n]]=s}else o?this.options.visualElement.renderState.vars[e]=s:t[e]=s}return this.options.layoutId&&(t.pointerEvents=r===this?Na(e?.pointerEvents)||``:`none`),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(zc),this.root.sharedNodes.clear()}}}function Pc(e){e.updateLayout()}function Fc(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners(`didUpdate`)){let{layoutBox:n,measuredBox:r}=e.layout,{animationType:i}=e.options,a=t.source!==e.layout.source;i===`size`?Q(e=>{let r=a?t.measuredBox[e]:t.layoutBox[e],i=X(r);r.min=n[e].min,r.max=r.min+i}):nl(i,t.layoutBox,n)&&Q(r=>{let i=a?t.measuredBox[r]:t.layoutBox[r],o=X(n[r]);i.max=i.min+o,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+o)});let o=qo();Vo(o,n,t.layoutBox);let s=qo();a?Vo(s,e.applyTransform(r,!0),t.measuredBox):Vo(s,n,t.layoutBox);let c=!_c(o),l=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:a}=r;if(i&&a){let o=Z();Go(o,t.layoutBox,i.layoutBox);let s=Z();Go(s,n,a.layoutBox),xc(o,s)||(l=!0),r.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=o,e.relativeParent=r)}}}e.notifyListeners(`didUpdate`,{layout:n,snapshot:t,delta:s,layoutDelta:o,hasLayoutChanged:c,hasRelativeLayoutChanged:l})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function Ic(e){k.value&&Ec.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty),e.isTransformDirty||=e.parent.isTransformDirty)}function Lc(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Rc(e){e.clearSnapshot()}function zc(e){e.clearMeasurements()}function Bc(e){e.isLayoutDirty=!1}function Vc(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify(`BeforeLayoutMeasure`),e.resetTransform()}function Hc(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Uc(e){e.resolveTargetDelta()}function Wc(e){e.calcProjection()}function Gc(e){e.resetSkewAndRotation()}function Kc(e){e.removeLeadSnapshot()}function qc(e,t,n){e.translate=V(t.translate,0,n),e.scale=V(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Jc(e,t,n,r){e.min=V(t.min,n.min,r),e.max=V(t.max,n.max,r)}function Yc(e,t,n,r){Jc(e.x,t.x,n.x,r),Jc(e.y,t.y,n.y,r)}function Xc(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const Zc={duration:.45,ease:[.4,0,.1,1]},Qc=e=>typeof navigator<`u`&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),$c=Qc(`applewebkit/`)&&!Qc(`chrome/`)?Math.round:E;function el(e){e.min=$c(e.min),e.max=$c(e.max)}function tl(e){el(e.x),el(e.y)}function nl(e,t,n){return e===`position`||e===`preserve-aspect`&&!zo(Sc(t),Sc(n),.2)}function rl(e){return e!==e.root&&e.scroll?.wasRoot}const il=Nc({attachResizeListener:(e,t)=>Eo(e,`resize`,t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),al={current:void 0},ol=Nc({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!al.current){let e=new il({});e.mount(window),e.setOptions({layoutScroll:!0}),al.current=e}return al.current},resetTransform:(e,t)=>{e.style.transform=t===void 0?`none`:t},checkIsScrollRoot:e=>window.getComputedStyle(e).position===`fixed`}),sl={pan:{Feature:Vs},drag:{Feature:zs,ProjectionNode:ol,MeasureLayout:qs}};function cl(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive(`whileHover`,n===`Start`);let i=`onHover`+n,a=r[i];a&&A.postRender(()=>a(t,Do(t)))}var ll=class extends Y{mount(){let{current:e}=this.node;e&&(this.unmount=$r(e,(e,t)=>(cl(this.node,t,`Start`),e=>cl(this.node,e,`End`))))}unmount(){}},ul=class extends Y{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(`:focus-visible`)}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive(`whileFocus`,!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive(`whileFocus`,!1),this.isActive=!1)}mount(){this.unmount=se(Eo(this.node.current,`focus`,()=>this.onFocus()),Eo(this.node.current,`blur`,()=>this.onBlur()))}unmount(){}};function dl(e,t,n){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive(`whileTap`,n===`Start`);let i=`onTap`+(n===`End`?``:n),a=r[i];a&&A.postRender(()=>a(t,Do(t)))}var fl=class extends Y{mount(){let{current:e}=this.node;e&&(this.unmount=li(e,(e,t)=>(dl(this.node,t,`Start`),(e,{success:t})=>dl(this.node,e,t?`End`:`Cancel`)),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}};const pl=new WeakMap,ml=new WeakMap,hl=e=>{let t=pl.get(e.target);t&&t(e)},gl=e=>{e.forEach(hl)};function _l({root:e,...t}){let n=e||document;ml.has(n)||ml.set(n,{});let r=ml.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(gl,{root:e,...t})),r[i]}function vl(e,t,n){let r=_l(t);return pl.set(e,n),r.observe(e),()=>{pl.delete(e),r.unobserve(e)}}const yl={some:0,all:1};var bl=class extends Y{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r=`some`,once:i}=e,a={root:t?t.current:void 0,rootMargin:n,threshold:typeof r==`number`?r:yl[r]},o=e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive(`whileInView`,t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),a=t?n:r;a&&a(e)};return vl(this.node.current,a,o)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>`u`)return;let{props:e,prevProps:t}=this.node,n=[`amount`,`margin`,`root`].some(xl(e,t));n&&this.startObserver()}unmount(){}};function xl({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const Sl={inView:{Feature:bl},tap:{Feature:fl},focus:{Feature:ul},hover:{Feature:ll}},Cl={layout:{ProjectionNode:ol,MeasureLayout:qs}},wl={current:null},Tl={current:!1};function El(){if(Tl.current=!0,y)if(window.matchMedia){let e=window.matchMedia(`(prefers-reduced-motion)`),t=()=>wl.current=e.matches;e.addListener(t),t()}else wl.current=!1}const Dl=new WeakMap;function Ol(e,t,n){for(let r in t){let i=t[r],a=n[r];if(q(i))e.addValue(r,i);else if(q(a))e.addValue(r,Kr(i,{owner:e}));else if(a!==i)if(e.hasValue(r)){let t=e.getValue(r);t.liveStyle===!0?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,Kr(t===void 0?i:t,{owner:e}))}}for(let r in n)t[r]===void 0&&e.removeValue(r);return t}const kl=[`AnimationStart`,`AnimationComplete`,`Update`,`BeforeLayoutMeasure`,`LayoutMeasure`,`LayoutAnimationStart`,`LayoutAnimationComplete`];var Al=class{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:a},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Hn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify(`Update`,this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=N.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,A.render(this.render,!1,!0))};let{latestValues:s,renderState:c}=a;this.latestValues=s,this.baseTarget={...s},this.initialValues=t.initial?{...s}:{},this.renderState=c,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.blockInitialAnimation=!!i,this.isControllingVariants=Wi(t),this.isVariantNode=Gi(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:l,...u}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in u){let t=u[e];s[e]!==void 0&&q(t)&&t.set(s[e],!1)}}mount(e){this.current=e,Dl.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),Tl.current||El(),this.shouldReduceMotion=this.reducedMotionConfig===`never`?!1:this.reducedMotionConfig===`always`?!0:wl.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),j(this.notifyUpdate),j(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let n=jn.has(e);n&&this.onBindTransform&&this.onBindTransform();let r=t.on(`change`,t=>{this.latestValues[e]=t,this.props.onUpdate&&A.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),i=t.on(`renderRequest`,this.scheduleRender),a;window.MotionCheckAppearSync&&(a=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{r(),i(),a&&a(),t.owner&&t.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e=`animation`;for(e in Ti){let t=Ti[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Z()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<kl.length;t++){let n=kl[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=`on`+n,i=e[r];i&&(this.propEventSubscriptions[n]=this.on(n,i))}this.prevMotionValues=Ol(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return n===void 0&&t!==void 0&&(n=Kr(t===null?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){let n=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options);return n!=null&&(typeof n==`string`&&(ne(n)||ie(n))?n=parseFloat(n):!pi(n)&&B.test(t)&&(n=Ir(e,t)),this.setBaseTarget(e,q(n)?n.get():n)),q(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let{initial:t}=this.props,n;if(typeof t==`string`||typeof t==`object`){let r=Ma(this.props,t,this.presenceContext?.custom);r&&(n=r[e])}if(t&&n!==void 0)return n;let r=this.getBaseTargetFromProps(this.props,e);return r!==void 0&&!q(r)?r:this.initialValues[e]!==void 0&&n===void 0?void 0:this.baseTarget[e]}on(e,t){return this.events[e]||(this.events[e]=new le),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}},jl=class extends Al{constructor(){super(...arguments),this.KeyframeResolver=zr}sortInstanceNodePosition(e,t){return e.compareDocumentPosition(t)&2?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;q(e)&&(this.childSubscription=e.on(`change`,e=>{this.current&&(this.current.textContent=`${e}`)}))}};function Ml(e,{style:t,vars:n},r,i){for(let a in Object.assign(e.style,t,i&&i.getProjectionStyles(r)),n)e.style.setProperty(a,n[a])}function Nl(e){return window.getComputedStyle(e)}var Pl=class extends jl{constructor(){super(...arguments),this.type=`html`,this.renderInstance=Ml}readValueFromInstance(e,t){if(jn.has(t))return this.projection?.isProjecting?En(t):On(e,t);{let n=Nl(e),r=(Ve(t)?n.getPropertyValue(t):n[t])||0;return typeof r==`string`?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return fs(e,t)}build(e,t,n){ha(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return La(e,t,n)}};const Fl=new Set([`baseFrequency`,`diffuseConstant`,`kernelMatrix`,`kernelUnitLength`,`keySplines`,`keyTimes`,`limitingConeAngle`,`markerHeight`,`markerWidth`,`numOctaves`,`targetX`,`targetY`,`surfaceScale`,`specularConstant`,`specularExponent`,`stdDeviation`,`tableValues`,`viewBox`,`gradientTransform`,`pathLength`,`startOffset`,`textLength`,`lengthAdjust`]);function Il(e,t,n,r){for(let n in Ml(e,t,void 0,r),t.attrs)e.setAttribute(Fl.has(n)?n:Qi(n),t.attrs[n])}var Ll=class extends jl{constructor(){super(...arguments),this.type=`svg`,this.isSVGTag=!1,this.measureInstanceViewportBox=Z}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(jn.has(t)){let e=Fr(t);return e&&e.default||0}return t=Fl.has(t)?t:Qi(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return za(e,t,n)}build(e,t,n){wa(e,t,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(e,t,n,r){Il(e,t,n,r)}mount(e){this.isSVGTag=Ea(e.tagName),super.mount(e)}};const Rl=(e,t)=>ka(e)?new Ll(t):new Pl(t,{allowProjection:e!==g.Fragment}),zl=Va({...To,...Sl,...sl,...Cl},Rl),Bl=Ri(zl),Vl=[{label:`Confidence`,value:85},{label:`Lighting`,value:78},{label:`Photo Quality`,value:88}],Hl=({value:e})=>(0,J.jsx)(`div`,{className:`w-full bg-gray-200 rounded-full h-4 mt-3`,children:(0,J.jsx)(Bl.div,{className:`bg-gradient-primary h-4 rounded-full`,initial:{width:0},animate:{width:`${e}%`},transition:{duration:.88,ease:`easeOut`}})});function Ul({rotation:e=0,className:t}){let[n,r]=(0,g.useState)(0);(0,g.useEffect)(()=>{let e=setInterval(()=>{r(e=>(e+1)%(Vl.length+2))},2200);return()=>clearInterval(e)},[]);let i=()=>{if(n===0)return(0,J.jsxs)(Bl.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.55},className:`px-4`,children:[(0,J.jsx)(`h3`,{className:`text-xl font-bold`,children:`Jessica, 26`}),(0,J.jsx)(`p`,{className:`text-sm text-graphite-60 mt-1`,children:`"Lover of dogs, pizza, and spontaneous adventures."`})]},`profile-bio`);if(n>0&&n<=Vl.length){let e=Vl[n-1];return(0,J.jsxs)(Bl.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.55},className:`px-4 py-2`,children:[(0,J.jsx)(`p`,{className:`text-lg font-semibold text-graphite-90 mb-1`,children:e.label}),(0,J.jsxs)(`p`,{className:`text-2xl font-bold text-flame-red mb-2`,children:[e.value,`%`]}),(0,J.jsx)(Hl,{value:e.value})]},e.label)}if(n===Vl.length+1)return(0,J.jsxs)(Bl.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.55},className:`px-4 text-center`,children:[(0,J.jsx)(`p`,{className:`text-lg font-semibold`,children:`Overall Score`}),(0,J.jsx)(`p`,{className:`text-5xl font-bold bg-clip-text text-transparent bg-gradient-primary`,children:`86`})]},`score`)};return(0,J.jsxs)(`div`,{className:a(`absolute h-[600px] w-[300px] rounded-[40px] bg-white shadow-2xl border-4 border-gray-200 overflow-hidden flex flex-col`,t),style:{transform:`rotate(${e}deg)`},children:[(0,J.jsx)(`img`,{src:`/attractive-person-profile.avif`,alt:`Profile`,className:`w-full h-[350px] object-cover object-center flex-shrink-0 bg-gray-100`}),(0,J.jsx)(`div`,{className:`flex-grow flex items-center justify-center p-4`,children:(0,J.jsx)(Si,{mode:`wait`,children:i()})})]})}function Wl({rotation:e=0,className:t}){return(0,J.jsx)(`div`,{className:a(`absolute h-[600px] w-[300px] rounded-[40px] bg-white shadow-2xl border-4 border-gray-200`,t),style:{transform:`rotate(${e}deg)`},children:(0,J.jsxs)(`div`,{className:`p-4`,children:[(0,J.jsx)(`div`,{className:`w-full h-[350px] rounded-2xl bg-gray-200`}),(0,J.jsxs)(`div`,{className:`mt-4 space-y-3`,children:[(0,J.jsx)(`div`,{className:`h-6 w-3/4 rounded bg-gray-200`}),(0,J.jsx)(`div`,{className:`h-4 w-full rounded bg-gray-200`}),(0,J.jsx)(`div`,{className:`h-4 w-5/6 rounded bg-gray-200`})]})]})})}function Gl(){return(0,J.jsxs)(`div`,{className:`bg-cloud-white text-graphite-90`,children:[(0,J.jsx)(Kl,{}),(0,J.jsx)(ql,{}),(0,J.jsx)(Jl,{}),(0,J.jsx)(Yl,{})]})}function Kl(){return(0,J.jsx)(`section`,{className:`w-full bg-gradient-hero`,children:(0,J.jsxs)(`div`,{className:`container mx-auto grid min-h-screen items-center gap-8 px-4 md:grid-cols-2 md:px-6 lg:gap-16`,children:[(0,J.jsxs)(`div`,{className:`space-y-6`,children:[(0,J.jsx)(`h1`,{className:`text-h1-mobile md:text-h1`,children:`Stop guessing. Start matching.`}),(0,J.jsx)(`p`,{className:`text-body-lg text-graphite-60 max-w-md`,children:`Upgrade your Tinder profile with AI-powered optimization. Get more matches, better conversations, and find your soulmate.`}),(0,J.jsx)(`div`,{className:`flex flex-col gap-4 sm:flex-row sm:flex-wrap`,children:(0,J.jsxs)(`div`,{className:`bg-red-200 flex flex-col gap-4`,children:[(0,J.jsx)(`h1`,{children:`DEVELOPMENT BUTTONS`}),(0,J.jsx)(c,{asChild:!0,size:`lg`,variant:`primary`,children:(0,J.jsx)(e,{to:`/image-analyzer`,children:`Try Image Analyzer`})}),(0,J.jsx)(c,{asChild:!0,size:`lg`,variant:`primary`,children:(0,J.jsx)(e,{to:`/welcome`,children:`Get Started`})}),(0,J.jsx)(c,{asChild:!0,size:`lg`,variant:`tertiary`,className:`sm:w-auto`,children:(0,J.jsx)(e,{to:`/bio-analyzer`,children:`Try Bio Analyzer`})})]})})]}),(0,J.jsxs)(`div`,{className:`relative ml-10 h-full items-center justify-center md:flex`,children:[(0,J.jsx)(Wl,{rotation:-6}),(0,J.jsx)(Ul,{rotation:6,className:`relative`})]})]})})}function ql(){let e=[{icon:p,value:`+25%`,label:`Average Match Rate Increase`},{icon:u,value:`9/10`,label:`Users Report Higher Quality Matches`},{icon:f,value:`4s`,label:`Average Analysis Time Per Photo`}];return(0,J.jsx)(`section`,{className:`py-12 md:py-24 bg-cloud-white`,children:(0,J.jsx)(`div`,{className:`container mx-auto px-4 md:px-6`,children:(0,J.jsx)(`div`,{className:`grid gap-8 md:grid-cols-3`,children:e.map((e,t)=>(0,J.jsxs)(`div`,{className:`text-center`,children:[(0,J.jsx)(e.icon,{className:`mx-auto h-12 w-12 text-flame-red`}),(0,J.jsx)(`p`,{className:`text-display-1-mobile md:text-display-1 mt-4`,children:e.value}),(0,J.jsx)(`p`,{className:`text-body-md text-graphite-60`,children:e.label})]},t))})})})}function Jl(){let e=[{icon:m,title:`Upload Your Profile`,description:`Securely upload your current dating profile photos and bio. We keep your data private.`},{icon:h,title:`AI-Powered Analysis`,description:`Our model scores your photos on key metrics and rewrites your bio to be more engaging.`},{icon:d,title:`Get Your Glow-Up`,description:`Implement our actionable tips, update your profile, and watch your matches increase.`}];return(0,J.jsx)(`section`,{id:`image-analyzer`,className:`py-12 md:py-24 bg-gray-50`,children:(0,J.jsxs)(`div`,{className:`container mx-auto px-4 md:px-6`,children:[(0,J.jsxs)(`div`,{className:`text-center max-w-2xl mx-auto`,children:[(0,J.jsx)(`h2`,{className:`text-h2-mobile md:text-h2`,children:`How It Works in 3 Simple Steps`}),(0,J.jsx)(`p`,{className:`text-body-lg text-graphite-60 mt-4`,children:`Transform your dating profile from overlooked to overbooked.`})]}),(0,J.jsx)(`div`,{className:`grid gap-8 md:grid-cols-3 mt-12`,children:e.map((e,t)=>(0,J.jsxs)(`div`,{className:`flex flex-col items-center text-center p-6 rounded-lg`,children:[(0,J.jsx)(`div`,{className:`flex h-16 w-16 items-center justify-center rounded-full bg-flame-red text-cloud-white mb-6`,children:(0,J.jsx)(e.icon,{className:`h-8 w-8`})}),(0,J.jsx)(`h3`,{className:`text-xl font-semibold`,children:e.title}),(0,J.jsx)(`p`,{className:`text-graphite-60 mt-2`,children:e.description})]},t))})]})})}function Yl(){return(0,J.jsx)(`footer`,{className:`bg-graphite-90 text-cloud-white/80`,children:(0,J.jsxs)(`div`,{className:`container mx-auto px-4 md:px-6 py-12`,children:[(0,J.jsxs)(`div`,{className:`grid gap-8 md:grid-cols-4`,children:[(0,J.jsxs)(`div`,{children:[(0,J.jsx)(`h4`,{className:`font-semibold text-lg text-cloud-white`,children:`TinderOptimizer`}),(0,J.jsx)(`p`,{className:`mt-2 text-sm`,children:`AI-powered profile analysis.`})]}),(0,J.jsxs)(`div`,{children:[(0,J.jsx)(`h5`,{className:`font-semibold text-cloud-white`,children:`Product`}),(0,J.jsxs)(`ul`,{className:`mt-4 space-y-2 text-sm`,children:[(0,J.jsx)(`li`,{children:(0,J.jsx)(e,{to:`/image-analyzer`,className:`hover:text-cloud-white`,children:`Image Analyzer`})}),(0,J.jsx)(`li`,{children:(0,J.jsx)(e,{to:`/bio-analyzer`,className:`hover:text-cloud-white`,children:`Bio Analyzer`})}),(0,J.jsx)(`li`,{children:(0,J.jsx)(e,{to:`#pricing`,className:`hover:text-cloud-white`,children:`Pricing`})})]})]}),(0,J.jsxs)(`div`,{children:[(0,J.jsx)(`h5`,{className:`font-semibold text-cloud-white`,children:`Company`}),(0,J.jsxs)(`ul`,{className:`mt-4 space-y-2 text-sm`,children:[(0,J.jsx)(`li`,{children:(0,J.jsx)(e,{to:`#`,className:`hover:text-cloud-white`,children:`About Us`})}),(0,J.jsx)(`li`,{children:(0,J.jsx)(e,{to:`#`,className:`hover:text-cloud-white`,children:`Privacy Policy`})}),(0,J.jsx)(`li`,{children:(0,J.jsx)(e,{to:`#`,className:`hover:text-cloud-white`,children:`Terms of Service`})})]})]}),(0,J.jsxs)(`div`,{children:[(0,J.jsx)(`h5`,{className:`font-semibold text-cloud-white`,children:`Connect`}),(0,J.jsx)(`div`,{className:`flex space-x-4 mt-4`})]})]}),(0,J.jsx)(`div`,{className:`border-t border-graphite-60/50 mt-8 pt-8 text-center text-sm`,children:(0,J.jsxs)(`p`,{children:[`© `,new Date().getFullYear(),` TinderOptimizer. All rights reserved.`]})})]})})}const Xl=Gl;export{Xl as component};