import{__commonJSMin as e,__toESM as t,cn as n,require_jsx_runtime as r,require_react as i,require_react_dom as a}from"./main-B4G73TvM.js";import{<PERSON><PERSON> as o,createLucideIcon as s}from"./createLucideIcon-JB7IMeGf.js";import{Badge as c}from"./badge-B_rsQKkv.js";import{Primitive as l,Trash2 as u,composeRefs as d,dispatchDiscreteCustomEvent as f,useComposedRefs as p}from"./trash-2-CkLpzZy2.js";import{Shield as m}from"./shield-Ct2yHJj1.js";import{X as h,imageStorage as g,sessionManager as _}from"./storage-BLisfAL7.js";import{composeEventHandlers as v,createContext2 as y,createContextScope as b,useCallbackRef as x,useControllableState as S,useLayoutEffect2 as C}from"./dist-DGuqR-4V.js";const w=s(`Database`,[[`ellipse`,{cx:`12`,cy:`5`,rx:`9`,ry:`3`,key:`msslwz`}],[`path`,{d:`M3 5V19A9 3 0 0 0 21 19V5`,key:`1wlel7`}],[`path`,{d:`M3 12A9 3 0 0 0 21 12`,key:`mv7ke4`}]]),T=s(`FileUp`,[[`path`,{d:`M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z`,key:`1rqfz7`}],[`path`,{d:`M14 2v4a2 2 0 0 0 2 2h4`,key:`tnqrlb`}],[`path`,{d:`M12 12v6`,key:`3ahymv`}],[`path`,{d:`m15 15-3-3-3 3`,key:`15xj92`}]]),E=s(`Info`,[[`circle`,{cx:`12`,cy:`12`,r:`10`,key:`1mglay`}],[`path`,{d:`M12 16v-4`,key:`1dtifu`}],[`path`,{d:`M12 8h.01`,key:`e9boi3`}]]);var ee=e((exports,t)=>{var n=`SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED`;t.exports=n}),te=e((exports,t)=>{var n=ee();function r(){}function i(){}i.resetWarningCache=r,t.exports=function(){function e(e,t,r,i,a,o){if(o!==n){var s=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name=`Invariant Violation`,s}}e.isRequired=e;function t(){return e}var a={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:r};return a.PropTypes=a,a}}),D=e((exports,t)=>{if(0)var n,r;else t.exports=te()()}),O=function(){return O=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n],t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},O.apply(this,arguments)};function k(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols==`function`)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n}function A(e,t,n,r){function i(e){return e instanceof n?e:new n(function(t){t(e)})}return new(n||=Promise)(function(n,a){function o(e){try{c(r.next(e))}catch(e){a(e)}}function s(e){try{c(r.throw(e))}catch(e){a(e)}}function c(e){e.done?n(e.value):i(e.value).then(o,s)}c((r=r.apply(e,t||[])).next())})}function ne(e,t,n){if(n||arguments.length===2)for(var r=0,i=t.length,a;r<i;r++)(a||!(r in t))&&(a||=Array.prototype.slice.call(t,0,r),a[r]=t[r]);return e.concat(a||Array.prototype.slice.call(t))}const re=new Map([[`1km`,`application/vnd.1000minds.decision-model+xml`],[`3dml`,`text/vnd.in3d.3dml`],[`3ds`,`image/x-3ds`],[`3g2`,`video/3gpp2`],[`3gp`,`video/3gp`],[`3gpp`,`video/3gpp`],[`3mf`,`model/3mf`],[`7z`,`application/x-7z-compressed`],[`7zip`,`application/x-7z-compressed`],[`123`,`application/vnd.lotus-1-2-3`],[`aab`,`application/x-authorware-bin`],[`aac`,`audio/x-acc`],[`aam`,`application/x-authorware-map`],[`aas`,`application/x-authorware-seg`],[`abw`,`application/x-abiword`],[`ac`,`application/vnd.nokia.n-gage.ac+xml`],[`ac3`,`audio/ac3`],[`acc`,`application/vnd.americandynamics.acc`],[`ace`,`application/x-ace-compressed`],[`acu`,`application/vnd.acucobol`],[`acutc`,`application/vnd.acucorp`],[`adp`,`audio/adpcm`],[`aep`,`application/vnd.audiograph`],[`afm`,`application/x-font-type1`],[`afp`,`application/vnd.ibm.modcap`],[`ahead`,`application/vnd.ahead.space`],[`ai`,`application/pdf`],[`aif`,`audio/x-aiff`],[`aifc`,`audio/x-aiff`],[`aiff`,`audio/x-aiff`],[`air`,`application/vnd.adobe.air-application-installer-package+zip`],[`ait`,`application/vnd.dvb.ait`],[`ami`,`application/vnd.amiga.ami`],[`amr`,`audio/amr`],[`apk`,`application/vnd.android.package-archive`],[`apng`,`image/apng`],[`appcache`,`text/cache-manifest`],[`application`,`application/x-ms-application`],[`apr`,`application/vnd.lotus-approach`],[`arc`,`application/x-freearc`],[`arj`,`application/x-arj`],[`asc`,`application/pgp-signature`],[`asf`,`video/x-ms-asf`],[`asm`,`text/x-asm`],[`aso`,`application/vnd.accpac.simply.aso`],[`asx`,`video/x-ms-asf`],[`atc`,`application/vnd.acucorp`],[`atom`,`application/atom+xml`],[`atomcat`,`application/atomcat+xml`],[`atomdeleted`,`application/atomdeleted+xml`],[`atomsvc`,`application/atomsvc+xml`],[`atx`,`application/vnd.antix.game-component`],[`au`,`audio/x-au`],[`avi`,`video/x-msvideo`],[`avif`,`image/avif`],[`aw`,`application/applixware`],[`azf`,`application/vnd.airzip.filesecure.azf`],[`azs`,`application/vnd.airzip.filesecure.azs`],[`azv`,`image/vnd.airzip.accelerator.azv`],[`azw`,`application/vnd.amazon.ebook`],[`b16`,`image/vnd.pco.b16`],[`bat`,`application/x-msdownload`],[`bcpio`,`application/x-bcpio`],[`bdf`,`application/x-font-bdf`],[`bdm`,`application/vnd.syncml.dm+wbxml`],[`bdoc`,`application/x-bdoc`],[`bed`,`application/vnd.realvnc.bed`],[`bh2`,`application/vnd.fujitsu.oasysprs`],[`bin`,`application/octet-stream`],[`blb`,`application/x-blorb`],[`blorb`,`application/x-blorb`],[`bmi`,`application/vnd.bmi`],[`bmml`,`application/vnd.balsamiq.bmml+xml`],[`bmp`,`image/bmp`],[`book`,`application/vnd.framemaker`],[`box`,`application/vnd.previewsystems.box`],[`boz`,`application/x-bzip2`],[`bpk`,`application/octet-stream`],[`bpmn`,`application/octet-stream`],[`bsp`,`model/vnd.valve.source.compiled-map`],[`btif`,`image/prs.btif`],[`buffer`,`application/octet-stream`],[`bz`,`application/x-bzip`],[`bz2`,`application/x-bzip2`],[`c`,`text/x-c`],[`c4d`,`application/vnd.clonk.c4group`],[`c4f`,`application/vnd.clonk.c4group`],[`c4g`,`application/vnd.clonk.c4group`],[`c4p`,`application/vnd.clonk.c4group`],[`c4u`,`application/vnd.clonk.c4group`],[`c11amc`,`application/vnd.cluetrust.cartomobile-config`],[`c11amz`,`application/vnd.cluetrust.cartomobile-config-pkg`],[`cab`,`application/vnd.ms-cab-compressed`],[`caf`,`audio/x-caf`],[`cap`,`application/vnd.tcpdump.pcap`],[`car`,`application/vnd.curl.car`],[`cat`,`application/vnd.ms-pki.seccat`],[`cb7`,`application/x-cbr`],[`cba`,`application/x-cbr`],[`cbr`,`application/x-cbr`],[`cbt`,`application/x-cbr`],[`cbz`,`application/x-cbr`],[`cc`,`text/x-c`],[`cco`,`application/x-cocoa`],[`cct`,`application/x-director`],[`ccxml`,`application/ccxml+xml`],[`cdbcmsg`,`application/vnd.contact.cmsg`],[`cda`,`application/x-cdf`],[`cdf`,`application/x-netcdf`],[`cdfx`,`application/cdfx+xml`],[`cdkey`,`application/vnd.mediastation.cdkey`],[`cdmia`,`application/cdmi-capability`],[`cdmic`,`application/cdmi-container`],[`cdmid`,`application/cdmi-domain`],[`cdmio`,`application/cdmi-object`],[`cdmiq`,`application/cdmi-queue`],[`cdr`,`application/cdr`],[`cdx`,`chemical/x-cdx`],[`cdxml`,`application/vnd.chemdraw+xml`],[`cdy`,`application/vnd.cinderella`],[`cer`,`application/pkix-cert`],[`cfs`,`application/x-cfs-compressed`],[`cgm`,`image/cgm`],[`chat`,`application/x-chat`],[`chm`,`application/vnd.ms-htmlhelp`],[`chrt`,`application/vnd.kde.kchart`],[`cif`,`chemical/x-cif`],[`cii`,`application/vnd.anser-web-certificate-issue-initiation`],[`cil`,`application/vnd.ms-artgalry`],[`cjs`,`application/node`],[`cla`,`application/vnd.claymore`],[`class`,`application/octet-stream`],[`clkk`,`application/vnd.crick.clicker.keyboard`],[`clkp`,`application/vnd.crick.clicker.palette`],[`clkt`,`application/vnd.crick.clicker.template`],[`clkw`,`application/vnd.crick.clicker.wordbank`],[`clkx`,`application/vnd.crick.clicker`],[`clp`,`application/x-msclip`],[`cmc`,`application/vnd.cosmocaller`],[`cmdf`,`chemical/x-cmdf`],[`cml`,`chemical/x-cml`],[`cmp`,`application/vnd.yellowriver-custom-menu`],[`cmx`,`image/x-cmx`],[`cod`,`application/vnd.rim.cod`],[`coffee`,`text/coffeescript`],[`com`,`application/x-msdownload`],[`conf`,`text/plain`],[`cpio`,`application/x-cpio`],[`cpp`,`text/x-c`],[`cpt`,`application/mac-compactpro`],[`crd`,`application/x-mscardfile`],[`crl`,`application/pkix-crl`],[`crt`,`application/x-x509-ca-cert`],[`crx`,`application/x-chrome-extension`],[`cryptonote`,`application/vnd.rig.cryptonote`],[`csh`,`application/x-csh`],[`csl`,`application/vnd.citationstyles.style+xml`],[`csml`,`chemical/x-csml`],[`csp`,`application/vnd.commonspace`],[`csr`,`application/octet-stream`],[`css`,`text/css`],[`cst`,`application/x-director`],[`csv`,`text/csv`],[`cu`,`application/cu-seeme`],[`curl`,`text/vnd.curl`],[`cww`,`application/prs.cww`],[`cxt`,`application/x-director`],[`cxx`,`text/x-c`],[`dae`,`model/vnd.collada+xml`],[`daf`,`application/vnd.mobius.daf`],[`dart`,`application/vnd.dart`],[`dataless`,`application/vnd.fdsn.seed`],[`davmount`,`application/davmount+xml`],[`dbf`,`application/vnd.dbf`],[`dbk`,`application/docbook+xml`],[`dcr`,`application/x-director`],[`dcurl`,`text/vnd.curl.dcurl`],[`dd2`,`application/vnd.oma.dd2+xml`],[`ddd`,`application/vnd.fujixerox.ddd`],[`ddf`,`application/vnd.syncml.dmddf+xml`],[`dds`,`image/vnd.ms-dds`],[`deb`,`application/x-debian-package`],[`def`,`text/plain`],[`deploy`,`application/octet-stream`],[`der`,`application/x-x509-ca-cert`],[`dfac`,`application/vnd.dreamfactory`],[`dgc`,`application/x-dgc-compressed`],[`dic`,`text/x-c`],[`dir`,`application/x-director`],[`dis`,`application/vnd.mobius.dis`],[`disposition-notification`,`message/disposition-notification`],[`dist`,`application/octet-stream`],[`distz`,`application/octet-stream`],[`djv`,`image/vnd.djvu`],[`djvu`,`image/vnd.djvu`],[`dll`,`application/octet-stream`],[`dmg`,`application/x-apple-diskimage`],[`dmn`,`application/octet-stream`],[`dmp`,`application/vnd.tcpdump.pcap`],[`dms`,`application/octet-stream`],[`dna`,`application/vnd.dna`],[`doc`,`application/msword`],[`docm`,`application/vnd.ms-word.template.macroEnabled.12`],[`docx`,`application/vnd.openxmlformats-officedocument.wordprocessingml.document`],[`dot`,`application/msword`],[`dotm`,`application/vnd.ms-word.template.macroEnabled.12`],[`dotx`,`application/vnd.openxmlformats-officedocument.wordprocessingml.template`],[`dp`,`application/vnd.osgi.dp`],[`dpg`,`application/vnd.dpgraph`],[`dra`,`audio/vnd.dra`],[`drle`,`image/dicom-rle`],[`dsc`,`text/prs.lines.tag`],[`dssc`,`application/dssc+der`],[`dtb`,`application/x-dtbook+xml`],[`dtd`,`application/xml-dtd`],[`dts`,`audio/vnd.dts`],[`dtshd`,`audio/vnd.dts.hd`],[`dump`,`application/octet-stream`],[`dvb`,`video/vnd.dvb.file`],[`dvi`,`application/x-dvi`],[`dwd`,`application/atsc-dwd+xml`],[`dwf`,`model/vnd.dwf`],[`dwg`,`image/vnd.dwg`],[`dxf`,`image/vnd.dxf`],[`dxp`,`application/vnd.spotfire.dxp`],[`dxr`,`application/x-director`],[`ear`,`application/java-archive`],[`ecelp4800`,`audio/vnd.nuera.ecelp4800`],[`ecelp7470`,`audio/vnd.nuera.ecelp7470`],[`ecelp9600`,`audio/vnd.nuera.ecelp9600`],[`ecma`,`application/ecmascript`],[`edm`,`application/vnd.novadigm.edm`],[`edx`,`application/vnd.novadigm.edx`],[`efif`,`application/vnd.picsel`],[`ei6`,`application/vnd.pg.osasli`],[`elc`,`application/octet-stream`],[`emf`,`image/emf`],[`eml`,`message/rfc822`],[`emma`,`application/emma+xml`],[`emotionml`,`application/emotionml+xml`],[`emz`,`application/x-msmetafile`],[`eol`,`audio/vnd.digital-winds`],[`eot`,`application/vnd.ms-fontobject`],[`eps`,`application/postscript`],[`epub`,`application/epub+zip`],[`es`,`application/ecmascript`],[`es3`,`application/vnd.eszigno3+xml`],[`esa`,`application/vnd.osgi.subsystem`],[`esf`,`application/vnd.epson.esf`],[`et3`,`application/vnd.eszigno3+xml`],[`etx`,`text/x-setext`],[`eva`,`application/x-eva`],[`evy`,`application/x-envoy`],[`exe`,`application/octet-stream`],[`exi`,`application/exi`],[`exp`,`application/express`],[`exr`,`image/aces`],[`ext`,`application/vnd.novadigm.ext`],[`ez`,`application/andrew-inset`],[`ez2`,`application/vnd.ezpix-album`],[`ez3`,`application/vnd.ezpix-package`],[`f`,`text/x-fortran`],[`f4v`,`video/mp4`],[`f77`,`text/x-fortran`],[`f90`,`text/x-fortran`],[`fbs`,`image/vnd.fastbidsheet`],[`fcdt`,`application/vnd.adobe.formscentral.fcdt`],[`fcs`,`application/vnd.isac.fcs`],[`fdf`,`application/vnd.fdf`],[`fdt`,`application/fdt+xml`],[`fe_launch`,`application/vnd.denovo.fcselayout-link`],[`fg5`,`application/vnd.fujitsu.oasysgp`],[`fgd`,`application/x-director`],[`fh`,`image/x-freehand`],[`fh4`,`image/x-freehand`],[`fh5`,`image/x-freehand`],[`fh7`,`image/x-freehand`],[`fhc`,`image/x-freehand`],[`fig`,`application/x-xfig`],[`fits`,`image/fits`],[`flac`,`audio/x-flac`],[`fli`,`video/x-fli`],[`flo`,`application/vnd.micrografx.flo`],[`flv`,`video/x-flv`],[`flw`,`application/vnd.kde.kivio`],[`flx`,`text/vnd.fmi.flexstor`],[`fly`,`text/vnd.fly`],[`fm`,`application/vnd.framemaker`],[`fnc`,`application/vnd.frogans.fnc`],[`fo`,`application/vnd.software602.filler.form+xml`],[`for`,`text/x-fortran`],[`fpx`,`image/vnd.fpx`],[`frame`,`application/vnd.framemaker`],[`fsc`,`application/vnd.fsc.weblaunch`],[`fst`,`image/vnd.fst`],[`ftc`,`application/vnd.fluxtime.clip`],[`fti`,`application/vnd.anser-web-funds-transfer-initiation`],[`fvt`,`video/vnd.fvt`],[`fxp`,`application/vnd.adobe.fxp`],[`fxpl`,`application/vnd.adobe.fxp`],[`fzs`,`application/vnd.fuzzysheet`],[`g2w`,`application/vnd.geoplan`],[`g3`,`image/g3fax`],[`g3w`,`application/vnd.geospace`],[`gac`,`application/vnd.groove-account`],[`gam`,`application/x-tads`],[`gbr`,`application/rpki-ghostbusters`],[`gca`,`application/x-gca-compressed`],[`gdl`,`model/vnd.gdl`],[`gdoc`,`application/vnd.google-apps.document`],[`geo`,`application/vnd.dynageo`],[`geojson`,`application/geo+json`],[`gex`,`application/vnd.geometry-explorer`],[`ggb`,`application/vnd.geogebra.file`],[`ggt`,`application/vnd.geogebra.tool`],[`ghf`,`application/vnd.groove-help`],[`gif`,`image/gif`],[`gim`,`application/vnd.groove-identity-message`],[`glb`,`model/gltf-binary`],[`gltf`,`model/gltf+json`],[`gml`,`application/gml+xml`],[`gmx`,`application/vnd.gmx`],[`gnumeric`,`application/x-gnumeric`],[`gpg`,`application/gpg-keys`],[`gph`,`application/vnd.flographit`],[`gpx`,`application/gpx+xml`],[`gqf`,`application/vnd.grafeq`],[`gqs`,`application/vnd.grafeq`],[`gram`,`application/srgs`],[`gramps`,`application/x-gramps-xml`],[`gre`,`application/vnd.geometry-explorer`],[`grv`,`application/vnd.groove-injector`],[`grxml`,`application/srgs+xml`],[`gsf`,`application/x-font-ghostscript`],[`gsheet`,`application/vnd.google-apps.spreadsheet`],[`gslides`,`application/vnd.google-apps.presentation`],[`gtar`,`application/x-gtar`],[`gtm`,`application/vnd.groove-tool-message`],[`gtw`,`model/vnd.gtw`],[`gv`,`text/vnd.graphviz`],[`gxf`,`application/gxf`],[`gxt`,`application/vnd.geonext`],[`gz`,`application/gzip`],[`gzip`,`application/gzip`],[`h`,`text/x-c`],[`h261`,`video/h261`],[`h263`,`video/h263`],[`h264`,`video/h264`],[`hal`,`application/vnd.hal+xml`],[`hbci`,`application/vnd.hbci`],[`hbs`,`text/x-handlebars-template`],[`hdd`,`application/x-virtualbox-hdd`],[`hdf`,`application/x-hdf`],[`heic`,`image/heic`],[`heics`,`image/heic-sequence`],[`heif`,`image/heif`],[`heifs`,`image/heif-sequence`],[`hej2`,`image/hej2k`],[`held`,`application/atsc-held+xml`],[`hh`,`text/x-c`],[`hjson`,`application/hjson`],[`hlp`,`application/winhlp`],[`hpgl`,`application/vnd.hp-hpgl`],[`hpid`,`application/vnd.hp-hpid`],[`hps`,`application/vnd.hp-hps`],[`hqx`,`application/mac-binhex40`],[`hsj2`,`image/hsj2`],[`htc`,`text/x-component`],[`htke`,`application/vnd.kenameaapp`],[`htm`,`text/html`],[`html`,`text/html`],[`hvd`,`application/vnd.yamaha.hv-dic`],[`hvp`,`application/vnd.yamaha.hv-voice`],[`hvs`,`application/vnd.yamaha.hv-script`],[`i2g`,`application/vnd.intergeo`],[`icc`,`application/vnd.iccprofile`],[`ice`,`x-conference/x-cooltalk`],[`icm`,`application/vnd.iccprofile`],[`ico`,`image/x-icon`],[`ics`,`text/calendar`],[`ief`,`image/ief`],[`ifb`,`text/calendar`],[`ifm`,`application/vnd.shana.informed.formdata`],[`iges`,`model/iges`],[`igl`,`application/vnd.igloader`],[`igm`,`application/vnd.insors.igm`],[`igs`,`model/iges`],[`igx`,`application/vnd.micrografx.igx`],[`iif`,`application/vnd.shana.informed.interchange`],[`img`,`application/octet-stream`],[`imp`,`application/vnd.accpac.simply.imp`],[`ims`,`application/vnd.ms-ims`],[`in`,`text/plain`],[`ini`,`text/plain`],[`ink`,`application/inkml+xml`],[`inkml`,`application/inkml+xml`],[`install`,`application/x-install-instructions`],[`iota`,`application/vnd.astraea-software.iota`],[`ipfix`,`application/ipfix`],[`ipk`,`application/vnd.shana.informed.package`],[`irm`,`application/vnd.ibm.rights-management`],[`irp`,`application/vnd.irepository.package+xml`],[`iso`,`application/x-iso9660-image`],[`itp`,`application/vnd.shana.informed.formtemplate`],[`its`,`application/its+xml`],[`ivp`,`application/vnd.immervision-ivp`],[`ivu`,`application/vnd.immervision-ivu`],[`jad`,`text/vnd.sun.j2me.app-descriptor`],[`jade`,`text/jade`],[`jam`,`application/vnd.jam`],[`jar`,`application/java-archive`],[`jardiff`,`application/x-java-archive-diff`],[`java`,`text/x-java-source`],[`jhc`,`image/jphc`],[`jisp`,`application/vnd.jisp`],[`jls`,`image/jls`],[`jlt`,`application/vnd.hp-jlyt`],[`jng`,`image/x-jng`],[`jnlp`,`application/x-java-jnlp-file`],[`joda`,`application/vnd.joost.joda-archive`],[`jp2`,`image/jp2`],[`jpe`,`image/jpeg`],[`jpeg`,`image/jpeg`],[`jpf`,`image/jpx`],[`jpg`,`image/jpeg`],[`jpg2`,`image/jp2`],[`jpgm`,`video/jpm`],[`jpgv`,`video/jpeg`],[`jph`,`image/jph`],[`jpm`,`video/jpm`],[`jpx`,`image/jpx`],[`js`,`application/javascript`],[`json`,`application/json`],[`json5`,`application/json5`],[`jsonld`,`application/ld+json`],[`jsonl`,`application/jsonl`],[`jsonml`,`application/jsonml+json`],[`jsx`,`text/jsx`],[`jxr`,`image/jxr`],[`jxra`,`image/jxra`],[`jxrs`,`image/jxrs`],[`jxs`,`image/jxs`],[`jxsc`,`image/jxsc`],[`jxsi`,`image/jxsi`],[`jxss`,`image/jxss`],[`kar`,`audio/midi`],[`karbon`,`application/vnd.kde.karbon`],[`kdb`,`application/octet-stream`],[`kdbx`,`application/x-keepass2`],[`key`,`application/x-iwork-keynote-sffkey`],[`kfo`,`application/vnd.kde.kformula`],[`kia`,`application/vnd.kidspiration`],[`kml`,`application/vnd.google-earth.kml+xml`],[`kmz`,`application/vnd.google-earth.kmz`],[`kne`,`application/vnd.kinar`],[`knp`,`application/vnd.kinar`],[`kon`,`application/vnd.kde.kontour`],[`kpr`,`application/vnd.kde.kpresenter`],[`kpt`,`application/vnd.kde.kpresenter`],[`kpxx`,`application/vnd.ds-keypoint`],[`ksp`,`application/vnd.kde.kspread`],[`ktr`,`application/vnd.kahootz`],[`ktx`,`image/ktx`],[`ktx2`,`image/ktx2`],[`ktz`,`application/vnd.kahootz`],[`kwd`,`application/vnd.kde.kword`],[`kwt`,`application/vnd.kde.kword`],[`lasxml`,`application/vnd.las.las+xml`],[`latex`,`application/x-latex`],[`lbd`,`application/vnd.llamagraphics.life-balance.desktop`],[`lbe`,`application/vnd.llamagraphics.life-balance.exchange+xml`],[`les`,`application/vnd.hhe.lesson-player`],[`less`,`text/less`],[`lgr`,`application/lgr+xml`],[`lha`,`application/octet-stream`],[`link66`,`application/vnd.route66.link66+xml`],[`list`,`text/plain`],[`list3820`,`application/vnd.ibm.modcap`],[`listafp`,`application/vnd.ibm.modcap`],[`litcoffee`,`text/coffeescript`],[`lnk`,`application/x-ms-shortcut`],[`log`,`text/plain`],[`lostxml`,`application/lost+xml`],[`lrf`,`application/octet-stream`],[`lrm`,`application/vnd.ms-lrm`],[`ltf`,`application/vnd.frogans.ltf`],[`lua`,`text/x-lua`],[`luac`,`application/x-lua-bytecode`],[`lvp`,`audio/vnd.lucent.voice`],[`lwp`,`application/vnd.lotus-wordpro`],[`lzh`,`application/octet-stream`],[`m1v`,`video/mpeg`],[`m2a`,`audio/mpeg`],[`m2v`,`video/mpeg`],[`m3a`,`audio/mpeg`],[`m3u`,`text/plain`],[`m3u8`,`application/vnd.apple.mpegurl`],[`m4a`,`audio/x-m4a`],[`m4p`,`application/mp4`],[`m4s`,`video/iso.segment`],[`m4u`,`application/vnd.mpegurl`],[`m4v`,`video/x-m4v`],[`m13`,`application/x-msmediaview`],[`m14`,`application/x-msmediaview`],[`m21`,`application/mp21`],[`ma`,`application/mathematica`],[`mads`,`application/mads+xml`],[`maei`,`application/mmt-aei+xml`],[`mag`,`application/vnd.ecowin.chart`],[`maker`,`application/vnd.framemaker`],[`man`,`text/troff`],[`manifest`,`text/cache-manifest`],[`map`,`application/json`],[`mar`,`application/octet-stream`],[`markdown`,`text/markdown`],[`mathml`,`application/mathml+xml`],[`mb`,`application/mathematica`],[`mbk`,`application/vnd.mobius.mbk`],[`mbox`,`application/mbox`],[`mc1`,`application/vnd.medcalcdata`],[`mcd`,`application/vnd.mcd`],[`mcurl`,`text/vnd.curl.mcurl`],[`md`,`text/markdown`],[`mdb`,`application/x-msaccess`],[`mdi`,`image/vnd.ms-modi`],[`mdx`,`text/mdx`],[`me`,`text/troff`],[`mesh`,`model/mesh`],[`meta4`,`application/metalink4+xml`],[`metalink`,`application/metalink+xml`],[`mets`,`application/mets+xml`],[`mfm`,`application/vnd.mfmp`],[`mft`,`application/rpki-manifest`],[`mgp`,`application/vnd.osgeo.mapguide.package`],[`mgz`,`application/vnd.proteus.magazine`],[`mid`,`audio/midi`],[`midi`,`audio/midi`],[`mie`,`application/x-mie`],[`mif`,`application/vnd.mif`],[`mime`,`message/rfc822`],[`mj2`,`video/mj2`],[`mjp2`,`video/mj2`],[`mjs`,`application/javascript`],[`mk3d`,`video/x-matroska`],[`mka`,`audio/x-matroska`],[`mkd`,`text/x-markdown`],[`mks`,`video/x-matroska`],[`mkv`,`video/x-matroska`],[`mlp`,`application/vnd.dolby.mlp`],[`mmd`,`application/vnd.chipnuts.karaoke-mmd`],[`mmf`,`application/vnd.smaf`],[`mml`,`text/mathml`],[`mmr`,`image/vnd.fujixerox.edmics-mmr`],[`mng`,`video/x-mng`],[`mny`,`application/x-msmoney`],[`mobi`,`application/x-mobipocket-ebook`],[`mods`,`application/mods+xml`],[`mov`,`video/quicktime`],[`movie`,`video/x-sgi-movie`],[`mp2`,`audio/mpeg`],[`mp2a`,`audio/mpeg`],[`mp3`,`audio/mpeg`],[`mp4`,`video/mp4`],[`mp4a`,`audio/mp4`],[`mp4s`,`application/mp4`],[`mp4v`,`video/mp4`],[`mp21`,`application/mp21`],[`mpc`,`application/vnd.mophun.certificate`],[`mpd`,`application/dash+xml`],[`mpe`,`video/mpeg`],[`mpeg`,`video/mpeg`],[`mpg`,`video/mpeg`],[`mpg4`,`video/mp4`],[`mpga`,`audio/mpeg`],[`mpkg`,`application/vnd.apple.installer+xml`],[`mpm`,`application/vnd.blueice.multipass`],[`mpn`,`application/vnd.mophun.application`],[`mpp`,`application/vnd.ms-project`],[`mpt`,`application/vnd.ms-project`],[`mpy`,`application/vnd.ibm.minipay`],[`mqy`,`application/vnd.mobius.mqy`],[`mrc`,`application/marc`],[`mrcx`,`application/marcxml+xml`],[`ms`,`text/troff`],[`mscml`,`application/mediaservercontrol+xml`],[`mseed`,`application/vnd.fdsn.mseed`],[`mseq`,`application/vnd.mseq`],[`msf`,`application/vnd.epson.msf`],[`msg`,`application/vnd.ms-outlook`],[`msh`,`model/mesh`],[`msi`,`application/x-msdownload`],[`msl`,`application/vnd.mobius.msl`],[`msm`,`application/octet-stream`],[`msp`,`application/octet-stream`],[`msty`,`application/vnd.muvee.style`],[`mtl`,`model/mtl`],[`mts`,`model/vnd.mts`],[`mus`,`application/vnd.musician`],[`musd`,`application/mmt-usd+xml`],[`musicxml`,`application/vnd.recordare.musicxml+xml`],[`mvb`,`application/x-msmediaview`],[`mvt`,`application/vnd.mapbox-vector-tile`],[`mwf`,`application/vnd.mfer`],[`mxf`,`application/mxf`],[`mxl`,`application/vnd.recordare.musicxml`],[`mxmf`,`audio/mobile-xmf`],[`mxml`,`application/xv+xml`],[`mxs`,`application/vnd.triscape.mxs`],[`mxu`,`video/vnd.mpegurl`],[`n-gage`,`application/vnd.nokia.n-gage.symbian.install`],[`n3`,`text/n3`],[`nb`,`application/mathematica`],[`nbp`,`application/vnd.wolfram.player`],[`nc`,`application/x-netcdf`],[`ncx`,`application/x-dtbncx+xml`],[`nfo`,`text/x-nfo`],[`ngdat`,`application/vnd.nokia.n-gage.data`],[`nitf`,`application/vnd.nitf`],[`nlu`,`application/vnd.neurolanguage.nlu`],[`nml`,`application/vnd.enliven`],[`nnd`,`application/vnd.noblenet-directory`],[`nns`,`application/vnd.noblenet-sealer`],[`nnw`,`application/vnd.noblenet-web`],[`npx`,`image/vnd.net-fpx`],[`nq`,`application/n-quads`],[`nsc`,`application/x-conference`],[`nsf`,`application/vnd.lotus-notes`],[`nt`,`application/n-triples`],[`ntf`,`application/vnd.nitf`],[`numbers`,`application/x-iwork-numbers-sffnumbers`],[`nzb`,`application/x-nzb`],[`oa2`,`application/vnd.fujitsu.oasys2`],[`oa3`,`application/vnd.fujitsu.oasys3`],[`oas`,`application/vnd.fujitsu.oasys`],[`obd`,`application/x-msbinder`],[`obgx`,`application/vnd.openblox.game+xml`],[`obj`,`model/obj`],[`oda`,`application/oda`],[`odb`,`application/vnd.oasis.opendocument.database`],[`odc`,`application/vnd.oasis.opendocument.chart`],[`odf`,`application/vnd.oasis.opendocument.formula`],[`odft`,`application/vnd.oasis.opendocument.formula-template`],[`odg`,`application/vnd.oasis.opendocument.graphics`],[`odi`,`application/vnd.oasis.opendocument.image`],[`odm`,`application/vnd.oasis.opendocument.text-master`],[`odp`,`application/vnd.oasis.opendocument.presentation`],[`ods`,`application/vnd.oasis.opendocument.spreadsheet`],[`odt`,`application/vnd.oasis.opendocument.text`],[`oga`,`audio/ogg`],[`ogex`,`model/vnd.opengex`],[`ogg`,`audio/ogg`],[`ogv`,`video/ogg`],[`ogx`,`application/ogg`],[`omdoc`,`application/omdoc+xml`],[`onepkg`,`application/onenote`],[`onetmp`,`application/onenote`],[`onetoc`,`application/onenote`],[`onetoc2`,`application/onenote`],[`opf`,`application/oebps-package+xml`],[`opml`,`text/x-opml`],[`oprc`,`application/vnd.palm`],[`opus`,`audio/ogg`],[`org`,`text/x-org`],[`osf`,`application/vnd.yamaha.openscoreformat`],[`osfpvg`,`application/vnd.yamaha.openscoreformat.osfpvg+xml`],[`osm`,`application/vnd.openstreetmap.data+xml`],[`otc`,`application/vnd.oasis.opendocument.chart-template`],[`otf`,`font/otf`],[`otg`,`application/vnd.oasis.opendocument.graphics-template`],[`oth`,`application/vnd.oasis.opendocument.text-web`],[`oti`,`application/vnd.oasis.opendocument.image-template`],[`otp`,`application/vnd.oasis.opendocument.presentation-template`],[`ots`,`application/vnd.oasis.opendocument.spreadsheet-template`],[`ott`,`application/vnd.oasis.opendocument.text-template`],[`ova`,`application/x-virtualbox-ova`],[`ovf`,`application/x-virtualbox-ovf`],[`owl`,`application/rdf+xml`],[`oxps`,`application/oxps`],[`oxt`,`application/vnd.openofficeorg.extension`],[`p`,`text/x-pascal`],[`p7a`,`application/x-pkcs7-signature`],[`p7b`,`application/x-pkcs7-certificates`],[`p7c`,`application/pkcs7-mime`],[`p7m`,`application/pkcs7-mime`],[`p7r`,`application/x-pkcs7-certreqresp`],[`p7s`,`application/pkcs7-signature`],[`p8`,`application/pkcs8`],[`p10`,`application/x-pkcs10`],[`p12`,`application/x-pkcs12`],[`pac`,`application/x-ns-proxy-autoconfig`],[`pages`,`application/x-iwork-pages-sffpages`],[`pas`,`text/x-pascal`],[`paw`,`application/vnd.pawaafile`],[`pbd`,`application/vnd.powerbuilder6`],[`pbm`,`image/x-portable-bitmap`],[`pcap`,`application/vnd.tcpdump.pcap`],[`pcf`,`application/x-font-pcf`],[`pcl`,`application/vnd.hp-pcl`],[`pclxl`,`application/vnd.hp-pclxl`],[`pct`,`image/x-pict`],[`pcurl`,`application/vnd.curl.pcurl`],[`pcx`,`image/x-pcx`],[`pdb`,`application/x-pilot`],[`pde`,`text/x-processing`],[`pdf`,`application/pdf`],[`pem`,`application/x-x509-user-cert`],[`pfa`,`application/x-font-type1`],[`pfb`,`application/x-font-type1`],[`pfm`,`application/x-font-type1`],[`pfr`,`application/font-tdpfr`],[`pfx`,`application/x-pkcs12`],[`pgm`,`image/x-portable-graymap`],[`pgn`,`application/x-chess-pgn`],[`pgp`,`application/pgp`],[`php`,`application/x-httpd-php`],[`php3`,`application/x-httpd-php`],[`php4`,`application/x-httpd-php`],[`phps`,`application/x-httpd-php-source`],[`phtml`,`application/x-httpd-php`],[`pic`,`image/x-pict`],[`pkg`,`application/octet-stream`],[`pki`,`application/pkixcmp`],[`pkipath`,`application/pkix-pkipath`],[`pkpass`,`application/vnd.apple.pkpass`],[`pl`,`application/x-perl`],[`plb`,`application/vnd.3gpp.pic-bw-large`],[`plc`,`application/vnd.mobius.plc`],[`plf`,`application/vnd.pocketlearn`],[`pls`,`application/pls+xml`],[`pm`,`application/x-perl`],[`pml`,`application/vnd.ctc-posml`],[`png`,`image/png`],[`pnm`,`image/x-portable-anymap`],[`portpkg`,`application/vnd.macports.portpkg`],[`pot`,`application/vnd.ms-powerpoint`],[`potm`,`application/vnd.ms-powerpoint.presentation.macroEnabled.12`],[`potx`,`application/vnd.openxmlformats-officedocument.presentationml.template`],[`ppa`,`application/vnd.ms-powerpoint`],[`ppam`,`application/vnd.ms-powerpoint.addin.macroEnabled.12`],[`ppd`,`application/vnd.cups-ppd`],[`ppm`,`image/x-portable-pixmap`],[`pps`,`application/vnd.ms-powerpoint`],[`ppsm`,`application/vnd.ms-powerpoint.slideshow.macroEnabled.12`],[`ppsx`,`application/vnd.openxmlformats-officedocument.presentationml.slideshow`],[`ppt`,`application/powerpoint`],[`pptm`,`application/vnd.ms-powerpoint.presentation.macroEnabled.12`],[`pptx`,`application/vnd.openxmlformats-officedocument.presentationml.presentation`],[`pqa`,`application/vnd.palm`],[`prc`,`application/x-pilot`],[`pre`,`application/vnd.lotus-freelance`],[`prf`,`application/pics-rules`],[`provx`,`application/provenance+xml`],[`ps`,`application/postscript`],[`psb`,`application/vnd.3gpp.pic-bw-small`],[`psd`,`application/x-photoshop`],[`psf`,`application/x-font-linux-psf`],[`pskcxml`,`application/pskc+xml`],[`pti`,`image/prs.pti`],[`ptid`,`application/vnd.pvi.ptid1`],[`pub`,`application/x-mspublisher`],[`pvb`,`application/vnd.3gpp.pic-bw-var`],[`pwn`,`application/vnd.3m.post-it-notes`],[`pya`,`audio/vnd.ms-playready.media.pya`],[`pyv`,`video/vnd.ms-playready.media.pyv`],[`qam`,`application/vnd.epson.quickanime`],[`qbo`,`application/vnd.intu.qbo`],[`qfx`,`application/vnd.intu.qfx`],[`qps`,`application/vnd.publishare-delta-tree`],[`qt`,`video/quicktime`],[`qwd`,`application/vnd.quark.quarkxpress`],[`qwt`,`application/vnd.quark.quarkxpress`],[`qxb`,`application/vnd.quark.quarkxpress`],[`qxd`,`application/vnd.quark.quarkxpress`],[`qxl`,`application/vnd.quark.quarkxpress`],[`qxt`,`application/vnd.quark.quarkxpress`],[`ra`,`audio/x-realaudio`],[`ram`,`audio/x-pn-realaudio`],[`raml`,`application/raml+yaml`],[`rapd`,`application/route-apd+xml`],[`rar`,`application/x-rar`],[`ras`,`image/x-cmu-raster`],[`rcprofile`,`application/vnd.ipunplugged.rcprofile`],[`rdf`,`application/rdf+xml`],[`rdz`,`application/vnd.data-vision.rdz`],[`relo`,`application/p2p-overlay+xml`],[`rep`,`application/vnd.businessobjects`],[`res`,`application/x-dtbresource+xml`],[`rgb`,`image/x-rgb`],[`rif`,`application/reginfo+xml`],[`rip`,`audio/vnd.rip`],[`ris`,`application/x-research-info-systems`],[`rl`,`application/resource-lists+xml`],[`rlc`,`image/vnd.fujixerox.edmics-rlc`],[`rld`,`application/resource-lists-diff+xml`],[`rm`,`audio/x-pn-realaudio`],[`rmi`,`audio/midi`],[`rmp`,`audio/x-pn-realaudio-plugin`],[`rms`,`application/vnd.jcp.javame.midlet-rms`],[`rmvb`,`application/vnd.rn-realmedia-vbr`],[`rnc`,`application/relax-ng-compact-syntax`],[`rng`,`application/xml`],[`roa`,`application/rpki-roa`],[`roff`,`text/troff`],[`rp9`,`application/vnd.cloanto.rp9`],[`rpm`,`audio/x-pn-realaudio-plugin`],[`rpss`,`application/vnd.nokia.radio-presets`],[`rpst`,`application/vnd.nokia.radio-preset`],[`rq`,`application/sparql-query`],[`rs`,`application/rls-services+xml`],[`rsa`,`application/x-pkcs7`],[`rsat`,`application/atsc-rsat+xml`],[`rsd`,`application/rsd+xml`],[`rsheet`,`application/urc-ressheet+xml`],[`rss`,`application/rss+xml`],[`rtf`,`text/rtf`],[`rtx`,`text/richtext`],[`run`,`application/x-makeself`],[`rusd`,`application/route-usd+xml`],[`rv`,`video/vnd.rn-realvideo`],[`s`,`text/x-asm`],[`s3m`,`audio/s3m`],[`saf`,`application/vnd.yamaha.smaf-audio`],[`sass`,`text/x-sass`],[`sbml`,`application/sbml+xml`],[`sc`,`application/vnd.ibm.secure-container`],[`scd`,`application/x-msschedule`],[`scm`,`application/vnd.lotus-screencam`],[`scq`,`application/scvp-cv-request`],[`scs`,`application/scvp-cv-response`],[`scss`,`text/x-scss`],[`scurl`,`text/vnd.curl.scurl`],[`sda`,`application/vnd.stardivision.draw`],[`sdc`,`application/vnd.stardivision.calc`],[`sdd`,`application/vnd.stardivision.impress`],[`sdkd`,`application/vnd.solent.sdkm+xml`],[`sdkm`,`application/vnd.solent.sdkm+xml`],[`sdp`,`application/sdp`],[`sdw`,`application/vnd.stardivision.writer`],[`sea`,`application/octet-stream`],[`see`,`application/vnd.seemail`],[`seed`,`application/vnd.fdsn.seed`],[`sema`,`application/vnd.sema`],[`semd`,`application/vnd.semd`],[`semf`,`application/vnd.semf`],[`senmlx`,`application/senml+xml`],[`sensmlx`,`application/sensml+xml`],[`ser`,`application/java-serialized-object`],[`setpay`,`application/set-payment-initiation`],[`setreg`,`application/set-registration-initiation`],[`sfd-hdstx`,`application/vnd.hydrostatix.sof-data`],[`sfs`,`application/vnd.spotfire.sfs`],[`sfv`,`text/x-sfv`],[`sgi`,`image/sgi`],[`sgl`,`application/vnd.stardivision.writer-global`],[`sgm`,`text/sgml`],[`sgml`,`text/sgml`],[`sh`,`application/x-sh`],[`shar`,`application/x-shar`],[`shex`,`text/shex`],[`shf`,`application/shf+xml`],[`shtml`,`text/html`],[`sid`,`image/x-mrsid-image`],[`sieve`,`application/sieve`],[`sig`,`application/pgp-signature`],[`sil`,`audio/silk`],[`silo`,`model/mesh`],[`sis`,`application/vnd.symbian.install`],[`sisx`,`application/vnd.symbian.install`],[`sit`,`application/x-stuffit`],[`sitx`,`application/x-stuffitx`],[`siv`,`application/sieve`],[`skd`,`application/vnd.koan`],[`skm`,`application/vnd.koan`],[`skp`,`application/vnd.koan`],[`skt`,`application/vnd.koan`],[`sldm`,`application/vnd.ms-powerpoint.slide.macroenabled.12`],[`sldx`,`application/vnd.openxmlformats-officedocument.presentationml.slide`],[`slim`,`text/slim`],[`slm`,`text/slim`],[`sls`,`application/route-s-tsid+xml`],[`slt`,`application/vnd.epson.salt`],[`sm`,`application/vnd.stepmania.stepchart`],[`smf`,`application/vnd.stardivision.math`],[`smi`,`application/smil`],[`smil`,`application/smil`],[`smv`,`video/x-smv`],[`smzip`,`application/vnd.stepmania.package`],[`snd`,`audio/basic`],[`snf`,`application/x-font-snf`],[`so`,`application/octet-stream`],[`spc`,`application/x-pkcs7-certificates`],[`spdx`,`text/spdx`],[`spf`,`application/vnd.yamaha.smaf-phrase`],[`spl`,`application/x-futuresplash`],[`spot`,`text/vnd.in3d.spot`],[`spp`,`application/scvp-vp-response`],[`spq`,`application/scvp-vp-request`],[`spx`,`audio/ogg`],[`sql`,`application/x-sql`],[`src`,`application/x-wais-source`],[`srt`,`application/x-subrip`],[`sru`,`application/sru+xml`],[`srx`,`application/sparql-results+xml`],[`ssdl`,`application/ssdl+xml`],[`sse`,`application/vnd.kodak-descriptor`],[`ssf`,`application/vnd.epson.ssf`],[`ssml`,`application/ssml+xml`],[`sst`,`application/octet-stream`],[`st`,`application/vnd.sailingtracker.track`],[`stc`,`application/vnd.sun.xml.calc.template`],[`std`,`application/vnd.sun.xml.draw.template`],[`stf`,`application/vnd.wt.stf`],[`sti`,`application/vnd.sun.xml.impress.template`],[`stk`,`application/hyperstudio`],[`stl`,`model/stl`],[`stpx`,`model/step+xml`],[`stpxz`,`model/step-xml+zip`],[`stpz`,`model/step+zip`],[`str`,`application/vnd.pg.format`],[`stw`,`application/vnd.sun.xml.writer.template`],[`styl`,`text/stylus`],[`stylus`,`text/stylus`],[`sub`,`text/vnd.dvb.subtitle`],[`sus`,`application/vnd.sus-calendar`],[`susp`,`application/vnd.sus-calendar`],[`sv4cpio`,`application/x-sv4cpio`],[`sv4crc`,`application/x-sv4crc`],[`svc`,`application/vnd.dvb.service`],[`svd`,`application/vnd.svd`],[`svg`,`image/svg+xml`],[`svgz`,`image/svg+xml`],[`swa`,`application/x-director`],[`swf`,`application/x-shockwave-flash`],[`swi`,`application/vnd.aristanetworks.swi`],[`swidtag`,`application/swid+xml`],[`sxc`,`application/vnd.sun.xml.calc`],[`sxd`,`application/vnd.sun.xml.draw`],[`sxg`,`application/vnd.sun.xml.writer.global`],[`sxi`,`application/vnd.sun.xml.impress`],[`sxm`,`application/vnd.sun.xml.math`],[`sxw`,`application/vnd.sun.xml.writer`],[`t`,`text/troff`],[`t3`,`application/x-t3vm-image`],[`t38`,`image/t38`],[`taglet`,`application/vnd.mynfc`],[`tao`,`application/vnd.tao.intent-module-archive`],[`tap`,`image/vnd.tencent.tap`],[`tar`,`application/x-tar`],[`tcap`,`application/vnd.3gpp2.tcap`],[`tcl`,`application/x-tcl`],[`td`,`application/urc-targetdesc+xml`],[`teacher`,`application/vnd.smart.teacher`],[`tei`,`application/tei+xml`],[`teicorpus`,`application/tei+xml`],[`tex`,`application/x-tex`],[`texi`,`application/x-texinfo`],[`texinfo`,`application/x-texinfo`],[`text`,`text/plain`],[`tfi`,`application/thraud+xml`],[`tfm`,`application/x-tex-tfm`],[`tfx`,`image/tiff-fx`],[`tga`,`image/x-tga`],[`tgz`,`application/x-tar`],[`thmx`,`application/vnd.ms-officetheme`],[`tif`,`image/tiff`],[`tiff`,`image/tiff`],[`tk`,`application/x-tcl`],[`tmo`,`application/vnd.tmobile-livetv`],[`toml`,`application/toml`],[`torrent`,`application/x-bittorrent`],[`tpl`,`application/vnd.groove-tool-template`],[`tpt`,`application/vnd.trid.tpt`],[`tr`,`text/troff`],[`tra`,`application/vnd.trueapp`],[`trig`,`application/trig`],[`trm`,`application/x-msterminal`],[`ts`,`video/mp2t`],[`tsd`,`application/timestamped-data`],[`tsv`,`text/tab-separated-values`],[`ttc`,`font/collection`],[`ttf`,`font/ttf`],[`ttl`,`text/turtle`],[`ttml`,`application/ttml+xml`],[`twd`,`application/vnd.simtech-mindmapper`],[`twds`,`application/vnd.simtech-mindmapper`],[`txd`,`application/vnd.genomatix.tuxedo`],[`txf`,`application/vnd.mobius.txf`],[`txt`,`text/plain`],[`u8dsn`,`message/global-delivery-status`],[`u8hdr`,`message/global-headers`],[`u8mdn`,`message/global-disposition-notification`],[`u8msg`,`message/global`],[`u32`,`application/x-authorware-bin`],[`ubj`,`application/ubjson`],[`udeb`,`application/x-debian-package`],[`ufd`,`application/vnd.ufdl`],[`ufdl`,`application/vnd.ufdl`],[`ulx`,`application/x-glulx`],[`umj`,`application/vnd.umajin`],[`unityweb`,`application/vnd.unity`],[`uoml`,`application/vnd.uoml+xml`],[`uri`,`text/uri-list`],[`uris`,`text/uri-list`],[`urls`,`text/uri-list`],[`usdz`,`model/vnd.usdz+zip`],[`ustar`,`application/x-ustar`],[`utz`,`application/vnd.uiq.theme`],[`uu`,`text/x-uuencode`],[`uva`,`audio/vnd.dece.audio`],[`uvd`,`application/vnd.dece.data`],[`uvf`,`application/vnd.dece.data`],[`uvg`,`image/vnd.dece.graphic`],[`uvh`,`video/vnd.dece.hd`],[`uvi`,`image/vnd.dece.graphic`],[`uvm`,`video/vnd.dece.mobile`],[`uvp`,`video/vnd.dece.pd`],[`uvs`,`video/vnd.dece.sd`],[`uvt`,`application/vnd.dece.ttml+xml`],[`uvu`,`video/vnd.uvvu.mp4`],[`uvv`,`video/vnd.dece.video`],[`uvva`,`audio/vnd.dece.audio`],[`uvvd`,`application/vnd.dece.data`],[`uvvf`,`application/vnd.dece.data`],[`uvvg`,`image/vnd.dece.graphic`],[`uvvh`,`video/vnd.dece.hd`],[`uvvi`,`image/vnd.dece.graphic`],[`uvvm`,`video/vnd.dece.mobile`],[`uvvp`,`video/vnd.dece.pd`],[`uvvs`,`video/vnd.dece.sd`],[`uvvt`,`application/vnd.dece.ttml+xml`],[`uvvu`,`video/vnd.uvvu.mp4`],[`uvvv`,`video/vnd.dece.video`],[`uvvx`,`application/vnd.dece.unspecified`],[`uvvz`,`application/vnd.dece.zip`],[`uvx`,`application/vnd.dece.unspecified`],[`uvz`,`application/vnd.dece.zip`],[`vbox`,`application/x-virtualbox-vbox`],[`vbox-extpack`,`application/x-virtualbox-vbox-extpack`],[`vcard`,`text/vcard`],[`vcd`,`application/x-cdlink`],[`vcf`,`text/x-vcard`],[`vcg`,`application/vnd.groove-vcard`],[`vcs`,`text/x-vcalendar`],[`vcx`,`application/vnd.vcx`],[`vdi`,`application/x-virtualbox-vdi`],[`vds`,`model/vnd.sap.vds`],[`vhd`,`application/x-virtualbox-vhd`],[`vis`,`application/vnd.visionary`],[`viv`,`video/vnd.vivo`],[`vlc`,`application/videolan`],[`vmdk`,`application/x-virtualbox-vmdk`],[`vob`,`video/x-ms-vob`],[`vor`,`application/vnd.stardivision.writer`],[`vox`,`application/x-authorware-bin`],[`vrml`,`model/vrml`],[`vsd`,`application/vnd.visio`],[`vsf`,`application/vnd.vsf`],[`vss`,`application/vnd.visio`],[`vst`,`application/vnd.visio`],[`vsw`,`application/vnd.visio`],[`vtf`,`image/vnd.valve.source.texture`],[`vtt`,`text/vtt`],[`vtu`,`model/vnd.vtu`],[`vxml`,`application/voicexml+xml`],[`w3d`,`application/x-director`],[`wad`,`application/x-doom`],[`wadl`,`application/vnd.sun.wadl+xml`],[`war`,`application/java-archive`],[`wasm`,`application/wasm`],[`wav`,`audio/x-wav`],[`wax`,`audio/x-ms-wax`],[`wbmp`,`image/vnd.wap.wbmp`],[`wbs`,`application/vnd.criticaltools.wbs+xml`],[`wbxml`,`application/wbxml`],[`wcm`,`application/vnd.ms-works`],[`wdb`,`application/vnd.ms-works`],[`wdp`,`image/vnd.ms-photo`],[`weba`,`audio/webm`],[`webapp`,`application/x-web-app-manifest+json`],[`webm`,`video/webm`],[`webmanifest`,`application/manifest+json`],[`webp`,`image/webp`],[`wg`,`application/vnd.pmi.widget`],[`wgt`,`application/widget`],[`wks`,`application/vnd.ms-works`],[`wm`,`video/x-ms-wm`],[`wma`,`audio/x-ms-wma`],[`wmd`,`application/x-ms-wmd`],[`wmf`,`image/wmf`],[`wml`,`text/vnd.wap.wml`],[`wmlc`,`application/wmlc`],[`wmls`,`text/vnd.wap.wmlscript`],[`wmlsc`,`application/vnd.wap.wmlscriptc`],[`wmv`,`video/x-ms-wmv`],[`wmx`,`video/x-ms-wmx`],[`wmz`,`application/x-msmetafile`],[`woff`,`font/woff`],[`woff2`,`font/woff2`],[`word`,`application/msword`],[`wpd`,`application/vnd.wordperfect`],[`wpl`,`application/vnd.ms-wpl`],[`wps`,`application/vnd.ms-works`],[`wqd`,`application/vnd.wqd`],[`wri`,`application/x-mswrite`],[`wrl`,`model/vrml`],[`wsc`,`message/vnd.wfa.wsc`],[`wsdl`,`application/wsdl+xml`],[`wspolicy`,`application/wspolicy+xml`],[`wtb`,`application/vnd.webturbo`],[`wvx`,`video/x-ms-wvx`],[`x3d`,`model/x3d+xml`],[`x3db`,`model/x3d+fastinfoset`],[`x3dbz`,`model/x3d+binary`],[`x3dv`,`model/x3d-vrml`],[`x3dvz`,`model/x3d+vrml`],[`x3dz`,`model/x3d+xml`],[`x32`,`application/x-authorware-bin`],[`x_b`,`model/vnd.parasolid.transmit.binary`],[`x_t`,`model/vnd.parasolid.transmit.text`],[`xaml`,`application/xaml+xml`],[`xap`,`application/x-silverlight-app`],[`xar`,`application/vnd.xara`],[`xav`,`application/xcap-att+xml`],[`xbap`,`application/x-ms-xbap`],[`xbd`,`application/vnd.fujixerox.docuworks.binder`],[`xbm`,`image/x-xbitmap`],[`xca`,`application/xcap-caps+xml`],[`xcs`,`application/calendar+xml`],[`xdf`,`application/xcap-diff+xml`],[`xdm`,`application/vnd.syncml.dm+xml`],[`xdp`,`application/vnd.adobe.xdp+xml`],[`xdssc`,`application/dssc+xml`],[`xdw`,`application/vnd.fujixerox.docuworks`],[`xel`,`application/xcap-el+xml`],[`xenc`,`application/xenc+xml`],[`xer`,`application/patch-ops-error+xml`],[`xfdf`,`application/vnd.adobe.xfdf`],[`xfdl`,`application/vnd.xfdl`],[`xht`,`application/xhtml+xml`],[`xhtml`,`application/xhtml+xml`],[`xhvml`,`application/xv+xml`],[`xif`,`image/vnd.xiff`],[`xl`,`application/excel`],[`xla`,`application/vnd.ms-excel`],[`xlam`,`application/vnd.ms-excel.addin.macroEnabled.12`],[`xlc`,`application/vnd.ms-excel`],[`xlf`,`application/xliff+xml`],[`xlm`,`application/vnd.ms-excel`],[`xls`,`application/vnd.ms-excel`],[`xlsb`,`application/vnd.ms-excel.sheet.binary.macroEnabled.12`],[`xlsm`,`application/vnd.ms-excel.sheet.macroEnabled.12`],[`xlsx`,`application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`],[`xlt`,`application/vnd.ms-excel`],[`xltm`,`application/vnd.ms-excel.template.macroEnabled.12`],[`xltx`,`application/vnd.openxmlformats-officedocument.spreadsheetml.template`],[`xlw`,`application/vnd.ms-excel`],[`xm`,`audio/xm`],[`xml`,`application/xml`],[`xns`,`application/xcap-ns+xml`],[`xo`,`application/vnd.olpc-sugar`],[`xop`,`application/xop+xml`],[`xpi`,`application/x-xpinstall`],[`xpl`,`application/xproc+xml`],[`xpm`,`image/x-xpixmap`],[`xpr`,`application/vnd.is-xpr`],[`xps`,`application/vnd.ms-xpsdocument`],[`xpw`,`application/vnd.intercon.formnet`],[`xpx`,`application/vnd.intercon.formnet`],[`xsd`,`application/xml`],[`xsl`,`application/xml`],[`xslt`,`application/xslt+xml`],[`xsm`,`application/vnd.syncml+xml`],[`xspf`,`application/xspf+xml`],[`xul`,`application/vnd.mozilla.xul+xml`],[`xvm`,`application/xv+xml`],[`xvml`,`application/xv+xml`],[`xwd`,`image/x-xwindowdump`],[`xyz`,`chemical/x-xyz`],[`xz`,`application/x-xz`],[`yaml`,`text/yaml`],[`yang`,`application/yang`],[`yin`,`application/yin+xml`],[`yml`,`text/yaml`],[`ymp`,`text/x-suse-ymp`],[`z`,`application/x-compress`],[`z1`,`application/x-zmachine`],[`z2`,`application/x-zmachine`],[`z3`,`application/x-zmachine`],[`z4`,`application/x-zmachine`],[`z5`,`application/x-zmachine`],[`z6`,`application/x-zmachine`],[`z7`,`application/x-zmachine`],[`z8`,`application/x-zmachine`],[`zaz`,`application/vnd.zzazz.deck+xml`],[`zip`,`application/zip`],[`zir`,`application/vnd.zul`],[`zirz`,`application/vnd.zul`],[`zmm`,`application/vnd.handheld-entertainment+xml`],[`zsh`,`text/x-scriptzsh`]]);function j(e,t,n){let r=ie(e),{webkitRelativePath:i}=e,a=typeof t==`string`?t:typeof i==`string`&&i.length>0?i:`./${e.name}`;return typeof r.path!=`string`&&ae(r,`path`,a),n!==void 0&&Object.defineProperty(r,`handle`,{value:n,writable:!1,configurable:!1,enumerable:!0}),ae(r,`relativePath`,a),r}function ie(e){let{name:t}=e,n=t&&t.lastIndexOf(`.`)!==-1;if(n&&!e.type){let n=t.split(`.`).pop().toLowerCase(),r=re.get(n);r&&Object.defineProperty(e,`type`,{value:r,writable:!1,configurable:!1,enumerable:!0})}return e}function ae(e,t,n){Object.defineProperty(e,t,{value:n,writable:!1,configurable:!1,enumerable:!0})}const oe=[`.DS_Store`,`Thumbs.db`];function se(e){return A(this,void 0,void 0,function*(){return N(e)&&M(e.dataTransfer)?de(e.dataTransfer,e.type):ce(e)?le(e):Array.isArray(e)&&e.every(e=>`getFile`in e&&typeof e.getFile==`function`)?ue(e):[]})}function M(e){return N(e)}function ce(e){return N(e)&&N(e.target)}function N(e){return typeof e==`object`&&!!e}function le(e){return F(e.target.files).map(e=>j(e))}function ue(e){return A(this,void 0,void 0,function*(){let t=yield Promise.all(e.map(e=>e.getFile()));return t.map(e=>j(e))})}function de(e,t){return A(this,void 0,void 0,function*(){if(e.items){let n=F(e.items).filter(e=>e.kind===`file`);if(t!==`drop`)return n;let r=yield Promise.all(n.map(I));return P(fe(r))}return P(F(e.files).map(e=>j(e)))})}function P(e){return e.filter(e=>oe.indexOf(e.name)===-1)}function F(e){if(e===null)return[];let t=[];for(let n=0;n<e.length;n++){let r=e[n];t.push(r)}return t}function I(e){if(typeof e.webkitGetAsEntry!=`function`)return pe(e);let t=e.webkitGetAsEntry();return t&&t.isDirectory?he(t):pe(e,t)}function fe(e){return e.reduce((e,t)=>[...e,...Array.isArray(t)?fe(t):[t]],[])}function pe(e,t){return A(this,void 0,void 0,function*(){var n;if(globalThis.isSecureContext&&typeof e.getAsFileSystemHandle==`function`){let t=yield e.getAsFileSystemHandle();if(t===null)throw Error(`${e} is not a File`);if(t!==void 0){let e=yield t.getFile();return e.handle=t,j(e)}}let r=e.getAsFile();if(!r)throw Error(`${e} is not a File`);let i=j(r,(n=t?.fullPath)??void 0);return i})}function me(e){return A(this,void 0,void 0,function*(){return e.isDirectory?he(e):L(e)})}function he(e){let t=e.createReader();return new Promise((e,n)=>{let r=[];function i(){t.readEntries(t=>A(this,void 0,void 0,function*(){if(t.length){let e=Promise.all(t.map(me));r.push(e),i()}else try{let t=yield Promise.all(r);e(t)}catch(e){n(e)}}),e=>{n(e)})}i()})}function L(e){return A(this,void 0,void 0,function*(){return new Promise((t,n)=>{e.file(n=>{let r=j(n,e.fullPath);t(r)},e=>{n(e)})})})}var ge=e(exports=>{exports.__esModule=!0,exports.default=function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(`,`);if(n.length===0)return!0;var r=e.name||``,i=(e.type||``).toLowerCase(),a=i.replace(/\/.*$/,``);return n.some(function(e){var t=e.trim().toLowerCase();return t.charAt(0)===`.`?r.toLowerCase().endsWith(t):t.endsWith(`/*`)?a===t.replace(/\/.*$/,``):i===t})}return!0}}),R=t(ge());function z(e){return ye(e)||ve(e)||we(e)||_e()}function _e(){throw TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ve(e){if(typeof Symbol<`u`&&e[Symbol.iterator]!=null||e[`@@iterator`]!=null)return Array.from(e)}function ye(e){if(Array.isArray(e))return Te(e)}function be(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function xe(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]==null?{}:arguments[t];t%2?be(Object(n),!0).forEach(function(t){Se(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):be(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Se(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function B(e,t){return De(e)||Ee(e,t)||we(e,t)||Ce()}function Ce(){throw TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function we(e,t){if(e){if(typeof e==`string`)return Te(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n===`Object`&&e.constructor&&(n=e.constructor.name),n===`Map`||n===`Set`)return Array.from(e);if(n===`Arguments`||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Te(e,t)}}function Te(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function Ee(e,t){var n=e==null?null:typeof Symbol<`u`&&e[Symbol.iterator]||e[`@@iterator`];if(n!=null){var r=[],i=!0,a=!1,o,s;try{for(n=n.call(e);!(i=(o=n.next()).done)&&(r.push(o.value),!(t&&r.length===t));i=!0);}catch(e){a=!0,s=e}finally{try{!i&&n.return!=null&&n.return()}finally{if(a)throw s}}return r}}function De(e){if(Array.isArray(e))return e}var Oe=typeof R.default==`function`?R.default:R.default.default,ke=`file-invalid-type`,Ae=`file-too-large`,je=`file-too-small`,Me=`too-many-files`,Ne=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:``,t=e.split(`,`),n=t.length>1?`one of ${t.join(`, `)}`:t[0];return{code:ke,message:`File type must be ${n}`}},Pe=function(e){return{code:Ae,message:`File is larger than ${e} ${e===1?`byte`:`bytes`}`}},Fe=function(e){return{code:je,message:`File is smaller than ${e} ${e===1?`byte`:`bytes`}`}},Ie={code:Me,message:`Too many files`};function Le(e,t){var n=e.type===`application/x-moz-file`||Oe(e,t);return[n,n?null:Ne(t)]}function Re(e,t,n){if(V(e.size)){if(V(t)&&V(n)){if(e.size>n)return[!1,Pe(n)];if(e.size<t)return[!1,Fe(t)]}else if(V(t)&&e.size<t)return[!1,Fe(t)];else if(V(n)&&e.size>n)return[!1,Pe(n)]}return[!0,null]}function V(e){return e!=null}function ze(e){var t=e.files,n=e.accept,r=e.minSize,i=e.maxSize,a=e.multiple,o=e.maxFiles,s=e.validator;return!a&&t.length>1||a&&o>=1&&t.length>o?!1:t.every(function(e){var t=Le(e,n),a=B(t,1),o=a[0],c=Re(e,r,i),l=B(c,1),u=l[0],d=s?s(e):null;return o&&u&&!d})}function Be(e){return typeof e.isPropagationStopped==`function`?e.isPropagationStopped():e.cancelBubble===void 0?!1:e.cancelBubble}function Ve(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(e){return e===`Files`||e===`application/x-moz-file`}):!!e.target&&!!e.target.files}function He(e){e.preventDefault()}function Ue(e){return e.indexOf(`MSIE`)!==-1||e.indexOf(`Trident/`)!==-1}function We(e){return e.indexOf(`Edge/`)!==-1}function Ge(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return Ue(e)||We(e)}function H(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){for(var n=arguments.length,r=Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return t.some(function(t){return!Be(e)&&t&&t.apply(void 0,[e].concat(r)),Be(e)})}}function Ke(){return`showOpenFilePicker`in window}function qe(e){if(V(e)){var t=Object.entries(e).filter(function(e){var t=B(e,2),n=t[0],r=t[1],i=!0;return Ze(n)||(console.warn(`Skipped "${n}" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.`),i=!1),(!Array.isArray(r)||!r.every(Qe))&&(console.warn(`Skipped "${n}" because an invalid file extension was provided.`),i=!1),i}).reduce(function(e,t){var n=B(t,2),r=n[0],i=n[1];return xe(xe({},e),{},Se({},r,i))},{});return[{description:`Files`,accept:t}]}return e}function Je(e){if(V(e))return Object.entries(e).reduce(function(e,t){var n=B(t,2),r=n[0],i=n[1];return[].concat(z(e),[r],z(i))},[]).filter(function(e){return Ze(e)||Qe(e)}).join(`,`)}function Ye(e){return e instanceof DOMException&&(e.name===`AbortError`||e.code===e.ABORT_ERR)}function Xe(e){return e instanceof DOMException&&(e.name===`SecurityError`||e.code===e.SECURITY_ERR)}function Ze(e){return e===`audio/*`||e===`video/*`||e===`image/*`||e===`text/*`||e===`application/*`||/\w+\/[-+.\w]+/g.test(e)}function Qe(e){return/^.*\.[\w]+$/.test(e)}var U=t(i()),W=t(D()),$e=[`children`],et=[`open`],tt=[`refKey`,`role`,`onKeyDown`,`onFocus`,`onBlur`,`onClick`,`onDragEnter`,`onDragOver`,`onDragLeave`,`onDrop`],nt=[`refKey`,`onChange`,`onClick`];function rt(e){return ot(e)||at(e)||lt(e)||it()}function it(){throw TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function at(e){if(typeof Symbol<`u`&&e[Symbol.iterator]!=null||e[`@@iterator`]!=null)return Array.from(e)}function ot(e){if(Array.isArray(e))return ut(e)}function st(e,t){return ft(e)||dt(e,t)||lt(e,t)||ct()}function ct(){throw TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function lt(e,t){if(e){if(typeof e==`string`)return ut(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n===`Object`&&e.constructor&&(n=e.constructor.name),n===`Map`||n===`Set`)return Array.from(e);if(n===`Arguments`||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ut(e,t)}}function ut(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function dt(e,t){var n=e==null?null:typeof Symbol<`u`&&e[Symbol.iterator]||e[`@@iterator`];if(n!=null){var r=[],i=!0,a=!1,o,s;try{for(n=n.call(e);!(i=(o=n.next()).done)&&(r.push(o.value),!(t&&r.length===t));i=!0);}catch(e){a=!0,s=e}finally{try{!i&&n.return!=null&&n.return()}finally{if(a)throw s}}return r}}function ft(e){if(Array.isArray(e))return e}function pt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function G(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]==null?{}:arguments[t];t%2?pt(Object(n),!0).forEach(function(t){mt(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):pt(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function mt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ht(e,t){if(e==null)return{};var n=gt(e,t),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)r=a[i],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function gt(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,a;for(a=0;a<r.length;a++)i=r[a],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}var _t=(0,U.forwardRef)(function(e,t){var n=e.children,r=ht(e,$e),i=bt(r),a=i.open,o=ht(i,et);return(0,U.useImperativeHandle)(t,function(){return{open:a}},[a]),U.createElement(U.Fragment,null,n(G(G({},o),{},{open:a})))});_t.displayName=`Dropzone`;var vt={disabled:!1,getFilesFromEvent:se,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};_t.defaultProps=vt,_t.propTypes={children:W.default.func,accept:W.default.objectOf(W.default.arrayOf(W.default.string)),multiple:W.default.bool,preventDropOnDocument:W.default.bool,noClick:W.default.bool,noKeyboard:W.default.bool,noDrag:W.default.bool,noDragEventsBubbling:W.default.bool,minSize:W.default.number,maxSize:W.default.number,maxFiles:W.default.number,disabled:W.default.bool,getFilesFromEvent:W.default.func,onFileDialogCancel:W.default.func,onFileDialogOpen:W.default.func,useFsAccessApi:W.default.bool,autoFocus:W.default.bool,onDragEnter:W.default.func,onDragLeave:W.default.func,onDragOver:W.default.func,onDrop:W.default.func,onDropAccepted:W.default.func,onDropRejected:W.default.func,onError:W.default.func,validator:W.default.func};var yt={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function bt(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=G(G({},vt),e),n=t.accept,r=t.disabled,i=t.getFilesFromEvent,a=t.maxSize,o=t.minSize,s=t.multiple,c=t.maxFiles,l=t.onDragEnter,u=t.onDragLeave,d=t.onDragOver,f=t.onDrop,p=t.onDropAccepted,m=t.onDropRejected,h=t.onFileDialogCancel,g=t.onFileDialogOpen,_=t.useFsAccessApi,v=t.autoFocus,y=t.preventDropOnDocument,b=t.noClick,x=t.noKeyboard,S=t.noDrag,C=t.noDragEventsBubbling,w=t.onError,T=t.validator,E=(0,U.useMemo)(function(){return Je(n)},[n]),ee=(0,U.useMemo)(function(){return qe(n)},[n]),te=(0,U.useMemo)(function(){return typeof g==`function`?g:St},[g]),D=(0,U.useMemo)(function(){return typeof h==`function`?h:St},[h]),O=(0,U.useRef)(null),k=(0,U.useRef)(null),A=(0,U.useReducer)(xt,yt),ne=st(A,2),re=ne[0],j=ne[1],ie=re.isFocused,ae=re.isFileDialogActive,oe=(0,U.useRef)(typeof window<`u`&&window.isSecureContext&&_&&Ke()),se=function(){!oe.current&&ae&&setTimeout(function(){if(k.current){var e=k.current.files;e.length||(j({type:`closeDialog`}),D())}},300)};(0,U.useEffect)(function(){return window.addEventListener(`focus`,se,!1),function(){window.removeEventListener(`focus`,se,!1)}},[k,ae,D,oe]);var M=(0,U.useRef)([]),ce=function(e){O.current&&O.current.contains(e.target)||(e.preventDefault(),M.current=[])};(0,U.useEffect)(function(){return y&&(document.addEventListener(`dragover`,He,!1),document.addEventListener(`drop`,ce,!1)),function(){y&&(document.removeEventListener(`dragover`,He),document.removeEventListener(`drop`,ce))}},[O,y]),(0,U.useEffect)(function(){return!r&&v&&O.current&&O.current.focus(),function(){}},[O,v,r]);var N=(0,U.useCallback)(function(e){w?w(e):console.error(e)},[w]),le=(0,U.useCallback)(function(e){e.preventDefault(),e.persist(),z(e),M.current=[].concat(rt(M.current),[e.target]),Ve(e)&&Promise.resolve(i(e)).then(function(t){if(!(Be(e)&&!C)){var n=t.length,r=n>0&&ze({files:t,accept:E,minSize:o,maxSize:a,multiple:s,maxFiles:c,validator:T}),i=n>0&&!r;j({isDragAccept:r,isDragReject:i,isDragActive:!0,type:`setDraggedFiles`}),l&&l(e)}}).catch(function(e){return N(e)})},[i,l,N,C,E,o,a,s,c,T]),ue=(0,U.useCallback)(function(e){e.preventDefault(),e.persist(),z(e);var t=Ve(e);if(t&&e.dataTransfer)try{e.dataTransfer.dropEffect=`copy`}catch{}return t&&d&&d(e),!1},[d,C]),de=(0,U.useCallback)(function(e){e.preventDefault(),e.persist(),z(e);var t=M.current.filter(function(e){return O.current&&O.current.contains(e)}),n=t.indexOf(e.target);n!==-1&&t.splice(n,1),M.current=t,!(t.length>0)&&(j({type:`setDraggedFiles`,isDragActive:!1,isDragAccept:!1,isDragReject:!1}),Ve(e)&&u&&u(e))},[O,u,C]),P=(0,U.useCallback)(function(e,t){var n=[],r=[];e.forEach(function(e){var t=Le(e,E),i=st(t,2),s=i[0],c=i[1],l=Re(e,o,a),u=st(l,2),d=u[0],f=u[1],p=T?T(e):null;if(s&&d&&!p)n.push(e);else{var m=[c,f];p&&(m=m.concat(p)),r.push({file:e,errors:m.filter(function(e){return e})})}}),(!s&&n.length>1||s&&c>=1&&n.length>c)&&(n.forEach(function(e){r.push({file:e,errors:[Ie]})}),n.splice(0)),j({acceptedFiles:n,fileRejections:r,isDragReject:r.length>0,type:`setFiles`}),f&&f(n,r,t),r.length>0&&m&&m(r,t),n.length>0&&p&&p(n,t)},[j,s,E,o,a,c,f,p,m,T]),F=(0,U.useCallback)(function(e){e.preventDefault(),e.persist(),z(e),M.current=[],Ve(e)&&Promise.resolve(i(e)).then(function(t){Be(e)&&!C||P(t,e)}).catch(function(e){return N(e)}),j({type:`reset`})},[i,P,N,C]),I=(0,U.useCallback)(function(){if(oe.current){j({type:`openDialog`}),te();var e={multiple:s,types:ee};window.showOpenFilePicker(e).then(function(e){return i(e)}).then(function(e){P(e,null),j({type:`closeDialog`})}).catch(function(e){Ye(e)?(D(e),j({type:`closeDialog`})):Xe(e)?(oe.current=!1,k.current?(k.current.value=null,k.current.click()):N(Error(`Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided.`))):N(e)});return}k.current&&(j({type:`openDialog`}),te(),k.current.value=null,k.current.click())},[j,te,D,_,P,N,ee,s]),fe=(0,U.useCallback)(function(e){!O.current||!O.current.isEqualNode(e.target)||(e.key===` `||e.key===`Enter`||e.keyCode===32||e.keyCode===13)&&(e.preventDefault(),I())},[O,I]),pe=(0,U.useCallback)(function(){j({type:`focus`})},[]),me=(0,U.useCallback)(function(){j({type:`blur`})},[]),he=(0,U.useCallback)(function(){b||(Ge()?setTimeout(I,0):I())},[b,I]),L=function(e){return r?null:e},ge=function(e){return x?null:L(e)},R=function(e){return S?null:L(e)},z=function(e){C&&e.stopPropagation()},_e=(0,U.useMemo)(function(){return function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=e.refKey,n=t===void 0?`ref`:t,i=e.role,a=e.onKeyDown,o=e.onFocus,s=e.onBlur,c=e.onClick,l=e.onDragEnter,u=e.onDragOver,d=e.onDragLeave,f=e.onDrop,p=ht(e,tt);return G(G(mt({onKeyDown:ge(H(a,fe)),onFocus:ge(H(o,pe)),onBlur:ge(H(s,me)),onClick:L(H(c,he)),onDragEnter:R(H(l,le)),onDragOver:R(H(u,ue)),onDragLeave:R(H(d,de)),onDrop:R(H(f,F)),role:typeof i==`string`&&i!==``?i:`presentation`},n,O),!r&&!x?{tabIndex:0}:{}),p)}},[O,fe,pe,me,he,le,ue,de,F,x,S,r]),ve=(0,U.useCallback)(function(e){e.stopPropagation()},[]),ye=(0,U.useMemo)(function(){return function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=e.refKey,n=t===void 0?`ref`:t,r=e.onChange,i=e.onClick,a=ht(e,nt),o=mt({accept:E,multiple:s,type:`file`,style:{border:0,clip:`rect(0, 0, 0, 0)`,clipPath:`inset(50%)`,height:`1px`,margin:`0 -1px -1px 0`,overflow:`hidden`,padding:0,position:`absolute`,width:`1px`,whiteSpace:`nowrap`},onChange:L(H(r,F)),onClick:L(H(i,ve)),tabIndex:-1},n,k);return G(G({},o),a)}},[k,n,s,F,r]);return G(G({},re),{},{isFocused:ie&&!r,getRootProps:_e,getInputProps:ye,rootRef:O,inputRef:k,open:L(I)})}function xt(e,t){switch(t.type){case`focus`:return G(G({},e),{},{isFocused:!0});case`blur`:return G(G({},e),{},{isFocused:!1});case`openDialog`:return G(G({},yt),{},{isFileDialogActive:!0});case`closeDialog`:return G(G({},e),{},{isFileDialogActive:!1});case`setDraggedFiles`:return G(G({},e),{},{isDragActive:t.isDragActive,isDragAccept:t.isDragAccept,isDragReject:t.isDragReject});case`setFiles`:return G(G({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections,isDragReject:t.isDragReject});case`reset`:return G({},yt);default:return e}}function St(){}var Ct=U.useId||(()=>void 0),wt=0;function Tt(e){let[t,n]=U.useState(Ct());return C(()=>{e||n(e=>e??String(wt++))},[e]),e||(t?`radix-${t}`:``)}function Et(e,t=globalThis?.document){let n=x(e);U.useEffect(()=>{let e=e=>{e.key===`Escape`&&n(e)};return t.addEventListener(`keydown`,e,{capture:!0}),()=>t.removeEventListener(`keydown`,e,{capture:!0})},[n,t])}var K=t(r(),1),Dt=`DismissableLayer`,Ot=`dismissableLayer.update`,kt=`dismissableLayer.pointerDownOutside`,At=`dismissableLayer.focusOutside`,jt,Mt=U.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Nt=U.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:i,onFocusOutside:a,onInteractOutside:o,onDismiss:s,...c}=e,u=U.useContext(Mt),[d,f]=U.useState(null),m=d?.ownerDocument??globalThis?.document,[,h]=U.useState({}),g=p(t,e=>f(e)),_=Array.from(u.layers),[y]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),b=_.indexOf(y),x=d?_.indexOf(d):-1,S=u.layersWithOutsidePointerEventsDisabled.size>0,C=x>=b,w=It(e=>{let t=e.target,n=[...u.branches].some(e=>e.contains(t));!C||n||(i?.(e),o?.(e),e.defaultPrevented||s?.())},m),T=Lt(e=>{let t=e.target,n=[...u.branches].some(e=>e.contains(t));n||(a?.(e),o?.(e),e.defaultPrevented||s?.())},m);return Et(e=>{let t=x===u.layers.size-1;t&&(r?.(e),!e.defaultPrevented&&s&&(e.preventDefault(),s()))},m),U.useEffect(()=>{if(d)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(jt=m.body.style.pointerEvents,m.body.style.pointerEvents=`none`),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),Rt(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(m.body.style.pointerEvents=jt)}},[d,m,n,u]),U.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),Rt())},[d,u]),U.useEffect(()=>{let e=()=>h({});return document.addEventListener(Ot,e),()=>document.removeEventListener(Ot,e)},[]),(0,K.jsx)(l.div,{...c,ref:g,style:{pointerEvents:S?C?`auto`:`none`:void 0,...e.style},onFocusCapture:v(e.onFocusCapture,T.onFocusCapture),onBlurCapture:v(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:v(e.onPointerDownCapture,w.onPointerDownCapture)})});Nt.displayName=Dt;var Pt=`DismissableLayerBranch`,Ft=U.forwardRef((e,t)=>{let n=U.useContext(Mt),r=U.useRef(null),i=p(t,r);return U.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,K.jsx)(l.div,{...e,ref:i})});Ft.displayName=Pt;function It(e,t=globalThis?.document){let n=x(e),r=U.useRef(!1),i=U.useRef(()=>{});return U.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){zt(kt,n,o,{discrete:!0})};var a=r;let o={originalEvent:e};e.pointerType===`touch`?(t.removeEventListener(`click`,i.current),i.current=r,t.addEventListener(`click`,i.current,{once:!0})):r()}else t.removeEventListener(`click`,i.current);r.current=!1},a=window.setTimeout(()=>{t.addEventListener(`pointerdown`,e)},0);return()=>{window.clearTimeout(a),t.removeEventListener(`pointerdown`,e),t.removeEventListener(`click`,i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Lt(e,t=globalThis?.document){let n=x(e),r=U.useRef(!1);return U.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let t={originalEvent:e};zt(At,n,t,{discrete:!1})}};return t.addEventListener(`focusin`,e),()=>t.removeEventListener(`focusin`,e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Rt(){let e=new CustomEvent(Ot);document.dispatchEvent(e)}function zt(e,t,n,{discrete:r}){let i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),r?f(i,a):i.dispatchEvent(a)}var Bt=`focusScope.autoFocusOnMount`,Vt=`focusScope.autoFocusOnUnmount`,Ht={bubbles:!1,cancelable:!0},Ut=`FocusScope`,Wt=U.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:i,onUnmountAutoFocus:a,...o}=e,[s,c]=U.useState(null),u=x(i),d=x(a),f=U.useRef(null),m=p(t,e=>c(e)),h=U.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;U.useEffect(()=>{if(r){let r=function(e){if(h.paused||!s)return;let t=e.target;s.contains(t)?f.current=t:q(f.current,{select:!0})},i=function(e){if(h.paused||!s)return;let t=e.relatedTarget;t!==null&&(s.contains(t)||q(f.current,{select:!0}))},a=function(e){let t=document.activeElement;if(t===document.body)for(let t of e)t.removedNodes.length>0&&q(s)};var e=r,t=i,n=a;document.addEventListener(`focusin`,r),document.addEventListener(`focusout`,i);let o=new MutationObserver(a);return s&&o.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener(`focusin`,r),document.removeEventListener(`focusout`,i),o.disconnect()}}},[r,s,h.paused]),U.useEffect(()=>{if(s){Zt.add(h);let e=document.activeElement,t=s.contains(e);if(!t){let t=new CustomEvent(Bt,Ht);s.addEventListener(Bt,u),s.dispatchEvent(t),t.defaultPrevented||(Gt(en(qt(s)),{select:!0}),document.activeElement===e&&q(s))}return()=>{s.removeEventListener(Bt,u),setTimeout(()=>{let t=new CustomEvent(Vt,Ht);s.addEventListener(Vt,d),s.dispatchEvent(t),t.defaultPrevented||q(e??document.body,{select:!0}),s.removeEventListener(Vt,d),Zt.remove(h)},0)}}},[s,u,d,h]);let g=U.useCallback(e=>{if(!n&&!r||h.paused)return;let t=e.key===`Tab`&&!e.altKey&&!e.ctrlKey&&!e.metaKey,i=document.activeElement;if(t&&i){let t=e.currentTarget,[r,a]=Kt(t),o=r&&a;o?!e.shiftKey&&i===a?(e.preventDefault(),n&&q(r,{select:!0})):e.shiftKey&&i===r&&(e.preventDefault(),n&&q(a,{select:!0})):i===t&&e.preventDefault()}},[n,r,h.paused]);return(0,K.jsx)(l.div,{tabIndex:-1,...o,ref:m,onKeyDown:g})});Wt.displayName=Ut;function Gt(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(q(r,{select:t}),document.activeElement!==n)return}function Kt(e){let t=qt(e),n=Jt(t,e),r=Jt(t.reverse(),e);return[n,r]}function qt(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t=e.tagName===`INPUT`&&e.type===`hidden`;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Jt(e,t){for(let n of e)if(!Yt(n,{upTo:t}))return n}function Yt(e,{upTo:t}){if(getComputedStyle(e).visibility===`hidden`)return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display===`none`)return!0;e=e.parentElement}return!1}function Xt(e){return e instanceof HTMLInputElement&&`select`in e}function q(e,{select:t=!1}={}){if(e&&e.focus){let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Xt(e)&&t&&e.select()}}var Zt=Qt();function Qt(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),e=$t(e,t),e.unshift(t)},remove(t){e=$t(e,t),e[0]?.resume()}}}function $t(e,t){let n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function en(e){return e.filter(e=>e.tagName!==`A`)}var tn=t(a(),1),nn=`Portal`,rn=U.forwardRef((e,t)=>{let{container:n,...r}=e,[i,a]=U.useState(!1);C(()=>a(!0),[]);let o=n||i&&globalThis?.document?.body;return o?tn.createPortal((0,K.jsx)(l.div,{...r,ref:t}),o):null});rn.displayName=nn;function an(e,t){return U.useReducer((e,n)=>{let r=t[e][n];return r??e},e)}var on=e=>{let{present:t,children:n}=e,r=sn(t),i=typeof n==`function`?n({present:r.isPresent}):U.Children.only(n),a=p(r.ref,ln(i)),o=typeof n==`function`;return o||r.isPresent?U.cloneElement(i,{ref:a}):null};on.displayName=`Presence`;function sn(e){let[t,n]=U.useState(),r=U.useRef({}),i=U.useRef(e),a=U.useRef(`none`),o=e?`mounted`:`unmounted`,[s,c]=an(o,{mounted:{UNMOUNT:`unmounted`,ANIMATION_OUT:`unmountSuspended`},unmountSuspended:{MOUNT:`mounted`,ANIMATION_END:`unmounted`},unmounted:{MOUNT:`mounted`}});return U.useEffect(()=>{let e=cn(r.current);a.current=s===`mounted`?e:`none`},[s]),C(()=>{let t=r.current,n=i.current,o=n!==e;if(o){let r=a.current,o=cn(t);if(e)c(`MOUNT`);else if(o===`none`||t?.display===`none`)c(`UNMOUNT`);else{let e=r!==o;c(n&&e?`ANIMATION_OUT`:`UNMOUNT`)}i.current=e}},[e,c]),C(()=>{if(t){let e,n=t.ownerDocument.defaultView??window,o=a=>{let o=cn(r.current),s=o.includes(a.animationName);if(a.target===t&&s&&(c(`ANIMATION_END`),!i.current)){let r=t.style.animationFillMode;t.style.animationFillMode=`forwards`,e=n.setTimeout(()=>{t.style.animationFillMode===`forwards`&&(t.style.animationFillMode=r)})}},s=e=>{e.target===t&&(a.current=cn(r.current))};return t.addEventListener(`animationstart`,s),t.addEventListener(`animationcancel`,o),t.addEventListener(`animationend`,o),()=>{n.clearTimeout(e),t.removeEventListener(`animationstart`,s),t.removeEventListener(`animationcancel`,o),t.removeEventListener(`animationend`,o)}}else c(`ANIMATION_END`)},[t,c]),{isPresent:[`mounted`,`unmountSuspended`].includes(s),ref:U.useCallback(e=>{e&&(r.current=getComputedStyle(e)),n(e)},[])}}function cn(e){return e?.animationName||`none`}function ln(e){let t=Object.getOwnPropertyDescriptor(e.props,`ref`)?.get,n=t&&`isReactWarning`in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,`ref`)?.get,n=t&&`isReactWarning`in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var un=0;function dn(){U.useEffect(()=>{let e=document.querySelectorAll(`[data-radix-focus-guard]`);return document.body.insertAdjacentElement(`afterbegin`,e[0]??fn()),document.body.insertAdjacentElement(`beforeend`,e[1]??fn()),un++,()=>{un===1&&document.querySelectorAll(`[data-radix-focus-guard]`).forEach(e=>e.remove()),un--}},[])}function fn(){let e=document.createElement(`span`);return e.setAttribute(`data-radix-focus-guard`,``),e.tabIndex=0,e.style.outline=`none`,e.style.opacity=`0`,e.style.position=`fixed`,e.style.pointerEvents=`none`,e}var pn=`right-scroll-bar-position`,mn=`width-before-scroll-bar`,hn=`with-scroll-bars-hidden`,gn=`--removed-body-scroll-bar-size`;function _n(e,t){return typeof e==`function`?e(t):e&&(e.current=t),e}function vn(e,t){var n=(0,U.useState)(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(e){var t=n.value;t!==e&&(n.value=e,n.callback(e,t))}}}})[0];return n.callback=t,n.facade}var yn=typeof window<`u`?U.useLayoutEffect:U.useEffect,bn=new WeakMap;function xn(e,t){var n=vn(t||null,function(t){return e.forEach(function(e){return _n(e,t)})});return yn(function(){var t=bn.get(n);if(t){var r=new Set(t),i=new Set(e),a=n.current;r.forEach(function(e){i.has(e)||_n(e,null)}),i.forEach(function(e){r.has(e)||_n(e,a)})}bn.set(n,e)},[e]),n}function Sn(e){return e}function Cn(e,t){t===void 0&&(t=Sn);var n=[],r=!1,i={read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(e){var i=t(e,r);return n.push(i),function(){n=n.filter(function(e){return e!==i})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var i=n;n=[],i.forEach(e),t=n}var a=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(a)};o(),n={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),n}}}};return i}function wn(e){e===void 0&&(e={});var t=Cn(null);return t.options=O({async:!0,ssr:!1},e),t}var Tn=function(e){var t=e.sideCar,n=k(e,[`sideCar`]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error(`Sidecar medium not found`);return U.createElement(r,O({},n))};Tn.isSideCarExport=!0;function En(e,t){return e.useMedium(t),Tn}var Dn=wn(),On=function(){},kn=U.forwardRef(function(e,t){var n=U.useRef(null),r=U.useState({onScrollCapture:On,onWheelCapture:On,onTouchMoveCapture:On}),i=r[0],a=r[1],o=e.forwardProps,s=e.children,c=e.className,l=e.removeScrollBar,u=e.enabled,d=e.shards,f=e.sideCar,p=e.noRelative,m=e.noIsolation,h=e.inert,g=e.allowPinchZoom,_=e.as,v=_===void 0?`div`:_,y=e.gapMode,b=k(e,[`forwardProps`,`children`,`className`,`removeScrollBar`,`enabled`,`shards`,`sideCar`,`noRelative`,`noIsolation`,`inert`,`allowPinchZoom`,`as`,`gapMode`]),x=f,S=xn([n,t]),C=O(O({},b),i);return U.createElement(U.Fragment,null,u&&U.createElement(x,{sideCar:Dn,removeScrollBar:l,shards:d,noRelative:p,noIsolation:m,inert:h,setCallbacks:a,allowPinchZoom:!!g,lockRef:n,gapMode:y}),o?U.cloneElement(U.Children.only(s),O(O({},C),{ref:S})):U.createElement(v,O({},C,{className:c,ref:S}),s))});kn.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},kn.classNames={fullWidth:mn,zeroRight:pn};var An,jn=function(){if(An)return An;if(typeof __webpack_nonce__<`u`)return __webpack_nonce__};function Mn(){if(!document)return null;var e=document.createElement(`style`);e.type=`text/css`;var t=jn();return t&&e.setAttribute(`nonce`,t),e}function Nn(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Pn(e){var t=document.head||document.getElementsByTagName(`head`)[0];t.appendChild(e)}var Fn=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Mn())&&(Nn(t,n),Pn(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},In=function(){var e=Fn();return function(t,n){U.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Ln=function(){var e=In(),t=function(t){var n=t.styles,r=t.dynamic;return e(n,r),null};return t},Rn={left:0,top:0,right:0,gap:0},zn=function(e){return parseInt(e||``,10)||0},Bn=function(e){var t=window.getComputedStyle(document.body),n=t[e===`padding`?`paddingLeft`:`marginLeft`],r=t[e===`padding`?`paddingTop`:`marginTop`],i=t[e===`padding`?`paddingRight`:`marginRight`];return[zn(n),zn(r),zn(i)]},Vn=function(e){if(e===void 0&&(e=`margin`),typeof window>`u`)return Rn;var t=Bn(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Hn=Ln(),J=`data-scroll-locked`,Un=function(e,t,n,r){var i=e.left,a=e.top,o=e.right,s=e.gap;return n===void 0&&(n=`margin`),`
  .${hn} {
   overflow: hidden ${r};
   padding-right: ${s}px ${r};
  }
  body[${J}] {
    overflow: hidden ${r};
    overscroll-behavior: contain;
    ${[t&&`position: relative ${r};`,n===`margin`&&`
    padding-left: ${i}px;
    padding-top: ${a}px;
    padding-right: ${o}px;
    margin-left:0;
    margin-top:0;
    margin-right: ${s}px ${r};
    `,n===`padding`&&`padding-right: ${s}px ${r};`].filter(Boolean).join(``)}
  }
  
  .${pn} {
    right: ${s}px ${r};
  }
  
  .${mn} {
    margin-right: ${s}px ${r};
  }
  
  .${pn} .${pn} {
    right: 0 ${r};
  }
  
  .${mn} .${mn} {
    margin-right: 0 ${r};
  }
  
  body[${J}] {
    ${gn}: ${s}px;
  }
`},Wn=function(){var e=parseInt(document.body.getAttribute(J)||`0`,10);return isFinite(e)?e:0},Gn=function(){U.useEffect(function(){return document.body.setAttribute(J,(Wn()+1).toString()),function(){var e=Wn()-1;e<=0?document.body.removeAttribute(J):document.body.setAttribute(J,e.toString())}},[])},Kn=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,i=r===void 0?`margin`:r;Gn();var a=U.useMemo(function(){return Vn(i)},[i]);return U.createElement(Hn,{styles:Un(a,!t,i,n?``:`!important`)})},qn=!1;if(typeof window<`u`)try{var Jn=Object.defineProperty({},`passive`,{get:function(){return qn=!0,!0}});window.addEventListener(`test`,Jn,Jn),window.removeEventListener(`test`,Jn,Jn)}catch{qn=!1}var Y=qn?{passive:!1}:!1,Yn=function(e){return e.tagName===`TEXTAREA`},Xn=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!==`hidden`&&!(n.overflowY===n.overflowX&&!Yn(e)&&n[t]===`visible`)},Zn=function(e){return Xn(e,`overflowY`)},Qn=function(e){return Xn(e,`overflowX`)},$n=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<`u`&&r instanceof ShadowRoot&&(r=r.host);var i=nr(e,r);if(i){var a=rr(e,r),o=a[1],s=a[2];if(o>s)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},er=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},tr=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},nr=function(e,t){return e===`v`?Zn(t):Qn(t)},rr=function(e,t){return e===`v`?er(t):tr(t)},ir=function(e,t){return e===`h`&&t===`rtl`?-1:1},ar=function(e,t,n,r,i){var a=ir(e,window.getComputedStyle(t).direction),o=a*r,s=n.target,c=t.contains(s),l=!1,u=o>0,d=0,f=0;do{if(!s)break;var p=rr(e,s),m=p[0],h=p[1],g=p[2],_=h-g-a*m;(m||_)&&nr(e,s)&&(d+=_,f+=m);var v=s.parentNode;s=v&&v.nodeType===Node.DOCUMENT_FRAGMENT_NODE?v.host:v}while(!c&&s!==document.body||c&&(t.contains(s)||t===s));return(u&&(i&&Math.abs(d)<1||!i&&o>d)||!u&&(i&&Math.abs(f)<1||!i&&-o>f))&&(l=!0),l},or=function(e){return`changedTouches`in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},sr=function(e){return[e.deltaX,e.deltaY]},cr=function(e){return e&&`current`in e?e.current:e},lr=function(e,t){return e[0]===t[0]&&e[1]===t[1]},ur=function(e){return`
  .block-interactivity-${e} {pointer-events: none;}
  .allow-interactivity-${e} {pointer-events: all;}
`},dr=0,X=[];function fr(e){var t=U.useRef([]),n=U.useRef([0,0]),r=U.useRef(),i=U.useState(dr++)[0],a=U.useState(Ln)[0],o=U.useRef(e);U.useEffect(function(){o.current=e},[e]),U.useEffect(function(){if(e.inert){document.body.classList.add(`block-interactivity-${i}`);var t=ne([e.lockRef.current],(e.shards||[]).map(cr),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add(`allow-interactivity-${i}`)}),function(){document.body.classList.remove(`block-interactivity-${i}`),t.forEach(function(e){return e.classList.remove(`allow-interactivity-${i}`)})}}},[e.inert,e.lockRef.current,e.shards]);var s=U.useCallback(function(e,t){if(`touches`in e&&e.touches.length===2||e.type===`wheel`&&e.ctrlKey)return!o.current.allowPinchZoom;var i=or(e),a=n.current,s=`deltaX`in e?e.deltaX:a[0]-i[0],c=`deltaY`in e?e.deltaY:a[1]-i[1],l,u=e.target,d=Math.abs(s)>Math.abs(c)?`h`:`v`;if(`touches`in e&&d===`h`&&u.type===`range`)return!1;var f=$n(d,u);if(!f)return!0;if(f?l=d:(l=d===`v`?`h`:`v`,f=$n(d,u)),!f)return!1;if(!r.current&&`changedTouches`in e&&(s||c)&&(r.current=l),!l)return!0;var p=r.current||l;return ar(p,t,e,p===`h`?s:c,!0)},[]),c=U.useCallback(function(e){var n=e;if(!(!X.length||X[X.length-1]!==a)){var r=`deltaY`in n?sr(n):or(n),i=t.current.filter(function(e){return e.name===n.type&&(e.target===n.target||n.target===e.shadowParent)&&lr(e.delta,r)})[0];if(i&&i.should){n.cancelable&&n.preventDefault();return}if(!i){var c=(o.current.shards||[]).map(cr).filter(Boolean).filter(function(e){return e.contains(n.target)}),l=c.length>0?s(n,c[0]):!o.current.noIsolation;l&&n.cancelable&&n.preventDefault()}}},[]),l=U.useCallback(function(e,n,r,i){var a={name:e,delta:n,target:r,should:i,shadowParent:pr(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),u=U.useCallback(function(e){n.current=or(e),r.current=void 0},[]),d=U.useCallback(function(t){l(t.type,sr(t),t.target,s(t,e.lockRef.current))},[]),f=U.useCallback(function(t){l(t.type,or(t),t.target,s(t,e.lockRef.current))},[]);U.useEffect(function(){return X.push(a),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:f}),document.addEventListener(`wheel`,c,Y),document.addEventListener(`touchmove`,c,Y),document.addEventListener(`touchstart`,u,Y),function(){X=X.filter(function(e){return e!==a}),document.removeEventListener(`wheel`,c,Y),document.removeEventListener(`touchmove`,c,Y),document.removeEventListener(`touchstart`,u,Y)}},[]);var p=e.removeScrollBar,m=e.inert;return U.createElement(U.Fragment,null,m?U.createElement(a,{styles:ur(i)}):null,p?U.createElement(Kn,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function pr(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}var mr=En(Dn,fr),hr=U.forwardRef(function(e,t){return U.createElement(kn,O({},e,{ref:t,sideCar:mr}))});hr.classNames=kn.classNames;var gr=hr,_r=function(e){if(typeof document>`u`)return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Z=new WeakMap,vr=new WeakMap,yr={},br=0,xr=function(e){return e&&(e.host||xr(e.parentNode))},Sr=function(e,t){return t.map(function(t){if(e.contains(t))return t;var n=xr(t);return n&&e.contains(n)?n:(console.error(`aria-hidden`,t,`in not contained inside`,e,`. Doing nothing`),null)}).filter(function(e){return!!e})},Cr=function(e,t,n,r){var i=Sr(t,Array.isArray(e)?e:[e]);yr[n]||(yr[n]=new WeakMap);var a=yr[n],o=[],s=new Set,c=new Set(i),l=function(e){!e||s.has(e)||(s.add(e),l(e.parentNode))};i.forEach(l);var u=function(e){!e||c.has(e)||Array.prototype.forEach.call(e.children,function(e){if(s.has(e))u(e);else try{var t=e.getAttribute(r),i=t!==null&&t!==`false`,c=(Z.get(e)||0)+1,l=(a.get(e)||0)+1;Z.set(e,c),a.set(e,l),o.push(e),c===1&&i&&vr.set(e,!0),l===1&&e.setAttribute(n,`true`),i||e.setAttribute(r,`true`)}catch(t){console.error(`aria-hidden: cannot operate on `,e,t)}})};return u(t),s.clear(),br++,function(){o.forEach(function(e){var t=Z.get(e)-1,i=a.get(e)-1;Z.set(e,t),a.set(e,i),t||(vr.has(e)||e.removeAttribute(r),vr.delete(e)),i||e.removeAttribute(n)}),br--,br||(Z=new WeakMap,Z=new WeakMap,vr=new WeakMap,yr={})}},wr=function(e,t,n){n===void 0&&(n=`data-aria-hidden`);var r=Array.from(Array.isArray(e)?e:[e]),i=t||_r(e);return i?(r.push.apply(r,Array.from(i.querySelectorAll(`[aria-live], script`))),Cr(r,i,n,`aria-hidden`)):function(){return null}},Tr=U.forwardRef((e,t)=>{let{children:n,...r}=e,i=U.Children.toArray(n),a=i.find(Or);if(a){let e=a.props.children,n=i.map(t=>t===a?U.Children.count(e)>1?U.Children.only(null):U.isValidElement(e)?e.props.children:null:t);return(0,K.jsx)(Er,{...r,ref:t,children:U.isValidElement(e)?U.cloneElement(e,void 0,n):null})}return(0,K.jsx)(Er,{...r,ref:t,children:n})});Tr.displayName=`Slot`;var Er=U.forwardRef((e,t)=>{let{children:n,...r}=e;if(U.isValidElement(n)){let e=Ar(n);return U.cloneElement(n,{...kr(r,n.props),ref:t?d(t,e):e})}return U.Children.count(n)>1?U.Children.only(null):null});Er.displayName=`SlotClone`;var Dr=({children:e})=>(0,K.jsx)(K.Fragment,{children:e});function Or(e){return U.isValidElement(e)&&e.type===Dr}function kr(e,t){let n={...t};for(let r in t){let i=e[r],a=t[r],o=/^on[A-Z]/.test(r);o?i&&a?n[r]=(...e)=>{a(...e),i(...e)}:i&&(n[r]=i):r===`style`?n[r]={...i,...a}:r===`className`&&(n[r]=[i,a].filter(Boolean).join(` `))}return{...e,...n}}function Ar(e){let t=Object.getOwnPropertyDescriptor(e.props,`ref`)?.get,n=t&&`isReactWarning`in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,`ref`)?.get,n=t&&`isReactWarning`in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var jr=`Dialog`,[Mr,Nr]=b(jr),[Pr,Q]=Mr(jr),Fr=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:i,onOpenChange:a,modal:o=!0}=e,s=U.useRef(null),c=U.useRef(null),[l=!1,u]=S({prop:r,defaultProp:i,onChange:a});return(0,K.jsx)(Pr,{scope:t,triggerRef:s,contentRef:c,contentId:Tt(),titleId:Tt(),descriptionId:Tt(),open:l,onOpenChange:u,onOpenToggle:U.useCallback(()=>u(e=>!e),[u]),modal:o,children:n})};Fr.displayName=jr;var Ir=`DialogTrigger`,Lr=U.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=Q(Ir,n),a=p(t,i.triggerRef);return(0,K.jsx)(l.button,{type:`button`,"aria-haspopup":`dialog`,"aria-expanded":i.open,"aria-controls":i.contentId,"data-state":ti(i.open),...r,ref:a,onClick:v(e.onClick,i.onOpenToggle)})});Lr.displayName=Ir;var Rr=`DialogPortal`,[zr,Br]=Mr(Rr,{forceMount:void 0}),Vr=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:i}=e,a=Q(Rr,t);return(0,K.jsx)(zr,{scope:t,forceMount:n,children:U.Children.map(r,e=>(0,K.jsx)(on,{present:n||a.open,children:(0,K.jsx)(rn,{asChild:!0,container:i,children:e})}))})};Vr.displayName=Rr;var Hr=`DialogOverlay`,Ur=U.forwardRef((e,t)=>{let n=Br(Hr,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,a=Q(Hr,e.__scopeDialog);return a.modal?(0,K.jsx)(on,{present:r||a.open,children:(0,K.jsx)(Wr,{...i,ref:t})}):null});Ur.displayName=Hr;var Wr=U.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=Q(Hr,n);return(0,K.jsx)(gr,{as:Tr,allowPinchZoom:!0,shards:[i.contentRef],children:(0,K.jsx)(l.div,{"data-state":ti(i.open),...r,ref:t,style:{pointerEvents:`auto`,...r.style}})})}),$=`DialogContent`,Gr=U.forwardRef((e,t)=>{let n=Br($,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,a=Q($,e.__scopeDialog);return(0,K.jsx)(on,{present:r||a.open,children:a.modal?(0,K.jsx)(Kr,{...i,ref:t}):(0,K.jsx)(qr,{...i,ref:t})})});Gr.displayName=$;var Kr=U.forwardRef((e,t)=>{let n=Q($,e.__scopeDialog),r=U.useRef(null),i=p(t,n.contentRef,r);return U.useEffect(()=>{let e=r.current;if(e)return wr(e)},[]),(0,K.jsx)(Jr,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:v(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:v(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=t.button===0&&t.ctrlKey===!0,r=t.button===2||n;r&&e.preventDefault()}),onFocusOutside:v(e.onFocusOutside,e=>e.preventDefault())})}),qr=U.forwardRef((e,t)=>{let n=Q($,e.__scopeDialog),r=U.useRef(!1),i=U.useRef(!1);return(0,K.jsx)(Jr,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(r.current||n.triggerRef.current?.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(r.current=!0,t.detail.originalEvent.type===`pointerdown`&&(i.current=!0));let a=t.target,o=n.triggerRef.current?.contains(a);o&&t.preventDefault(),t.detail.originalEvent.type===`focusin`&&i.current&&t.preventDefault()}})}),Jr=U.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:a,...o}=e,s=Q($,n),c=U.useRef(null),l=p(t,c);return dn(),(0,K.jsxs)(K.Fragment,{children:[(0,K.jsx)(Wt,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:a,children:(0,K.jsx)(Nt,{role:`dialog`,id:s.contentId,"aria-describedby":s.descriptionId,"aria-labelledby":s.titleId,"data-state":ti(s.open),...o,ref:l,onDismiss:()=>s.onOpenChange(!1)})}),(0,K.jsxs)(K.Fragment,{children:[(0,K.jsx)(ai,{titleId:s.titleId}),(0,K.jsx)(si,{contentRef:c,descriptionId:s.descriptionId})]})]})}),Yr=`DialogTitle`,Xr=U.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=Q(Yr,n);return(0,K.jsx)(l.h2,{id:i.titleId,...r,ref:t})});Xr.displayName=Yr;var Zr=`DialogDescription`,Qr=U.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=Q(Zr,n);return(0,K.jsx)(l.p,{id:i.descriptionId,...r,ref:t})});Qr.displayName=Zr;var $r=`DialogClose`,ei=U.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=Q($r,n);return(0,K.jsx)(l.button,{type:`button`,...r,ref:t,onClick:v(e.onClick,()=>i.onOpenChange(!1))})});ei.displayName=$r;function ti(e){return e?`open`:`closed`}var ni=`DialogTitleWarning`,[ri,ii]=y(ni,{contentName:$,titleName:Yr,docsSlug:`dialog`}),ai=({titleId:e})=>{let t=ii(ni),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return U.useEffect(()=>{if(e){let t=document.getElementById(e);t||console.error(n)}},[n,e]),null},oi=`DialogDescriptionWarning`,si=({contentRef:e,descriptionId:t})=>{let n=ii(oi),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return U.useEffect(()=>{let n=e.current?.getAttribute(`aria-describedby`);if(t&&n){let e=document.getElementById(t);e||console.warn(r)}},[r,e,t]),null},ci=Fr,li=Lr,ui=Vr,di=Ur,fi=Gr,pi=Xr,mi=Qr,hi=ei;const gi=ci,_i=li,vi=ui,yi=hi,bi=U.forwardRef(({className:e,...t},r)=>(0,K.jsx)(di,{ref:r,className:n(`fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0`,e),...t}));bi.displayName=di.displayName;const xi=U.forwardRef(({className:e,children:t,...r},i)=>(0,K.jsxs)(vi,{children:[(0,K.jsx)(bi,{}),(0,K.jsxs)(fi,{ref:i,className:n(`fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg`,e),...r,children:[t,(0,K.jsxs)(hi,{className:`absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground`,children:[(0,K.jsx)(h,{className:`h-4 w-4`}),(0,K.jsx)(`span`,{className:`sr-only`,children:`Close`})]})]})]}));xi.displayName=fi.displayName;const Si=({className:e,...t})=>(0,K.jsx)(`div`,{className:n(`flex flex-col space-y-1.5 text-center sm:text-left`,e),...t});Si.displayName=`DialogHeader`;const Ci=({className:e,...t})=>(0,K.jsx)(`div`,{className:n(`flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2`,e),...t});Ci.displayName=`DialogFooter`;const wi=U.forwardRef(({className:e,...t},r)=>(0,K.jsx)(pi,{ref:r,className:n(`text-lg font-semibold leading-none tracking-tight`,e),...t}));wi.displayName=pi.displayName;const Ti=U.forwardRef(({className:e,...t},r)=>(0,K.jsx)(mi,{ref:r,className:n(`text-sm text-muted-foreground`,e),...t}));Ti.displayName=mi.displayName;function Ei(){let e=async()=>{try{let e=_.getCurrentSession();await g.clearSession(e.id),_.clearSession();let t=document.querySelectorAll(`img[src^="blob:"]`);return t.forEach(e=>{let t=e.src;t.startsWith(`blob:`)&&URL.revokeObjectURL(t)}),!0}catch(e){return console.error(`Failed to clear all data:`,e),!1}},t=async()=>{try{let e=_.getCurrentSession(),t=await g.getSessionImages(e.id),n=t.reduce((e,t)=>e+t.blob.size,0),r=t.length;return{totalSize:n,totalImages:r,sizeMB:(n/(1024*1024)).toFixed(2)}}catch(e){return console.error(`Failed to get data usage:`,e),null}};return{clearAllData:e,getDataUsage:t}}function Di(){let[e,t]=(0,U.useState)(!1),[n,r]=(0,U.useState)(null),{clearAllData:i,getDataUsage:a}=Ei(),s=async()=>{let e=await a();r(e)},l=async()=>{let e=await i();e&&r({totalImages:0,sizeMB:`0.00`})};return(0,K.jsxs)(gi,{open:e,onOpenChange:t,children:[(0,K.jsx)(_i,{asChild:!0,children:(0,K.jsxs)(o,{variant:`ghost`,size:`sm`,className:`text-graphite-60 hover:text-graphite-90`,onClick:s,children:[(0,K.jsx)(m,{className:`h-4 w-4 mr-1`}),`Privacy`]})}),(0,K.jsxs)(xi,{className:`max-w-md`,children:[(0,K.jsxs)(Si,{children:[(0,K.jsxs)(wi,{className:`flex items-center gap-2`,children:[(0,K.jsx)(m,{className:`h-5 w-5 text-success-green`}),`Privacy & Data Protection`]}),(0,K.jsx)(Ti,{children:`Your privacy is our priority. Here's how we handle your images.`})]}),(0,K.jsxs)(`div`,{className:`space-y-4`,children:[(0,K.jsxs)(`div`,{className:`space-y-3`,children:[(0,K.jsxs)(`div`,{className:`flex items-start gap-3`,children:[(0,K.jsx)(w,{className:`h-5 w-5 text-blue-500 mt-0.5`}),(0,K.jsxs)(`div`,{children:[(0,K.jsx)(`h4`,{className:`font-medium text-sm`,children:`Local Storage Only`}),(0,K.jsx)(`p`,{className:`text-xs text-graphite-60`,children:`Images are stored temporarily in your browser's IndexedDB, never on our servers`})]})]}),(0,K.jsxs)(`div`,{className:`flex items-start gap-3`,children:[(0,K.jsx)(m,{className:`h-5 w-5 text-success-green mt-0.5`}),(0,K.jsxs)(`div`,{children:[(0,K.jsx)(`h4`,{className:`font-medium text-sm`,children:`No Server Upload`}),(0,K.jsx)(`p`,{className:`text-xs text-graphite-60`,children:`AI analysis happens client-side, images never leave your device`})]})]}),(0,K.jsxs)(`div`,{className:`flex items-start gap-3`,children:[(0,K.jsx)(u,{className:`h-5 w-5 text-orange-500 mt-0.5`}),(0,K.jsxs)(`div`,{children:[(0,K.jsx)(`h4`,{className:`font-medium text-sm`,children:`Auto-Cleanup`}),(0,K.jsx)(`p`,{className:`text-xs text-graphite-60`,children:`Images are automatically deleted after 24 hours or when you close the tab`})]})]})]}),n&&(0,K.jsxs)(`div`,{className:`border-t pt-4`,children:[(0,K.jsx)(`h4`,{className:`font-medium text-sm mb-2`,children:`Current Usage`}),(0,K.jsxs)(`div`,{className:`flex gap-2`,children:[(0,K.jsxs)(c,{variant:`outline`,children:[n.totalImages,` images`]}),(0,K.jsxs)(c,{variant:`outline`,children:[n.sizeMB,` MB`]})]})]}),(0,K.jsxs)(`div`,{className:`border-t pt-4 space-y-2`,children:[(0,K.jsx)(`h4`,{className:`font-medium text-sm`,children:`Manual Controls`}),(0,K.jsxs)(o,{variant:`secondary`,size:`sm`,onClick:l,className:`w-full`,children:[(0,K.jsx)(u,{className:`h-4 w-4 mr-2`}),`Clear All Data Now`]}),(0,K.jsx)(`p`,{className:`text-xs text-graphite-60`,children:`This will remove all images and analysis results from your browser`})]}),(0,K.jsx)(`div`,{className:`bg-blue-50 p-3 rounded-md`,children:(0,K.jsxs)(`div`,{className:`flex gap-2`,children:[(0,K.jsx)(E,{className:`h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0`}),(0,K.jsx)(`p`,{className:`text-xs text-blue-700`,children:`For maximum privacy, use incognito/private browsing mode. All data will be automatically deleted when you close the window.`})]})})]})]})]})}export{w as Database,T as FileUp,Di as PrivacyNotice,bt as useDropzone};