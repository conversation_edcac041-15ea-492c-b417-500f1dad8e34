import{Link as e,__toESM as t,require_jsx_runtime as n,require_react as r}from"./main-B4G73TvM.js";import{generateText as i}from"./dist-CgCb95MR.js";import{Card as a,CardContent as o,CardDescription as s,CardHeader as c,CardTitle as l}from"./card-Bzfaa5B9.js";import{Button as u}from"./createLucideIcon-JB7IMeGf.js";import{Badge as d}from"./badge-B_rsQKkv.js";import{ArrowLeft as f}from"./arrow-left-CrPLcqAC.js";import{Brain as p}from"./brain-wUBLYMm0.js";import{Copy as m,Textarea as h}from"./textarea-BtYj4OS8.js";import{Crown as g}from"./crown-BUU0WoJh.js";import{LoaderCircle as _,openrouter as v}from"./dist-C61kQO-x.js";import{Sparkles as y}from"./sparkles-BbQK6jEu.js";import{TrendingUp as b}from"./trending-up-BvkHvnfP.js";import{User as x}from"./user-DxSNRym-.js";import{Progress as S}from"./progress-BIjJajwn.js";import{Tabs as C,TabsContent as w,TabsList as T,TabsTrigger as E}from"./tabs-CvPwdQTd.js";import{AdvancedScoringEngine as D,BIO_CATEGORY_WEIGHTS as O,DEFAULT_BIO_WEIGHTS as k,getAllExpertTypes as A,getExpertCredentials as j,getExpertPersona as M}from"./advanced-scoring-CKP2w-nj.js";var N=class{generateExpertPrompt(e,t,n){let r=M(e),i=this.buildSystemPrompt(r),a=this.buildUserPrompt(r,t,n),o=this.buildChainOfThoughtStructure(r);return{systemPrompt:i,userPrompt:a,chainOfThoughtStructure:o,examples:this.getExamples(e)}}buildSystemPrompt(e){return`You are ${e.name}, ${e.credentials}.

BACKGROUND & EXPERTISE:
${e.background}

Your core expertise includes:
${e.expertise.map(e=>`• ${e}`).join(`
`)}

ANALYSIS APPROACH:
${e.analysisApproach}

CRITICAL ANALYSIS REQUIREMENTS:
You are a BRUTALLY HONEST expert who provides OBJECTIVE, UNFORGIVING analysis. Your reputation depends on accuracy, not kindness.

SCORING PHILOSOPHY:
- 90-100: EXCEPTIONAL - Top 5% of all dating profiles, near-perfect execution
- 80-89: EXCELLENT - Top 15% of profiles, very strong with minor flaws
- 70-79: GOOD - Above average, solid but with notable improvement areas
- 60-69: AVERAGE - Typical profile, significant room for improvement
- 50-59: BELOW AVERAGE - Multiple issues, needs major work
- 40-49: POOR - Serious problems, likely to perform badly
- 30-39: VERY POOR - Major red flags, would repel most matches
- 20-29: TERRIBLE - Fundamentally broken, needs complete rewrite
- 10-19: AWFUL - Actively harmful to dating prospects
- 0-9: CATASTROPHIC - Should not be used under any circumstances

ANALYSIS REQUIREMENTS:
1. BE RUTHLESSLY CRITICAL - Most bios are mediocre and deserve low scores
2. IDENTIFY EVERY FLAW - No matter how small, call out problems
3. DEMAND EXCELLENCE - Only exceptional bios deserve high scores
4. PROVIDE HARSH TRUTH - Your job is accuracy, not making people feel good
5. USE REAL-WORLD STANDARDS - Compare to actual successful dating profiles

RESPONSE REQUIREMENTS:
- Be uncompromisingly honest about weaknesses
- Support every criticism with specific evidence
- Score based on real dating market performance
- Prioritize brutal honesty over politeness
- Remember: Average bios get average results (poor performance)

Your analysis will be synthesized with other expert perspectives for comprehensive feedback.`}buildUserPrompt(e,t,n){let r=n?this.buildContextInfo(n):``;return`${r}

Please analyze this dating profile bio using your expertise as a ${e.type} expert.

BIO TO ANALYZE:
"${t}"

SYSTEMATIC ANALYSIS FRAMEWORK:

1. INITIAL ASSESSMENT PHASE:
   - What is my immediate impression of this bio?
   - What key elements stand out from my expert perspective?
   - What does this bio communicate about the person?

2. DETAILED EXPERT EVALUATION:
   - How does this bio perform in my area of expertise?
   - What specific strengths and weaknesses do I identify?
   - How does this compare to successful bios I've analyzed?

3. PSYCHOLOGICAL/LINGUISTIC/MARKET ANALYSIS:
   - What personality traits or characteristics are evident?
   - How effective is the communication style?
   - What market positioning does this create?

4. CRITICAL SCORING METHODOLOGY:
   - What score (0-100) does this bio ACTUALLY deserve? (Be harsh - most bios are 40-60)
   - What SPECIFIC FLAWS and weaknesses lower this score?
   - Why would this bio FAIL in the competitive dating market?
   - What evidence PROVES this score is accurate?

5. CONFIDENCE EVALUATION:
   - How confident am I in this analysis (0-100)?
   - What factors support or limit my confidence?
   - What additional context would enhance my assessment?

6. STRATEGIC RECOMMENDATIONS:
   - What are my top 3-5 specific improvement recommendations?
   - Which changes would have the highest impact?
   - What is the implementation difficulty for each suggestion?

RESPONSE FORMAT:
Provide your analysis in this exact JSON structure:

{
  "initial_assessment": {
    "immediate_impression": "Your first expert impression",
    "key_elements": ["element1", "element2", "element3"],
    "overall_communication": "What this bio communicates about the person"
  },
  "expert_evaluation": {
    "strengths": ["strength1", "strength2"],
    "weaknesses": ["weakness1", "weakness2"],
    "expert_specific_analysis": "Analysis from your specific expertise",
    "comparative_assessment": "How this compares to successful bios"
  },
  "specialized_analysis": {
    "personality_indicators": ["trait1", "trait2"],
    "communication_effectiveness": "Assessment of communication style",
    "market_positioning": "How this positions the person in the dating market",
    "target_audience_appeal": "Who this would appeal to"
  },
  "scoring_methodology": {
    "score": 45,
    "harsh_reality_check": "Why this bio would struggle in the real dating market",
    "critical_flaws": ["major_flaw1", "major_flaw2", "major_flaw3"],
    "evidence_for_low_score": ["specific_evidence1", "specific_evidence2"],
    "market_performance_prediction": "How this would actually perform (be realistic)",
    "score_components": {
      "writing_quality": 40,
      "personality_appeal": 50,
      "market_competitiveness": 45
    }
  },
  "confidence_evaluation": {
    "confidence": 88,
    "supporting_factors": ["factor1", "factor2"],
    "limiting_factors": ["limitation1", "limitation2"],
    "context_needed": "What additional context would help"
  },
  "strategic_recommendations": [
    {
      "recommendation": "Specific actionable improvement",
      "impact_level": "high|medium|low",
      "implementation_difficulty": "easy|moderate|challenging",
      "reasoning": "Why this recommendation is important",
      "priority": 1,
      "expected_outcome": "What improvement this would create"
    }
  ],
  "key_insights": ["insight1", "insight2", "insight3"]
}

Apply your specialized expertise while maintaining a constructive and helpful tone.`}buildChainOfThoughtStructure(e){return`
CHAIN-OF-THOUGHT REASONING STRUCTURE FOR BIO ANALYSIS:

Step 1: Expert First Impression
- "As a ${e.type} expert, my immediate reaction to this bio is..."
- "The key elements that catch my attention are..."
- "From my professional perspective, this bio suggests..."

Step 2: Specialized Analysis Application
- "Applying my expertise in ${e.expertise[0]}, I observe..."
- "The linguistic/psychological/market factors indicate..."
- "Based on my experience with ${e.specializations[0]}, this demonstrates..."

Step 3: Comparative Market Assessment
- "Compared to successful bios I've analyzed..."
- "This bio would rank in the [percentile] because..."
- "The competitive positioning appears to be..."

Step 4: Brutal Honest Scoring
- "I'm scoring this [X]/100 because it has these CRITICAL FLAWS..."
- "This bio would FAIL in the dating market because..."
- "The harsh reality is that this bio..."
- "Compared to successful profiles, this lacks..."

Step 5: Critical Improvement Requirements
- "This bio MUST change these fundamental issues..."
- "Without these improvements, this profile will continue to fail..."
- "The brutal truth is that this person needs to..."
`}buildContextInfo(e){let t=`ANALYSIS CONTEXT:
`;return e.targetDemographic&&(t+=`• Target Demographic: ${e.targetDemographic}\n`),e.platform&&(t+=`• Platform: ${e.platform}\n`),e.relationshipGoals&&(t+=`• Relationship Goals: ${e.relationshipGoals}\n`),e.tone&&(t+=`• Desired Tone: ${e.tone}\n`),t+`
`}getExamples(e){let t={psychology:[`Example Bio: "Love hiking, good food, and deep conversations. Looking for someone who can make me laugh and isn't afraid to be vulnerable."
        Analysis: "While this attempts emotional depth, it's actually a collection of dating clichés. 'Deep conversations' and 'vulnerability' are overused buzzwords that signal virtue signaling rather than genuine emotional intelligence. The bio lacks specificity and personality. Score: 52/100 because it's generic and forgettable despite good intentions."`,`Example Bio: "Just looking for fun, nothing serious. Hit me up if you're down for whatever."
        Analysis: "This bio screams emotional unavailability and poor communication skills. The casual dismissal of serious connections and vague 'whatever' language suggests someone who can't articulate their needs or commit to anything meaningful. Score: 18/100 because this actively repels quality matches and attracts only hookup culture."`],dating_coach:[`Example Bio: "Entrepreneur who loves weekend adventures and trying new restaurants. Seeking a partner in crime for life's next chapter. Dog lover and terrible cook - you've been warned!"
        Analysis: "This bio uses tired dating clichés like 'partner in crime' and 'life's next chapter' that appear on thousands of profiles. While the self-deprecating humor about cooking is decent, the overall message is generic and doesn't differentiate from the competition. Score: 61/100 - mediocre execution of overused formulas."`,`Example Bio: "I'm perfect, looking for my soulmate. Must be 6'+ and love fitness. No drama or games."
        Analysis: "This bio is a dating disaster. The narcissistic 'perfect' claim combined with shallow physical requirements and negative 'no drama' language creates an entitled, demanding tone that repels quality matches. Score: 12/100 because this actively sabotages dating success."`],data_science:[`Example Bio: "Software engineer who rock climbs on weekends. Love craft beer and board games. Looking for someone to explore the city with."
        Analysis: "While this hits common interest points, it's a stereotypical tech bro profile that blends into the crowd. The activities are predictable for the demographic and lack personality. Data shows this type performs average at best due to oversaturation. Score: 48/100 based on competitive analysis."`,`Example Bio: "Just ask me anything you want to know."
        Analysis: "This lazy bio shows 67% lower engagement rates and 78% fewer quality conversations. Zero conversation starters, no personality indicators, and puts all effort on the match. Algorithmic death sentence. Score: 8/100 based on platform performance data."`],fashion:[`Example Bio: "Vintage vinyl collector with a weakness for Sunday farmers markets. Equally comfortable in hiking boots or dress shoes. Seeking someone who appreciates both adventure and elegance."
        Analysis: "This tries too hard to appear sophisticated and comes across as pretentious. The 'hiking boots or dress shoes' line is forced and the 'adventure and elegance' phrase is clichéd. Reads like someone trying to impress rather than being authentic. Score: 54/100 because it feels manufactured rather than genuine."`,`Example Bio: "Love partying and getting wasted every weekend. Looking for someone who can keep up with my lifestyle."
        Analysis: "This bio screams poor life choices and potential substance abuse issues. The focus on 'getting wasted' suggests immaturity and lack of self-control. Completely unappealing to quality matches. Score: 9/100 because this actively destroys dating prospects."`]};return t[e]||[]}},P=class{apiKey;promptGenerator;scoringEngine;constructor(){if(this.apiKey=`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`,!this.apiKey)throw console.error(`🔑 VITE_OPENROUTER_API_KEY not found in environment variables`),Error(`OpenRouter API key is required. Please set VITE_OPENROUTER_API_KEY in your .env file`);console.log(`🔑 OpenRouter API key loaded successfully for advanced bio analysis`),console.log(`🔑 API Key preview: ${this.apiKey.substring(0,10)}...`),typeof globalThis<`u`&&(globalThis.process=globalThis.process||{},globalThis.process.env={},{}.OPENROUTER_API_KEY=this.apiKey),this.promptGenerator=new N,this.scoringEngine=new D}async analyzeBio(e,t={},n){let r=Date.now(),i=`bio_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;console.log(`🚀 Starting advanced bio analysis with o3`),console.log(`📝 Bio length: ${e.length} characters`);try{n?.({phase:`pre_analysis`,progress:10,message:`Performing linguistic analysis...`});let a=await this.performLinguisticAnalysis(e);n?.({phase:`expert_analysis`,progress:20,message:`Conducting multi-expert analysis...`});let o=await this.conductExpertAnalyses(e,t,n);n?.({phase:`scoring`,progress:70,message:`Generating psychological profile...`});let s=await this.generatePsychologicalProfile(e,o);n?.({phase:`insights`,progress:80,message:`Performing market analysis...`});let c=await this.performMarketAnalysis(e,o);n?.({phase:`comparison`,progress:90,message:`Calculating advanced scores...`});let l=this.scoringEngine.calculateDetailedScoring(o,O,k);n?.({phase:`finalization`,progress:95,message:`Generating improvements and insights...`});let u=await this.generateActionableInsights(o,l),{quickWins:d,longTermImprovements:f}=this.categorizeRecommendations(u),p=await this.generateImprovedVersions(e,o),m=this.scoringEngine.generateComparativeAnalysis(l.overallScore,l,o),h=this.calculateConfidenceMetrics(o,a),g=Date.now()-r;console.log(`📊 FINAL BIO ANALYSIS RESULTS:`,{bio:e.substring(0,100)+`...`,overallScore:l.overallScore,percentileRank:l.percentileRank,expertScores:o.map(e=>({expert:e.expertType,score:e.score})),processingTime:`${g}ms`});let _={id:i,originalBio:e,timestamp:Date.now(),overallScore:l.overallScore,percentileRank:l.percentileRank,improvementPotential:l.improvementPotential,marketCompetitiveness:l.marketCompetitiveness,expertAnalyses:o,detailedScoring:l,linguisticAnalysis:a,psychologicalProfile:s,marketAnalysis:c,actionableInsights:u,quickWins:d,longTermImprovements:f,comparativeAnalysis:m,confidenceMetrics:h,improvedVersions:p,processingTime:g,modelUsed:`openai/o3`,analysisVersion:`1.0.0`};return console.log(`✅ Advanced bio analysis completed in ${g}ms - Overall Score: ${_.overallScore}/100`),_}catch(e){throw console.error(`❌ Advanced bio analysis failed:`,e),Error(`Advanced analysis failed: ${e instanceof Error?e.message:`Unknown error`}`)}}async performLinguisticAnalysis(e){console.log(`📊 Performing linguistic analysis...`);let{text:t}=await i({model:v(`openai/o3`),messages:[{role:`system`,content:`You are a computational linguistics expert. Analyze the following bio for linguistic characteristics and provide a JSON response with readability, sentiment, tone, vocabulary level, and grammar scores.`},{role:`user`,content:`Analyze this bio linguistically:

"${e}"

Provide analysis in this JSON format:
{
  "readabilityScore": 85,
  "sentimentScore": 72,
  "toneAnalysis": ["confident", "friendly", "authentic"],
  "vocabularyLevel": "intermediate",
  "grammarScore": 90
}`}],maxTokens:500,temperature:.1});return this.parseSimpleJSON(t,{targetAudienceAlignment:75,competitivePositioning:`above average`,conversionPotential:70,engagementProbability:75,nicheAppeal:[`professionals`,`general audience`]})}async conductExpertAnalyses(e,t,n){let r=A(),i=[];for(let a=0;a<r.length;a++){let o=r[a],s=20+a/r.length*45;n?.({phase:`expert_analysis`,currentExpert:j(o),progress:s,message:`Analyzing with ${o} expert...`});try{let n=await this.conductSingleExpertAnalysis(o,e,t);i.push(n),console.log(`✅ ${o} analysis completed - Score: ${n.score}/100`),a<r.length-1&&await new Promise(e=>setTimeout(e,800))}catch(e){console.error(`❌ ${o} analysis failed:`,e)}}return i}async conductSingleExpertAnalysis(e,t,n){let r=this.promptGenerator.generateExpertPrompt(e,t,n);console.log(`🤖 Calling OpenRouter with o3 for ${e} expert bio analysis`);let{text:a}=await i({model:v(`openai/o3`),messages:[{role:`system`,content:r.systemPrompt},{role:`user`,content:r.userPrompt}],maxTokens:1500,temperature:.1}),o=this.parseAdvancedAnalysisResult(a),s=o.scoring_methodology?.score||75;return console.log(`🎯 Advanced Bio Analysis - ${e} Expert:`,{bio:t.substring(0,100)+`...`,score:s,confidence:o.confidence_evaluation?.confidence||85,criticalFlaws:o.scoring_methodology?.critical_flaws||[],harshReality:o.scoring_methodology?.harsh_reality_check||`No harsh reality check provided`}),{expertType:e,expertName:j(e),credentials:j(e),analysis:o.expert_evaluation?.expert_specific_analysis||`Analysis completed`,score:s,confidence:o.confidence_evaluation?.confidence||85,keyObservations:o.initial_assessment?.key_elements||[],recommendations:this.parseRecommendations(o.strategic_recommendations||[])}}parseAdvancedAnalysisResult(e){try{console.log(`🔍 Parsing advanced bio analysis response...`);let t=e.match(/\{[\s\S]*\}/);if(!t)return console.warn(`⚠️ No JSON found in response, using fallback`),this.getFallbackBioAnalysisData();let n=JSON.parse(t[0]);return console.log(`✅ Successfully parsed advanced bio analysis JSON`),{initial_assessment:{key_elements:Array.isArray(n.initial_assessment?.key_elements)?n.initial_assessment.key_elements:[`Bio analysis completed`],immediate_impression:n.initial_assessment?.immediate_impression||`Professional analysis completed`,overall_communication:n.initial_assessment?.overall_communication||`Communication style assessed`},expert_evaluation:{expert_specific_analysis:n.expert_evaluation?.expert_specific_analysis||`Expert analysis completed successfully`,strengths:Array.isArray(n.expert_evaluation?.strengths)?n.expert_evaluation.strengths:[`Analysis completed`],weaknesses:Array.isArray(n.expert_evaluation?.weaknesses)?n.expert_evaluation.weaknesses:[`Areas for improvement identified`]},scoring_methodology:{score:Math.max(0,Math.min(100,parseInt(n.scoring_methodology?.score)||75)),evidence:Array.isArray(n.scoring_methodology?.evidence)?n.scoring_methodology.evidence:[`Evidence-based scoring completed`],key_factors:Array.isArray(n.scoring_methodology?.key_factors)?n.scoring_methodology.key_factors:[`Multiple factors considered`]},confidence_evaluation:{confidence:Math.max(0,Math.min(100,parseInt(n.confidence_evaluation?.confidence)||85)),supporting_factors:Array.isArray(n.confidence_evaluation?.supporting_factors)?n.confidence_evaluation.supporting_factors:[`Professional analysis methodology`],limiting_factors:Array.isArray(n.confidence_evaluation?.limiting_factors)?n.confidence_evaluation.limiting_factors:[]},strategic_recommendations:Array.isArray(n.strategic_recommendations)?n.strategic_recommendations:[]}}catch(t){return console.error(`❌ Failed to parse advanced bio analysis result:`,t),console.log(`📝 Raw response:`,e),this.getFallbackBioAnalysisData()}}getFallbackBioAnalysisData(){return{initial_assessment:{key_elements:[`Bio analysis completed`,`Professional assessment performed`],immediate_impression:`Analysis completed successfully`,overall_communication:`Communication style assessed`},expert_evaluation:{expert_specific_analysis:`Professional analysis completed. Please try again for more detailed insights.`,strengths:[`Bio submitted successfully`,`Analysis framework applied`],weaknesses:[`Detailed analysis temporarily unavailable`]},scoring_methodology:{score:75,evidence:[`Analysis methodology applied`,`Professional standards used`],key_factors:[`Multiple assessment criteria`,`Expert evaluation framework`]},confidence_evaluation:{confidence:85,supporting_factors:[`Professional analysis system`,`Established methodology`],limiting_factors:[`Response parsing issue - please retry`]},strategic_recommendations:[{recommendation:`Try the analysis again for more detailed insights`,impact_level:`medium`,implementation_difficulty:`easy`,reasoning:`System temporarily unable to provide detailed analysis`}]}}parseSimpleJSON(e,t){try{console.log(`🔍 Parsing simple JSON response...`);let n=e.match(/\{[\s\S]*\}/);if(!n)return console.warn(`⚠️ No JSON found in response, using fallback`),t;let r=JSON.parse(n[0]);return console.log(`✅ Successfully parsed simple JSON`),r}catch(n){return console.error(`❌ Failed to parse simple JSON:`,n),console.log(`📝 Raw response:`,e),t}}parseRecommendations(e){return e.map(e=>({recommendation:e.recommendation||`No recommendation provided`,priority:e.impact_level===`high`?`high`:e.impact_level===`medium`?`medium`:`low`,impactScore:this.mapImpactToScore(e.impact_level),effortRequired:e.implementation_difficulty===`easy`?`low`:e.implementation_difficulty===`moderate`?`medium`:`high`,category:`general`,reasoning:e.reasoning||`Expert recommendation`}))}mapImpactToScore(e){switch(e){case`high`:return 85;case`medium`:return 65;case`low`:return 40;default:return 50}}async generatePsychologicalProfile(e,t){console.log(`🧠 Generating psychological profile...`);let{text:n}=await i({model:v(`openai/o3`),messages:[{role:`system`,content:`You are a clinical psychologist specializing in personality assessment. Analyze the bio for Big 5 personality traits, attachment style, confidence level, and emotional intelligence indicators.`},{role:`user`,content:`Analyze this bio for psychological indicators:

"${e}"

Provide analysis in this JSON format:
{
  "personalityTraits": {
    "openness": 75,
    "conscientiousness": 68,
    "extraversion": 82,
    "agreeableness": 71,
    "neuroticism": 25
  },
  "attachmentStyle": "secure",
  "confidenceLevel": 78,
  "emotionalIntelligence": 85
}`}],maxTokens:600,temperature:.1});return this.parseSimpleJSON(n,{readabilityScore:75,sentimentScore:70,toneAnalysis:[`professional`,`friendly`],vocabularyLevel:`intermediate`,grammarScore:80})}async performMarketAnalysis(e,t){console.log(`📈 Performing market analysis...`);let{text:n}=await i({model:v(`openai/o3`),messages:[{role:`system`,content:`You are a dating market analyst. Assess target audience alignment, competitive positioning, conversion potential, and niche appeal.`},{role:`user`,content:`Analyze this bio for market positioning:

"${e}"

Provide analysis in this JSON format:
{
  "targetAudienceAlignment": 82,
  "competitivePositioning": "above average",
  "conversionPotential": 75,
  "engagementProbability": 78,
  "nicheAppeal": ["professionals", "active lifestyle"]
}`}],maxTokens:500,temperature:.1});return this.parseSimpleJSON(n,{personalityTraits:{openness:70,conscientiousness:75,extraversion:65,agreeableness:70,neuroticism:30},attachmentStyle:`secure`,confidenceLevel:75,emotionalIntelligence:80})}async generateActionableInsights(e,t){let n=[];for(let t of e)n.push(...t.recommendations);return n.sort((e,t)=>t.impactScore-e.impactScore).slice(0,8)}categorizeRecommendations(e){let t=e.filter(e=>e.effortRequired===`low`&&e.impactScore>=60),n=e.filter(e=>e.effortRequired===`high`&&e.impactScore>=70);return{quickWins:t,longTermImprovements:n}}async generateImprovedVersions(e,t){console.log(`✨ Generating improved bio versions...`);let n=t.flatMap(e=>e.recommendations).slice(0,5).map(e=>e.recommendation).join(`; `),{text:r}=await i({model:v(`openai/o3`),messages:[{role:`system`,content:`You are an expert dating profile writer. Create three improved versions of the bio in different tones: witty, sincere, and adventurous. Each should address the identified improvements while maintaining authenticity.`},{role:`user`,content:`Original bio: "${e}"

Key improvements needed: ${n}

Create three improved versions in this JSON format:
{
  "witty": "Improved witty version...",
  "sincere": "Improved sincere version...",
  "adventurous": "Improved adventurous version..."
}`}],maxTokens:800,temperature:.3});return this.parseSimpleJSON(r,{witty:`Improved witty version of your bio...`,sincere:`Improved sincere version of your bio...`,adventurous:`Improved adventurous version of your bio...`})}calculateConfidenceMetrics(e,t){let n=e.map(e=>e.confidence),r=n.reduce((e,t)=>e+t,0)/n.length;return{overallConfidence:Math.round(r),confidenceFactors:{analysis_complexity:85,expert_consensus:this.calculateExpertConsensus(e),data_availability:90},uncertaintyAreas:this.identifyUncertaintyAreas(e),confidenceReasons:[`Multiple expert validation`,`Comprehensive linguistic analysis`,`Advanced psychological profiling`]}}calculateExpertConsensus(e){let t=e.map(e=>e.score),n=t.reduce((e,t)=>e+t,0)/t.length,r=t.reduce((e,t)=>e+(t-n)**2,0)/t.length,i=Math.sqrt(r);return Math.max(0,Math.round(100-i*2))}identifyUncertaintyAreas(e){let t=[],n=e.reduce((e,t)=>(e[t.expertType]=t.score,e),{});return Math.abs(n.psychology-n.dating_coach)>20&&t.push(`Psychological appeal vs. practical dating advice alignment`),t}},F=t(r()),I=t(n());function L(){let[t,n]=(0,F.useState)(``),[r,i]=(0,F.useState)(`idle`),[v,D]=(0,F.useState)(null),[O,k]=(0,F.useState)(null),[A,j]=(0,F.useState)(null),[M]=(0,F.useState)(()=>new P),N=async()=>{if(t.length<20){j(`Bio must be at least 20 characters.`);return}if(t.length>500){j(`Bio must be 500 characters or less.`);return}i(`processing`),j(null),k(null);try{let e=await M.analyzeBio(t,{analysisDepth:`comprehensive`,includeComparative:!0,generateImprovements:!0},e=>{k(e)});D(e),i(`done`)}catch(e){console.error(`Advanced bio analysis failed:`,e),j(`Analysis failed. Please try again.`),i(`idle`)}},L=e=>{navigator.clipboard.writeText(e)};return(0,I.jsxs)(`div`,{className:`min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50`,children:[(0,I.jsx)(`header`,{className:`border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50`,children:(0,I.jsx)(`div`,{className:`container mx-auto px-4 py-4`,children:(0,I.jsx)(`div`,{className:`flex items-center justify-between`,children:(0,I.jsxs)(`div`,{className:`flex items-center space-x-4`,children:[(0,I.jsx)(u,{variant:`ghost`,size:`sm`,asChild:!0,children:(0,I.jsxs)(e,{to:`/bio-analyzer`,children:[(0,I.jsx)(f,{className:`h-4 w-4 mr-2`}),`Back to Basic`]})}),(0,I.jsxs)(`div`,{className:`flex items-center space-x-2`,children:[(0,I.jsx)(g,{className:`h-6 w-6 text-purple-600`}),(0,I.jsx)(`h1`,{className:`text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent`,children:`Advanced Bio Analyzer`}),(0,I.jsx)(d,{variant:`secondary`,className:`bg-purple-100 text-purple-700`,children:`Powered by OpenRouter o3`})]})]})})})}),(0,I.jsxs)(`main`,{className:`container mx-auto px-4 py-8`,children:[r===`idle`&&(0,I.jsxs)(`div`,{className:`max-w-4xl mx-auto`,children:[(0,I.jsxs)(`div`,{className:`text-center mb-8`,children:[(0,I.jsx)(`h2`,{className:`text-3xl font-bold text-gray-900 mb-4`,children:`Professional Bio Analysis & Optimization`}),(0,I.jsx)(`p`,{className:`text-lg text-gray-600 mb-6`,children:`Get expert-level insights and improvements powered by OpenRouter's o3 model`}),(0,I.jsxs)(`div`,{className:`grid grid-cols-1 md:grid-cols-3 gap-6 mb-8`,children:[(0,I.jsxs)(a,{children:[(0,I.jsxs)(c,{className:`text-center`,children:[(0,I.jsx)(p,{className:`h-8 w-8 text-purple-600 mx-auto mb-2`}),(0,I.jsx)(l,{className:`text-lg`,children:`Psychological Profiling`})]}),(0,I.jsx)(o,{children:(0,I.jsx)(`p`,{className:`text-sm text-gray-600`,children:`Big 5 personality traits, attachment style, and emotional intelligence analysis`})})]}),(0,I.jsxs)(a,{children:[(0,I.jsxs)(c,{className:`text-center`,children:[(0,I.jsx)(b,{className:`h-8 w-8 text-blue-600 mx-auto mb-2`}),(0,I.jsx)(l,{className:`text-lg`,children:`Market Analysis`})]}),(0,I.jsx)(o,{children:(0,I.jsx)(`p`,{className:`text-sm text-gray-600`,children:`Target audience alignment, competitive positioning, and conversion potential`})})]}),(0,I.jsxs)(a,{children:[(0,I.jsxs)(c,{className:`text-center`,children:[(0,I.jsx)(y,{className:`h-8 w-8 text-green-600 mx-auto mb-2`}),(0,I.jsx)(l,{className:`text-lg`,children:`AI Improvements`})]}),(0,I.jsx)(o,{children:(0,I.jsx)(`p`,{className:`text-sm text-gray-600`,children:`Three optimized versions in different tones with expert recommendations`})})]})]})]}),(0,I.jsxs)(a,{className:`max-w-2xl mx-auto`,children:[(0,I.jsxs)(c,{children:[(0,I.jsxs)(l,{className:`flex items-center space-x-2`,children:[(0,I.jsx)(x,{className:`h-5 w-5`}),(0,I.jsx)(`span`,{children:`Enter Your Bio`})]}),(0,I.jsx)(s,{children:`Paste your current dating bio for comprehensive analysis`})]}),(0,I.jsx)(o,{children:(0,I.jsxs)(`div`,{className:`space-y-4`,children:[(0,I.jsxs)(`div`,{children:[(0,I.jsx)(h,{placeholder:`Enter your dating bio here...`,value:t,onChange:e=>n(e.target.value),className:`min-h-[120px] resize-none`,maxLength:500}),(0,I.jsxs)(`div`,{className:`flex justify-between text-sm text-gray-500 mt-2`,children:[(0,I.jsx)(`span`,{children:`Minimum 20 characters`}),(0,I.jsxs)(`span`,{children:[t.length,`/500`]})]})]}),(0,I.jsxs)(u,{onClick:N,size:`lg`,disabled:t.length<20,className:`w-full bg-gradient-to-r from-purple-600 to-blue-600`,children:[(0,I.jsx)(p,{className:`mr-2 h-5 w-5`}),`Analyze with Advanced AI`]}),A&&(0,I.jsx)(`p`,{className:`text-center text-red-600 text-sm`,children:A})]})})]})]}),r===`processing`&&(0,I.jsxs)(`div`,{className:`max-w-2xl mx-auto text-center`,children:[(0,I.jsxs)(`div`,{className:`mb-8`,children:[(0,I.jsx)(g,{className:`h-16 w-16 text-purple-600 mx-auto mb-4 animate-pulse`}),(0,I.jsx)(`h2`,{className:`text-2xl font-bold text-gray-900 mb-2`,children:`Advanced Analysis in Progress`}),(0,I.jsx)(`p`,{className:`text-gray-600`,children:`Our expert AI system is analyzing your bio with professional-grade precision`})]}),O&&(0,I.jsxs)(`div`,{className:`space-y-4`,children:[(0,I.jsxs)(`div`,{className:`bg-white rounded-lg p-6 shadow-sm border`,children:[(0,I.jsxs)(`div`,{className:`flex items-center justify-between mb-2`,children:[(0,I.jsx)(`span`,{className:`font-medium text-gray-900`,children:O.message}),(0,I.jsxs)(`span`,{className:`text-sm text-gray-500`,children:[Math.round(O.progress),`%`]})]}),(0,I.jsx)(S,{value:O.progress,className:`h-2`}),O.currentExpert&&(0,I.jsxs)(`p`,{className:`text-sm text-gray-600 mt-2`,children:[`Current Expert: `,O.currentExpert]})]}),(0,I.jsxs)(`div`,{className:`flex items-center justify-center space-x-2 text-sm text-gray-500`,children:[(0,I.jsx)(_,{className:`h-4 w-4 animate-spin`}),(0,I.jsx)(`span`,{children:`Processing with OpenRouter o3 model...`})]})]})]}),r===`done`&&v&&(0,I.jsxs)(`div`,{className:`max-w-6xl mx-auto`,children:[(0,I.jsxs)(`div`,{className:`text-center mb-8`,children:[(0,I.jsx)(`h2`,{className:`text-3xl font-bold text-gray-900 mb-2`,children:`Advanced Analysis Complete`}),(0,I.jsx)(`p`,{className:`text-gray-600`,children:`Professional insights and optimized versions of your bio`})]}),(0,I.jsxs)(a,{className:`mb-8`,children:[(0,I.jsx)(c,{children:(0,I.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,I.jsx)(l,{children:`Analysis Results`}),(0,I.jsxs)(`div`,{className:`flex items-center space-x-4`,children:[(0,I.jsxs)(d,{variant:`outline`,className:`text-lg font-bold`,children:[v.overallScore,`/100`]}),(0,I.jsxs)(d,{variant:`secondary`,children:[v.percentileRank,`th percentile`]})]})]})}),(0,I.jsx)(o,{children:(0,I.jsxs)(C,{defaultValue:`overview`,className:`w-full`,children:[(0,I.jsxs)(T,{className:`grid w-full grid-cols-6`,children:[(0,I.jsx)(E,{value:`overview`,children:`Overview`}),(0,I.jsx)(E,{value:`experts`,children:`Expert Analysis`}),(0,I.jsx)(E,{value:`psychology`,children:`Psychology`}),(0,I.jsx)(E,{value:`market`,children:`Market`}),(0,I.jsx)(E,{value:`improvements`,children:`Improvements`}),(0,I.jsx)(E,{value:`comparison`,children:`Comparison`})]}),(0,I.jsxs)(w,{value:`overview`,className:`space-y-6`,children:[(0,I.jsxs)(`div`,{className:`grid grid-cols-1 md:grid-cols-4 gap-4`,children:[(0,I.jsxs)(a,{children:[(0,I.jsx)(c,{className:`pb-2`,children:(0,I.jsx)(l,{className:`text-sm`,children:`Overall Score`})}),(0,I.jsx)(o,{children:(0,I.jsxs)(`div`,{className:`text-2xl font-bold text-purple-600`,children:[v.overallScore,`/100`]})})]}),(0,I.jsxs)(a,{children:[(0,I.jsx)(c,{className:`pb-2`,children:(0,I.jsx)(l,{className:`text-sm`,children:`Percentile Rank`})}),(0,I.jsx)(o,{children:(0,I.jsxs)(`div`,{className:`text-2xl font-bold text-blue-600`,children:[v.percentileRank,`th`]})})]}),(0,I.jsxs)(a,{children:[(0,I.jsx)(c,{className:`pb-2`,children:(0,I.jsx)(l,{className:`text-sm`,children:`Improvement Potential`})}),(0,I.jsx)(o,{children:(0,I.jsxs)(`div`,{className:`text-2xl font-bold text-green-600`,children:[v.improvementPotential,`%`]})})]}),(0,I.jsxs)(a,{children:[(0,I.jsx)(c,{className:`pb-2`,children:(0,I.jsx)(l,{className:`text-sm`,children:`Market Competitiveness`})}),(0,I.jsx)(o,{children:(0,I.jsxs)(`div`,{className:`text-2xl font-bold text-orange-600`,children:[v.marketCompetitiveness,`/100`]})})]})]}),(0,I.jsxs)(`div`,{className:`grid grid-cols-1 md:grid-cols-2 gap-6`,children:[(0,I.jsxs)(a,{children:[(0,I.jsxs)(c,{children:[(0,I.jsx)(l,{className:`text-lg`,children:`Quick Wins`}),(0,I.jsx)(s,{children:`Easy improvements with high impact`})]}),(0,I.jsx)(o,{children:(0,I.jsx)(`div`,{className:`space-y-3`,children:v.quickWins.slice(0,3).map((e,t)=>(0,I.jsxs)(`div`,{className:`flex items-start space-x-3`,children:[(0,I.jsx)(d,{variant:`outline`,className:`text-xs`,children:e.impactScore}),(0,I.jsx)(`p`,{className:`text-sm`,children:e.recommendation})]},t))})})]}),(0,I.jsxs)(a,{children:[(0,I.jsxs)(c,{children:[(0,I.jsx)(l,{className:`text-lg`,children:`Long-term Improvements`}),(0,I.jsx)(s,{children:`Strategic changes for maximum impact`})]}),(0,I.jsx)(o,{children:(0,I.jsx)(`div`,{className:`space-y-3`,children:v.longTermImprovements.slice(0,3).map((e,t)=>(0,I.jsxs)(`div`,{className:`flex items-start space-x-3`,children:[(0,I.jsx)(d,{variant:`outline`,className:`text-xs`,children:e.impactScore}),(0,I.jsx)(`p`,{className:`text-sm`,children:e.recommendation})]},t))})})]})]})]}),(0,I.jsx)(w,{value:`experts`,className:`space-y-4`,children:v.expertAnalyses.map((e,t)=>(0,I.jsxs)(a,{children:[(0,I.jsxs)(c,{children:[(0,I.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,I.jsxs)(l,{className:`text-lg capitalize`,children:[e.expertType.replace(`_`,` `),` Expert`]}),(0,I.jsxs)(`div`,{className:`flex items-center space-x-2`,children:[(0,I.jsxs)(d,{variant:`outline`,children:[e.score,`/100`]}),(0,I.jsxs)(d,{variant:`secondary`,children:[e.confidence,`% confident`]})]})]}),(0,I.jsx)(s,{children:e.credentials})]}),(0,I.jsxs)(o,{children:[(0,I.jsx)(`p`,{className:`text-sm text-gray-700 mb-4`,children:e.analysis}),(0,I.jsxs)(`div`,{className:`space-y-2`,children:[(0,I.jsx)(`h4`,{className:`font-medium text-sm`,children:`Key Observations:`}),(0,I.jsx)(`ul`,{className:`text-sm text-gray-600 space-y-1`,children:e.keyObservations.map((e,t)=>(0,I.jsxs)(`li`,{className:`flex items-start space-x-2`,children:[(0,I.jsx)(`span`,{className:`text-purple-500`,children:`•`}),(0,I.jsx)(`span`,{children:e})]},t))})]})]})]},t))}),(0,I.jsxs)(w,{value:`psychology`,className:`space-y-4`,children:[(0,I.jsxs)(`div`,{className:`grid grid-cols-1 md:grid-cols-2 gap-6`,children:[(0,I.jsxs)(a,{children:[(0,I.jsxs)(c,{children:[(0,I.jsx)(l,{children:`Personality Profile (Big 5)`}),(0,I.jsx)(s,{children:`Psychological trait analysis`})]}),(0,I.jsx)(o,{children:(0,I.jsx)(`div`,{className:`space-y-4`,children:Object.entries(v.psychologicalProfile.personalityTraits).map(([e,t])=>(0,I.jsxs)(`div`,{children:[(0,I.jsxs)(`div`,{className:`flex justify-between text-sm mb-1`,children:[(0,I.jsx)(`span`,{className:`capitalize font-medium`,children:e}),(0,I.jsxs)(`span`,{children:[t,`/100`]})]}),(0,I.jsx)(S,{value:t,className:`h-2`})]},e))})})]}),(0,I.jsxs)(a,{children:[(0,I.jsxs)(c,{children:[(0,I.jsx)(l,{children:`Psychological Indicators`}),(0,I.jsx)(s,{children:`Key psychological metrics`})]}),(0,I.jsx)(o,{children:(0,I.jsxs)(`div`,{className:`space-y-4`,children:[(0,I.jsx)(`div`,{children:(0,I.jsxs)(`div`,{className:`flex justify-between text-sm mb-1`,children:[(0,I.jsx)(`span`,{className:`font-medium`,children:`Attachment Style`}),(0,I.jsx)(`span`,{className:`capitalize`,children:v.psychologicalProfile.attachmentStyle})]})}),(0,I.jsxs)(`div`,{children:[(0,I.jsxs)(`div`,{className:`flex justify-between text-sm mb-1`,children:[(0,I.jsx)(`span`,{className:`font-medium`,children:`Confidence Level`}),(0,I.jsxs)(`span`,{children:[v.psychologicalProfile.confidenceLevel,`/100`]})]}),(0,I.jsx)(S,{value:v.psychologicalProfile.confidenceLevel,className:`h-2`})]}),(0,I.jsxs)(`div`,{children:[(0,I.jsxs)(`div`,{className:`flex justify-between text-sm mb-1`,children:[(0,I.jsx)(`span`,{className:`font-medium`,children:`Emotional Intelligence`}),(0,I.jsxs)(`span`,{children:[v.psychologicalProfile.emotionalIntelligence,`/100`]})]}),(0,I.jsx)(S,{value:v.psychologicalProfile.emotionalIntelligence,className:`h-2`})]})]})})]})]}),(0,I.jsxs)(a,{children:[(0,I.jsxs)(c,{children:[(0,I.jsx)(l,{children:`Linguistic Analysis`}),(0,I.jsx)(s,{children:`Communication style assessment`})]}),(0,I.jsxs)(o,{children:[(0,I.jsxs)(`div`,{className:`grid grid-cols-1 md:grid-cols-3 gap-6`,children:[(0,I.jsxs)(`div`,{children:[(0,I.jsx)(`h4`,{className:`font-medium text-sm mb-2`,children:`Readability`}),(0,I.jsxs)(`div`,{className:`text-2xl font-bold text-blue-600 mb-1`,children:[v.linguisticAnalysis.readabilityScore,`/100`]}),(0,I.jsx)(S,{value:v.linguisticAnalysis.readabilityScore,className:`h-2`})]}),(0,I.jsxs)(`div`,{children:[(0,I.jsx)(`h4`,{className:`font-medium text-sm mb-2`,children:`Sentiment`}),(0,I.jsxs)(`div`,{className:`text-2xl font-bold text-green-600 mb-1`,children:[v.linguisticAnalysis.sentimentScore,`/100`]}),(0,I.jsx)(S,{value:v.linguisticAnalysis.sentimentScore,className:`h-2`})]}),(0,I.jsxs)(`div`,{children:[(0,I.jsx)(`h4`,{className:`font-medium text-sm mb-2`,children:`Grammar`}),(0,I.jsxs)(`div`,{className:`text-2xl font-bold text-purple-600 mb-1`,children:[v.linguisticAnalysis.grammarScore,`/100`]}),(0,I.jsx)(S,{value:v.linguisticAnalysis.grammarScore,className:`h-2`})]})]}),(0,I.jsxs)(`div`,{className:`mt-6`,children:[(0,I.jsx)(`h4`,{className:`font-medium text-sm mb-2`,children:`Tone Analysis:`}),(0,I.jsx)(`div`,{className:`flex flex-wrap gap-2`,children:v.linguisticAnalysis.toneAnalysis.map((e,t)=>(0,I.jsx)(d,{variant:`outline`,className:`text-xs`,children:e},t))}),(0,I.jsxs)(`div`,{className:`mt-4`,children:[(0,I.jsx)(`span`,{className:`font-medium text-sm`,children:`Vocabulary Level: `}),(0,I.jsx)(d,{variant:`secondary`,className:`capitalize`,children:v.linguisticAnalysis.vocabularyLevel})]})]})]})]})]}),(0,I.jsx)(w,{value:`market`,className:`space-y-4`,children:(0,I.jsxs)(`div`,{className:`grid grid-cols-1 md:grid-cols-2 gap-6`,children:[(0,I.jsxs)(a,{children:[(0,I.jsxs)(c,{children:[(0,I.jsx)(l,{children:`Market Performance`}),(0,I.jsx)(s,{children:`Dating market analysis`})]}),(0,I.jsx)(o,{children:(0,I.jsxs)(`div`,{className:`space-y-4`,children:[(0,I.jsxs)(`div`,{children:[(0,I.jsxs)(`div`,{className:`flex justify-between text-sm mb-1`,children:[(0,I.jsx)(`span`,{className:`font-medium`,children:`Target Audience Alignment`}),(0,I.jsxs)(`span`,{children:[v.marketAnalysis.targetAudienceAlignment,`/100`]})]}),(0,I.jsx)(S,{value:v.marketAnalysis.targetAudienceAlignment,className:`h-2`})]}),(0,I.jsxs)(`div`,{children:[(0,I.jsxs)(`div`,{className:`flex justify-between text-sm mb-1`,children:[(0,I.jsx)(`span`,{className:`font-medium`,children:`Conversion Potential`}),(0,I.jsxs)(`span`,{children:[v.marketAnalysis.conversionPotential,`/100`]})]}),(0,I.jsx)(S,{value:v.marketAnalysis.conversionPotential,className:`h-2`})]}),(0,I.jsxs)(`div`,{children:[(0,I.jsxs)(`div`,{className:`flex justify-between text-sm mb-1`,children:[(0,I.jsx)(`span`,{className:`font-medium`,children:`Engagement Probability`}),(0,I.jsxs)(`span`,{children:[v.marketAnalysis.engagementProbability,`/100`]})]}),(0,I.jsx)(S,{value:v.marketAnalysis.engagementProbability,className:`h-2`})]})]})})]}),(0,I.jsxs)(a,{children:[(0,I.jsxs)(c,{children:[(0,I.jsx)(l,{children:`Market Positioning`}),(0,I.jsx)(s,{children:`Competitive analysis`})]}),(0,I.jsx)(o,{children:(0,I.jsxs)(`div`,{className:`space-y-4`,children:[(0,I.jsxs)(`div`,{children:[(0,I.jsx)(`h4`,{className:`font-medium text-sm mb-2`,children:`Competitive Position:`}),(0,I.jsx)(d,{variant:`outline`,className:`text-lg capitalize`,children:v.marketAnalysis.competitivePositioning})]}),(0,I.jsxs)(`div`,{children:[(0,I.jsx)(`h4`,{className:`font-medium text-sm mb-2`,children:`Niche Appeal:`}),(0,I.jsx)(`div`,{className:`flex flex-wrap gap-2`,children:v.marketAnalysis.nicheAppeal.map((e,t)=>(0,I.jsx)(d,{variant:`secondary`,className:`text-xs`,children:e},t))})]})]})})]})]})}),(0,I.jsx)(w,{value:`improvements`,className:`space-y-6`,children:(0,I.jsxs)(`div`,{className:`grid grid-cols-1 gap-6`,children:[(0,I.jsxs)(a,{children:[(0,I.jsxs)(c,{children:[(0,I.jsx)(l,{children:`Original Bio`}),(0,I.jsx)(s,{children:`Your current bio for reference`})]}),(0,I.jsxs)(o,{children:[(0,I.jsx)(`div`,{className:`bg-gray-50 p-4 rounded-lg mb-4`,children:(0,I.jsx)(`p`,{className:`text-sm whitespace-pre-wrap`,children:v.originalBio})}),(0,I.jsxs)(u,{onClick:()=>L(v.originalBio),size:`sm`,variant:`outline`,children:[(0,I.jsx)(m,{className:`mr-2 h-4 w-4`}),` Copy Original`]})]})]}),(0,I.jsx)(`div`,{className:`grid grid-cols-1 md:grid-cols-3 gap-6`,children:Object.entries(v.improvedVersions).map(([e,t])=>(0,I.jsxs)(a,{children:[(0,I.jsxs)(c,{children:[(0,I.jsxs)(l,{className:`capitalize`,children:[e,` Version`]}),(0,I.jsxs)(s,{children:[`AI-optimized bio in `,e,` tone`]})]}),(0,I.jsxs)(o,{children:[(0,I.jsx)(`div`,{className:`bg-gray-50 p-4 rounded-lg mb-4 min-h-[120px]`,children:(0,I.jsx)(`p`,{className:`text-sm whitespace-pre-wrap`,children:t})}),(0,I.jsxs)(u,{onClick:()=>L(t),size:`sm`,variant:`outline`,className:`w-full`,children:[(0,I.jsx)(m,{className:`mr-2 h-4 w-4`}),` Copy `,e]})]})]},e))})]})}),(0,I.jsx)(w,{value:`comparison`,className:`space-y-4`,children:(0,I.jsxs)(a,{children:[(0,I.jsxs)(c,{children:[(0,I.jsx)(l,{children:`Market Comparison`}),(0,I.jsx)(s,{children:`How your bio compares to others`})]}),(0,I.jsx)(o,{children:(0,I.jsxs)(`div`,{className:`grid grid-cols-1 md:grid-cols-2 gap-6`,children:[(0,I.jsxs)(`div`,{children:[(0,I.jsx)(`h4`,{className:`font-medium text-sm mb-4`,children:`Market Position`}),(0,I.jsxs)(`div`,{className:`text-center p-6 bg-gray-50 rounded-lg`,children:[(0,I.jsx)(`div`,{className:`text-3xl font-bold text-purple-600 mb-2`,children:v.comparativeAnalysis.marketPosition.replace(`_`,` `).toUpperCase()}),(0,I.jsxs)(`p`,{className:`text-sm text-gray-600`,children:[`Top `,v.comparativeAnalysis.topPercentile,`% of bios`]})]})]}),(0,I.jsxs)(`div`,{children:[(0,I.jsx)(`h4`,{className:`font-medium text-sm mb-4`,children:`Competitive Advantages`}),(0,I.jsx)(`div`,{className:`space-y-2`,children:v.comparativeAnalysis.competitiveAdvantages.map((e,t)=>(0,I.jsxs)(`div`,{className:`flex items-center space-x-2`,children:[(0,I.jsx)(`span`,{className:`text-green-500`,children:`✓`}),(0,I.jsx)(`span`,{className:`text-sm`,children:e})]},t))}),(0,I.jsx)(`h4`,{className:`font-medium text-sm mb-2 mt-4`,children:`Areas for Improvement`}),(0,I.jsx)(`div`,{className:`space-y-2`,children:v.comparativeAnalysis.areasForImprovement.map((e,t)=>(0,I.jsxs)(`div`,{className:`flex items-center space-x-2`,children:[(0,I.jsx)(`span`,{className:`text-orange-500`,children:`→`}),(0,I.jsx)(`span`,{className:`text-sm`,children:e})]},t))})]})]})})]})})]})})]}),(0,I.jsxs)(`div`,{className:`text-center`,children:[(0,I.jsx)(u,{variant:`outline`,onClick:()=>{i(`idle`),D(null),n(``)},className:`mr-4`,children:`Analyze Another Bio`}),(0,I.jsx)(u,{asChild:!0,size:`lg`,children:(0,I.jsxs)(e,{to:`/image-analyzer-pro`,children:[`Analyze Photos with Advanced AI `,(0,I.jsx)(y,{className:`ml-2 h-5 w-5`})]})})]})]})]})]})}const R=L;export{R as component};