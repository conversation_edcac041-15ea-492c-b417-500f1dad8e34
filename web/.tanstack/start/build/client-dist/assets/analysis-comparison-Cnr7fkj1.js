import{Link as e,__toESM as t,require_jsx_runtime as n}from"./main-B4G73TvM.js";import{Card as r,CardContent as i,CardDescription as a,CardHeader as o,CardTitle as s}from"./card-Bzfaa5B9.js";import{Button as c,createLucideIcon as l}from"./createLucideIcon-JB7IMeGf.js";import{Badge as u}from"./badge-B_rsQKkv.js";import{Brain as d}from"./brain-wUBLYMm0.js";import{Camera as f}from"./camera-P6IreDGA.js";import{Crown as p}from"./crown-BUU0WoJh.js";import{Sparkles as m}from"./sparkles-BbQK6jEu.js";import{TrendingUp as h}from"./trending-up-BvkHvnfP.js";import{User as g}from"./user-DxSNRym-.js";const _=l(`ArrowRight`,[[`path`,{d:`M5 12h14`,key:`1ays0h`}],[`path`,{d:`m12 5 7 7-7 7`,key:`xquz4c`}]]),v=l(`Check`,[[`path`,{d:`M20 6 9 17l-5-5`,key:`1gmf2c`}]]);var y=t(n());function b(){let t=[{category:`AI Model`,basic:`Gemini 2.5 Flash / GPT-4o Mini`,advanced:`OpenRouter o3 (Latest Reasoning Model)`,basicIcon:`⚡`,advancedIcon:`🧠`},{category:`Analysis Depth`,basic:`6 Standard Steps`,advanced:`Multi-Expert, Multi-Phase Analysis`,basicIcon:`📊`,advancedIcon:`🔬`},{category:`Expert Perspectives`,basic:`Single AI Analysis`,advanced:`5 Expert Personas (Photography, Psychology, Fashion, Data Science, Dating Coach)`,basicIcon:`👤`,advancedIcon:`👥`},{category:`Scoring System`,basic:`Simple 0-100 Scores`,advanced:`Weighted Sub-Scores, Percentile Rankings, Market Competitiveness`,basicIcon:`📈`,advancedIcon:`📊`},{category:`Insights Quality`,basic:`Generic Recommendations`,advanced:`Prioritized, Impact-Scored, Evidence-Based Insights`,basicIcon:`💡`,advancedIcon:`🎯`},{category:`Reasoning Transparency`,basic:`Score + Basic Insights`,advanced:`Full Chain-of-Thought, Evidence Citations, Confidence Metrics`,basicIcon:`📝`,advancedIcon:`🔍`},{category:`Comparative Analysis`,basic:`Standalone Assessment`,advanced:`Market Positioning, Competitive Benchmarking, Percentile Rankings`,basicIcon:`📋`,advancedIcon:`📊`},{category:`Personalization`,basic:`One-Size-Fits-All`,advanced:`Demographic-Aware, Platform-Specific, Target Audience Aligned`,basicIcon:`🎨`,advancedIcon:`🎯`},{category:`Processing Time`,basic:`~30 seconds`,advanced:`~2-3 minutes`,basicIcon:`⚡`,advancedIcon:`⏱️`},{category:`Cost`,basic:`Free`,advanced:`Premium Feature`,basicIcon:`🆓`,advancedIcon:`👑`}],n=[`Pre-analysis demographic detection`,`Technical quality assessment`,`Multi-expert visual analysis`,`Psychological appeal evaluation`,`Fashion and style assessment`,`Market performance prediction`,`Platform-specific optimization`,`Improvement impact scoring`],l=[`Linguistic analysis (readability, sentiment, grammar)`,`Big 5 personality trait detection`,`Attachment style assessment`,`Emotional intelligence indicators`,`Market positioning analysis`,`Target audience alignment`,`Three optimized bio versions`,`Conversion potential scoring`];return(0,y.jsxs)(`div`,{className:`min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50`,children:[(0,y.jsx)(`header`,{className:`border-b bg-white/80 backdrop-blur-sm`,children:(0,y.jsx)(`div`,{className:`container mx-auto px-4 py-6`,children:(0,y.jsxs)(`div`,{className:`text-center`,children:[(0,y.jsx)(`h1`,{className:`text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-2`,children:`Basic vs Advanced Analysis`}),(0,y.jsx)(`p`,{className:`text-lg text-gray-600`,children:`Compare our free basic analyzers with premium advanced AI analysis`})]})})}),(0,y.jsxs)(`main`,{className:`container mx-auto px-4 py-8`,children:[(0,y.jsxs)(`div`,{className:`grid grid-cols-1 md:grid-cols-2 gap-8 mb-12`,children:[(0,y.jsxs)(r,{className:`border-2`,children:[(0,y.jsxs)(o,{className:`text-center`,children:[(0,y.jsxs)(`div`,{className:`flex items-center justify-center space-x-2 mb-2`,children:[(0,y.jsx)(m,{className:`h-6 w-6 text-blue-600`}),(0,y.jsx)(s,{className:`text-2xl`,children:`Basic Analysis`})]}),(0,y.jsx)(a,{children:`Fast, reliable, and completely free`}),(0,y.jsx)(u,{variant:`secondary`,className:`w-fit mx-auto`,children:`Free Forever`})]}),(0,y.jsx)(i,{className:`text-center`,children:(0,y.jsxs)(`div`,{className:`space-y-4`,children:[(0,y.jsx)(`p`,{className:`text-sm text-gray-600`,children:`Get instant feedback with our proven AI analysis system. Perfect for quick improvements and general guidance.`}),(0,y.jsxs)(`div`,{className:`flex justify-center space-x-4`,children:[(0,y.jsx)(c,{asChild:!0,children:(0,y.jsxs)(e,{to:`/image-analyzer`,children:[(0,y.jsx)(f,{className:`mr-2 h-4 w-4`}),`Try Image Analyzer`]})}),(0,y.jsx)(c,{asChild:!0,variant:`outline`,children:(0,y.jsxs)(e,{to:`/bio-analyzer`,children:[(0,y.jsx)(g,{className:`mr-2 h-4 w-4`}),`Try Bio Analyzer`]})})]})]})})]}),(0,y.jsxs)(r,{className:`border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-blue-50`,children:[(0,y.jsxs)(o,{className:`text-center`,children:[(0,y.jsxs)(`div`,{className:`flex items-center justify-center space-x-2 mb-2`,children:[(0,y.jsx)(p,{className:`h-6 w-6 text-purple-600`}),(0,y.jsx)(s,{className:`text-2xl`,children:`Advanced Analysis`})]}),(0,y.jsx)(a,{children:`Professional-grade insights powered by OpenRouter o3`}),(0,y.jsx)(u,{className:`w-fit mx-auto bg-purple-600`,children:`Premium Feature`})]}),(0,y.jsx)(i,{className:`text-center`,children:(0,y.jsxs)(`div`,{className:`space-y-4`,children:[(0,y.jsx)(`p`,{className:`text-sm text-gray-600`,children:`Get expert-level analysis with our most advanced AI system. Comprehensive insights, detailed scoring, and professional recommendations.`}),(0,y.jsxs)(`div`,{className:`flex justify-center space-x-4`,children:[(0,y.jsx)(c,{asChild:!0,className:`bg-gradient-to-r from-purple-600 to-blue-600`,children:(0,y.jsxs)(e,{to:`/image-analyzer-pro`,children:[(0,y.jsx)(d,{className:`mr-2 h-4 w-4`}),`Try Advanced Image`]})}),(0,y.jsx)(c,{asChild:!0,variant:`outline`,className:`border-purple-300`,children:(0,y.jsxs)(e,{to:`/bio-analyzer-pro`,children:[(0,y.jsx)(h,{className:`mr-2 h-4 w-4`}),`Try Advanced Bio`]})})]})]})})]})]}),(0,y.jsxs)(r,{className:`mb-12`,children:[(0,y.jsxs)(o,{children:[(0,y.jsx)(s,{className:`text-2xl text-center`,children:`Feature Comparison`}),(0,y.jsx)(a,{className:`text-center`,children:`See exactly what you get with each analysis tier`})]}),(0,y.jsx)(i,{children:(0,y.jsx)(`div`,{className:`overflow-x-auto`,children:(0,y.jsxs)(`table`,{className:`w-full`,children:[(0,y.jsx)(`thead`,{children:(0,y.jsxs)(`tr`,{className:`border-b`,children:[(0,y.jsx)(`th`,{className:`text-left py-4 px-2 font-semibold`,children:`Feature`}),(0,y.jsx)(`th`,{className:`text-center py-4 px-2 font-semibold`,children:(0,y.jsxs)(`div`,{className:`flex items-center justify-center space-x-2`,children:[(0,y.jsx)(m,{className:`h-4 w-4 text-blue-600`}),(0,y.jsx)(`span`,{children:`Basic`})]})}),(0,y.jsx)(`th`,{className:`text-center py-4 px-2 font-semibold`,children:(0,y.jsxs)(`div`,{className:`flex items-center justify-center space-x-2`,children:[(0,y.jsx)(p,{className:`h-4 w-4 text-purple-600`}),(0,y.jsx)(`span`,{children:`Advanced`})]})})]})}),(0,y.jsx)(`tbody`,{children:t.map((e,t)=>(0,y.jsxs)(`tr`,{className:`border-b hover:bg-gray-50`,children:[(0,y.jsx)(`td`,{className:`py-4 px-2 font-medium`,children:e.category}),(0,y.jsx)(`td`,{className:`py-4 px-2 text-center`,children:(0,y.jsxs)(`div`,{className:`flex items-center justify-center space-x-2`,children:[(0,y.jsx)(`span`,{className:`text-lg`,children:e.basicIcon}),(0,y.jsx)(`span`,{className:`text-sm text-gray-600`,children:e.basic})]})}),(0,y.jsx)(`td`,{className:`py-4 px-2 text-center`,children:(0,y.jsxs)(`div`,{className:`flex items-center justify-center space-x-2`,children:[(0,y.jsx)(`span`,{className:`text-lg`,children:e.advancedIcon}),(0,y.jsx)(`span`,{className:`text-sm text-gray-700 font-medium`,children:e.advanced})]})})]},t))})]})})})]}),(0,y.jsxs)(`div`,{className:`grid grid-cols-1 md:grid-cols-2 gap-8 mb-12`,children:[(0,y.jsxs)(r,{children:[(0,y.jsxs)(o,{children:[(0,y.jsxs)(s,{className:`flex items-center space-x-2`,children:[(0,y.jsx)(f,{className:`h-5 w-5 text-purple-600`}),(0,y.jsx)(`span`,{children:`Advanced Image Analysis Features`})]}),(0,y.jsx)(a,{children:`Professional photography and psychology insights`})]}),(0,y.jsx)(i,{children:(0,y.jsx)(`div`,{className:`space-y-3`,children:n.map((e,t)=>(0,y.jsxs)(`div`,{className:`flex items-start space-x-3`,children:[(0,y.jsx)(v,{className:`h-4 w-4 text-green-500 mt-0.5 flex-shrink-0`}),(0,y.jsx)(`span`,{className:`text-sm`,children:e})]},t))})})]}),(0,y.jsxs)(r,{children:[(0,y.jsxs)(o,{children:[(0,y.jsxs)(s,{className:`flex items-center space-x-2`,children:[(0,y.jsx)(g,{className:`h-5 w-5 text-blue-600`}),(0,y.jsx)(`span`,{children:`Advanced Bio Analysis Features`})]}),(0,y.jsx)(a,{children:`Psychological profiling and market optimization`})]}),(0,y.jsx)(i,{children:(0,y.jsx)(`div`,{className:`space-y-3`,children:l.map((e,t)=>(0,y.jsxs)(`div`,{className:`flex items-start space-x-3`,children:[(0,y.jsx)(v,{className:`h-4 w-4 text-green-500 mt-0.5 flex-shrink-0`}),(0,y.jsx)(`span`,{className:`text-sm`,children:e})]},t))})})]})]}),(0,y.jsx)(r,{className:`bg-gradient-to-r from-purple-600 to-blue-600 text-white`,children:(0,y.jsxs)(i,{className:`text-center py-12`,children:[(0,y.jsx)(`h2`,{className:`text-3xl font-bold mb-4`,children:`Ready to Upgrade Your Analysis?`}),(0,y.jsx)(`p`,{className:`text-lg mb-8 opacity-90`,children:`Experience the power of professional-grade AI analysis with our advanced system`}),(0,y.jsxs)(`div`,{className:`flex justify-center space-x-4`,children:[(0,y.jsx)(c,{size:`lg`,variant:`secondary`,children:(0,y.jsxs)(e,{to:`/image-analyzer-pro`,className:`flex items-center`,children:[`Start Advanced Image Analysis`,(0,y.jsx)(_,{className:`ml-2 h-5 w-5`})]})}),(0,y.jsx)(c,{size:`lg`,variant:`outline`,className:`border-white text-white hover:bg-white hover:text-purple-600`,children:(0,y.jsxs)(e,{to:`/bio-analyzer-pro`,className:`flex items-center`,children:[`Start Advanced Bio Analysis`,(0,y.jsx)(_,{className:`ml-2 h-5 w-5`})]})})]})]})})]})]})}const x=b;export{x as component};