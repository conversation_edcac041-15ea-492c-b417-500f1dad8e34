const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/image-editing-service-Bu1i98x1.js","assets/image-editing-service-Ck8uvW7j.js","assets/dist-CgCb95MR.js","assets/main-B4G73TvM.js","assets/main-Cx3HDUKW.css","assets/analysis-BhwX0Tn_.js"])))=>i.map(i=>d[i]);
import{__toESM as e,__vitePreload as t,cn as n,require_jsx_runtime as r,require_react as i}from"./main-B4G73TvM.js";import"./dist-CgCb95MR.js";import{imageEditingService as a,securityService as o}from"./image-editing-service-Ck8uvW7j.js";import"./analysis-BhwX0Tn_.js";import{Card as s,CardContent as c,CardDescription as l,<PERSON><PERSON>eader as u,CardTitle as d}from"./card-Bzfaa5B9.js";import{Button as f,createLucideIcon as p,cva as m}from"./createLucideIcon-JB7IMeGf.js";import{Badge as h}from"./badge-B_rsQKkv.js";import{Primitive as g,Trash2 as _,useComposedRefs as v}from"./trash-2-CkLpzZy2.js";import{Download as y}from"./download-D5v-g3Ed.js";import{RefreshCw as b,Slider as x}from"./slider-DbQhj1b4.js";import{Settings as S}from"./settings-Tged_CGs.js";import{Progress as C}from"./progress-BIjJajwn.js";import{Tabs as w,TabsContent as T,TabsList as ee,TabsTrigger as E}from"./tabs-CvPwdQTd.js";import{composeEventHandlers as D,createContextScope as O,useControllableState as k,useLayoutEffect2 as A}from"./dist-DGuqR-4V.js";const j=p(`ChartNoAxesColumnIncreasing`,[[`line`,{x1:`12`,x2:`12`,y1:`20`,y2:`10`,key:`1vz5eb`}],[`line`,{x1:`18`,x2:`18`,y1:`20`,y2:`4`,key:`cun8e5`}],[`line`,{x1:`6`,x2:`6`,y1:`20`,y2:`16`,key:`hq0ia6`}]]),M=p(`CircleAlert`,[[`circle`,{cx:`12`,cy:`12`,r:`10`,key:`1mglay`}],[`line`,{x1:`12`,x2:`12`,y1:`8`,y2:`12`,key:`1pkeuh`}],[`line`,{x1:`12`,x2:`12.01`,y1:`16`,y2:`16`,key:`4dfq90`}]]),te=p(`Clock`,[[`circle`,{cx:`12`,cy:`12`,r:`10`,key:`1mglay`}],[`polyline`,{points:`12 6 12 12 16 14`,key:`68esgv`}]]),ne=p(`Image`,[[`rect`,{width:`18`,height:`18`,x:`3`,y:`3`,rx:`2`,ry:`2`,key:`1m3agn`}],[`circle`,{cx:`9`,cy:`9`,r:`2`,key:`af1f0g`}],[`path`,{d:`m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21`,key:`1xmnt7`}]]),re=p(`Pause`,[[`rect`,{x:`14`,y:`4`,width:`4`,height:`16`,rx:`1`,key:`zuxfzm`}],[`rect`,{x:`6`,y:`4`,width:`4`,height:`16`,rx:`1`,key:`1okwgv`}]]),ie=p(`Play`,[[`polygon`,{points:`6 3 20 12 6 21 6 3`,key:`1oa8hb`}]]),ae=p(`Upload`,[[`path`,{d:`M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4`,key:`ih7n3h`}],[`polyline`,{points:`17 8 12 3 7 8`,key:`t8dd8p`}],[`line`,{x1:`12`,x2:`12`,y1:`3`,y2:`15`,key:`widbto`}]]),oe=p(`Zap`,[[`path`,{d:`M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z`,key:`1xq2db`}]]);var N=e(i()),P=e(r());const F=m(`relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground`,{variants:{variant:{default:`bg-background text-foreground`,destructive:`border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive`}},defaultVariants:{variant:`default`}}),I=N.forwardRef(({className:e,variant:t,...r},i)=>(0,P.jsx)(`div`,{ref:i,role:`alert`,className:n(F({variant:t}),e),...r}));I.displayName=`Alert`;const L=N.forwardRef(({className:e,...t},r)=>(0,P.jsx)(`h5`,{ref:r,className:n(`mb-1 font-medium leading-none tracking-tight`,e),...t}));L.displayName=`AlertTitle`;const R=N.forwardRef(({className:e,...t},r)=>(0,P.jsx)(`div`,{ref:r,className:n(`text-sm [&_p]:leading-relaxed`,e),...t}));R.displayName=`AlertDescription`;function z(e){let t=N.useRef({value:e,previous:e});return N.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}function B(e){let[t,n]=N.useState(void 0);return A(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{if(!Array.isArray(t)||!t.length)return;let r=t[0],i,a;if(`borderBoxSize`in r){let e=r.borderBoxSize,t=Array.isArray(e)?e[0]:e;i=t.inlineSize,a=t.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return t.observe(e,{box:`border-box`}),()=>t.unobserve(e)}else n(void 0)},[e]),t}var V=`Switch`,[H,U]=O(V),[W,G]=H(V),K=N.forwardRef((e,t)=>{let{__scopeSwitch:n,name:r,checked:i,defaultChecked:a,required:o,disabled:s,value:c=`on`,onCheckedChange:l,form:u,...d}=e,[f,p]=N.useState(null),m=v(t,e=>p(e)),h=N.useRef(!1),_=f?u||!!f.closest(`form`):!0,[y=!1,b]=k({prop:i,defaultProp:a,onChange:l});return(0,P.jsxs)(W,{scope:n,checked:y,disabled:s,children:[(0,P.jsx)(g.button,{type:`button`,role:`switch`,"aria-checked":y,"aria-required":o,"data-state":Y(y),"data-disabled":s?``:void 0,disabled:s,value:c,...d,ref:m,onClick:D(e.onClick,e=>{b(e=>!e),_&&(h.current=e.isPropagationStopped(),h.current||e.stopPropagation())})}),_&&(0,P.jsx)(se,{control:f,bubbles:!h.current,name:r,value:c,checked:y,required:o,disabled:s,form:u,style:{transform:`translateX(-100%)`}})]})});K.displayName=V;var q=`SwitchThumb`,J=N.forwardRef((e,t)=>{let{__scopeSwitch:n,...r}=e,i=G(q,n);return(0,P.jsx)(g.span,{"data-state":Y(i.checked),"data-disabled":i.disabled?``:void 0,...r,ref:t})});J.displayName=q;var se=e=>{let{control:t,checked:n,bubbles:r=!0,...i}=e,a=N.useRef(null),o=z(n),s=B(t);return N.useEffect(()=>{let e=a.current,t=window.HTMLInputElement.prototype,i=Object.getOwnPropertyDescriptor(t,`checked`),s=i.set;if(o!==n&&s){let t=new Event(`click`,{bubbles:r});s.call(e,n),e.dispatchEvent(t)}},[o,n,r]),(0,P.jsx)(`input`,{type:`checkbox`,"aria-hidden":!0,defaultChecked:n,...i,tabIndex:-1,ref:a,style:{...e.style,...s,position:`absolute`,pointerEvents:`none`,opacity:0,margin:0}})};function Y(e){return e?`checked`:`unchecked`}var ce=K,le=J;const X=N.forwardRef(({className:e,...t},r)=>(0,P.jsx)(ce,{className:n(`peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input`,e),...t,ref:r,children:(0,P.jsx)(le,{className:n(`pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0`)})}));X.displayName=ce.displayName;var ue=`Label`,de=N.forwardRef((e,t)=>(0,P.jsx)(g.label,{...e,ref:t,onMouseDown:t=>{let n=t.target;n.closest(`button, input, select, textarea`)||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));de.displayName=ue;var fe=de;const pe=m(`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70`),Z=N.forwardRef(({className:e,...t},r)=>(0,P.jsx)(fe,{ref:r,className:n(pe(),e),...t}));Z.displayName=fe.displayName;var me=class{maxRetries=3;baseDelay=1e3;maxDelay=3e4;async handleWithRetry(e,t,n=0){try{return await e()}catch(r){let i=this.categorizeError(r);if(this.logError(r,t,n),i.retryable&&n<this.maxRetries){let r=this.calculateDelay(n,i.retryAfter);return console.log(`Retrying ${t} in ${r}ms (attempt ${n+1}/${this.maxRetries})`),await this.sleep(r),this.handleWithRetry(e,t,n+1)}throw this.transformError(r,i)}}categorizeError(e){let t=e.message.toLowerCase(),n=e.toString().toLowerCase();return t.includes(`rate limit`)?{code:`RATE_LIMIT_EXCEEDED`,message:`Rate limit exceeded. Please try again later.`,retryable:!0,retryAfter:this.extractRetryAfter(e.message)||6e4}:t.includes(`insufficient quota`)||t.includes(`billing`)?{code:`INSUFFICIENT_QUOTA`,message:`Insufficient API quota. Please check your billing.`,retryable:!1}:t.includes(`invalid api key`)||t.includes(`unauthorized`)?{code:`INVALID_API_KEY`,message:`Invalid API key. Please check your configuration.`,retryable:!1}:t.includes(`timeout`)||t.includes(`network`)?{code:`NETWORK_ERROR`,message:`Network error. Please check your connection.`,retryable:!0,retryAfter:2e3}:t.includes(`server error`)||t.includes(`internal error`)?{code:`SERVER_ERROR`,message:`Server error. Please try again.`,retryable:!0,retryAfter:5e3}:t.includes(`image too large`)||t.includes(`file size`)?{code:`IMAGE_TOO_LARGE`,message:`Image file is too large. Please use a smaller image.`,retryable:!1}:t.includes(`invalid image`)||t.includes(`unsupported format`)?{code:`INVALID_IMAGE_FORMAT`,message:`Invalid image format. Please use a supported format (JPEG, PNG, WebP).`,retryable:!1}:t.includes(`content policy`)||t.includes(`safety`)?{code:`CONTENT_POLICY_VIOLATION`,message:`Image violates content policy. Please use a different image.`,retryable:!1}:n.includes(`fetch`)?{code:`FETCH_ERROR`,message:`Network request failed. Please try again.`,retryable:!0,retryAfter:2e3}:t.includes(`parse`)||t.includes(`json`)?{code:`PARSE_ERROR`,message:`Failed to parse response. Please try again.`,retryable:!0,retryAfter:1e3}:{code:`UNKNOWN_ERROR`,message:`An unexpected error occurred. Please try again.`,retryable:!0,retryAfter:5e3,details:{originalMessage:e.message,stack:e.stack}}}transformError(e,t){let n=Error(t.message);return n.code=t.code,n.retryable=t.retryable,n.retryAfter=t.retryAfter,n.details=t.details,n.originalError=e,n}extractRetryAfter(e){let t=e.match(/retry after (\d+) seconds?/i);if(t)return parseInt(t[1])*1e3;let n=e.match(/try again in (\d+) minutes?/i);return n?parseInt(n[1])*60*1e3:null}calculateDelay(e,t){if(t)return Math.min(t,this.maxDelay);let n=this.baseDelay*2**e,r=Math.random()*.1*n;return Math.min(n+r,this.maxDelay)}logError(e,t,n){let r={timestamp:new Date().toISOString(),context:t,retryCount:n,message:e.message,stack:e.stack,userAgent:navigator.userAgent,url:window.location.href};console.error(`ImageEditingError:`,r),this.sendToErrorTracking(r)}sendToErrorTracking(e){try{let t=JSON.parse(localStorage.getItem(`tinderop_errors`)||`[]`);t.push(e),t.length>100&&t.splice(0,t.length-100),localStorage.setItem(`tinderop_errors`,JSON.stringify(t))}catch(e){console.warn(`Failed to store error for tracking:`,e)}}getRecentErrors(){try{return JSON.parse(localStorage.getItem(`tinderop_errors`)||`[]`)}catch(e){return console.warn(`Failed to retrieve recent errors:`,e),[]}}clearErrors(){localStorage.removeItem(`tinderop_errors`)}sleep(e){return new Promise(t=>setTimeout(t,e))}validateImage(e,t){let n=e.length*3/4,r=20*1024*1024;if(n>r)throw Error(`Image file is too large (${(n/1024/1024).toFixed(1)}MB). Maximum size is ${r/1024/1024}MB.`);let i=[`image/jpeg`,`image/png`,`image/webp`],a=this.getImageFormat(e);if(!i.includes(a))throw Error(`Unsupported image format: ${a}. Supported formats: ${i.join(`, `)}`);if(!this.isValidBase64(e))throw Error(`Invalid image data. Please try uploading the image again.`)}getImageFormat(e){let t=e.substring(0,50);return t.startsWith(`/9j/`)?`image/jpeg`:t.startsWith(`iVBORw0KGgo`)?`image/png`:t.startsWith(`UklGR`)?`image/webp`:`unknown`}isValidBase64(e){try{let t=e.replace(/^data:image\/[a-z]+;base64,/,``);return btoa(atob(t))===t}catch{return!1}}};const he=new me;var ge=class{logs=[];maxLogs=1e3;logLevel=`INFO`;constructor(){this.startHealthMonitoring()}logCacheHit(e,t){this.log(`INFO`,`CACHE_HIT`,{requestId:e,imageHash:t.substring(0,10),timestamp:Date.now()})}logCacheMiss(e,t){this.log(`INFO`,`CACHE_MISS`,{requestId:e,imageHash:t.substring(0,10),timestamp:Date.now()})}logApiRequest(e,t,n){this.log(`INFO`,`API_REQUEST`,{requestId:e,endpoint:t,method:n,timestamp:Date.now()})}logApiResponse(e,t,n){this.log(`INFO`,`API_RESPONSE`,{requestId:e,statusCode:t,responseTime:n,timestamp:Date.now()})}logImageAnalysis(e,t){this.log(`INFO`,`IMAGE_ANALYSIS`,{requestId:e,imageHash:t.imageHash.substring(0,10),processingTime:t.processingTime,recommendationsCount:t.recommendationsCount,improvementScore:t.improvementScore,timestamp:Date.now()})}logImageGeneration(e,t){this.log(`INFO`,`IMAGE_GENERATION`,{requestId:e,promptLength:t.prompt.length,fidelityLevel:t.fidelityLevel,tokens:t.tokens,cost:t.cost,timestamp:Date.now()})}logError(e,t){this.log(`ERROR`,`ERROR_OCCURRED`,{requestId:e,errorMessage:t.message,errorStack:t.stack,timestamp:Date.now()})}logRateLimitHit(e,t){this.log(`WARN`,`RATE_LIMIT_HIT`,{userId:e,limitType:t,timestamp:Date.now()})}logPerformanceMetrics(e,t){this.log(`INFO`,`PERFORMANCE_METRICS`,{requestId:e,...t,timestamp:Date.now()})}logUserAction(e,t,n){this.log(`INFO`,`USER_ACTION`,{userId:e,action:t,metadata:n,timestamp:Date.now()})}logSystemHealth(e){this.log(`INFO`,`SYSTEM_HEALTH`,{...e,timestamp:Date.now()})}getLogs(e){let t=this.logs;return e&&(e.level&&(t=t.filter(t=>t.level===e.level)),e.type&&(t=t.filter(t=>t.type===e.type)),e.requestId&&(t=t.filter(t=>t.data.requestId===e.requestId)),e.userId&&(t=t.filter(t=>t.data.userId===e.userId)),e.startTime&&(t=t.filter(t=>t.timestamp>=e.startTime)),e.endTime&&(t=t.filter(t=>t.timestamp<=e.endTime))),t.sort((e,t)=>t.timestamp-e.timestamp),e?.limit&&(t=t.slice(0,e.limit)),t}getAnalytics(e=`24h`){let t=Date.now(),n=this.getTimeRangeMs(e),r=t-n,i=this.logs.filter(e=>e.timestamp>=r),a=i.filter(e=>e.type===`API_REQUEST`).length,o=i.filter(e=>e.type===`API_RESPONSE`&&e.data.statusCode>=200&&e.data.statusCode<300).length,s=i.filter(e=>e.level===`ERROR`).length,c=i.filter(e=>e.type===`CACHE_HIT`).length,l=i.filter(e=>e.type===`CACHE_MISS`).length,u=i.filter(e=>e.type===`API_RESPONSE`).map(e=>e.data.responseTime||0),d=u.length>0?u.reduce((e,t)=>e+t,0)/u.length:0,f=i.filter(e=>e.type===`IMAGE_GENERATION`).map(e=>e.data.fidelityLevel||`unknown`),p=f.reduce((e,t)=>(e[t]=(e[t]||0)+1,e),{}),m=Object.entries(p).map(([e,t])=>({type:e,count:t})).sort((e,t)=>t.count-e.count),h=i.reduce((e,t)=>{let n=new Date(t.timestamp).getHours();return e[n]=(e[n]||0)+1,e},{}),g=Object.entries(h).map(([e,t])=>({hour:parseInt(e),count:t})).sort((e,t)=>t.count-e.count).slice(0,5),_=i.filter(e=>e.type===`IMAGE_GENERATION`),v=_.reduce((e,t)=>e+(t.data.cost||0),0),y=_.length>0?v/_.length:0,b=_.reduce((e,t)=>{let n=t.data.fidelityLevel||`unknown`;return e[n]=(e[n]||0)+(t.data.cost||0),e},{});return{totalRequests:a,successRate:a>0?o/a*100:0,averageResponseTime:d,errorRate:a>0?s/a*100:0,cacheHitRate:c+l>0?c/(c+l)*100:0,popularEditingTypes:m,peakHours:g,costAnalysis:{totalCost:v,averageCostPerRequest:y,costByType:Object.entries(b).map(([e,t])=>({type:e,cost:t}))}}}exportLogs(e=`json`){if(e===`json`)return JSON.stringify(this.logs,null,2);let t=[`timestamp`,`level`,`type`,`requestId`,`data`],n=this.logs.map(e=>[new Date(e.timestamp).toISOString(),e.level,e.type,e.data.requestId||``,JSON.stringify(e.data)]);return[t.join(`,`),...n.map(e=>e.join(`,`))].join(`
`)}clearLogs(){this.logs=[],this.persistLogs()}log(e,t,n){if(!this.shouldLog(e))return;let r={timestamp:Date.now(),level:e,type:t,data:n};this.logs.push(r),this.logs.length>this.maxLogs&&(this.logs=this.logs.slice(-this.maxLogs)),this.persistLogs()}shouldLog(e){let t=[`DEBUG`,`INFO`,`WARN`,`ERROR`],n=t.indexOf(this.logLevel),r=t.indexOf(e);return r>=n}getTimeRangeMs(e){let t={"1h":60*60*1e3,"24h":24*60*60*1e3,"7d":7*24*60*60*1e3,"30d":30*24*60*60*1e3};return t[e]||t[`24h`]}persistLogs(){try{let e=this.logs.slice(-100);localStorage.setItem(`tinderop_logs`,JSON.stringify(e))}catch(e){console.warn(`Failed to persist logs:`,e)}}loadPersistedLogs(){try{let e=localStorage.getItem(`tinderop_logs`);if(e){let t=JSON.parse(e);this.logs=t}}catch(e){console.warn(`Failed to load persisted logs:`,e)}}startHealthMonitoring(){this.loadPersistedLogs(),setInterval(()=>{this.checkSystemHealth()},5*60*1e3)}checkSystemHealth(){let e=this.logs.filter(e=>e.timestamp>Date.now()-5*60*1e3),t=e.filter(e=>e.level===`ERROR`),n=e.filter(e=>e.type===`API_REQUEST`).length,r=e.filter(e=>e.type===`API_RESPONSE`&&e.data.statusCode>=200&&e.data.statusCode<300).length,i=n>0?t.length/n*100:0,a=n>0?r/n*100:100,o=e.filter(e=>e.type===`API_RESPONSE`).map(e=>e.data.responseTime||0),s=o.length>0?o.reduce((e,t)=>e+t,0)/o.length:0,c=`healthy`;i>50||a<50?c=`down`:(i>10||s>1e4)&&(c=`degraded`);let l={status:c,lastCheck:Date.now(),responseTime:s,errorRate:i,queueSize:0,cacheHitRate:this.calculateCacheHitRate(),activeRequests:0};this.logSystemHealth(l)}calculateCacheHitRate(){let e=this.logs.filter(e=>e.timestamp>Date.now()-60*60*1e3),t=e.filter(e=>e.type===`CACHE_HIT`).length,n=e.filter(e=>e.type===`CACHE_MISS`).length,r=t+n;return r>0?t/r*100:0}};const Q=new ge;var _e=class{queue=[];processing=new Map;maxConcurrentRequests=3;maxQueueSize=100;maxRetries=3;retryDelay=5e3;processingInterval;isProcessing=!1;constructor(){this.startProcessing()}async enqueue(e,t=0){if(this.queue.length>=this.maxQueueSize)throw Error(`Queue is full. Please try again later.`);let n=this.generateQueueId(),r={id:n,priority:t,request:e,retryCount:0,maxRetries:this.maxRetries,createdAt:Date.now()};return this.insertByPriority(r),Q.logUserAction(e.userId,`queue_added`,{queueId:n,priority:t,queueSize:this.queue.length}),n}getQueueStatus(){let e=Date.now(),t=this.queue.map(t=>e-t.createdAt),n=t.length>0?t.reduce((e,t)=>e+t,0)/t.length:0,r=3e4,i=this.queue.length*r/this.maxConcurrentRequests;return{queueSize:this.queue.length,processing:this.processing.size,averageWaitTime:n,estimatedWaitTime:i}}getItemStatus(e){if(this.processing.has(e))return{status:`processing`};let t=this.queue.findIndex(t=>t.id===e);if(t>=0){let e=this.queue[t],n=t*3e4;return{status:`queued`,position:t+1,estimatedTime:n,retryCount:e.retryCount}}return{status:`not_found`}}remove(e){let t=this.queue.findIndex(t=>t.id===e);return t>=0?(this.queue.splice(t,1),Q.logUserAction(`system`,`queue_removed`,{queueId:e}),!0):!1}clear(){this.queue=[],Q.logUserAction(`system`,`queue_cleared`,{timestamp:Date.now()})}pause(){this.isProcessing=!1,this.processingInterval&&clearInterval(this.processingInterval),Q.logUserAction(`system`,`queue_paused`,{timestamp:Date.now()})}resume(){this.isProcessing=!0,this.startProcessing(),Q.logUserAction(`system`,`queue_resumed`,{timestamp:Date.now()})}getAnalytics(){return{totalProcessed:0,successRate:95,averageProcessingTime:25e3,currentThroughput:2.5,peakQueueSize:15,retryRate:5}}startProcessing(){this.processingInterval&&clearInterval(this.processingInterval),this.isProcessing=!0,this.processingInterval=setInterval(()=>{this.processQueue()},1e3)}async processQueue(){if(!this.isProcessing||this.processing.size>=this.maxConcurrentRequests)return;let e=this.queue.shift();if(!e)return;e.processingStartedAt=Date.now();let t=this.processItem(e);this.processing.set(e.id,t);try{let n=await t;this.processing.delete(e.id),Q.logUserAction(e.request.userId,`queue_processed`,{queueId:e.id,success:!0,processingTime:Date.now()-(e.processingStartedAt||Date.now()),retryCount:e.retryCount}),this.notifyCompletion(e.id,n)}catch(t){this.processing.delete(e.id),e.retryCount<e.maxRetries?(e.retryCount++,setTimeout(()=>{this.insertByPriority(e)},this.retryDelay*e.retryCount),Q.logUserAction(e.request.userId,`queue_retry`,{queueId:e.id,retryCount:e.retryCount,error:t.message})):(Q.logUserAction(e.request.userId,`queue_failed`,{queueId:e.id,error:t.message,retryCount:e.retryCount}),this.notifyFailure(e.id,t))}}async processItem(e){let{imageEditingService:n}=await t(async()=>{let{imageEditingService:e}=await import(`./image-editing-service-Bu1i98x1.js`);return{imageEditingService:e}},__vite__mapDeps([0,1,2,3,4,5]));return n.analyzeImageForEditing(e.request,{onProgress:t=>{this.notifyProgress(e.id,t)}})}insertByPriority(e){let t=0;for(let n=0;n<this.queue.length;n++){if(this.queue[n].priority<e.priority){t=n;break}t=n+1}this.queue.splice(t,0,e)}generateQueueId(){return`queue_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}notifyCompletion(e,t){let n=new CustomEvent(`imageEditingComplete`,{detail:{id:e,result:t}});window.dispatchEvent(n)}notifyProgress(e,t){let n=new CustomEvent(`imageEditingProgress`,{detail:{id:e,progress:t}});window.dispatchEvent(n)}notifyFailure(e,t){let n=new CustomEvent(`imageEditingError`,{detail:{id:e,error:t.message}});window.dispatchEvent(n)}async enqueueBatch(e,t=0){let n=[];for(let r of e){let i=t+(e.length-n.length),a=await this.enqueue(r,i);n.push(a)}return n}updatePriority(e,t){let n=this.queue.findIndex(t=>t.id===e);if(n>=0){let e=this.queue.splice(n,1)[0];return e.priority=t,this.insertByPriority(e),!0}return!1}getHealth(){let e=[],t=Date.now();this.processing.size>0&&!this.isProcessing&&e.push(`Processing stalled`),this.queue.length>this.maxQueueSize*.8&&e.push(`Queue nearly full`);let n=this.queue.reduce((e,t)=>t.createdAt<e?t.createdAt:e,t),r=t-n;return r>5*60*1e3&&e.push(`Old items in queue`),{isHealthy:e.length===0,issues:e,metrics:{queueSize:this.queue.length,processingCount:this.processing.size,oldestItemAge:r,isProcessing:this.isProcessing}}}};const $=new _e;function ve(e={}){let{enableQueue:t=!1,enableBatch:n=!1,maxConcurrentRequests:r=3,autoRetry:i=!0,userId:s=`anonymous`}=e,[c,l]=(0,N.useState)(!1),[u,d]=(0,N.useState)(null),[f,p]=(0,N.useState)(null),[m,h]=(0,N.useState)(null),[g,_]=(0,N.useState)(null),[v,y]=(0,N.useState)({totalRequests:0,successRate:100,averageProcessingTime:0}),b=(0,N.useRef)(null),x=(0,N.useRef)(null);(0,N.useEffect)(()=>()=>{x.current&&x.current.abort()},[]),(0,N.useEffect)(()=>{if(!t)return;let e=e=>{e.detail.id===b.current&&d(e.detail.progress)},n=e=>{e.detail.id===b.current&&(p(e.detail.result),l(!1),d(null),b.current=null)},r=e=>{e.detail.id===b.current&&(h(e.detail.error),l(!1),d(null),b.current=null)};return window.addEventListener(`imageEditingProgress`,e),window.addEventListener(`imageEditingComplete`,n),window.addEventListener(`imageEditingError`,r),()=>{window.removeEventListener(`imageEditingProgress`,e),window.removeEventListener(`imageEditingComplete`,n),window.removeEventListener(`imageEditingError`,r)}},[t]);let S=(0,N.useCallback)(async(e,n)=>{try{l(!0),h(null),p(null),d(null);let r=await o.validateFile(e);if(!r.valid)throw Error(r.errors.join(`, `));let i=e,c=await ye(i),u=await o.generateSecureCacheKey(c),f={userId:s,imageBase64:c,imageHash:u,fileName:i.name,mimeType:i.type,preferences:n},m;if(t){let e=await $.enqueue(f,0);b.current=e;let t=$.getItemStatus(e);return _({position:t.position||0,estimatedTime:t.estimatedTime||0,queueSize:$.getQueueStatus().queueSize}),new Promise((t,n)=>{let r=()=>{let i=$.getItemStatus(e);i.status===`completed`?t(m):i.status===`failed`?n(Error(`Queue processing failed`)):i.status===`not_found`?n(Error(`Queue item not found`)):setTimeout(r,1e3)};r()})}else m=await a.analyzeImageForEditing(f,{onProgress:e=>{d(e)}});return p(m),E(!0,Date.now()-Date.now()),m}catch(e){let t=e instanceof Error?e.message:`Unknown error occurred`;throw h(t),E(!1,0),e}finally{l(!1),d(null)}},[t,s]),C=(0,N.useCallback)(async(e,t)=>{try{l(!0),h(null);let n=await a.generateEditedImage(t,e.editingPrompt,`high`);return n}catch(e){let t=e instanceof Error?e.message:`Failed to generate edited image`;throw h(t),e}finally{l(!1)}},[]),w=(0,N.useCallback)(async()=>{f?.originalImageHash&&console.log(`Retrying analysis...`)},[f]),T=(0,N.useCallback)(()=>{x.current&&x.current.abort(),b.current&&($.remove(b.current),b.current=null),l(!1),d(null),h(null)},[]),ee=(0,N.useCallback)(async(e,t)=>{if(!n)throw Error(`Batch processing is not enabled`);try{l(!0),h(null);let n=[];for(let r of e){let e=await o.validateFile(r);if(!e.valid)throw Error(`Invalid file ${r.name}: ${e.errors.join(`, `)}`);let i=r,a=await ye(i),c=await o.generateSecureCacheKey(a);n.push({userId:s,imageBase64:a,imageHash:c,fileName:i.name,mimeType:i.type,preferences:t})}let i=[],c=Math.min(r,n.length);for(let e=0;e<n.length;e+=c){let t=n.slice(e,e+c),r=t.map(t=>a.analyzeImageForEditing(t,{onProgress:t=>{d({...t,message:`Processing ${e+1} of ${n.length} images...`})}})),o=await Promise.allSettled(r);for(let e of o)e.status===`fulfilled`?i.push(e.value):console.error(`Batch processing error:`,e.reason)}return i}catch(e){let t=e instanceof Error?e.message:`Batch processing failed`;throw h(t),e}finally{l(!1),d(null)}},[n,r,s]),E=(0,N.useCallback)((e,t)=>{y(n=>({totalRequests:n.totalRequests+1,successRate:(n.successRate*n.totalRequests+(e?100:0))/(n.totalRequests+1),averageProcessingTime:(n.averageProcessingTime*n.totalRequests+t)/(n.totalRequests+1)}))},[]);return{isLoading:c,progress:u,result:f,error:m,analyzeImage:S,generateEditedImage:C,retryAnalysis:w,cancelAnalysis:T,analyzeBatch:ee,queueStatus:g,analytics:v}}function ye(e){return new Promise((t,n)=>{let r=new FileReader;r.onload=()=>{let e=r.result,n=e.split(`,`)[1];t(n)},r.onerror=n,r.readAsDataURL(e)})}function be(){let[e,t]=(0,N.useState)({queueSize:0,processing:0,averageWaitTime:0,estimatedWaitTime:0}),[n,r]=(0,N.useState)({isHealthy:!0,issues:[],metrics:{queueSize:0,processingCount:0,oldestItemAge:0,isProcessing:!0}}),[i,a]=(0,N.useState)({totalProcessed:0,successRate:0,averageProcessingTime:0,currentThroughput:0,peakQueueSize:0,retryRate:0});(0,N.useEffect)(()=>{let e=()=>{let e=$.getQueueStatus();t(e);let n=$.getHealth();r(n);let i=$.getAnalytics();a(i)};e();let n=setInterval(e,2e3);return()=>clearInterval(n)},[]);let o=(0,N.useCallback)(()=>{$.pause()},[]),s=(0,N.useCallback)(()=>{$.resume()},[]),c=(0,N.useCallback)(()=>{$.clear()},[]),l=(0,N.useCallback)(e=>$.getItemStatus(e),[]),u=(0,N.useCallback)(e=>$.remove(e),[]),d=(0,N.useCallback)((e,t)=>$.updatePriority(e,t),[]);return{queueStatus:e,queueHealth:n,pauseQueue:o,resumeQueue:s,clearQueue:c,getItemStatus:l,removeItem:u,updateItemPriority:d,analytics:i}}function xe(){let[e,t]=(0,N.useState)(`24h`),[n,r]=(0,N.useState)({}),[i,a]=(0,N.useState)({totalRequests:0,successRate:0,averageResponseTime:0,errorRate:0,cacheHitRate:0,popularEditingTypes:[],peakHours:[],costAnalysis:{totalCost:0,averageCostPerRequest:0,costByType:[]}}),[o,s]=(0,N.useState)([]),[c,l]=(0,N.useState)({status:`healthy`,lastCheck:Date.now(),responseTime:0,errorRate:0,queueSize:0,cacheHitRate:0,activeRequests:0}),u=(0,N.useCallback)(()=>{let t=Q.getAnalytics(e);a(t);let r=Q.getLogs({level:`ERROR`,limit:20,...n}),i=r.map(e=>({timestamp:new Date(e.timestamp).toISOString(),context:e.data.context||`Unknown`,message:e.data.errorMessage||`Unknown error`,retryCount:e.data.retryCount||0}));s(i);let o=Q.getLogs({type:`SYSTEM_HEALTH`,limit:1});if(o.length>0){let e=o[0].data;l({status:e.status||`healthy`,lastCheck:e.lastCheck||Date.now(),responseTime:e.responseTime||0,errorRate:e.errorRate||0,queueSize:e.queueSize||0,cacheHitRate:e.cacheHitRate||0,activeRequests:e.activeRequests||0})}},[e,n]);(0,N.useEffect)(()=>{u();let e=setInterval(u,3e4);return()=>clearInterval(e)},[u]);let d=(0,N.useCallback)(e=>{let t=Q.getLogs(n);return Q.exportLogs(e)},[n]),f=(0,N.useCallback)(()=>{Q.clearLogs(),u()},[u]);return{analytics:i,recentErrors:o,systemHealth:c,refreshAnalytics:u,exportAnalytics:d,clearAnalytics:f,setTimeRange:t,setFilters:r}}function Se(){let[e,t]=(0,N.useState)(null),[n,r]=(0,N.useState)(null),[i,a]=(0,N.useState)({style:`natural`,conservative:!0,maxBudget:5});console.log(`🎨 ImageEditorTest: Component initialized`,{selectedFile:e?.name,previewUrl:!!n,editingPreferences:i});let{isLoading:o,progress:p,result:m,error:g,analyzeImage:v,generateEditedImage:D,cancelAnalysis:O,analytics:k}=ve({enableQueue:!0,enableBatch:!0,userId:`test-user`});console.log(`🔧 ImageEditorTest: useImageEditing state`,{isLoading:o,hasProgress:!!p,hasResult:!!m,hasError:!!g,analytics:k});let{queueStatus:A,queueHealth:F,pauseQueue:L,resumeQueue:z,clearQueue:B}=be();console.log(`📋 ImageEditorTest: Queue status`,{queueStatus:A,queueHealth:F});let{analytics:V,recentErrors:H,systemHealth:U,refreshAnalytics:W,exportAnalytics:G}=xe();console.log(`📊 ImageEditorTest: Analytics`,{detailedAnalytics:V,recentErrors:H?.length,systemHealth:U});let K=(0,N.useCallback)(e=>{let n=e.target.files?.[0];if(console.log(`📁 ImageEditorTest: File selected`,{fileName:n?.name,fileSize:n?.size,fileType:n?.type}),n){t(n);let e=URL.createObjectURL(n);r(e),console.log(`🖼️ ImageEditorTest: Preview URL created`,{url:e})}},[]),q=(0,N.useCallback)(async()=>{if(!e){console.warn(`⚠️ ImageEditorTest: No file selected for analysis`);return}console.log(`🔍 ImageEditorTest: Starting analysis`,{fileName:e.name,preferences:i});try{let t=await v(e,i);console.log(`✅ ImageEditorTest: Analysis completed`,{analysisResult:t})}catch(e){console.error(`❌ ImageEditorTest: Analysis failed`,{error:e})}},[e,i,v]),J=(0,N.useCallback)(async e=>{if(!n){console.warn(`⚠️ ImageEditorTest: No preview URL for image generation`);return}console.log(`🎨 ImageEditorTest: Starting image generation`,{recommendation:e});try{let t=await fetch(n),r=await t.blob(),i=new FileReader;i.onload=async()=>{let t=i.result.split(`,`)[1];console.log(`🔄 ImageEditorTest: Converting to base64 completed`);let n=await D(e,t);console.log(`✅ ImageEditorTest: Image generation completed`,{editedImageLength:n.length});let r=new Blob([Buffer.from(n,`base64`)],{type:`image/png`}),a=URL.createObjectURL(r);console.log(`🖼️ ImageEditorTest: Edited image URL created`,{editedUrl:a})},i.readAsDataURL(r)}catch(e){console.error(`❌ ImageEditorTest: Image generation failed`,{error:e})}},[n,D]);return(0,P.jsxs)(`div`,{className:`container mx-auto p-6 space-y-6`,children:[(0,P.jsxs)(`div`,{className:`text-center space-y-2`,children:[(0,P.jsx)(`h1`,{className:`text-3xl font-bold`,children:`🧪 Image Editor Test Lab`}),(0,P.jsx)(`p`,{className:`text-muted-foreground`,children:`Test and debug the AI-powered image editing system`})]}),(0,P.jsxs)(w,{defaultValue:`upload`,className:`w-full`,children:[(0,P.jsxs)(ee,{className:`grid w-full grid-cols-5`,children:[(0,P.jsx)(E,{value:`upload`,children:`Upload & Test`}),(0,P.jsx)(E,{value:`queue`,children:`Queue`}),(0,P.jsx)(E,{value:`analytics`,children:`Analytics`}),(0,P.jsx)(E,{value:`health`,children:`Health`}),(0,P.jsx)(E,{value:`settings`,children:`Settings`})]}),(0,P.jsxs)(T,{value:`upload`,className:`space-y-6`,children:[(0,P.jsxs)(s,{children:[(0,P.jsxs)(u,{children:[(0,P.jsxs)(d,{className:`flex items-center gap-2`,children:[(0,P.jsx)(ae,{className:`w-5 h-5`}),`Upload Test Image`]}),(0,P.jsx)(l,{children:`Select an image to test the editing functionality`})]}),(0,P.jsxs)(c,{className:`space-y-4`,children:[(0,P.jsxs)(`div`,{className:`border-2 border-dashed border-gray-300 rounded-lg p-6 text-center`,children:[(0,P.jsx)(`input`,{type:`file`,accept:`image/*`,onChange:K,className:`hidden`,id:`file-upload`}),(0,P.jsxs)(`label`,{htmlFor:`file-upload`,className:`cursor-pointer`,children:[(0,P.jsx)(ae,{className:`mx-auto h-12 w-12 text-gray-400`}),(0,P.jsx)(`p`,{className:`mt-2 text-sm text-gray-600`,children:`Click to upload or drag and drop`}),(0,P.jsx)(`p`,{className:`text-xs text-gray-500`,children:`PNG, JPG, GIF up to 10MB`})]})]}),n&&(0,P.jsxs)(`div`,{className:`mt-4`,children:[(0,P.jsx)(`img`,{src:n,alt:`Preview`,className:`max-w-full h-64 object-contain mx-auto rounded-lg border`}),(0,P.jsxs)(`p`,{className:`text-sm text-center mt-2 text-gray-600`,children:[e?.name,` (`,(e?.size||0/1024/1024).toFixed(2),` MB)`]})]})]})]}),(0,P.jsxs)(s,{children:[(0,P.jsx)(u,{children:(0,P.jsxs)(d,{className:`flex items-center gap-2`,children:[(0,P.jsx)(S,{className:`w-5 h-5`}),`Test Settings`]})}),(0,P.jsx)(c,{className:`space-y-4`,children:(0,P.jsxs)(`div`,{className:`grid grid-cols-1 md:grid-cols-3 gap-4`,children:[(0,P.jsxs)(`div`,{className:`space-y-2`,children:[(0,P.jsx)(Z,{children:`Style`}),(0,P.jsxs)(`select`,{value:i.style,onChange:e=>a(t=>({...t,style:e.target.value})),className:`w-full p-2 border rounded`,children:[(0,P.jsx)(`option`,{value:`natural`,children:`Natural`}),(0,P.jsx)(`option`,{value:`dramatic`,children:`Dramatic`}),(0,P.jsx)(`option`,{value:`artistic`,children:`Artistic`})]})]}),(0,P.jsxs)(`div`,{className:`space-y-2`,children:[(0,P.jsx)(Z,{children:`Conservative Mode`}),(0,P.jsxs)(`div`,{className:`flex items-center space-x-2`,children:[(0,P.jsx)(X,{checked:i.conservative,onCheckedChange:e=>a(t=>({...t,conservative:e}))}),(0,P.jsx)(`span`,{className:`text-sm`,children:i.conservative?`On`:`Off`})]})]}),(0,P.jsxs)(`div`,{className:`space-y-2`,children:[(0,P.jsxs)(Z,{children:[`Max Budget: $`,i.maxBudget]}),(0,P.jsx)(x,{value:[i.maxBudget],onValueChange:([e])=>a(t=>({...t,maxBudget:e})),max:20,min:1,step:.5,className:`w-full`})]})]})})]}),(0,P.jsxs)(`div`,{className:`flex gap-4 justify-center`,children:[(0,P.jsx)(f,{onClick:q,disabled:!e||o,size:`lg`,children:o?(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(b,{className:`w-4 h-4 mr-2 animate-spin`}),`Analyzing...`]}):(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(oe,{className:`w-4 h-4 mr-2`}),`Start Analysis`]})}),o&&(0,P.jsx)(f,{onClick:O,variant:`outline`,size:`lg`,children:`Cancel`})]}),p&&(0,P.jsxs)(s,{children:[(0,P.jsx)(u,{children:(0,P.jsxs)(d,{className:`flex items-center gap-2`,children:[(0,P.jsx)(te,{className:`w-5 h-5`}),`Processing Progress`]})}),(0,P.jsx)(c,{children:(0,P.jsxs)(`div`,{className:`space-y-2`,children:[(0,P.jsxs)(`div`,{className:`flex justify-between text-sm`,children:[(0,P.jsx)(`span`,{children:p.stage}),(0,P.jsxs)(`span`,{children:[p.percentage,`%`]})]}),(0,P.jsx)(C,{value:p.percentage,className:`w-full`}),p.estimatedTimeRemaining&&(0,P.jsxs)(`p`,{className:`text-sm text-gray-600`,children:[`Estimated time remaining: `,p.estimatedTimeRemaining,`s`]})]})})]}),g&&(0,P.jsxs)(I,{variant:`destructive`,children:[(0,P.jsx)(M,{className:`h-4 w-4`}),(0,P.jsxs)(R,{children:[(0,P.jsx)(`strong`,{children:`Error:`}),` `,g]})]}),m&&(0,P.jsxs)(s,{children:[(0,P.jsx)(u,{children:(0,P.jsxs)(d,{className:`flex items-center gap-2`,children:[(0,P.jsx)(ne,{className:`w-5 h-5`}),`Analysis Results`]})}),(0,P.jsx)(c,{children:(0,P.jsxs)(`div`,{className:`space-y-4`,children:[(0,P.jsxs)(`div`,{className:`grid grid-cols-2 md:grid-cols-4 gap-4 text-center`,children:[(0,P.jsxs)(`div`,{children:[(0,P.jsx)(`div`,{className:`text-2xl font-bold`,children:m.overallScore}),(0,P.jsx)(`div`,{className:`text-sm text-gray-600`,children:`Overall Score`})]}),(0,P.jsxs)(`div`,{children:[(0,P.jsx)(`div`,{className:`text-2xl font-bold`,children:m.recommendations?.length||0}),(0,P.jsx)(`div`,{className:`text-sm text-gray-600`,children:`Recommendations`})]}),(0,P.jsxs)(`div`,{children:[(0,P.jsxs)(`div`,{className:`text-2xl font-bold`,children:[`$`,m.estimatedCost?.toFixed(2)||`0.00`]}),(0,P.jsx)(`div`,{className:`text-sm text-gray-600`,children:`Est. Cost`})]}),(0,P.jsxs)(`div`,{children:[(0,P.jsxs)(`div`,{className:`text-2xl font-bold`,children:[m.processingTime||0,`s`]}),(0,P.jsx)(`div`,{className:`text-sm text-gray-600`,children:`Process Time`})]})]}),m.recommendations&&m.recommendations.length>0&&(0,P.jsxs)(`div`,{className:`space-y-3`,children:[(0,P.jsx)(`h4`,{className:`font-medium`,children:`Editing Recommendations:`}),m.recommendations.map((e,t)=>(0,P.jsxs)(`div`,{className:`border rounded-lg p-4 space-y-2`,children:[(0,P.jsxs)(`div`,{className:`flex justify-between items-start`,children:[(0,P.jsx)(`h5`,{className:`font-medium`,children:e.type}),(0,P.jsxs)(`div`,{className:`flex gap-2`,children:[(0,P.jsxs)(h,{variant:`outline`,children:[`$`,e.estimatedCost?.toFixed(2)||`0.00`]}),(0,P.jsx)(h,{variant:`outline`,children:e.difficulty||`Unknown`})]})]}),(0,P.jsx)(`p`,{className:`text-sm text-gray-600`,children:e.description||`No description available`}),(0,P.jsxs)(`div`,{className:`flex justify-between items-center`,children:[(0,P.jsxs)(`span`,{className:`text-sm font-medium`,children:[`Expected: `,e.expectedImprovement||`Unknown`]}),(0,P.jsx)(f,{size:`sm`,onClick:()=>J(e),children:`Generate Edit`})]})]},t))]})]})})]})]}),(0,P.jsx)(T,{value:`queue`,className:`space-y-6`,children:(0,P.jsxs)(s,{children:[(0,P.jsx)(u,{children:(0,P.jsxs)(d,{className:`flex items-center gap-2`,children:[(0,P.jsx)(j,{className:`w-5 h-5`}),`Queue Management`]})}),(0,P.jsx)(c,{children:(0,P.jsxs)(`div`,{className:`space-y-4`,children:[(0,P.jsxs)(`div`,{className:`flex gap-2`,children:[(0,P.jsxs)(f,{onClick:L,variant:`outline`,size:`sm`,children:[(0,P.jsx)(re,{className:`w-4 h-4 mr-2`}),`Pause Queue`]}),(0,P.jsxs)(f,{onClick:z,variant:`outline`,size:`sm`,children:[(0,P.jsx)(ie,{className:`w-4 h-4 mr-2`}),`Resume Queue`]}),(0,P.jsxs)(f,{onClick:B,variant:`outline`,size:`sm`,children:[(0,P.jsx)(_,{className:`w-4 h-4 mr-2`}),`Clear Queue`]})]}),A&&(0,P.jsxs)(`div`,{className:`grid grid-cols-3 gap-4 text-center`,children:[(0,P.jsxs)(`div`,{children:[(0,P.jsx)(`div`,{className:`text-2xl font-bold`,children:A.position}),(0,P.jsx)(`div`,{className:`text-sm text-gray-600`,children:`Position`})]}),(0,P.jsxs)(`div`,{children:[(0,P.jsx)(`div`,{className:`text-2xl font-bold`,children:A.queueSize}),(0,P.jsx)(`div`,{className:`text-sm text-gray-600`,children:`Queue Size`})]}),(0,P.jsxs)(`div`,{children:[(0,P.jsxs)(`div`,{className:`text-2xl font-bold`,children:[A.estimatedTime,`s`]}),(0,P.jsx)(`div`,{className:`text-sm text-gray-600`,children:`Est. Time`})]})]}),F&&(0,P.jsxs)(`div`,{className:`mt-4`,children:[(0,P.jsx)(`h4`,{className:`font-medium mb-2`,children:`Queue Health`}),(0,P.jsxs)(`div`,{className:`text-sm space-y-1`,children:[(0,P.jsxs)(`div`,{children:[`Status: `,(0,P.jsx)(h,{children:F.status})]}),(0,P.jsxs)(`div`,{children:[`Processing Rate: `,F.processingRate,`/min`]}),(0,P.jsxs)(`div`,{children:[`Error Rate: `,F.errorRate,`%`]})]})]})]})})]})}),(0,P.jsx)(T,{value:`analytics`,className:`space-y-6`,children:(0,P.jsxs)(s,{children:[(0,P.jsxs)(u,{children:[(0,P.jsxs)(d,{className:`flex items-center gap-2`,children:[(0,P.jsx)(j,{className:`w-5 h-5`}),`System Analytics`]}),(0,P.jsxs)(`div`,{className:`flex gap-2`,children:[(0,P.jsxs)(f,{onClick:W,variant:`outline`,size:`sm`,children:[(0,P.jsx)(b,{className:`w-4 h-4 mr-2`}),`Refresh`]}),(0,P.jsxs)(f,{onClick:G,variant:`outline`,size:`sm`,children:[(0,P.jsx)(y,{className:`w-4 h-4 mr-2`}),`Export`]})]})]}),(0,P.jsxs)(c,{children:[(0,P.jsxs)(`div`,{className:`grid grid-cols-2 md:grid-cols-4 gap-4 text-center`,children:[(0,P.jsxs)(`div`,{children:[(0,P.jsx)(`div`,{className:`text-2xl font-bold`,children:V.totalRequests}),(0,P.jsx)(`div`,{className:`text-sm text-gray-600`,children:`Total Requests`})]}),(0,P.jsxs)(`div`,{children:[(0,P.jsxs)(`div`,{className:`text-2xl font-bold`,children:[V.successRate.toFixed(1),`%`]}),(0,P.jsx)(`div`,{className:`text-sm text-gray-600`,children:`Success Rate`})]}),(0,P.jsxs)(`div`,{children:[(0,P.jsxs)(`div`,{className:`text-2xl font-bold`,children:[V.averageProcessingTime.toFixed(1),`s`]}),(0,P.jsx)(`div`,{className:`text-sm text-gray-600`,children:`Avg Process Time`})]}),(0,P.jsxs)(`div`,{children:[(0,P.jsx)(`div`,{className:`text-2xl font-bold`,children:H?.length||0}),(0,P.jsx)(`div`,{className:`text-sm text-gray-600`,children:`Recent Errors`})]})]}),H&&H.length>0&&(0,P.jsxs)(`div`,{className:`mt-6`,children:[(0,P.jsx)(`h4`,{className:`font-medium mb-2`,children:`Recent Errors`}),(0,P.jsx)(`div`,{className:`space-y-2`,children:H.slice(0,5).map((e,t)=>(0,P.jsxs)(`div`,{className:`text-sm p-2 bg-red-50 rounded border-l-4 border-red-400`,children:[(0,P.jsx)(`div`,{className:`font-medium`,children:e.type}),(0,P.jsx)(`div`,{className:`text-gray-600`,children:e.message}),(0,P.jsx)(`div`,{className:`text-xs text-gray-500`,children:e.timestamp})]},t))})]})]})]})}),(0,P.jsx)(T,{value:`health`,className:`space-y-6`,children:(0,P.jsxs)(s,{children:[(0,P.jsx)(u,{children:(0,P.jsxs)(d,{className:`flex items-center gap-2`,children:[(0,P.jsx)(M,{className:`w-5 h-5`}),`System Health`]})}),(0,P.jsx)(c,{children:U?(0,P.jsxs)(`div`,{className:`space-y-4`,children:[(0,P.jsxs)(`div`,{className:`grid grid-cols-2 md:grid-cols-3 gap-4`,children:[(0,P.jsxs)(`div`,{className:`text-center`,children:[(0,P.jsx)(h,{variant:U.status===`healthy`?`default`:`destructive`,children:U.status}),(0,P.jsx)(`div`,{className:`text-sm text-gray-600 mt-1`,children:`Overall Status`})]}),(0,P.jsxs)(`div`,{className:`text-center`,children:[(0,P.jsxs)(`div`,{className:`text-2xl font-bold`,children:[U.uptime,`h`]}),(0,P.jsx)(`div`,{className:`text-sm text-gray-600`,children:`Uptime`})]}),(0,P.jsxs)(`div`,{className:`text-center`,children:[(0,P.jsxs)(`div`,{className:`text-2xl font-bold`,children:[U.memoryUsage,`%`]}),(0,P.jsx)(`div`,{className:`text-sm text-gray-600`,children:`Memory Usage`})]})]}),U.services&&(0,P.jsxs)(`div`,{children:[(0,P.jsx)(`h4`,{className:`font-medium mb-2`,children:`Service Status`}),(0,P.jsx)(`div`,{className:`space-y-2`,children:Object.entries(U.services).map(([e,t])=>(0,P.jsxs)(`div`,{className:`flex justify-between items-center`,children:[(0,P.jsx)(`span`,{className:`text-sm`,children:e}),(0,P.jsx)(h,{variant:t===`healthy`?`default`:`destructive`,children:t})]},e))})]})]}):(0,P.jsx)(`p`,{className:`text-gray-600`,children:`Loading system health data...`})})]})}),(0,P.jsx)(T,{value:`settings`,className:`space-y-6`,children:(0,P.jsxs)(s,{children:[(0,P.jsxs)(u,{children:[(0,P.jsx)(d,{children:`Debug Settings`}),(0,P.jsx)(l,{children:`Configure test environment settings`})]}),(0,P.jsx)(c,{children:(0,P.jsxs)(`div`,{className:`space-y-4`,children:[(0,P.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,P.jsx)(Z,{children:`Verbose Logging`}),(0,P.jsx)(X,{})]}),(0,P.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,P.jsx)(Z,{children:`Mock API Responses`}),(0,P.jsx)(X,{})]}),(0,P.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,P.jsx)(Z,{children:`Enable Queue`}),(0,P.jsx)(X,{defaultChecked:!0})]}),(0,P.jsxs)(`div`,{className:`flex items-center justify-between`,children:[(0,P.jsx)(Z,{children:`Enable Batch Processing`}),(0,P.jsx)(X,{defaultChecked:!0})]})]})})]})})]})]})}const Ce=Se;export{Ce as component};